stages:
    - build

buildRelease:
    stage: build
    cache:
        key: ${CI_COMMIT_REF_SLUG}
        paths:
            - node_modules/
            - .m2/
    variables:
        GIT_SUBMODULE_STRATEGY: recursive
        rc_version: $PKG_TAG
        projectid: $CI_PROJECT_ID
        projectname: $CI_PROJECT_NAME
        jobid: $CI_JOB_ID
        pipelineid: $CI_PIPELINE_ID
        commitid: $CI_COMMIT_SHA
        envlabel: "最初构建"

    script:
        - yarn install
        - npm run build:test
        - echo $workdir ${PWD}
        - /deploy/script/ci/frontend_file.py $projectname  $rc_version"_"$(date +%Y%m%d%H%M)  $projectid $pipelineid $jobid ${PWD} ${commitid} $envlabel
    tags:
        - t_PUBLIC
    only:
        refs:
            - test
    except:
        variables:
            - $CI_PIPELINE_SOURCE == 'push'
            - $CI_PIPELINE_SOURCE == 'merge_request_event'
            - $CI_PIPELINE_SOURCE == 'external_pull_request_event'

buildPre:
    stage: build
    cache:
        key: ${CI_COMMIT_REF_SLUG}
        paths:
            - node_modules/
            - .m2/
    variables:
        GIT_SUBMODULE_STRATEGY: recursive
        rc_version: $PKG_TAG
        projectid: $CI_PROJECT_ID
        projectname: $CI_PROJECT_NAME
        jobid: $CI_JOB_ID
        pipelineid: $CI_PIPELINE_ID
        commitid: $CI_COMMIT_SHA
        envlabel: "最初构建"

    script:
        - yarn install
        - npm run build:pre
        - echo $workdir ${PWD}
        - /deploy/script/ci/frontend_file.py $projectname  $rc_version"_"$(date +%Y%m%d%H%M)  $projectid $pipelineid $jobid ${PWD} ${commitid} $envlabel
    tags:
        - t_PUBLIC
    only:
        refs:
            - test
    except:
        variables:
            - $CI_PIPELINE_SOURCE == 'push'
            - $CI_PIPELINE_SOURCE == 'merge_request_event'
            - $CI_PIPELINE_SOURCE == 'external_pull_request_event'

buildMaster:
    stage: build
    cache:
        key: ${CI_COMMIT_REF_SLUG}
        paths:
            - node_modules/
            - .m2/
    variables:
        GIT_SUBMODULE_STRATEGY: recursive
        rc_version: "GA"
        projectid: $CI_PROJECT_ID
        projectname: $CI_PROJECT_NAME
        jobid: $CI_JOB_ID
        pipelineid: $CI_PIPELINE_ID
        commitid: $CI_COMMIT_SHA
        envlabel: "最初构建"

    script:
        - yarn install
        - npm run build:prod
        - echo $workdir ${PWD}
        - /deploy/script/ci/frontend_file.py $projectname  $rc_version"_"$(date +%Y%m%d%H%M)  $projectid $pipelineid $jobid ${PWD} ${commitid} $envlabel
    tags:
        - t_PUBLIC
    only:
        refs:
            - master
    except:
        variables:
            - $CI_PIPELINE_SOURCE == 'push'
            - $CI_PIPELINE_SOURCE == 'merge_request_event'
            - $CI_PIPELINE_SOURCE == 'external_pull_request_event'
