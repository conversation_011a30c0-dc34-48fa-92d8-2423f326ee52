FROM reg-wx.ymdd.tech/library/jdk:8u241-centos-tools
MAINTAINER ops

ENV APPNAME="edijfsshopeeorder"

ENV JAVA_MEM_OPTS="-Xms512m -Xmx512m -Xmn128m -Xss512k -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m" \
        JAVA_OTHER_OPTS=" -Djava.awt.headless=true -d64 -server -Djava.awt.headless=true -Djava.net.preferIPv4Stack=true -Djavax.servlet.request.encoding=UTF-8 -Dfile.encoding=UTF-8  -XX:+AlwaysPreTouch" \
        JAVA_GC_OPTS="-XX:+UseConcMarkSweepGC -XX:CMSInitiatingOccupancyFraction=75 -XX:+UseCMSInitiatingOccupancyOnly  -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${APPNAME}/logs/${APPNAME}/"

RUN  mkdir -p $APPNAME/logs \
    && groupadd -g 1000 tomcat \
    && adduser -u 1000 -d /var/cache/nginx -s /sbin/nologin -G tomcat -g 1000  tomcat \
    && chown -R 1000:1000 /$APPNAME/

COPY target/*.jar /$APPNAME/

WORKDIR /$APPNAME

CMD  java $JAVA_MEM_OPTS $JAVA_OTHER_OPTS $JAVA_GC_OPTS -jar *.jar $APP_START_OPS