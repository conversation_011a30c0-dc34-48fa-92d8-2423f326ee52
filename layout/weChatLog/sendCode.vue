<!--微信登录-系统登录安全认证发送验证码&微信绑定登录账号发送验证码-->
<template>
  <div class="sendCode">
    <header class="el-header" style="height:48px;">
      <div class="logo-box" @click="toIndex">
        <i class="iconfont coin iconJTLOGO logo-img"></i>
      </div>
      <div class="header-right">{{weekDay}}&nbsp;&nbsp;{{date}}</div>
    </header>
    <div class="content">
      <h3 class="title" v-if="weChatBindStatus">{{$lang('系统登录安全认证')}}</h3>
      <h3 class="title" v-else>{{$lang('微信绑定登录账号')}}</h3>
      <div class="contentWrap">
        <p v-if="weChatBindStatus">{{userName}}（{{userCode}}）{{$lang(',监测到你登录的地址发生了变化，需要对您的身份再次确认，请输入你的账号在系统绑定的手机号后获取验证码认证（提示：')}}{{phoneNo}}{{$lang('）')}}</p>
        <p v-else>{{userName}}（{{userCode}}）{{$lang('您在系统绑定的手机号是')}}{{phoneNo}}{{$lang('，请输入完整的手机号以便接收验证码')}}</p>
      </div>
      <div class="formWrap">
        <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" class="ruleForm">
          <el-form-item :label="$lang('请输入手机号')" prop="phone" class="form-item phoneInput">
            <el-input type="text" v-model="ruleForm.phone"></el-input>
            <div class="resendCode">
              <span @click="getCode" v-if="regetCodeStatus">{{$lang('发送验证码')}}</span>
              <span v-else class="normal-text">{{countTime}}{{$lang('s后可重发')}}</span>
            </div>
          </el-form-item>
          <el-form-item :label="$lang('请输入验证码')" prop="code" class="form-item">
            <el-input type="text" v-model="ruleForm.code" ref='codeInput'></el-input>
          </el-form-item>
        </el-form>
        <div class="confirmBtn">
          <el-button type="primary" @click="submitCode" class="submission" :loading="keyBtn">{{$lang('提交')}}</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatPhone } from '@public/utils/format'
import { RESPONSE_CODE } from '@public/http/config.js'
import { RULES } from '@public/utils/validators'
import { CommonData } from '@public/http/pool.js'
import { mapActions } from 'vuex'
import mixin from './mixin'

export default {
  name: 'WeChatScanLogin',
  mixins: [mixin],
  data() {
    return {
      way: 'weChat', // 接收验证码方式
      userName: '', // 员工姓名
      userCode: '', // 员工编号
      phoneNo: '', // 用户手机号
      bindStatus: true, // 手机号绑定状态
      regetCodeStatus: true, // 重新获取验证码
      weChatBindStatus: false, // 微信绑定状态
      smsCheckCodeEnum: '',
      countTime: 90, // 重新发送验证码倒计时
      keyBtn: false,
      ruleForm: {
        phone: '',
        code: ''
      }, // 手机号
      rules: {
        phone: [RULES.required, RULES.checkPhone],
        code: [RULES.required]
      }
    }
  },
  created() {
    this.getDate()
    this.way = this.$route.query.way || 'weChat'
    const { weChatBindStatus } = this.$route.query
    try {
      const wxLoginData = JSON.parse(localStorage.getItem('wxLoginData'))
      const { name, mobile, staffNo } = wxLoginData
      this.userName = name
      this.userCode = staffNo
      this.weChatBindStatus = weChatBindStatus
      this.phoneNo = formatPhone(mobile)
      const isPcLogin = localStorage.getItem('isPcLogin')
      if (isPcLogin) {
        this.smsCheckCodeEnum = 'LOGIN_PC_DEVICE_CHANGE'
      } else {
        if (this.weChatBindStatus) {
          this.smsCheckCodeEnum = 'LOGIN_WEIXIN_DEVICE_CHANGE'
        } else {
          this.smsCheckCodeEnum = 'LOGIN_WEIXIN'
        }
      }
    } catch (err) {
      console.log('error:' + err);
    }
  },
  methods: {
    ...mapActions(['setLang', 'set_themeInfo', 'LOGIN_INIT']),
    // 发送验证码
    getCode() {
      this.$refs.ruleForm.validateField('phone', async (phoneError) => {
        if (!phoneError) {
          const params = { account: this.userCode, mobile: this.ruleForm.phone, smsCheckCodeEnum: this.smsCheckCodeEnum }
          const { code, msg } = await CommonData.sendWeChatCheckCode(params)
          if (code === RESPONSE_CODE.SUCCESS) {
            this.regetCodeStatus = false
            this.$message.success(msg)
            this.$nextTick(() => {
              this.$refs.codeInput.focus()
            })
            clearInterval(this.codeTimer)
            this.codeTimer = setInterval(() => {
              --this.countTime
              if (this.countTime === 0) {
                this.countTime = 90
                this.regetCodeStatus = true
                clearInterval(this.codeTimer)
              }
            }, 1000)
          } else {
            this.$message.error(msg)
            return
          }
        }
      })
    },
    submitCode() {
      this.checkPassCodeByLogin({ smsCheckCodeEnum: this.smsCheckCodeEnum })
    },
    // 微信绑定  绑定成功后会返回登录数据
    async wechatBind(codekey) {
      try {
        const wxLoginData = JSON.parse(localStorage.getItem('wxLoginData'))
        const { openKey, word, staffNo } = wxLoginData
        const paramsData = {
          codeKey: codekey, // 校验验证码成功后的返回结果中的codekey
          openKey, // 前面status为4的时候的openKey
          password: word, // 上个页面用户输入的密码也要带过来  加过密的，和web登录的一样
          staffNo // 用户编号
        }
        const { code, data, msg } = await CommonData.wechatBind(paramsData)
        this.keyBtn = false
        this.loginCommon(code, data, msg)
      } catch (error) {
        this.keyBtn = false
      }
    },
    loginCommon(code, data, msg) {
      if (code === RESPONSE_CODE.SUCCESS) {
        if (this.smsCheckCodeEnum === 'LOGIN_WEIXIN') {
          this.$message.success(this.$lang('账号绑定成功，后续可直接微信扫码登录!'))
        }
        this.loginCommonFn(data)
      } else {
        this.$message.error(msg)
      }
    },
    // // 校验验证码
    async checkPassCodeByLogin(smsCheckCodeEnum) {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          this.keyBtn = true
          try {
            const params = { code: this.ruleForm.code, account: this.userCode, ...smsCheckCodeEnum }
            const { code, data, msg } = await CommonData.checkPassCodeByLogin(params)
            if (code === RESPONSE_CODE.SUCCESS) {
              if (this.smsCheckCodeEnum === 'LOGIN_WEIXIN') {
                this.wechatBind(data.codeKey)
                return
              }
              this.keyBtn = false
              this.loginCommon(code, data, msg)
            } else {
              this.keyBtn = false
              this.$message.error(msg)
            }
          } catch (error) {
            this.keyBtn = false
          }
        }
      })
    }
  },
  beforeDestroy() {
    clearInterval(this.codeTimer)
    clearInterval(this.forgetPasswordTimer)
  }
}
</script>
<style lang="scss" rel="text/css" scope>
 .sendCode .content .ruleForm .form-item .el-form-item__label{
    font-size: 14px !important;
    color: #999999;
    font-weight: 600;
  }
 .sendCode .content .ruleForm .form-item .el-input__inner{
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;
    border-bottom: 1px solid #dddddd;
    border-radius: 0px;
    padding: 0;
  }
  input:-webkit-autofill, textarea:-webkit-autofill, select:-webkit-autofill {
    box-shadow: 0 0 0px 1000px white inset !important;
    -webkit-box-shadow: 0 0 0px 1000px white inset !important;
    background-color: rgb(0, 0, 0) !important;
    background-image: none !important;
    color: rgb(0, 0, 0) !important;
    -webkit-tap-highlight-color:rgba(0,0,0,0) !important;
  }
.sendCode{
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-repeat: no-repeat;
  background:url("../../assets/images/setPasswordBg.jpg") no-repeat top;
  .el-header {
    display: flex;
    position: absolute;
    top: 0;
    z-index: 1000;
    padding: 0;
    left: 0;
    right: 0;
    @include topbar-bg();
    .logo-box {
      width: $asideWidth;
      height: $topHeight;
      text-align: center;
      transition: width 0.1s ease-out;
      cursor: pointer;
      line-height: $topHeight;
      @include logo-bg();
      .logo-img {
        font-size: 24px;
        transition: width 0.1s ease-out;
        color: #fff;
      }
    }
    .header-right {
      height: $topHeight;
      text-align: center;
      text-indent: -240px;
      line-height: $topHeight;
      @include base-color('clr');
      flex: 1;
      justify-content: center;
      background: #fff;
      padding: 0 20px;
    }
  }
  .content{
    width:540px;
    padding: 0 0 40px;
    min-height: 408px;
    margin: 0;
    height: auto;
    background:rgba(255,255,255,1);
    border-radius:10px;
    border:1px solid rgba(255,255,255,1);
    text-align: center;
    .title{
      height: 40px;
      line-height: 40px;
      margin: 0;
      border-bottom: 1px solid #ddd;
    }
    .contentWrap{
      padding: 40px 40px 0;
      line-height: 30px;
      text-align: left;
      color: #606266;
    }
    .formWrap{
      padding:0px 40px;
      .ruleForm{
        background: #FFFFFF;
        border-radius:10px;
        border:1px solid #FFFFFF;
        box-sizing: border-box;
        .phoneInput{
          position: relative;
          .resendCode{
            position: absolute;
            right: 0;
            top: 30px;
            background: #fff;
            cursor: pointer;
            z-index: 2;
            @include base-color('clr');
            .normal-text{
              color: #333;
            }
          }
        }
      }
      .submission.submit-disabled{
          background-color: #ddd;
          border: none;
      }
    }

  }
}
</style>
