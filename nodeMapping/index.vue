<template>
  <div class="NodeMapping main-wrap">
    <div class="btnList">
      <el-button icon="el-icon-plus" plain @click="add">{{
        $lang("新增")
      }}</el-button>
      <el-button icon="el-icon-search" plain @click="searchFun(1)">{{
        $lang("查询")
      }}</el-button>
      <el-button plain @click="resetSearch"
        ><i class="iconfont icongongnengtubiao_qingkong_moren"></i
        >{{ $lang("清空") }}</el-button
      >
    </div>
    <el-row :gutter="10" class="row">
      <el-form
        label-position="top"
        label-width="150"
        class="form-search"
        label-suffix=" :"
      >
        <el-col :span="4" :md="5" :lg="4" :xl="3" class="span4">
          <el-form-item :label="$lang('ApiAccount')">
            <el-select
              v-model="formInline.apiAccount"
              filterable
              remote
              reserve-keyword
              placeholder="请输入关键词"
              :remote-method="getApiAccount"
              :loading="loading"
              @change="apiAccountChange"
              clearable
            >
              <el-option
                v-for="item in apiAccountOptions"
                :key="item.apiAccount"
                :label="`${item.orderSource} / ${item.account} / ${item.apiAccount}`"
                :value="item.apiAccount"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" :md="5" :lg="4" :xl="3" class="span4">
          <el-form-item :label="$lang('状态')">
            <el-select
              v-model="formInline.status"
              clearable
              :placeholder="$lang('请选择状态')"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                clearable
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" :md="5" :lg="4" :xl="3" class="span4">
          <el-form-item :label="$lang('内部节点类型')">
            <el-cascader
              clearable
              v-model="inlineNodeType"
              :options="standardNodeOptions"
              @change="changeNode"
              :props="{ expandTrigger: 'hover', checkStrictly: true }"
            ></el-cascader>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="4" :md="5" :lg="4" :xl="3" class="span4">
          <el-form-item :label="$lang('内部轨迹编码')">
            <el-input
              v-model="formInline.jtTrackCode"
              :placeholder="$lang('内部轨迹编码')"
            ></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="4" :md="5" :lg="4" :xl="3" class="span4">
          <el-form-item :label="$lang('外部渠道节点编码')">
            <el-input
              v-model="formInline.platformTrackCode"
              :placeholder="$lang('外部渠道节点编码')"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4" :md="5" :lg="4" :xl="3" class="span4">
          <el-form-item :label="$lang('外部渠道节点名称')">
            <el-input
              v-model="formInline.platformTrackName"
              :placeholder="$lang('外部渠道节点名称')"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>

    <el-table
      v-loading="isLoading"
      class="div_table"
      show-overflow-tooltip
      :data="tableList"
      border
      width="90%"
      height="65vh"
      empty-text="暂无数据"
    >
      <el-table-column
        type="index"
        width="60"
        label="序号"
        align="center"
        header-align="center"
      />
      <el-table-column
        prop="sourceName"
        label="渠道"
        width="80"
        align="center"
        header-align="center"
      >
      </el-table-column>
      <el-table-column
        prop="apiAccount"
        label="ApiAccount"
        width="160"
        align="center"
        header-align="center"
      >
      </el-table-column>
      <el-table-column
        :formatter="statusformatter"
        prop="status"
        label="启用状态"
        width="160"
        align="center"
        header-align="center"
      >
      </el-table-column>
      <el-table-column
        :formatter="weightFeePushFormatter"
        prop="weightFeePush"
        label="推送重量运费"
        width="160"
        align="center"
        header-align="center"
      >
      </el-table-column>

      <el-table-column
        prop="jtTrackCode"
        label="内部节点编码（一级）"
        width="120"
        align="center"
        header-align="center"
      >
      </el-table-column>
      <el-table-column
        prop="jtTrackName"
        label="内部节点名称（一级）"
        align="center"
        width="120"
        header-align="center"
      >
      </el-table-column>
      <el-table-column
        prop="subJtTrackCode"
        label="内部节点编码（二级）"
        width="120"
        align="center"
        header-align="center"
      >
      </el-table-column>
      <el-table-column
        prop="subJtTrackName"
        label="内部节点名称（二级）"
        align="center"
        width="120"
        header-align="center"
      >
      </el-table-column>

      <el-table-column
        prop="jtTrackNameLocal"
        label="轨迹国际语"
        align="center"
        width="160"
        header-align="center"
      >
      </el-table-column>
      <el-table-column
        prop="platformTrackCode"
        label="外部轨迹编码"
        align="center"
        width="140"
        header-align="center"
      >
      </el-table-column>
      <el-table-column
        prop="platformTrackName"
        label="外部轨迹名称"
        width="140"
        align="center"
        header-align="center"
      >
      </el-table-column>

      <el-table-column
        :formatter="createTimeformatter"
        prop="createTime"
        label="创建时间"
        width="180"
        align="center"
        header-align="center"
      >
      </el-table-column>

      <!-- <el-table-column
        prop="updateTime"
        label="修改时间"
        align="center"
        header-align="center"
      >
      </el-table-column> -->
      <el-table-column
        align="center"
        header-align="center"
        fixed="right"
        label="操作"
        width="160"
      >
        <template #default="scope">
          <!-- <el-button
            link
            type="danger"
            size="small"
            @click="deleteOrder(scope.$index, scope.row)"
            >删除</el-button
          > -->
          <div class="iconList">
            <i
              class="el-icon-edit-outline"
              @click="editRow(scope.$index, scope.row)"
            ></i>
            <i
              class="el-icon-search"
              @click="viewRow(scope.$index, scope.row)"
            ></i>
            <i
              class="el-icon-delete"
              @click="deleteOrder(scope.$index, scope.row)"
            ></i>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div style="margin-top: 30px; text-align: right">
      <el-pagination
        @size-change="sizeChange"
        @current-change="currentChange"
        :current-page="page.currentPage"
        :page-sizes="[20, 50, 100, 500]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
      >
      </el-pagination>
    </div>

    <addNodeMapping
      :viewType="viewType"
      :editId="editId"
      :key="componentKey"
      :standardNodeOptions="standardNodeOptions"
      :dialog-visible="addVisible"
      @close="closeAddNode"
      @save="save"
      @refresh="apiAccountChange"
      :type="addType"
      :dialogTitle="dialogTitle"
    />
    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
      <span>确定删除本条记录吗？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDeleteOrder">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixin from "@/mixins/mixin.js";
import dayjs from "dayjs";
import { RESPONSE_SUCCESS } from "@/constants";
import {
  page,
  saveOrUpdate,
  standardNode,
  deleteOrder,
} from "@/views/track/api/trackApi";
import addNodeMapping from "./components/addNodeMapping";
import { DICT } from "@utils/dict";

export default {
  name: "NodeMapping",
  mixins: [mixin],
  components: {
    addNodeMapping,
  },
  data() {
    return {
      dialogTitle: "新增节点映射",
      inlineNodeType: "", //内部节点类型
      viewType: false,
      editId: "",
      addType: "add",
      componentKey: 0,
      isLoading: false,
      formInline: {
        trackType: 1,
      },
      tableList: [],
      apiAccountOptions: [],
      nodeOptionsList: [
        {
          label: "快件揽收",
          value: "快件揽收",
        },
        {
          label: "发件扫描",
          value: "发件扫描",
        },
        {
          label: "到件扫描",
          value: "到件扫描",
        },
      ],
      COM_HTTP: {
        reqList: page,
      },
      addVisible: false,
      options: [
        {
          value: "",
          label: "全部",
        },
        {
          value: 1,
          label: "启用",
        },
        {
          value: 0,
          label: "禁用",
        },
      ],
      standardNodeOptions: [],
      delId: "",
      dialogVisible: false,
    };
  },

  mounted() {
    debugger
    this.handleStandardNode();
  
  },
  methods: {

    add() {
      this.dialogTitle = "新增节点映射";
      this.addVisible = true;
    },
    createTimeformatter(row) {
      if (row.createTime) {
        return dayjs(new Date(row.createTime).getTime()).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
    },
    statusformatter(row, column) {
      if (row.status == 1) {
        return "启用";
      }
      if (row.status == 0) {
        return "禁用";
      }
    },
    weightFeePushFormatter(row, column) {
      if (row.weightFeePush) {
        return "启用";
      }
      if (!row.weightFeePush) {
        return "禁用";
      }
    },
    editRow(index, row) {
      this.apiAccountChange(row.apiAccount)
      this.dialogTitle = "编辑节点映射";
      this.editId = row.id ? row.id : "";
      this.addVisible = true;
      this.addType = "edit";
    },
    viewRow(index, row) {
      this.apiAccountChange(row.apiAccount)
      this.dialogTitle = "查看详情";
      this.editId = row.id ? row.id : "";
      this.viewType = true;
      this.addVisible = true;
      this.addType = "edit";
    },
    deleteOrder(index, row) {
      let id = row.id ? row.id : "";
      this.delId = id;
      this.dialogVisible = true;
    },
    confirmDeleteOrder(index, row) {
      if (this.delId) {
        deleteOrder({
          id: this.delId,
        }).then((res) => {
          if (res.code === RESPONSE_SUCCESS) {
            this.$message.success(this.$lang(res.msg));
            this.searchFun();
            this.dialogVisible = false;
          } else {
            this.$message.error(this.$lang(res.msg));
            this.dialogVisible = false;
          }
        });
      }
    },
    closeAddNode() {
      this.addType = "add";
      this.viewType = false;
      this.addVisible = false;
      this.componentKey += 1;
    },
    changeNode(val) {
      if (val && val.length > 0) {
        if (val.length == 1) {
          this.formInline.jtTrackCode = val[0];
          this.formInline.subJtTrackCode = "";
        } else if (val.length == 2) {
          this.formInline.jtTrackCode = val[0];
          this.formInline.subJtTrackCode = val[1];
        }
      } else {
        this.formInline.jtTrackCode = "";
        this.formInline.subJtTrackCode = "";
      }
    },
    handleStandardNode(apiAccount) {
      standardNode({'apiAccount':apiAccount}).then((res) => {
        if (res.code === RESPONSE_SUCCESS) {
          if (res.data && res.data.length > 0) {
            let tmpArr = [];

            for (let i of res.data) {
              if (i.subNode && i.subNode.length > 0) {
                let tmpObj1 = {};
                tmpObj1.value = i.code ? i.code : "";
                tmpObj1.label = i.desc ? i.desc : "";
                tmpObj1.descLocal = i.descLocal ? i.descLocal : "";
                //去除子节点
                let tmpArr1 = [];
                for (let j of i.subNode) {
                  tmpArr1.push({
                    value: j.code ? j.code : "",
                    label: j.desc ? j.desc : "",
                    descLocal: j.descLocal ? j.descLocal : "",
                  });
                }
                tmpObj1.children = tmpArr1;
                tmpArr.push(tmpObj1);
              } else {
                let tmpObj = {};
                tmpObj.value = i.code ? i.code : "";
                tmpObj.label = i.desc ? i.desc : "";
                tmpObj.descLocal = i.descLocal ? i.descLocal : "";
                tmpArr.push(tmpObj);
              }
            }
            console.log(tmpArr);
            this.standardNodeOptions = tmpArr;
          } else {
            this.standardNodeOptions = [];
          }
        }
      });
    },
    resetSearch() {
      this.formInline = {
        trackType: 1,
      };
      this.inlineNodeType = "";
    },

    getParams() {
      return this.formInline;
    },
    getApiAccount(keyWord) {
      if (keyWord !== "") {
        this.$API.getApiAccount({ keyWord: keyWord }).then((res) => {
          if (res.code === RESPONSE_SUCCESS && res.data) {
            this.apiAccountOptions = res.data;
          }
        });
      }
    },
    save(val) {
      saveOrUpdate(val).then((res) => {
        if (res.code === RESPONSE_SUCCESS) {
          this.$message.success(this.$lang(res.msg));
          this.addVisible = false;
          this.addType = "add";
          this.viewType = false;
          this.componentKey += 1;
          this.searchFun();
        } else {
          this.$message.error(this.$lang(res.msg));
        }
      });
    },
    apiAccountChange(val){
      console.log(val);
      this.handleStandardNode(val)
      this.inlineNodeType = null
      this.formInline.jtTrackCode = "";
      this.formInline.subJtTrackCode = "";
    }
  },
};
</script>

<style lang="scss" scoped>
.btnList {
  display: flex;
  > * {
    margin-right: 20px;
  }
}
.NodeMapping {
  height: 100%;
  overflow-y: auto;
  .form-search {
    display: flex;
    flex-wrap: wrap;
    ::v-deep [class*="el-col-"] {
      float: none;
    }
  }
  &::v-deep .el-input__inner:hover {
    border-color: $theme-color;
  }
  .span4 {
    margin-right: 20px;
    .el-select {
      width: 100%;
    }
  }
  .row {
    margin: 20px 0 10px 0;
  }

  .iconfont {
    cursor: pointer;
    margin-right: 5px;
    font-size: 14px;
    &:active {
      color: $theme-color;
    }
  }
}
.div_table {
  width: calc(100% - 22px);
  // margin: 0 10px;
  border: 1px solid gainsboro;
  border-radius: 5px;
  th {
    padding: 0 !important;
    height: 10px;
    line-height: 10px;
  }
  td {
    padding: 0 !important;
    height: 30px;
    line-height: 30px;
  }
}
.iconList {
  display: flex;
  align-content: center;
  font-size: 20px;
  justify-content: space-between;
  padding: 0 10px;
  > * {
    // margin-right: 20px;
  }
}
</style>