<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yl-jfs-knowledgebase-api</artifactId>
    <version>2.1.0-RELEASE</version>

    <parent>
        <groupId>com.yl</groupId>
        <artifactId>yl-platform-base</artifactId>
        <version>2.0.0.jfsstd-SNAPSHOT</version>
    </parent>

    <description>Demo project for Spring Boot</description>


    <!--配置参数-->
    <properties>
        <guava.version>27.0-jre</guava.version>
        <yl-module.version>3.0.0-RELEASE</yl-module.version>
        <yl-sharding-jdbc-spring-boot-starter.version>4.0.0-RC1</yl-sharding-jdbc-spring-boot-starter.version>
        <jms-network-model.version>1.0.0-RELEASE</jms-network-model.version>
        <ibk-model-api.version>1.1.0-RELEASE</ibk-model-api.version>
        <jasperreports.version>6.0.0</jasperreports.version>
        <itext-asian.version>5.2.0</itext-asian.version>
        <barbecue.version>1.5-beta1</barbecue.version>
        <zxing.version>2.2</zxing.version>
        <pdd-edi-model.version>1.0.1-RELEASE</pdd-edi-model.version>
        <ebcss-model-api.version>3.2.2-RELEASE</ebcss-model-api.version>
        <ra2ro.version>2.0.0.jfsstd</ra2ro.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>yl-platform-starter-shardingjdbc</artifactId>
        </dependency>
        <!-- zipkin相关依赖 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-stream-binder-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>yl-platform-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>yl-platform-starter-ra2ro</artifactId>
            <version>${ra2ro.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>lmdm-model-api</artifactId>
            <version>${yl-module.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>operation-log</artifactId>
                    <groupId>com.yl</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
            <version>${yl-sharding-jdbc-spring-boot-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>yl-platform-starter-id</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>yl-platform-starter-access-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>ibk-model-api</artifactId>
            <version>${ibk-model-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>base</artifactId>
                    <groupId>com.yl</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- iReport打印start -->
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>${jasperreports.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.olap4j</groupId>
                    <artifactId>olap4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports-fonts</artifactId>
            <version>${jasperreports.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>${itext-asian.version}</version>
        </dependency>

        <dependency>
            <groupId>net.sourceforge.barbecue</groupId>
            <artifactId>barbecue</artifactId>
            <version>${barbecue.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>${zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>pdd-edi-model</artifactId>
            <version>${pdd-edi-model.version}</version>
        </dependency>
        <!-- iReport打印end -->
        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>yl-platform-starter-file</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>jms-network-model</artifactId>
            <version>${jms-network-model.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>base</artifactId>
                    <groupId>com.yl</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
    </dependencies>
</project>
