<template>
  <div class="menu_mobile animated slideInRight">
    <svg-icon class="close_icon" iconClass="daohang_fanhui" @click="$router.replace('/')" />
    <!-- <q-icon name="iconfont icon-danchuangguanbi" class="close_icon" @click="closeMenu"></q-icon> -->
    <div class="menu_mobile_scroll">
      <svg class="icon_title" aria-hidden="true">
        <use xlink:href="#icon-logo"></use>
      </svg>
      <!-- 登录框框 -->
      <div class="login-card">
        <div class="q-pa-md">
          <q-form ref="myForm" class="q-gutter-md">
            <q-input
              ref="formPhone"
              v-model="phone"
              label=""
              stack-label
              dense
              no-error-icon
              maxlength="30"
              :placeholder="$t('pageOnlineOrder.手机号码')"
              :rules="[(val) => (val && val.length > 0) || '']"
              class="phone-input"
            >
              <template v-slot:prepend>
                <span>62</span>
              </template>
            </q-input>
            <!-- <q-input
              ref="formCtoken"
              v-model="captchaCode"
              label=""
              stack-label
              dense
              :placeholder="$t('图形验证码')"
              no-error-icon
              maxlength="8"
              :rules="[(val) => (val !== null && val !== '') || '']"
            >
              <template v-slot:append>
                <img
                  style="height: 30px"
                  :src="captchaData.img"
                  alt=""
                  srcset=""
                  @click="getCaptcha"
                />
              </template>
            </q-input> -->
            <q-input
              ref="formCode"
              v-model="code"
              label=""
              stack-label
              dense
              :placeholder="$t('验证码')"
              no-error-icon
              maxlength="4"
              lazy-rules="ondemand"
              @blur="codeInputBlur"
              :rules="[
                (val) => (val !== null && val !== '') || '',
                (val) => /^\d{4}$/g.test(val) || '',
              ]"
            >
              <template v-slot:append>
                <q-btn
                  @click="getCode"
                  padding="8px 10px"
                  outline
                  color="primary"
                  v-if="hasCode"
                  :label="codeLabel"
                  size="12px"
                />
                <q-btn flat color="999" v-else :label="codeText + 's'" />
              </template>
            </q-input>
            <div class="flex items-center accept-wrap">
              <q-checkbox size="32px" dense v-model="accept"> </q-checkbox>
              <div class="flex accept-box" style="flex-wrap: nowrap">
                <div>
                  {{ $t('我已经阅读') }} <span @click="goPage(0)"> {{ $t('服务条款') }}</span>
                  {{ $t('与') }}
                  <span @click="goPage(1)"> {{ $t('Privacy') }}</span>
                </div>
              </div>
            </div>
            <div class="flex flex-center btn-div">
              <q-btn
                style="width: 100%"
                unelevated
                size="18px"
                padding="9px 102px"
                :label="$t('登录')"
                type="submit"
                :color="accept ? 'primary' : 'grey-5'"
                :loading="loading"
                :disable="!accept"
                @click.native.prevent="loginHandle"
              />
            </div>
          </q-form>
        </div>
      </div>

      <!-- 语言切换 -->
      <ul class="lang_ul flex flex-center">
        <li
          class="lang_li"
          :class="lang === item.value && 'active'"
          v-for="(item, index) in langOptions"
          :key="index"
          @click="langChange(item.value)"
        >
          {{ item.label }}
        </li>
      </ul>

      <!-- <div class="yl-bottom flex column flex-center">
        <img src="~assets/img/yl-logo.png" alt="" />
        <span>{{ $t('云路科技') }}</span>
      </div> -->
    </div>
  </div>
</template>

<script>
const TIME_COUNT = 59
import { sendVerificationCode, login, logout, captcha } from '../api/user'
import { mapActions } from 'vuex'
import _ from 'lodash'
import { longMap, defaultLangType } from '../assets/js/const'
export default {
  name: "MobileMenu",
  data () {
    return {
      lang: this.$i18n.locale || defaultLangType,
      langOptions: [
        { value: "zh-cn", label: "简体中文" },
        { value: "en-us", label: "English" },
        { value: "id", label: "Indonesia" }
      ],
      phone: '',
      code: '',
      accept: false,
      codeText: '',
      codeLabel: this.$t('获取验证码'),
      hasCode: true,
      timer: null,
      loading: false,
      captchaCode: '',
      captchaData: {}
    }
  },
  watch: {
    lang (lang, old) {
      import("quasar/lang/" + longMap[lang]).then(langObj => {
        // 设置本地国际化
        this.$q.lang.set(langObj.default);
        this.$i18n.locale = lang;
        // 存储lang
        this.$q.cookies.set("lang", lang, { path: "/" });
        location.reload();
      });
    }
  },
  mounted() {
    this.initTencentCaptcha()
  },
  destroyed () {
    this.$nextTick(_ => {
      this.$refs.myForm && this.$refs.myForm.reset()
      this.resetForm()
      clearInterval(this.timer) // 产品说关闭要可以重新获取验证码
      this.hasCode = true
      this.timer = null
    })
  },
  computed: {
    // tx验证码开关状态
    txVeriryCodeSwitchStatus() {
      return this.$store.state.common.txVeriryCodeSwitchStatus
    },
    // 去除电话号码第一个0
    removeFirstZero() {
      return (phone) => {
        if (phone) {
          phone = phone.substring(0,1) === '0' ? phone.substring(1) : phone
        }
        return phone
      }
    }
  },
  methods: {
    ...mapActions({
      updateUserData: 'common/updateUserData' //更新用户信息
    }),
    // 初始化tx验证码
    initTencentCaptcha() {
      // 生成实例对象
      this.captcha = new window.TencentCaptcha('2031495304', this.tencentCaptchaCallback);
      // 显示验证码
      // this.captcha.show()
    },
    // 验证码回调
    async tencentCaptchaCallback(res = {}) {
      console.log('腾讯验证码回调：callback', res);
      // 验证成功，返回对象
      if (res.ret === 0) {
        // // 验证成功，不继续显示验证码
        // this.isShowVerifyCode = false
        // 解构参数
        // ticket票据 randstr随机字符串
        const { ticket, randstr } = res
        // 调用登陆
        this.getCode( { ticket, randstr, isTxVerifyCode: true })
        return res
      }
      // 验证不成功
      return null
    },
    loginHandle () {
      if (!this.accept) {
        return false
      }
      this.$refs.myForm.validate().then(async success => {
        if (success) {
          // 是的，模型是正确的
          try {
            this.loading = true
            const res = await login({
              mobile: 62 + this.removeFirstZero(this.phone),
              verificationCode: this.code
            })
            this.loading = false
            if (res.succ) {
              // 更新store 用户信息
              this.updateUserData(res.data)
              // 存储token
              this.$q.cookies.set('token', res.data.token, { expires: 1, path: '/' })
              this.$q.localStorage.set('token', res.data.token)
              this.$q.localStorage.set('userData', res.data)
              const query = this.$route.query
              if (query && query.form === 'order') {
                this.$router.push({ name: 'onlineOrder', params: { fromIndex: true } })
              } else if (query && query.form === 'question') {
                this.$router.push({ name: 'questionnaire', params: { fromIndex: true } })
              } else {
                this.$router.push('/')
              }
            } else {
              this.$q.notify({
                icon: 'iconfont icon-icon-cuowutishi',
                position: 'center',
                timeout: 3000,
                message: res.msg
              })
            }
          } catch (error) {
            this.loading = false
            console.log(error)
          }
        } else {
          // 哦，不，用户至少
          // 填写了一个无效值
          if (!this.phone) {
            this.$q.notify({
              position: 'center',
              message: this.$t('pageOnlineOrder.请输入手机号码'),
              timeout: 1000
            })
            return
          }
          if (!this.code) {
            this.$q.notify({
              position: 'center',
              message: this.$t('请输入验证码'),
              timeout: 1000
            })
            return
          }
          if (!/^\d{4}$/g.test(this.code)) {
            this.$q.notify({
              position: 'center',
              message: this.$t('验证码输入错误'),
              timeout: 1000
            })
            return
          }
        }
      })
    },
    async onSubmit () {
      // 登录
      if (!this.accept) {
        return false
      }
      try {
        this.loading = true
        const res = await login({
          mobile: 62 + this.removeFirstZero(this.phone),
          verificationCode: this.code
        })
        this.loading = false
        if (res.succ) {
          // 更新store 用户信息
          this.updateUserData(res.data)
          // 存储token
          this.$q.cookies.set('token', res.data.token, { expires: 1, path: '/' })
          this.$q.localStorage.set('token', res.data.token)
          this.$q.localStorage.set('userData', res.data)
          this.$router.push('/')
        } else {
          this.$q.notify({
            icon: 'iconfont icon-icon-cuowutishi',
            position: 'center',
            timeout: 3000,
            message: res.msg
          })
        }
      } catch (error) {

      }
    },
    goPage (index) {
      // 跳转
      let newTab = null
      if (index === 0) {
        // newTab = this.$router.resolve('/problem/termsAndAgreement')
        this.$router.push('/problem/termsAndAgreement')

      } else {
        this.$router.push('/problem/privacyPolicy')
      }
      // window.open(newTab.href, '_blank')
    },
    codeInputBlur () {
      // const hasCode = this.$refs.formCode.validate()
      // console.log(this.$refs.formCode.validate())
    },
    getCode: _.debounce(function (pras) {
      // 获取验证码
      this.getCodeFn(pras)
    }, 300, { leading: true, trailing: false }),
    async getCodeFn (pras) {
      // 获取腾讯验证码参数
      const { ticket, randstr, isTxVerifyCode } = pras
      console.log(pras, 'pras')
      // txVeriryCodeSwitchStatus腾讯验证码后台开关 true开启 false关闭
      // isTxVerifyCode 是否有腾讯验证码参数 true验证码选中后的回调状态 false还未选中验证码
      if (this.txVeriryCodeSwitchStatus && !isTxVerifyCode) {
        // 显示tx验证码
        return this.captcha.show()
      }
      // 获取验证码-接口触发
      const hasPhoen = this.$refs.formPhone.validate()
      // const hasCtoken = this.$refs.formCtoken.validate()
      if (!hasPhoen) {
        this.$q.notify({
          position: 'center',
          message: this.$t('pageOnlineOrder.请输入手机号码'),
          timeout: 3000
        })
        return
      }
      try {
        const res = await sendVerificationCode({
          mobile: 62 + this.removeFirstZero(this.phone.replace(/\s+/g, '')),
          type: 1,
          ticket,
          randstr
        })
        if (res && res.succ) {
          this.$q.notify({
            position: 'center',
            message: this.$t('验证码已发送到手机'),
            timeout: 1000
          })
          if (!this.timer) {
            this.codeText = TIME_COUNT
            this.hasCode = false
            this.timer = setInterval(() => {
              this.codeText--
              if (this.codeText <= 0) {
                clearInterval(this.timer)
                this.hasCode = true
                this.timer = null
              }
            }, 1000);
          }
        } else {
          this.$q.notify({
            icon: 'iconfont icon-icon-cuowutishi',
            position: 'center',
            timeout: 3000,
            message: res.msg
          })
        }
      } catch (error) {

      }
    },
    resetForm () {
      this.phone = ''
      this.code = ''
      this.accept = false
      this.captchaCode = ''
      this.captchaData = {}
    },
    closeMenu () {
      this.$emit("close");
    },
    routerClick (url) {
      if (this.$route.path !== url) {
        this.$router.push(url);
      }

      this.closeMenu();
    },
    langChange (lang) {
      this.lang = lang;
      this.closeMenu();
    },
    async getCaptcha () {
      try {
        const res = await captcha()
        if (res && res.succ) {
          console.log(res.data)
          this.captchaData = res.data
        }
      } catch (error) {

      }
    }
  }
};
</script>
<style>
#verify_icon {
  margin-top: 56px;
}
</style>
<style lang="scss" scoped>
[dir='rtl'] .right_icon {
  transform: rotate(180deg);
}
.el-collapse {
  border-top: none;
  ::v-deep .el-collapse-item__header {
    height: 110rpx;
    line-height: 110rpx;
  }
  .title-text {
    font-size: 32rpx;
    font-weight: 500;
    color: #2b2b2b;
  }
  ::v-deep .el-collapse-item__content {
    &:first-of-type {
      // border-top: 1px solid #eee;
    }
    div {
      padding-top: 40rpx;
      text-indent: 1em;
      font-size: 28rpx;
      font-weight: 400;
      color: #2b2b2b;
      line-height: 40rpx;
      &.active {
        color: #00b075;
      }
    }
  }
  ::v-deep .el-collapse-item__header.is-active {
    border-bottom-color: #ebeef5 !important;
  }
}

.menu_mobile {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #fff;
  z-index: 3333;
  overflow-y: auto;
  .menu_mobile_scroll {
    // min-height: 114vw;
    height: calc(100vh - 180rpx);
    position: relative;
    padding-top: 180rpx;
  }
  .close_icon {
    position: absolute;
    left: 40rpx;
    top: 40rpx;
    font-size: 36rpx;
    // color: #d8d8d8;
    z-index: 5;
  }
  .icon_title {
    width: 408rpx;
    height: 85rpx;
    display: block;
    margin: 0 auto 80rpx;
  }
  .lang_ul {
    // position: absolute;
    // bottom: 72rpx;
    // left: 0;
    margin-top: 80rpx;
    width: 100%;
    .lang_li {
      cursor: pointer;
      user-select: none;
      padding: 0 40rpx;
      font-size: 24rpx;
      color: #484848;
      position: relative;
      &.active {
        color: $primary;
      }
      &:not(:nth-last-child(1)) {
        &::after {
          position: absolute;
          content: ' ';
          right: 0;
          top: 50%;
          width: 0;
          height: 20rpx;
          border-right: 1px solid #484848;
          transform: translateY(-10rpx);
        }
      }
    }
  }
}

.q-pa-md {
  padding: 40rpx;
  padding-bottom: 0;
}

.phone-input{
  ::v-deep .q-field__prepend{
    padding: 14px 10px 2px 0;
    font-size: 14px;
    color: #333;
    font-weight: 600;
  }
}
.accept-wrap {
  margin-left: 16px;
}
.accept-box {
  flex: 1;
  margin-left: 10rpx;
  color: #999;
  font-size: 12px;
  font-weight: 400;
  cursor: pointer;
  span {
    color: #00b075;
  }
}
.btn-div {
  margin-top: 36px;
}
.icon-danchuangguanbi {
  color: #999;
  cursor: pointer;
}
.q-field--standard.q-field--highlighted ::v-deep .q-field__control:after {
  transform: scale3d(1, 0.5, 1);
}
::v-deep .q-field--dense .q-field__control,
::v-deep .q-field--dense .q-field__marginal {
  height: 56px;
  // padding-bottom: 16rpx;
}
.yl-bottom {
  position: absolute;
  bottom: 40rpx;
  width: 100%;
  min-height: 50rpx;
  img {
    width: 223rpx;
    height: 39rpx;
  }
  span {
    margin-top: 17rpx;
    font-size: 24rpx;
    font-family: SourceHanSansCN, SourceHanSansCN-Regular;
    font-weight: 400;
    text-align: center;
    color: #999999;
  }
}
</style>
