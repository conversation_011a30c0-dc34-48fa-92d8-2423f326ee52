<template>
  <q-page>
    <q-list>
      <q-item :to="{ name: item.route }" v-for="item in userPersonalCenter" :key="item.route" exact>
        <q-item-section avatar>
          <i :class="`iconfont ${item.icon}`" />
        </q-item-section>
        <q-item-section>{{ item.label }}</q-item-section>
        <q-item-section side>
          <i class="iconfont icon-jiantou" />
        </q-item-section>
      </q-item>
    </q-list>
    <div class="logout" @click="confirm = true">
      {{ $t('退出登录') }}
    </div>
    <mobile-dialog
      title="是否退出登录"
      @confirm="logoutClik"
      :dialogSync.sync="confirm"
    />
  </q-page>
</template>

<script>
import { mapMutations, mapState, mapActions } from "vuex";
import { logout } from '../../api/user'

export default {
  data () {
    return {
      confirm: false,
      userPersonalCenter: [
        { route: 'myOrdersList', icon: 'icon-gerenzhongxin_wodedingdan', label: this.$t('personalCenter.我的订单') },
        { route: 'addressList', icon: 'icon-gerenzhongxin_tongxunlu', label: this.$t('personalCenter.通讯录') },
        { route: 'personalMsg', icon: 'icon-gerenzhongxin_gerenxinxi', label: this.$t('personalCenter.个人信息') },
        { route: 'userBinding', icon: 'icon-gerenzhongxin_gerenxinxi', label: this.$t('personalCenter.绑定网点') },
      ],
    }
  },
  methods: {
    ...mapActions({
      updateUserData: 'common/updateUserData'
    }),
    async logoutClik () {
      await logout()
      // clear   cookie localStorage信息
      this.$q.cookies.remove('token')
      this.$q.localStorage.remove('userData')
      this.$q.localStorage.remove('token')
      this.$router.push('/')
      this.updateUserData({})
    },
  }
}
</script>

<style lang="scss" scoped>
.q-page {
  padding-top: 24rpx;
  background: #f8f8f8;
}
.icon-jiantou {
  font-size: 24rpx;
}
.q-list {
  padding-left: 30rpx;
  background: #fff;
}
.q-item {
  padding: 0;
  min-height: 110rpx;
  border-bottom: 1px solid #eee;
  &:last-child {
    border: none;
  }
}
.q-item__section--side {
  padding-right: 20rpx;
}
.q-item__section--avatar {
  min-width: auto;
  i {
    font-size: 32rpx;
  }
}
.q-item__section--main {
  font-size: 32rpx;
  color: #2b2b2b;
  font-weight: 400;
}
.logout {
  margin-top: 40rpx;
  padding: 33rpx;
  background: #fff;
  text-align: center;
  font-size: 32rpx;
  color: #2b2b2b;
  font-weight: 400;
}
.logou-card {
  padding: 48rpx 25rpx;
  div {
    font-size: 32rpx;
    font-weight: 600;
    text-align: center;
    color: #2b2b2b;
    line-height: 22rpx;
  }
  .btns-bottom {
    margin-top: 48rpx;
  }
  .text-999 ::v-deep {
    .block {
      color: #2b2b2b;
    }
  }
}
</style>
