/*
 * @Date: 2020-08-24 09:39:34
 * @Author: MX
 * @Description:
 */
/**
 * 公共接口
 */

import { post, get } from '@http/request'
import qs from 'qs'

// 验证码生成
export const getCaptcha = (params) => post(`/vip/captcha`, params)

/**
 * 登录
 * @param { Object } userName<params> 用户名
 * @param { Object } password<params> 密码
 */

export const LOGIN = params => post(`/vip/login`, params)

// 用户详情
export const UserDetail = params => get(`/vip/user/detail`, params)
/**
 * 登出
 */
export const LOGOUT = () => post(`/vip/logout`)

/**
 * 修改密码
 */
export const CHANGE_PASSWORD = (params) => post(`/vip/user/changePwd`, params)

/**
 * 查询行政区域
 */
// export const GET_AREA = (params) => post(`/vip/area/page`, qs.stringify(params))
export const GET_AREA = (params) => post(`/vip/area/selectByCondition`, qs.stringify(params))

/**
 * 发送验证码(绑定手机号)
 * @param {*} params
 */
export const getCodeBindMobile = params => post(`/vip/user/sendBindMobileVerifyCode`, qs.stringify(params))

/**
 * 绑定手机号
 * @param {*} params
 */
export const bindMobile = params => post(`/vip/user/bindMobile`, qs.stringify(params))

/**
 * 发送验证码(修改密码)
 * @param {*} params
 */
export const getCodeUpdatePwd = params => post(`/vip/user/sendPasswordVerifyCode`, qs.stringify(params))

/**
 * 通过手机号修改密码
 * @param {*} params
 */
export const updatePwdByMobile = params => post(`/vip/user/changePwdByMobile`, qs.stringify(params))

/**
 * 修改密码
 */
export const savePrintSetup = (params) => post(`/vip/user/savePrintSetup`, qs.stringify(params))

/**
 * 数据字典
 */
export const getSysDictionary = (params) => get(`/vip/sysDictionary/listAll`, params)
/**
 * 保存隐私面单设置
 */
export const savePrivacy = (params) => post(`/vip/user/saveIsPrivacy`, qs.stringify(params))
/**
 * 查询行政区域
 */
export const GET_QUERY_AREA = (params) => post(`/vip/area/selectByFuzzyCondition`, qs.stringify(params))
