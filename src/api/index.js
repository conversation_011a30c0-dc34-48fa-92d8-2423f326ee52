/*
 * @Author: zengpan
 * @Date: 2022-01-10 15:01:21
 * @LastEditTime: 2022-01-12 17:49:42
 * @LastEditors: Please set LastEditors
 */
import { post, get } from '@public/http/request'

//登录
export const login = (params) => post(`/officialadmin/login`, params)
// export const logout = () => post(`/officialadmin/${APICUS}/user/logout`)
export const UPLOADUMG = `${process.env.VUE_APP_API_URL}/officialadmin/file/uploadImg`
// 新闻模块
export const News = {
  reqList(params) { return post(`/officialadmin/information/page`, params) }, // 新闻列表
  reqDetail(params) { return get(`/officialadmin/information/getListByNumber`, params) }, // 新闻详情
  reqAdd(params) { return post(`/officialadmin/information/addBatch`, params) }, // 添加新闻
  reqDelete(params) { return get(`/officialadmin/information/delete`, params) }, // 删除新闻
  reqModify(params) { return post(`/officialadmin/information/editBatch`, params) }, // 修改新闻
  reqUp(params) { return get(`/officialadmin/information/release`, params) }, // 新闻上线
  reqDown(params) { return get(`/officialadmin/information/offline`, params) }, // 新闻下线
}

// 招聘模块
export const Recruit = {
  reqList(params) { return post(`/officialadmin/careers/selectPageCareers`, params) }, // 招聘列表
  reqDetail(params) { return get(`/officialadmin/careers/detailAdminCareers`, params) }, // 招聘详情
  reqAdd(params) { return post(`/officialadmin/careers/recruitAdd`, params) }, // 添加招聘
  reqDelete(params) { return get(`/officialadmin/careers/deleteCareers`, params) }, // 招聘公告
  reqModify(params) { return post(`/officialadmin/careers/editCareers`, params) }, // 招聘公告编辑
}

export const Scrollbar = {
  reqList(params) { return post(`/officialadmin/scrollbar/search_srollbar`, params) }, // 招聘列表
  reqDetail(params) { return get(`/officialadmin/scrollbar/detail`, params) }, // 招聘详情
  reqAdd(params) { return post(`/officialadmin/scrollbar/add`, params) }, // 添加招聘
  reqDelete(params) { return get(`/officialadmin/scrollbar/delete`, params) }, // 招聘公告
  reqModify(params) { return post(`/officialadmin/scrollbar/update`, params) }, // 招聘公告编辑
  reqUpList(params) { return get(`/officialadmin/scrollbar/online`, params) },
  reqDown(params) { return get(`/officialadmin/scrollbar/offline`, params) },
}

export const Imgconfig = {
  reqList(params) { return post(`/officialadmin/imgConfig/search_imgconfig`, params) }, // 招聘列表
  reqDetail(params) { return get(`/officialadmin/imgConfig/detail`, params) }, // 招聘详情
  reqAdd(params) { return post(`/officialadmin/imgConfig/add`, params) }, // 添加招聘
  reqDelete(params) { return get(`/officialadmin/imgConfig/delete`, params) }, // 招聘公告
  reqModify(params) { return post(`/officialadmin/imgConfig/update`, params) }, // 招聘公告编辑
  reqUpdateStatus(params) { return post(`/officialadmin/imgConfig/updateStatus`, params) },
  reqImgUrl(params) { return get(`/officialadmin/file/getImgUrl`, params) },
  reqLevel(params) {return get('/officialadmin/imgConfig/queryLevelList', params)}
}

// 职位类型模块
export const Jobtype = {
  reqList(params) { return post(`/officialadmin/dict/selectPageDict`, params) }, // 职位类型列表
  reqDetail(params) { return get(`/officialadmin/dict/searchDetail`, params) }, // 职位类型详情
  reqAdd(params) { return post(`/officialadmin/dict/createDict`, params) }, // 添加职位类型
  reqModify(params) { return post(`/officialadmin/dict/editDict`, params) }, // 职位类型编辑
}

//用户模块
export const User = {
  reqList(params) { return post(`/officialadmin/userView`, params) }, // 用户列表
  reqDetail(params) { return get(`/officialadmin/userDetail`, params) }, // 用户详情
  reqAdd(params) { return post(`/officialadmin/userAdd`, params) }, // 添加用户
  reqModify(params) { return post(`/officialadmin/userUpdate`, params) }, // 编辑用户
  getRoles(params) { return post(`/officialadmin/getRoles`, params) }, // 查询角色列表
  getOss(params) { return post(`/officialadmin/getOss`, params) },  //查询oss信息
}

//用户模块
export const Banner = {
  reqList(params) { return post(`/officialadmin/search_banner`, params) }, // 列表
  reqAdd(params) { return post(`/officialadmin/add_banner`, params) }, // 添加
  reqDelete(params) { return get(`/officialadmin/delete_banner`, params) }, // 删除
  reqModify(params) { return post(`/officialadmin/update_banner`, params) }, // 修改
  reqUp(params) { return post(`/officialadmin/release_banner`, params) }, // 上线
  reqDown(params) { return get(`/officialadmin/offline_banner`, params) }, // 下线
  reqUpList(params) { return get(`/officialadmin/online_banner`, params) }, // 列表中上线
  reqDetail(params) { return get(`/officialadmin/bannerDetail`, params) }, // 详情
}

// 联系我们模块
export const Feedback = {
  reqList(params) { return post(`/officialadmin/feedBack/selectPage`, params) }, // 分页查建议反馈列表
  reqDetail(params) { return get(`/officialadmin/feedBack/detail`, params) }, // 建议反馈详情
  reqExcel(params) { return post(`/officialadmin/feedBack/export`, params, { responseType: 'arraybuffer' }) }, // 导出
}

// 加盟合作汇总模块
export const JoinUsList = {
  reqList(params) { return post(`officialadmin/joinCooperation/selectPage`, params) } ,// 列表
  reqExcel(params) { return post(`/officialadmin/joinCooperation/export`, params, { responseType: 'arraybuffer' }) },
}

export const VipList = {
    reqList(params) { return post(`officialadmin/vipRegistration/selectPage`, params) } ,// 列表,
    reqExcel(params) { return post(`/officialadmin/vipRegistration/export`, params, { responseType: 'arraybuffer' }) },
  }
  