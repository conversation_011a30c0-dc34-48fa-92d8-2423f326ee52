// import fetch from '../utils/request'
import { service as fetch } from '../boot/axios'

// const BASE_URL = 'http://10.33.61.11:30273/official'

export const order = {
  /**
   *  四级地址解析
   * @param {*} params
   * @returns
   */
  getAreaByGeoCoding: params => fetch.get(`/official/lmdm/place/getAreaByGeoCoding`, {params}),
  /**
   *  公司新闻
   * @param {*} params
   * @returns
   */
  getNewsList: params => fetch.post(`/official/information/pageRelease`, params),   
  /**
   *  获取新闻详情
   * @param {*} params
   * @returns
   */
  getDetailNew: params => fetch.get(`/official/information/getInfoRelease`, {params}),
   /**
   *  获取Banner
   * @param {*} params
   * @returns
   */
    getBannerList: params => fetch.post(`/official/getAllrelease`, params),
  /**
   *  获取下单物品类型
   * @param {*} params
   * @returns
   */
  getArticleType: params => fetch.get(`/official/lmdm/getArticleType`, params),
  /**
   *  获取下单支付方式
   * @param {*} params
   * @returns
   */
  getPaymentType: params => fetch.get(`/official/lmdm/getPaymentType`, params),

  /**
   *  下单
   * @param {*} params
   * @returns
   */
  saveOrder: params => fetch.post(`/official/oms/saveOrder`, params),

  /**
   *  获取产品类型
   * @param {*} params
   * @returns
   */
  getProducttType: params => fetch.get(`/official/lmdm/getProducttType`, params),
  /**
   *  获取产品类型
   * @param {*} params
   * @returns
   */
  getProducttType2: params => fetch.get(`/official/lmdm/getProductTypeToCache?externalFlag=1`, params),

  /**
   *  寄件地址
   * @param {*} params
   * @returns
   */
  findOnLineAreas: params =>
    fetch.post(`/official/lmdm/area/online/findOnLineAreas`, params),

  /**
   *  结算方式
   * @param {*} params
   * @returns
   */
  getSettlement: params => fetch.get(`/official/lmdm/getSettlement`, params),

  /**
   *  收件地址
   * @param {*} params
   * @returns
   */
  findOnLineAreasDispatch: params =>
    fetch.post(`/official/lmdm/area/online/findOnLineAreas`, params),

  /**
   *  获取主子单号
   * @param {*} params
   * @returns
   */
   getMainSubBillCode: params => fetch.post(`/official/waybill/getMainSubBillCode`, params),

     /**
   *  判断是否有轨迹
   * @param {*} params
   * @returns
   */
     trackingIsNotEmpty: params => fetch.post(`/official/waybill/trackingIsNotEmpty`, params),
  /**
   *  校验电话号码四位
   * @param {*} params
   * @returns
   */
  trackingValidate: params => fetch.post(`/official/waybill/trackingValidate`, params),

   /**
   *  物流追踪
   * @param {*} params
   * @returns
   */
    trackingCustomerByWaybillNo: params => fetch.post(`/official/waybill/trackingCustomerByWaybillNo`, params),

    /**
   *  网点查询
   * @param {*} params
   * @returns
   */
     getNetwork: params => fetch.post(`/official/lmdm/getNetwork`, params),
  /**
   *  时效查询
   * @param {*} params
   * @returns
   */
  getAging: params => fetch.get(`/official/lmdm/sysEstimateTime/getAging`, {params}),
  /**
   *  运费查询
   * @param {*} params
   * @returns
   */
  newGetAging: params => fetch.get(`/official/specialTimeLimitProductType/getList`, {params}),
  /**
   *  运费查询（新）
   * @param {*} params
   * @returns
   */
  getFreight: params => fetch.post(`/official/costCalculation/spmStandardShippingQuote/comCost`, params),
  /**
   *  省市区查询
   * @param {*} params
   * @returns
   */
   findAllAreas: params => fetch.post(`/official/lmdm/area/online/findAllAreas`, params, {config:{
    timeout: 8000
   }}),
   paymentLimit: params => fetch.post(`/official/oms/paymentLimit`,params),
  /**
   *  获取付款方式下拉列表
   */
  getPaymentMethod: () => fetch.get(`/official/baseData/getPaymentModeList`),
  /**
   *  获取产品类型
   */
  getProductTypeByAddress: params => fetch.get(`/official/lmdm/getProductTypeList?sendCityId=${params.sendCityId}&dispatchCityId=${params.dispatchCityId}`),
/**
   *  根据邮编查询省市区乡镇
   * @param {*} params
   * @returns
   */
selectByFuzzyCondition: params => fetch.post(`/official/lmdm/area/online/selectByFuzzyCondition`, params),  
}
// 二期0730个人中心
export const pensonCenter = {
  /**
   *  我的订单列表
   * @param {*} params
   * @returns
  */
  orderList: params => fetch.post(`/official/oms/getMyOrderPage`, params),
  /**
   *  我的订单详情
   * @param {*} params
   * @returns
  */
   detailOrder: params => fetch.get(`/official/oms/detailOrder`, {params}),
   
   /**
   *  我的订单取消订单
   * @param {*} params
   * @returns
  */
  cancelOrder: params => fetch.post(`/official/oms/cancel`, params),
  /**
   *  通讯录列表
   * @param {*} params
   * @returns
  */
   getAddressList: params => fetch.get(`/official/address/page`, { params }),
   /**
    *  通讯录默认
    * @param {*} params
    * @returns
   */
   getDefaultAddress: params => fetch.get(`/official/address/getDefault`, { params }),
   /**
    *  通讯录新增
    * @param {*} params
    * @returns
   */
  getAddAddress: params => fetch.post(`/official/address/add`, {params}),
  /**
    *  通讯录删除
    * @param {*} params
    * @returns
   */
  getDeleteAddress: params => fetch.post(`/official/address/delete`, params),
  /**
    *  通讯录修改
    * @param {*} params
    * @returns
   */
  getUpdateAddress: params => fetch.post(`/official/address/update`, params),

  /**
    *  个人信息获取用户名和手机号
    * @param {*} params
    * @returns
   */
  getUserInfo: params => fetch.post(`/official/selectNickName`, params),
  /**
    *  修改账户信息手机号
    * @param {*} params
    * @returns
   */
   updateMobile: params => fetch.post(`/official/updateMobile`, params),
  /**
    *  修改账户信息用户名
    * @param {*} params
    * @returns
  */
   updateNickName: params => fetch.post(`/official/updateNickName`, params),
  /**
    *  查看地址详情
    * @param {*} params
    * @returns
  */
      getAddressDetail: params => fetch.get(`/official/address/detail`, {params}),
  /**
   *  查询用户绑定网点
   * @param {*} params
   * @returns
   */
  queryBindingNetwork: params => fetch.get(`/official/userBinding/queryBindingNetwork`, {params}),
  /**
   *  查询用户绑定网点
   * @param {*} params
   * @returns
   */
  calculateDistance: params => fetch.get(`/official/distance/calculateDistance`, {params}),
  /**
   *  查询用户绑定网点
   * @param {*} params
   * @returns
   */
  searchNetWork: params => fetch.get(`/official/lmdm/sysNetworkVO/getByName`, {params}),
  /**
   *  获取传入经纬度最近的五个网点信息
   * @param {*} params
   * @returns
   */
  searchNearNetWork: params => fetch.get(`/official/lmdm/sysNetworkVO/getNearbySysNetworkVo`, {params}),
  /**
   *  保存用户绑定网点
   * @param {*} params
   * @returns
   */
  saveUserBinding: params => fetch.post(`/official/userBinding/saveUserBinding`, params),
  /**
   *  取消绑定网点
   * @param {*} params
   * @returns
   */
  unBindByUserMark: params => fetch.get(`/official/userBinding/unBindByUserMark`, {params}),

}

export const aboutUs = {
  /**
   * 查询招聘信息
   */
  // getRecruitment: params => fetch.post(`/official/searchCareersList`, {params})
  getRecruitment: params => fetch.post(`/official/officialadmin/searchCareersList`, params)
}

export const contactUs = {
  // 提交问卷反馈
  commitQuestion: params => fetch.post(`/official/officialadmin/addFeedBack`, params),
  // 提交加盟合作
  addJoinCooperation: params => fetch.post(`/official/officialadmin/addJoinCooperation`, params),
  //vip
  addVipRegistration: params => fetch.post(`/official/officialadmin/addVipRegistration`, params),
  getGoodsList: params => fetch.get(`/official/baseData/getGoodsList`)
}
// export const setNeedDispatch = (reqData) => post(`/vip/user/setNeedDispatch?needDispatchFlag=${reqData.needDispatchFlag}`)
// 首页banner
export const homeApi = {
  getBannerList: params => fetch.post(`/official/getAllrelease`, params),
  getScrollbarQuery: params => fetch.get(`/official/scrollbar/query`, {params}),
  getImgconfigQuery: params => fetch.get(`/official/imgconfig/query`, {params}),
}
