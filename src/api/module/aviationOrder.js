import { get, post } from '@http/request'

export const getAviationBasicData = (params) => get(`/vip/aviationOrderManage/getAviationBasicData`, params)

export const saveAviationOrder = (params) => post(`/vip/aviationOrderManage/saveAviationOrder`, params)
export const batchSaveAviationOrder = (params) => post(`/vip/aviationOrderManage/batchSaveAviationOrder`, params)

export const downloadAviationOrderTemplate = () => post(`/vip/aviationOrderManage/downloadTemplate`, '', { responseType: 'blob' })


export const aviationOrderManage = (params) => post(`/vip/aviationOrderManage/page`, params)
