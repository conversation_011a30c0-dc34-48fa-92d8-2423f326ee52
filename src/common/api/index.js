/*
 * @title: 合并api目录下的所有定义的url，并按文件名（带有.url和没带）分类
 * @author: Alinc
 * @date: 2020-6-28 14:08:21
 */

import Api from '@public/api'
const API_URL = { ...Api.API_URL }
const API = { ...Api.API }

// 当前目录下所有js文件
const files = require.context('.', true, /\.js$/)

files.keys().forEach(key => {
  if (key === './index.js') {
    return
  }
  const fils = files(key)
  if (key.includes('url.js')) {
    let k = key.replace(/(^\.\/|\.url.js$)/g, '')
    const ka = k.split('/')
    k = ka[ka.length - 1]
    API_URL[k] = API_URL[k] || {}
    Object.keys(fils).forEach(item => {
      // export default {}写法的文件
      if (item === 'default') {
        API_URL[k] = { ...API_URL[k], ...fils[item] }
      } else {
        // 多个 export const 写法的文件
        API_URL[k][item] = fils[item]
      }
    })
  } else {
    let k = key.replace(/(^\.\/|\.js$)/g, '')
    const ka = k.split('/')
    k = ka[ka.length - 1]
    API[k] = API[k] || {}
    Object.keys(fils).forEach(item => {
      // export default {}写法的文件
      if (item === 'default') {
        API[k] = { ...API[k], ...fils[item] }
      } else {
        // 多个 export const 写法的文件
        API[k][item] = fils[item]
      }
    })
  }
})
export default {
  API_URL, API
}
