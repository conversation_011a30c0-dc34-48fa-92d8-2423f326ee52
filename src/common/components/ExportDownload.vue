/* *@Description: 预约下载中心 */

<template>
  <div class="gateway">
    <div class="main-wrap">
      <el-dialog :title="$lang('下载中心')" :width="'900px'" @close="close" :close-on-click-modal="false" :visible="dialogOutputVisible" @opened="opened" top="10vh">
        <avue-crud
          class="box-list"
          ref="params"
          :data="tableList"
          :option="listOpt"
          :page="page"
          :table-loading="loading"
          @size-change="sizeChange"
          @current-change="currentChange"
          @search-reset="resetList"
          @search-change="searchChange"
          @row-save="addFun"
          @delete="deleteView"
          @row-del="deleteView"
        >
          <template slot="menu" slot-scope="{ row }">
            <el-button class="row-icon" icon="iconfont iconbiaodan-xiazai" type="text" v-if="row.isfinished == 1" :title="$lang('下载')" @click="download(row)"></el-button>
            <!-- <el-button class="row-icon" icon="iconfont iconshanchu2" type="text" :title="$lang('删除')" @click="deleteRow(row)"></el-button> -->
          </template>
        </avue-crud>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import mixin from '@mixins/mixin'
import { RESPONSE_CODE } from '@public/http/config'

export default {
  name: 'ExportDownload',
  mixins: [mixin],
  data() {
    return {
      COM_HTTP: {
        reqList: this.apiConf.getExportTaskList,
        deleteDown: this.apiConf.delLoadExcel
      },
      isShowFirst: false
    }
  },
  props: {
    moduleType: {
      type: String,
      default: ''
    },
    dialogOutputVisible: {
      type: Boolean,
      default: false
    },
    /**
     * Record<'getExportTaskList' | 'delLoadExcel' | 'download',Promise<any>>
     *
     */
    apiConf: {
      required: true
    }
  },
  computed: {
    listOpt() {
      return {
        search: true,
        deleteBtn: false,
        addBtn: false,
        editBtn: false,
        viewBtn: false,
        fixHeight: 25,
        ignorePermission: true, // 忽略权限校验
        searchExtend: true,
        menuWidth: 120,
        selection: false, // 开启选择操作
        //  搜索框和表格列及其字段
        column: [
          {
            label: '导出时间',
            prop: 'exportTime'
          },
          {
            label: '模块名称',
            prop: 'moduleName',
            sort: 2
          },
          {
            label: '结果',
            prop: 'isfinished',
            formatter: (row, value, label, column) => {
              const map = {
                '-1': '<i class="dot red"></i> ' + this.$lang('失败'),
                0: '<i class="dot"></i> ' + this.$lang('正在导出'),
                1: '<i class="dot active"></i> ' + this.$lang('导出完成'),
                2: '<i class="icondengdaizhong iconfont"></i> ' + this.$lang('未知')
              }
              return map[row.isfinished]
            }
          }
        ]
      }
    }
  },
  methods: {
    async deleteRow(row) {
      const params = {
        id: row.id
      }
      const { code, msg } = await this.apiConf.delLoadExcel(params)
      if (code === RESPONSE_CODE.SUCCESS) {
        this.$message.success(this.$lang('删除成功'))
        this.searchFun()
      } else {
        this.$message.error(msg)
      }
    },
    async download(row) {
      const elink = document.createElement('a')
      elink.style.display = 'none'
      elink.href = row.exportExcelUrl
      elink.setAttribute('download', this.$lang(row.moduleName) + '.xls')
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
      this.searchFun()
    },
    async searchFun(params, current) {
      this.loading = true
      console.log(this.apiConf)
      console.log(this.COM_HTTP)
      if (typeof this.searchFunBefore === 'function') {
        const state = this.searchFunBefore()
        if (!state) {
          this.loading = false
          return
        }
      }
      // avue-crud的ref名称是params
      if (!params && this.$refs.params) {
        params = this.rangHandle(this.$refs.params.searchForm)
      }
      // 传入参数有current
      if (current) {
        this.page.current = current
      }
      const param = this.searchFunParamsHandle(params)
      if (param === false) {
        this.loading = false
        return
      }
      console.log(param)
      // 添加超时清除loading状态的定时器
      // const timeState = setTimeout(() => {
      //   if (this.loading) this.loading = false
      // }, TIMEOUT_TIMES)
      try {
        console.log('util/mixin.searchFun::params', param)
        const res = await this.COM_HTTP.reqList(param)
        // 清除超时定时器
        // clearTimeout(timeState)
        this.loading = false
        this.pageLoading = false
        if (res.code === RESPONSE_CODE.SUCCESS) {
          if (this.usePagination) {
            this.tableList = res.data.records ? (this.formatList ? this.formatList(res.data) : res.data.records) : []
            this.page.total = res.data.total || 0
            this.page.current = res.data.current || 1
          } else {
            this.tableList = this.formatList ? this.formatList(res.data) : res.data || []
          }
          this.searchAfterFun()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        console.error('searchFun::error', error)
        // 清除超时定时器
        // clearTimeout(timeState)
        this.loading = false
        this.pageLoading = false
      }
    },
    // 打开
    opened() {
      this.searchFun()
    },
    // 关闭
    close() {
      this.$emit('update:dialogOutputVisible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.main-wrap {
  height: calc(100% - 238px);
  padding: 0 20px;
  ::v-deep .avue-crud {
    .avue-crud__right {
      display: none;
    }
    .table-search {
      display: none;
    }
    .el-button.el-button--info {
      display: none;
    }
  }
}
</style>
