<!--
 * @Description: 导出订单
 * @Author: ganjia
 * @Date: 2019-10-14 10:19:21
 -->
<template>
  <div class="expoOrder">
    <el-dialog :title="title" :visible.sync="visible" :close-on-click-modal="false" :modal-append-to-body='true'  width="900px" @close='close'>
      <div  class="export-center">
        <!-- <div class="export-time" v-if="idsLength < 1">
          <div class="export-t">{{exportData.exportType==1?"订单":'揽收'}}时间：</div>
          <el-date-picker
            v-model="value3"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            end-placeholder="结束日期">
          </el-date-picker>
        </div> -->
        <div class="select-sort">
          <div class="select">
            <div class="select-top">
              <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">{{$lang('全选')}}</el-checkbox>
            </div>
            <div class="selects">
              <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange">
                <el-checkbox v-for="city in cities" :title="$lang(city.name)"  :label="city" :key="city.key" class="checkbox" :checked="city.required" :disabled="city.disabled">{{$lang(city.name)}}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="sort">
            <div class="sort-top">
              <span class="sort-t">{{$lang('导出排序')}}</span>
            </div>
            <div class="sorts">
              <vuedraggable class="wrapper" v-model="checkedCities">
                <transition-group>
                  <div class="sorts-list" v-for="(item, index) in checkedCities" :key="item.key">
                    <span :class="{'hoverColor':index==hoverIndex}" @mouseover="hoverIndex = index" @mouseout="hoverIndex = -1">{{$lang(item.name)}}</span>
                    <el-button type="text" icon="el-icon-error" @click="selected(item,index)" v-if="!item.required"></el-button>
                  </div>
                </transition-group>
              </vuedraggable>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="exportFn" class="h40">{{$lang('导出订单')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import vuedraggable from 'vuedraggable'
import { commonFun } from '@utils/common'

export default {
  name: 'Export',
  props: {
    idsLength: {
      type: Number,
      default: 0,
    },
    exportVisible: {
      type: Boolean,
      default: false,
    },
    exportData: {
      type: Object,
      default() {
        return {
          formProps: {
            exportStatus: '',
            exportType: 1, // 1：订单； 2：运单
          },
        }
      },
    },
    // 字段列表
    cityOptions: {
      type: Array,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      value3: commonFun.GetTodayDate(),
      checkAll: false,
      checkedCities: [],
      isIndeterminate: true,
      hoverIndex: -1,
      title: '',
      visible: false,
    }
  },
  computed: {
    cities() {
      return this.cityOptions
    },
  },
  watch: {
    exportVisible: {
      handler(val) {
        this.visible = val
      },
      deep: true,
    },
    // checkedCities: {
    //   handler(newV, oldV) {
    //     console.log(newV, oldV)
    //   },
    //   deep: true,
    // },
    exportData: {
      handler(val) {
        if (val.exportType === 1) {
          this.title = this.$lang(`导出订单`)
        } else {
          this.title = this.$lang(`导出运单`)
        }
      },
      deep: true,
    },
  },
  methods: {
    // 全选事件
    handleCheckAllChange(val) {
      this.checkedCities = val ? [...this.cityOptions] : this.cityOptions.filter(item => item.required)
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.cities.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length
    },
    selected(item, index) {
      this.checkedCities.splice(this.checkedCities.findIndex(item1 => item1 === item), 1)
    },
    close() {
      this.$emit('export-close', false)
      // 重置默认选中
      this.checkedCities = this.cityOptions.filter(item => item.required)
      this.isIndeterminate = true
    },
    exportFn() {
      // this.$emit('export-fn', false, this.checkedCities, this.idsLength < 1 ? this.value3 : null)
      this.$emit('export-fn', false, this.checkedCities)
    },
  },
  components: {
    vuedraggable,
  },
}
</script>
<style lang="scss" scoped>
  .hoverColor{
    color: #E60012 !important;
    cursor: pointer;
  }
  .export-time{
    padding-bottom: 16px;
    border-bottom: 1px solid #eee;
    .export-t{
      height:14px;
      font-size:14px;
      font-family:PingFangSC-Regular,PingFangSC;
      font-weight:400;
      color:rgba(51,51,51,1);
      line-height:14px;
      margin-bottom: 4px;
    }
  }
  .select-sort{
    display: flex;
    justify-content:space-between;
    align-items: center;
    // border-bottom: 1px solid #eee;
    padding:0 30px;
    .select{
      width: 690px;
      height: 354px;
    //   border-right: 1px solid #eee;
      box-sizing: border-box;
      .select-top{
        width: 100%;
        height: 30px;
        display: flex;
        justify-content:flex-start;
        align-items: center;
        // border-bottom: 1px solid #eee;
      }
      .selects{
          margin-top: 16px;
        height: 324px;
        display: flex;
        justify-content:space-between;
        align-items: flex-start;
        flex-wrap: wrap ;
        overflow: auto;
        background: #f7f7f9;
        border: 1px solid #eeeeee;
        border-radius: 8px;
        padding: 24px 24px 0px 48px ;
        box-sizing: border-box;
        .checkbox{
          width: 130px;
          margin: 0 0 24px 0px;
        }
        .checkbox:nth-child(4n){
          margin-right: 0;
        }
      }

    }
    .selects::-webkit-scrollbar{
        width:0;
    }
    .sort{
      width: 160px;
      height: 354px;
      box-sizing: border-box;
      margin-left: 24px;
      box-sizing: border-box;
      .sort-top{
        width: 100%;
        height: 30px;
        display: flex;
        justify-content:flex-start;
        align-items: center;
        // border-bottom: 1px solid #eee;
        .sort-t{
          display: inline-block;
          font-size:16px;
          font-family:PingFangSC-Regular,PingFangSC;
          color:#333;
          line-height:30px;
          margin-left: 8px;
          font-weight: 600;
        }

      }
      .sorts{
        height: 324px;
        overflow: auto;
        border: 1px solid #eeeeee;
        border-radius: 8px;
            background: #f7f7f9;
            margin-top: 16px;
        .sorts-list{
          height: 14px;
          padding: 16px 8px 0;
          display: flex;
          justify-content:space-between;
          align-items: center;
          span{
            width:100px;
            height:14px;
            font-size:14px;
            font-family:PingFangSC-Regular,PingFangSC;
            font-weight:400;
            color:rgba(102,102,102,1);
            line-height:14px;
          }
        }
        .wrapper{
            padding: 0 24px;
        }
      }
    }
    .sorts::-webkit-scrollbar{
        width:0;
    }
  }
  .el-dialog__footer {
      padding: 8px 24px 16px;
      text-align: right;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
  }
</style>
<style lang="scss">
.expoOrder{
    .el-checkbox {
        .el-checkbox__label{
           white-space: nowrap;
            text-overflow: ellipsis;
            width: 100px;
            overflow: hidden;
            line-height: 12px;
        }
    }
}

</style>

