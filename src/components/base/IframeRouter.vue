<template>
  <div>
    <!-- iframe页 -->
    <component v-for="item in hasOpenComponentsArr"
               :key="item.name"
               :is="item.name"
               v-show="$route.name === item.name"></component>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Vue from 'vue'
export default {
  name: 'IframeRouter',
  components: {
  },
  data() {
    return {
      componentsArr: [], // 需要展示的iframArr数据
      componentsArrOrigin: [] // 初始化注册数据
    }
  },
  computed: {
    ...mapGetters({
      cachedViews: 'tags/cachedViews'
    }),
    // 实现懒加载，只渲染已经打开过（hasOpen:true）的iframe页
    hasOpenComponentsArr() {
      return this.componentsArr.filter(item => item.hasOpen)
    }
  },
  watch: {
    'cachedViews'(val) {
      // 监听tags变化删除缓存
      const oldView = this.cachedViews.map(name => name.charAt(0).toLowerCase() + name.substr(1))
      this.componentsArr = this.componentsArrOrigin.filter(item => oldView.includes(item.name))
      this.isOpenIframePage()
    }
  },
  created() {
    // 获取含有iframe的组件
    const componentsArr = this.getComponentsArr()
    // 初始化将含有iframe的组件注册到组件里
    componentsArr.forEach(item => Vue.component(item.name, item.component))
    this.componentsArr = this.componentsArrOrigin = componentsArr
    this.isOpenIframePage()
  },
  mounted() {
  },
  methods: {
    // 根据当前路由设置hasOpen
    isOpenIframePage() {
      const target = this.componentsArr.find(item => item.name === this.$route.name)
      if (target && !target.hasOpen) target.hasOpen = true
    },
    // 遍历路由的所有页面，把含有iframeComponent标识的收集起来
    getComponentsArr() {
      const routes = this.$router.options.routes
      const iframeArr = routes.filter(item => item.iframeComponent)
      return iframeArr.map(item => {
        return {
          name: item.name,
          path: item.path,
          hasOpen: false, // 是否打开过，默认false
          component: item.iframeComponent // 组件文件的引用
        }
      })
    }
  }
}
</script>
