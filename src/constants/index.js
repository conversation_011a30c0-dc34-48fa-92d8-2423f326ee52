/*
 * @Date: 2020-08-28 15:16:30
 * @Author: MX
 * @LastEditTime: 2021-11-18 16:30:41
 * @Description: 常量配置
 */
//接口前缀
export const APICUS = "openplatformweb"
// 请求成功
export const RESPONSE_SUCCESS = 1
//OpenAPI 对外接口
//test
let testUrl = 'https://test-ynopenapi.yimidida.com/webopenplatformapi/api'
if(process.env.VUE_APP_ENV === 'prod'){
    testUrl = 'https://demoopenapi.jtcargo.co.id/webopenplatformapi/api'
}
let testUrlNew = 'https://test-ynopenapi.yimidida.com'
if(process.env.VUE_APP_ENV === 'prod'){
  testUrlNew = 'https://demoopenapi.jtcargo.co.id'
}
export const OPEN_API_TEST = testUrl;
export const OPEN_API_TEST_NEW = testUrlNew;
//pro
export const OPEN_API_PRO = 'https://openapi.jtcargo.co.id/webopenplatformapi/api'
// 国际化接口
export const I18N_URL = process.env.VUE_APP_in18_URL

// 后端语种与element国际化的对应map
export const LANG_ELEMENT_MAP = {
  'CN': 'zh-cn', // 中文
  'EN': 'en', // 英语
  'TH': 'th', // 泰语
  'KH': 'kh', // 柬埔寨(暂无)
  'MY': 'my', // 马来语(暂无)
  'ID': 'id', // 印尼语
  'VN': 'vi', // 越南语
  'AR': 'ar', // 阿拉伯语
}
export const DEFAULT_LANG = "zh-cn"
