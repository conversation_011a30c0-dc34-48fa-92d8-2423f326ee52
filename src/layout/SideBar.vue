<template>
  <div class="sidebar">
    <div class="logo-box" :class="isCollapse ? 'logo-box-mini' : ''">
      <i class="iconlogo iconfont logo-img"></i>
    </div>

    <el-menu class="el-menu-vertical-demo" menu-trigger="click" :collapse="isCollapse" :unique-opened="uniqueOpened" :default-active="changeMenu" router>
      <template v-for="item in menuList">
        <el-menu-item v-if="item.moduleType === 3 && item.isShow === 1" :key="item.id.toString()" :index="item.routeName" :route="'/' + item.routeName">
          <i class="iconfont" :class="item.menuIcon" />
          <span slot="title" style="padding-left:16px;">{{$lang(item.name)}}</span>
        </el-menu-item>
        <el-submenu v-if="item.moduleType !== 3 && item.isShow === 1" :key="item.id" :index="item.id.toString()">
          <template slot="title"
            ><i class="iconfont" :class="item.menuIcon" /><span slot="title">{{$lang(item.name)}}</span></template>
          <template v-for="child in item.children">
            <el-menu-item v-if="child.moduleType === 3" :key="child.id" :route="'/' + child.routeName" :index="child.routeName">
              <i class="iconfont coin" :class="child.menuIcon" />
              <span slot="title">{{ $lang(child.name)}}</span>
            </el-menu-item>
            <!-- 二级菜单 -->
            <el-submenu v-if="child.moduleType !== 3 && child.isShow === 1" :key="child.id" :index="child.id.toString()">
              <template slot="title"><i class="iconfont" :class="child.menuIcon" /><span slot="title">{{$lang(child.name)}}</span></template>
              <template v-for="grandson in child.children">
                <el-menu-item v-if="grandson.moduleType === 3" :key="grandson.id" :route="'/' + grandson.routeName" :index="grandson.routeName">
                  <i class="iconfont coin" :class="grandson.menuIcon" />
                  <span slot="title">{{ $lang(grandson.name)}}</span>
                </el-menu-item>
              </template>
            </el-submenu>
          </template>
        </el-submenu>
      </template>
    </el-menu>
  </div>
</template>

<script>
export default {
  data() {
    return {
      changeMenu: '',
      uniqueOpened: true,
    }
  },
  props: ['isCollapse'],
  created() {
    this.changeMenu = this.$route.name
  },
  computed: {
    menuList() {
      return this.$store.state.base.systemMenus || []
    },
    // 用户信息
    userInfo() {
      return this.$store.state.base.user || {}
    },
  },
  watch: {
    $route(item) {
      this.changeMenu = item.name // 路由发生变化选中左侧菜单
    },
  },
  methods: {},
}
</script>

<style lang="scss" rel="text/css">
$bg: rgba(0, 0, 0, 0.1);
$bgTxt: rgba(0, 0, 0, 1);
$blackTextColor: #e60012;

.sidebar {
  .logo-box {
    line-height: 48px;
    text-align: center;
    height: 48px;
    color: #197D3B;
    font-size: 18px;
    width: 170px;
    box-sizing: border-box;
    transition: width 0.1s ease-out;
     .logo-img {
      font-size: 24px;
      transition: width 0.1s ease-out;
      color: #197D3B;
    }
  }
  .logo-box-mini {
    width: $mini-sidebar;
    text-align: center;
    font-size: 15px;
    transition: width 0.1s ease-out;
    text-align: left;
    transition: width 0.1s ease-out;
    .logo-img {
      transition: width 0.1s ease-out;
      display: none;
    }
    &:before {
      content: '\E6CD';
      font-family: 'iconfont';
    }
  }

  .slider-day {
    width: 100%;
    height: 60px;
    border-radius: 40px;
    text-align: center;
    margin-bottom: 30px;
    transition: all 0.1s ease-out;
    .week {
      font-size: 18px;
      padding-top: 8px;
    }
    .time {
      padding-top: 10px;
      font-size: 12px;
    }
  }
  .slider-day-mini {
    height: 0;
    width: 0;
    margin: 0;
    .week,
    .time {
      font-size: 12px;
    }
  }
  .el-menu {
    border-right: none;
    transition: all 0.1s ease-out;
    background-color: transparent;
    width: 170px;
    [class^='iconfont'] {
    //   width: 22px;
      display: inline-block;
      text-align: center;
    }
    .el-submenu {
      &.is-active {
        background: #121725;
      }
      .el-menu-item {
        border: none;
        background: #121725;
        font-size: 16px ;
        padding: 0 30px !important;
      }
      .el-submenu__title {
        height: 56px;
        // line-height: 56px;
        font-size: 16px;
        color: hsla(0, 0%, 100%, 0.65);
        padding: 0 20px;
        list-style: none;
        cursor: pointer;
        position: relative;
        transition: border-color 0.3s, background-color 0.3s, color 0.3s;
        box-sizing: border-box;

        display: flex;
        line-height: 1;
        white-space: normal;
        word-break: break-all;
        align-items: center;
        i {
          font-size: 15px;
          right: 12px;
          color: #fff;
          &.el-submenu__icon-arrow {
            font-size: 12px;
            font-weight: bold;
            top: 50%;
            // margin-right: 10px;
          }
        }
        &:focus,
        &:hover {
          background: inherit;
          color: #fff;
          opacity: 1;
        }
      }
    }
    .el-menu-item {
        color: rgba(255, 255, 255, 0.65);
            display: flex;
            line-height: 1;
            white-space: normal;
            word-break: break-all;
            align-items: center;
      span {
        padding-left: 0px !important;
      }
      .el-tooltip {
        padding: 0px 12px !important;
      }
      i {
        color: #fff;
      }
      .coin {
        visibility: hidden;
        background-color: #999999;
        border-radius: 4px;
        margin-left: 5px;
        width: 4px;
        height: 4px;
      }
      &:hover,
      &:focus {
        background: #27262c;
        opacity: 1;
      }
      &.is-active {
        color: #e60012;
        i {
          color: #e60012;
        }
        .coin {
          visibility: visible;
          background: #e60012;
        }
      }
    }
    .el-menu--inline {
      .el-menu-item {
        font-size: 15px;
        padding-left: 40px !important;

        display: flex;
        line-height: 1;
        white-space: normal;
        word-break: break-all;
        align-items: center;
      }
    }
    span{
        margin-left:8px
    }
  }
  // 收起状态
  .el-menu--collapse {
    width: $topHeight;
    transition: width 0.1s ease-in-out;
    .el-menu-item.is-active {
      background-color: transparent !important;
    }
    .el-submenu {
      &.is-active {
        .el-submenu__title {
          background-color: transparent !important;
          opacity: 1;
          i {
            color: $blackTextColor;
          }
        }
        .el-menu--inline {
          .el-menu-item {
            color: $blackTextColor;
          }
        }
      }
    }
    .el-menu-item {
      height: $submenu-height !important;
      line-height: $submenu-height !important;
    }
     .el-submenu__title {
         padding-left: 12px !important;
      }
  }
  // 展开状态
  .el-menu-vertical-demo:not(.el-menu--collapse) {
    // width: $asideWidth;
    min-height: 400px;
    transition: width 0.1s ease-in-out;
  }
}
.el-tooltip__popper {
  line-height: 24px;
}
//  收缩状态二级菜单（悬浮菜单）
.el-menu--vertical {
  max-height: 100%;
  max-width: 130px;
  overflow-y: auto;
  background: #17161c;
  .el-menu {
    margin-right: 0;
    background: #17161c;
    .el-menu-item {
      color: #fff;
      opacity: 0.6;
      padding-left: 10px !important;
      height: $menu-height;
      line-height: $menu-height;
      padding-left: 10px;
      font-size: 12px;
      &:hover {
        opacity: 1;
        background: inherit;
      }
      .coin {
        visibility: hidden;
        border-radius: 4px;
        margin-right: 4px;
        width: 4px;
        height: 4px;
        display: inline-block;
      }
      &.is-active {
        opacity: 1 !important;
        span {
          color: $blackTextColor;
        }
        i {
          color: $blackTextColor;
        }
        .coin {
          visibility: visible;
          background-color: $blackTextColor;
        }
      }
    }
  }
}
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
/*滚动条小方块*/
::-webkit-scrollbar-thumb {
  background-color: $bg;
  -webkit-border-radius: 5px; /* Safari 和 Chrome */
  -moz-border-radius: 5px; /* Firefox */
  -o-border-radius: 5px; /* Opera */
  border-radius: 5px;
}
/*滚动轨道两端按钮*/
::-webkit-scrollbar-button {
  -webkit-box-shadow: inset 0 0 5px $bg;
  /*border-radius: 10px;*/
  background-color: $bg;
}
/*滚动轨道 内阴影*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px $bg;
  /*border-radius: 10px;*/
  background-color: $bg;
}
</style>
