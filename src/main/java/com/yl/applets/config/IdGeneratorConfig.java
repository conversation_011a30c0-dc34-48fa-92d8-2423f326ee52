/*
package com.yl.applets.config;

import com.yl.id.DefaultWorkerIdAssigner;
import com.yl.id.IdGenerator;
import com.yl.id.SnowFlakeIdGenerator;
import com.yl.id.WorkerIdAssigner;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

*/
/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019
 *
 * @Description:ID生成器相关配置
 * @Project:
 * @CreateDate: Created in 2019/7/18 17:40
 * @Author: <a href="<EMAIL>">zijing.lin</a>
 *//*

@Configuration
public class IdGeneratorConfig {

    @Value("${spring.application.name}")
    private String appName;

    @Value("${zkUrl}")
    private String zkUrl;

    */
/**
     * worker id分配器实例
     *
     * @return workerIdAssigner
     *//*

    @Bean
    public WorkerIdAssigner workerIdAssigner() {
        return new DefaultWorkerIdAssigner(appName, zkUrl);
    }

    */
/**
     * Demo对象的ID生成器实例
     *
     * @return demoIdGenerator
     *//*

    @Bean
    public IdGenerator<Long> essIdGenerator() {
        return new SnowFlakeIdGenerator(workerIdAssigner());
    }
}
*/
