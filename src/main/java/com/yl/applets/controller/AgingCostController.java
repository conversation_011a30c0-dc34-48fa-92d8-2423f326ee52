package com.yl.applets.controller;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.AgingCostDTO;
import com.yl.applets.service.AgingCostService;
import com.yl.applets.vo.spmo.AgingCostVO;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 运费时效查询
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/14 14:52
 */
@RestController
@RequestMapping("/agingCost")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AgingCostController extends BaseController {
    private final AgingCostService agingCostService;

    /**
     * 运费时效查询
     * @param agingCostDTO
     * @return
     */
    @PostMapping("/get")
    public Result<List<AgingCostVO>> get(@Valid @RequestBody AgingCostDTO agingCostDTO){
        log.info("运费时效查询请求入参:{}", JSON.toJSONString(agingCostDTO));
        List<AgingCostVO> agingCosts = agingCostService.get(agingCostDTO);
        return Result.success(agingCosts);
    }

}
