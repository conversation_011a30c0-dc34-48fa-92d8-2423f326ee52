package com.yl.applets.dto;

import com.yl.applets.enums.DefAddressType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:
 * author: chenyejun
 * Date: 2021-09-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DefaultAddrDTO  implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(name = "id" ,value = "唯一id(修改操作必传)")
    private Long id;

    @ApiModelProperty(name = "userId" ,value = "用户id",hidden = true)
    private Long userId;

    /**
     * 是否设置默认地址 1=默认 2=否
     * {@link DefAddressType}
     */
    @ApiModelProperty(name = "defaultType" ,value = "是否默认地址:1是,2否",required = true)
    @NotNull(message = "是否默认地址不能为空")
    private Integer defaultType;

    /**
     * 地址类型：1=寄件 2=收件
     */
    @ApiModelProperty(name = "addressType" ,value = "地址类型：1=寄件 2=收件",required = true)
    @NotNull(message = "地址类型不能为空")
    private Integer addressType;

    @ApiModelProperty(name = "updateByName" ,value = "修改人姓名",hidden = true)
    private String updateByName;

}
