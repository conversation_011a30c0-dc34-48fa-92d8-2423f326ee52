/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OmsOrderCancelApiDTO
 * Author:   luhong
 * Date:     2020-10-15 14:43
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import com.yl.common.base.valid.dto.BaseApiDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(
        value = "OmsOrderApi取消订单请求对象",
        description = "取消订单请求对象"
)
public class OmsOrderCancelApiDTO extends BaseApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "订单编号id不能为空")
    @ApiModelProperty(value = "订单编号集合",required = true)
    private List<Long> orderIds;

    @ApiModelProperty(value = "客户编号code",hidden = true)
    @Size(
            max = 30,
            message = "客户编号不能超过30个字符"
    )
    private String customerCode;

    @ApiModelProperty(value = "客户订单编号",hidden = true)
    @Size(
            max = 10000,
            message = "客户订单编号不能超过10000个字符"
    )
    private String customerOrderId;

    @ApiModelProperty(value = "原因code 见OrderCancelEnum",hidden = true)
    private Integer cancelReasonCode;

    @ApiModelProperty(value = "取消说明",hidden = true)
    private String cancelExplain;

    @ApiModelProperty("原因")
    @Size(
            max = 40,
            message = "客户编号不能超过40个字符"
    )
    private String reason;

    @ApiModelProperty(hidden = true)
    private String operatorNumber;

    @ApiModelProperty(hidden = true)
    private String operatorName;

    @ApiModelProperty(hidden = true)
    private Integer updateBy;

    @ApiModelProperty(hidden = true)
    private String updateByName;
    /**
     * 操作来源
     */
    @ApiModelProperty(hidden = true)
    private String operateSource;
}