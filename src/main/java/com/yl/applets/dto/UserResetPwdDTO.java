package com.yl.applets.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description: 注册
 * @Author: luobin
 * @Date: 2021/8/16 2:00 下午
 */
@Data
public class UserResetPwdDTO {

    @ApiModelProperty(value = "手机国家区号")
    @NotNull(message = "手机国家区号不能为空")
    private Integer countryCode;


    @ApiModelProperty(value = "验证码")
    @NotBlank(message = "验证码不能为空")
    @Size(max = 100,message = "验证码不能超过100个字符")
    private String pwd;


    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    @Size(max = 16,message = "手机号不能超过16个字符")
    private String phoneNum;

    @ApiModelProperty(value = "验证码")
    @NotBlank(message = "验证码不能为空")
    @Size(max = 6,message = "验证码不能超过6个字符")
    private String code;


}
