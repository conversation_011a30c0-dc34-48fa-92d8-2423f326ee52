/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: ExpressTypeEnum
 * Author:   luhong
 * Date:     2020-10-14 18:28
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.enums;

import com.yl.common.base.util.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
public enum ExpressTypeEnum {
    ORDINARY_EXPRESS("FT", "标准快递"),
    MAX_EXPRESS("MAX", "大货产品"),
    AIR_EXPRESS("AIR", "空运产品");

    private String code;
    private String name;

    private ExpressTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        } else {
            ExpressTypeEnum expressTypeEnum = (ExpressTypeEnum)Arrays.stream(values()).filter((e) -> {
                return e.code.equals(code);
            }).findFirst().get();
            return Objects.isNull(expressTypeEnum) ? null : expressTypeEnum.name;
        }
    }

    public static ExpressTypeEnum of(String code) {
        return (ExpressTypeEnum) Arrays.stream(values()).filter((r) -> {
            return r.code.equals(code);
        }).findFirst().orElse(null);
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }
}