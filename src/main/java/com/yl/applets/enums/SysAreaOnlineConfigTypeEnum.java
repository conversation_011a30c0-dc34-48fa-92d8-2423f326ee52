package com.yl.applets.enums;

/**
 * 上线省市区配置枚举
 * 支持1:小程序,2:VIP大客户,3:官网,4:巴枪APP,5:JMSWEB
 *
 * <AUTHOR> 2020年2月29日15:33:31
 */
public enum SysAreaOnlineConfigTypeEnum {
    TYPE_SMALL_ROUTINE(1, "小程序"),
    TYPE_VIP(2, "VIP大客户"),
    TYPE_GW(3, "官网"),
    TYPE_APPBC(4, "巴枪APP"),
    TYPE_JMSWEB(5, "JMSWEB"),
    ;

    private int type;
    private String remark;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


    private SysAreaOnlineConfigTypeEnum(int type, String remark) {
        this.type = type;
        this.remark = remark;
    }

    public static SysAreaOnlineConfigTypeEnum getByType(Integer type) {
        for (SysAreaOnlineConfigTypeEnum typeEnum : SysAreaOnlineConfigTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }

}
