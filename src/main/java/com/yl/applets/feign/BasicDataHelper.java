package com.yl.applets.feign;

import com.yl.applets.utils.JsonUtils;
import com.yl.applets.utils.ResultUtil;
import com.yl.applets.vo.BasicDataVO;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2020
 * author: songxg <br>
 * description: BasicDataHelper <br>
 * date: 2021-08-06 16:45 <br>
 */
@Slf4j
@Component
public class BasicDataHelper {

    @Autowired
    private BasicDataFeignClient basicDataFeignClient;
    /**
     * 根据编码查询服务方式
     * @return
     */
    public String getServiceMethodNameByCode(String code){
        Result<BasicDataVO> result = basicDataFeignClient.getServiceMethodByCode(code);
        ResultUtil.checkFeignResult(result);
        log.info("获取服务方式：{}", JsonUtils.toJson(result));
        return result.getData().getName();
    }

    /**
     * 根据编码批量查询订单运单状态
     *
     * @return
     */
    public String getWayBillStatusByCodes(String code){
        List<String> codes = Arrays.asList(code);
        Result<List<BasicDataVO>> result = basicDataFeignClient.getWayBillStatusByCodes(codes);
        ResultUtil.checkFeignResult(result);
        log.info("获取订单运单状态：{}", JsonUtils.toJson(result));
        List<BasicDataVO> data = result.getData();
        if(CollectionUtils.isNotEmpty(data)){
            return data.get(0).getName();
        }
        return null;
    }

    /**
     * 获取物品类型
     * @return
     */
    public List<BasicDataVO> getArticleTypeAll(){
        Result<List<BasicDataVO>> result =  basicDataFeignClient.getArticleTypeAll();
        ResultUtil.checkFeignResult(result);
        log.info("获取商品类型：{}", JsonUtils.toJson(result));
        return result.getData();
    }

    /**
     * 获取结算方式
     * @return
     */
    public List<BasicDataVO> getPaymentMannerAll(){
        Result<List<BasicDataVO>> result =  basicDataFeignClient.getPaymentMannerAll();
        ResultUtil.checkFeignResult(result);
        log.info("获结算方式：{}", JsonUtils.toJson(result));
        return result.getData();
    }




}
