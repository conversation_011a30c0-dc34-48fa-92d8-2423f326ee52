/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: AppBaseDataFeignClient
 * Author:   luhong
 * Date:     2020-10-13 16:00
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign;

import com.yl.applets.feign.fallback.AppBaseDataFeignClientFallBack;
import com.yl.applets.vo.lmdm.AppBaseDataVO;
import com.yl.applets.vo.lmdm.SysNetworkVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 */
@FeignClient(
        name = "yllmdmapi",
        path = "/lmdmapi/network",
//        configuration = {LmdmApiFeignConfiguration.class},
        fallback = AppBaseDataFeignClientFallBack.class
)
public interface LmdmapiDataFeignClient {
    /**
     * 获取传入经纬度最近的五个网点信息
     *
     * @param latitude  纬度
     * @param longitude 经度
     * @return 数据集
     */
    @ApiOperation(value = "获取传入经纬度最近的五个网点信息", notes = "获取传入经纬度最近的五个网点信息")
    @GetMapping("/getNearbySysNetworkVo")
    Result<List<SysNetworkVO>> getNearbySysNetworkVo(@RequestParam(value = "latitude") double latitude,
                                                     @RequestParam(value = "longitude") double longitude);

    /**
     * 根据ids查询网点详情
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据ids查询网点详情", notes = "根据ids查询网点详情")
    @GetMapping(value = "/getDetailByIds")
    Result<List<SysNetworkVO>> getDetailByIds(@RequestParam("ids") List<Integer> ids);

}