/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: MaterialSpecificationFallback
 * Author:   luhong
 * Date:     2020-10-14 13:48
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign.fallback;

import com.yl.applets.dto.MaterialSpecificationQueryDTO;
import com.yl.applets.feign.MaterialSpecificationFeignClient;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.MaterialSpecificationDetailVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
@Slf4j
@Component
public class MaterialSpecificationFallback implements MaterialSpecificationFeignClient {

    @Override
    public Result<List<MaterialSpecificationDetailVO>> list(MaterialSpecificationQueryDTO dto) {
        log.warn("feign调用基础数据根据平台MaterialSpecification列表失败，传参：{}", JsonUtils.toJson(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }
}