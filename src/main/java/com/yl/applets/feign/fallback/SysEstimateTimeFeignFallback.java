package com.yl.applets.feign.fallback;

import com.yl.applets.feign.SysEstimateTimeFeignClient;
import com.yl.applets.vo.spmo.SysEstimateTimeVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: caiyao
 * @create: 2021-11-18 16:00
 */
@Component
@Slf4j
public class SysEstimateTimeFeignFallback implements SysEstimateTimeFeignClient {

    @Override
    public Result<List<SysEstimateTimeVO>> getAging(Integer sendProvinceId, Integer sendCityId, Integer dispatchProvinceId, Integer dispatchCityId) {
        log.error("========================SysEstimateTimeFeignClient 服务调用[getAging]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }
}
