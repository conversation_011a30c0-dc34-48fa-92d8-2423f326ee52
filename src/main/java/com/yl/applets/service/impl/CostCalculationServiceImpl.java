package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.yl.applets.bo.CurrentUser;
import com.yl.applets.dto.*;
import com.yl.applets.dto.lmdm.CustomerDestinationAndCostAndWeightDTO;
import com.yl.applets.dto.lmdm.StandardDestinationAndCostAndWeightDTO;
import com.yl.applets.dto.lmdm.SysSettlementDestinationBaseQueryDTO;
import com.yl.applets.enums.DispatchServiceTypeEnum;
import com.yl.applets.enums.ExpressTypeEnum;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.*;
import com.yl.applets.service.ICostCalculationService;
import com.yl.applets.utils.ResultUtil;
import com.yl.applets.utils.SettlementUtils;
import com.yl.applets.vo.*;
import com.yl.applets.vo.lmdm.DestinationAndCommonCostVO;
import com.yl.applets.vo.lmdm.SysSettlementDestinationVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description: 费用计算业务
 * @Project:
 * @CreateDate: Created in 2019-08-31 13:57 <br>
 * @Author: zhipeng.liu
 */
@Service
@Slf4j
public class CostCalculationServiceImpl extends BaseServiceImpl implements ICostCalculationService {

    @Autowired
    private SpmoBusinessInsuranceFeeFeignClient spmoBusinessInsuranceFeeFeignClient;

    @Autowired
    private SpmBusinessCommonFeignClient spmBusinessCommonFeignClient;

    @Autowired
    private RetailAppDestinationFeignClient retailAppDestinationFeignClient;

    @Autowired
    private CpAreaFeignClient cpAreaFeignClient;

    @Autowired
    private OrikaBeanMapper beanMapper;

    @Autowired
    private SysSettlementDestinationFeignClient sysSettlementDestinationFeignClient;

    @Autowired
    private SpmoCustomerShippingQuoteFeignClient customerShippingQuoteFeignClient;

    @Autowired
    private SpmoStandardShippingQuoteFeignClient spmoStandardShippingQuoteFeignClient;

    @Override
    public BigDecimal computationCost(SpmApiInsuranceTrialDTO spmApiInsuranceTrialDTO) {
        return spmoBusinessInsuranceFeeFeignClient.computationCost(spmApiInsuranceTrialDTO).result();
    }

    @Override
    public SpmComCostVO comCost(SpmCommonComCostDTO dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        //先根据始发地省市计算始发地id，再根据目的地省市计算目的地id
        SysSettlementDestinationVO startid=new SysSettlementDestinationVO();
        SysSettlementDestinationVO endid=new SysSettlementDestinationVO();

//            老接口
//            SysSettlementDestinationBaseQueryDTO start = new SysSettlementDestinationBaseQueryDTO();
//            start.setProviderId(dto.getStartProviderId());
//            start.setCityId(dto.getStartCityId());
//            start.setAreaId(null);
//            log.info("通过省市区获取结算始发地入参：{}，requestId={}", JSON.toJSONString(start),requestId);
//            startid = sysSettlementDestinationFeignClient.getSettlementDestinationByProvinceCityAreaId(start).getData();
//            log.info("通过省市区获取结算始发地出参：{},requestId={}",JSON.toJSONString(startid),requestId);
//            if(startid == null){
//                throw new BusinessException(ServiceErrCodeEnum.NOT_FOUNT_COST);
//            }
//            SysSettlementDestinationBaseQueryDTO end = new SysSettlementDestinationBaseQueryDTO();
//            end.setProviderId(dto.getEndProviderId());
//            end.setCityId(dto.getEndCityId());
//            end.setAreaId(null);
//            log.info("通过省市区获取结算始发地入参：{}，requestId={}", JSON.toJSONString(end),requestId);
//            endid = sysSettlementDestinationFeignClient.getSettlementDestinationByProvinceCityAreaId(end).getData();
//            log.info("通过省市区获取结算始发地出参：{},requestId={}",JSON.toJSONString(endid),requestId);
//            if(endid == null){
//                throw new BusinessException(ServiceErrCodeEnum.NOT_FOUNT_COST);
//            }

        //根据城市，查省区
        log.info("通过始发市id，查询省id，入参==>{}，requestId={}",dto.getStartCityId(),requestId);
        Result<List<CpAreaDetailVO>> startVo = cpAreaFeignClient.getUpLevelById(dto.getStartCityId(), null);
        log.info("通过始发市id，查询省id，返回==>{}，requestId={}",JSON.toJSONString(startVo),requestId);
        Integer startProviderId=null;
        if(startVo==null||startVo.getData()==null||startVo.getData().isEmpty()){
            log.info("通过始发市id，查询省id，未查到");
            throw new BusinessException(ServiceErrCodeEnum.NOT_FOUNT_COST);
        }else {
            for (CpAreaDetailVO datum : startVo.getData()) {
                startProviderId =  datum.getProviderId();
                log.info("通过始发市id，返回省id===>{}",startVo);
                break;
            }
            if(startProviderId==null){
                log.info("始发省id，未查到,为空.requestId={}",requestId);
                throw new BusinessException(ServiceErrCodeEnum.NOT_FOUNT_COST);
            }
        }

        log.info("通过目的市id，查询省id，入参==>{}，requestId={}",dto.getEndCityId(),requestId);
        Result<List<CpAreaDetailVO>> endVo = cpAreaFeignClient.getUpLevelById(dto.getEndCityId(), null);
        log.info("通过目的市id，查询省id，返回==>{}，requestId={}",JSON.toJSONString(endVo),requestId);
        Integer endProviderId=null;
        if(endVo==null||endVo.getData()==null||endVo.getData().isEmpty()){
            log.info("通过目的市id，查询省id，未查到");
            throw new BusinessException(ServiceErrCodeEnum.NOT_FOUNT_COST);
        }else {
            for (CpAreaDetailVO datum : endVo.getData()) {
                endProviderId =  datum.getProviderId();
                log.info("通过目的市id，返回省id===>{}",endProviderId);
                break;
            }
            if(endProviderId==null){
                log.info("始发省id，未查到,为空.requestId={}",requestId);
                throw new BusinessException(ServiceErrCodeEnum.NOT_FOUNT_COST);
            }
        }
        //新接口
        log.info("通过省市区获取结算始发地入参：{}，{}，requestId={}", startProviderId,dto.getStartCityId(),requestId);
        startid = retailAppDestinationFeignClient.getSettlementDestinationByProvinceCity(startProviderId,dto.getStartCityId()).result();
        log.info("通过省市区获取结算始发地出参：{},requestId={}",JSON.toJSONString(startid),requestId);
        if(startid == null){
            throw new BusinessException(ServiceErrCodeEnum.NOT_FOUNT_COST);
        }

        log.info("通过省市区获取结算目的地入参：{}，{},requestId={}", endProviderId,dto.getEndCityId(),requestId);
        endid = retailAppDestinationFeignClient.getSettlementDestinationByProvinceCity(endProviderId,dto.getEndCityId()).result();
        log.info("通过省市区获取结算目的地出参：{},requestId={}",JSON.toJSONString(endid),requestId);
        if(endid == null){
            throw new BusinessException(ServiceErrCodeEnum.NOT_FOUNT_COST);
        }

        //2022-12-29 去掉写死FT,改用前端传来参数
//        dto.setProductTypeCode(ExpressTypeEnum.ORDINARY_EXPRESS.getCode());
        dto.setServiceMethodCode(DispatchServiceTypeEnum.DISPATCH_DOOR.getCode());

        CurrentUser user = getUser();
        if(user.isVipFlag()){
            AppRetailVipCustomerVO appRetailVipCustomerVO=getVIPUser();
            dto.setComCostType(1);//客户算费
            dto.setCustomerId(appRetailVipCustomerVO.getCustomerId());
            dto.setNetworkId(appRetailVipCustomerVO.getNetworkId());
        }else {
            dto.setComCostType(2);//标准算费
        }
        dto.setDateTime(LocalDateTime.now());
        dto.setGoodsTypeCode(dto.getGoodsTypeCode());
        dto.setValue(dto.getValue());
        dto.setStartPointId(startid.getId());
        dto.setTerminalPointId(endid.getId());
        if(user.isVipFlag()){
            log.info("vip定制费用...");
        } else{
            log.info("散客标准费用...");
        }
        log.info("调用运费计算参数：{},requestId={}",JSON.toJSONString(dto),requestId);
        SpmCommonComCostVO spmCommonComCostVO= spmBusinessCommonFeignClient.computationCost(dto).result();
        log.info("调用运费计算响应参数：{},requestId={}",JSON.toJSONString(spmCommonComCostVO),requestId);
        if(spmCommonComCostVO != null){
            if(user.isVipFlag()){
                return spmCommonComCostVO.getCustomerCost();
            } else{
                return spmCommonComCostVO.getStandardCost();
            }
        }

        return new SpmComCostVO();
    }


    /**
     * 目的地+客户运费+结算重量
     * @return
     */
    @Override
    public DestinationAndCommonCostVO destinationAndCostAndWeight(CustomerDestinationAndCostAndWeightDTO dto) {
        dto.setSmMode(SettlementUtils.getSettlementModeByCode(dto.getSettlementCode()));
        // 需求调整，巴枪前端算费全部按标准报价显示，提交运单后后台再按客户报价重算运费
        StandardDestinationAndCostAndWeightDTO standardDto = beanMapper.map(dto, StandardDestinationAndCostAndWeightDTO.class);
        standardDto.setStartPointId(dto.getStartAddressId());
        standardDto.setTerminalPointId(dto.getEndAddressId());
        standardDto.setValue(dto.getNumber());
        standardDto.setDateTime(dto.getCurrentTime());

        //返回VO
        DestinationAndCommonCostVO destinationAndCommonCostVO = new DestinationAndCommonCostVO();

        SysSettlementDestinationBaseQueryDTO sysSettlementDestinationBaseQueryDTO = new SysSettlementDestinationBaseQueryDTO();
        sysSettlementDestinationBaseQueryDTO.setProviderId(dto.getSenderProvinceId());
        sysSettlementDestinationBaseQueryDTO.setCityId(dto.getSenderCityId());
        //sysSettlementDestinationBaseQueryDTO.setAreaId(dto.getSenderAreaId());
        Result<SysSettlementDestinationVO> SenderResult = sysSettlementDestinationFeignClient.getSettlementDestinationByProvinceCityAreaId(sysSettlementDestinationBaseQueryDTO);
        ResultUtil.checkFeignResult(SenderResult);
        SysSettlementDestinationVO senderResultData = SenderResult.getData();
        if(!Objects.isNull(senderResultData)){
            dto.setStartAddressId(senderResultData.getId());
            destinationAndCommonCostVO.setOriginId(senderResultData.getId());
            destinationAndCommonCostVO.setOriginCode(senderResultData.getCode());
            destinationAndCommonCostVO.setOriginName(senderResultData.getName());
        }else{
            destinationAndCommonCostVO.setCost(BigDecimal.ZERO);
            destinationAndCommonCostVO.setSetWeight(BigDecimal.ZERO);
            return destinationAndCommonCostVO;
        }

        sysSettlementDestinationBaseQueryDTO.setProviderId(dto.getReceiverProvinceId());
        sysSettlementDestinationBaseQueryDTO.setCityId(dto.getReceiverCityId());
        //sysSettlementDestinationBaseQueryDTO.setAreaId(dto.getReceiverAreaId());
        Result<SysSettlementDestinationVO> receiverResult = sysSettlementDestinationFeignClient.getSettlementDestinationByProvinceCityAreaId(sysSettlementDestinationBaseQueryDTO);
        ResultUtil.checkFeignResult(receiverResult);
        SysSettlementDestinationVO receiverResultData = receiverResult.getData();
        if(!Objects.isNull(receiverResultData)){
            dto.setEndAddressId(receiverResultData.getId());
            destinationAndCommonCostVO.setDestinationId(receiverResultData.getId());
            destinationAndCommonCostVO.setDestinationCode(receiverResultData.getCode());
            destinationAndCommonCostVO.setDestinationName(receiverResultData.getName());
        }else{
            destinationAndCommonCostVO.setCost(BigDecimal.ZERO);
            destinationAndCommonCostVO.setSetWeight(BigDecimal.ZERO);
            return destinationAndCommonCostVO;
        }

        dto.setSmMode(SettlementUtils.getSettlementModeByCode(dto.getSettlementCode()));
        SpmApiCustomerShippingQuoteTryCalcDTO spmApiCustomerShippingQuoteTryCalcDTO = beanMapper.map(dto, SpmApiCustomerShippingQuoteTryCalcDTO.class);
        log.info("(目的地+客户运费+结算重量)运费请求参数：【{}】", JSON.toJSONString(spmApiCustomerShippingQuoteTryCalcDTO));
        Result<SpmCommonCostVO> spmCommonCostVOResult = customerShippingQuoteFeignClient.comCostAndWeight(spmApiCustomerShippingQuoteTryCalcDTO);
        ResultUtil.checkFeignResult(receiverResult);
        log.info("(目的地+客户运费+结算重量)运费返回参数：【{}】", JSON.toJSONString(spmCommonCostVOResult));
        SpmCommonCostVO result = spmCommonCostVOResult.result();
        destinationAndCommonCostVO.setCost(result.getCost());
        destinationAndCommonCostVO.setSetWeight(result.getSetWeight());
        destinationAndCommonCostVO.setCouponAmount(result.getCouponAmount());
        destinationAndCommonCostVO.setStandardCost(result.getStandardCost());
        return destinationAndCommonCostVO;
    }


    /**
     * 目的地+标准运费+结算重量
     * @return
     */
    @Override
    public DestinationAndCommonCostVO destinationAndCostAndWeight(StandardDestinationAndCostAndWeightDTO dto) {
        //返回VO
        DestinationAndCommonCostVO destinationAndCommonCostVO = new DestinationAndCommonCostVO();

        SysSettlementDestinationBaseQueryDTO sysSettlementDestinationBaseQueryDTO = new SysSettlementDestinationBaseQueryDTO();
        sysSettlementDestinationBaseQueryDTO.setProviderId(dto.getSenderProvinceId());
        sysSettlementDestinationBaseQueryDTO.setCityId(dto.getSenderCityId());
        //获取方式变化,去掉区域
        //sysSettlementDestinationBaseQueryDTO.setAreaId(dto.getSenderAreaId());
        Result<SysSettlementDestinationVO> SenderResult = sysSettlementDestinationFeignClient.getSettlementDestinationByProvinceCityAreaId(sysSettlementDestinationBaseQueryDTO);
        ResultUtil.checkFeignResult(SenderResult);
        SysSettlementDestinationVO senderResultData = SenderResult.getData();
        if(!Objects.isNull(senderResultData)){
            dto.setStartPointId(senderResultData.getId());
            destinationAndCommonCostVO.setOriginId(senderResultData.getId());
            destinationAndCommonCostVO.setOriginCode(senderResultData.getCode());
            destinationAndCommonCostVO.setOriginName(senderResultData.getName());
        }else{
            destinationAndCommonCostVO.setCost(BigDecimal.ZERO);
            destinationAndCommonCostVO.setSetWeight(BigDecimal.ZERO);
            return destinationAndCommonCostVO;
        }
        sysSettlementDestinationBaseQueryDTO.setProviderId(dto.getReceiverProvinceId());
        sysSettlementDestinationBaseQueryDTO.setCityId(dto.getReceiverCityId());
        //sysSettlementDestinationBaseQueryDTO.setAreaId(dto.getReceiverAreaId());
        Result<SysSettlementDestinationVO> receiverResult = sysSettlementDestinationFeignClient.getSettlementDestinationByProvinceCityAreaId(sysSettlementDestinationBaseQueryDTO);
        ResultUtil.checkFeignResult(receiverResult);
        SysSettlementDestinationVO receiverResultData = receiverResult.getData();
        if(!Objects.isNull(receiverResultData)){
            dto.setTerminalPointId(receiverResultData.getId());
            destinationAndCommonCostVO.setDestinationId(receiverResultData.getId());
            destinationAndCommonCostVO.setDestinationCode(receiverResultData.getCode());
            destinationAndCommonCostVO.setDestinationName(receiverResultData.getName());
        }else{
            destinationAndCommonCostVO.setCost(BigDecimal.ZERO);
            destinationAndCommonCostVO.setSetWeight(BigDecimal.ZERO);
            return destinationAndCommonCostVO;
        }

        dto.setSmMode(SettlementUtils.getSettlementModeByCode(dto.getSettlementCode()));
        SpmApiTrialDTO spmApiTrialDTO = beanMapper.map(dto, SpmApiTrialDTO.class);
//        CurrentUser user = getUser();
//        if(user != null){
//            spmApiTrialDTO.setNetworkId(user.getCustomerCode());
//        }
        log.info("(目的地+标准运费+结算重量)运费请求参数：【{}】", JSON.toJSONString(spmApiTrialDTO));
        Result<SpmCommonCostVO> spmCommonCostVOResult = spmoStandardShippingQuoteFeignClient.comCostAndWeight(spmApiTrialDTO);
        ResultUtil.checkFeignResult(receiverResult);
        log.info("(目的地+标准运费+结算重量)运费返回参数：【{}】", JSON.toJSONString(spmCommonCostVOResult));
        SpmCommonCostVO result = spmCommonCostVOResult.result();
        destinationAndCommonCostVO.setCost(result.getCost());
        destinationAndCommonCostVO.setSetWeight(result.getSetWeight());
        destinationAndCommonCostVO.setCodScale(result.getCodScale());
        destinationAndCommonCostVO.setCouponAmount(result.getCouponAmount());
        destinationAndCommonCostVO.setStandardCost(result.getStandardCost());
        return destinationAndCommonCostVO;
    }
}
