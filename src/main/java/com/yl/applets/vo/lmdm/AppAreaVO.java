/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: AppAreaVO
 * Author:   luhong
 * Date:     2020-10-13 15:05
 * Description: App行政区域
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.vo.lmdm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 〈一句话功能简述〉<br> 
 * 〈App行政区域〉
 *
 * <AUTHOR>
 * @create 2020-10-13
 * @since 1.0.0
 */
@ApiModel(
        value = "AppAreaVO",
        description = "App行政区域"
)
@Data
public class AppAreaVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer id;
    @ApiModelProperty(
            name = "parentId",
            value = "父级ID"
    )
    private Integer parentId;
    @ApiModelProperty(
            name = "type",
            value = "区域类型"
    )
    private Integer type;
    @ApiModelProperty(
            name = "nativeName",
            value = "母语名称"
    )
    private String nativeName;
}