package com.yl.classroom.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程对象 ${tableComment}
 *
 * <AUTHOR>
 * @date 2021-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ExamCourseManageDTO", description = "课程管理")
public class ExamCourseManageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("课程id")
    private Long id;
    /**
     * 课程名称
     */
    @NotBlank(message = "课程名称不能为空")
    @Size(min = 1, max = 50, message = "课程名称只能在1-50之间")
    @ApiModelProperty("课程名称")
    private String courseName;
    /**
     * 课程图片地址
     */
    @NotBlank(message = "课程图片地址不能为空")
    @ApiModelProperty("课程图片地址")
    private String picturePath;;
    /**
     * 课程章数
     */
    @ApiModelProperty("课程章数")
    private Integer chapterCount;
    /**
     * 课程小节数
     */
    @ApiModelProperty("课程小节数")
    private Integer sectionCount;
    /**
     * 课程老师
     */
    @ApiModelProperty("课程老师")
    private String teacher;
    /**
     * 课程开始时间
     */
    @ApiModelProperty("课程开始时间")
    private LocalDateTime startTime;
    /**
     * 课程结束时间
     */
    @ApiModelProperty("课程结束时间")
    private LocalDateTime endTime;
    /**
     * 课程类别
     */
    @ApiModelProperty("课程类别")
    private Integer courseType;
    /**
     * 课程状态(1已发布 2待发布)
     */
    @ApiModelProperty("课程状态(1已发布 2待发布)")
    private Integer status;
    /**
     * 是否必学(1必学 2不必)
     */
    @NotBlank(message = "是否必学不能为空")
    @ApiModelProperty("是否必学(1必学 2不必)")
    private String must;
    /**
     * 考试是否已通过(1通过 2未通过)
     */
    @ApiModelProperty("考试是否已通过(1通过 2未通过)")
    private Integer passExam;
    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    private Long createId;
    /**
     * 更新人id
     */
    @ApiModelProperty("更新人id")
    private Long updateId;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
    /**
     * 课程简介
     */
    @NotBlank(message = "课程简介不能为空")
    @Size(min = 1, max = 200, message = "课程简介只能在1-200之间")
    @ApiModelProperty("课程简介")
    private String introduction;
    /**
     * 试卷数
     */
    @ApiModelProperty("试卷数")
    private String paperCount;


    @ApiModelProperty(name = "examCourseChapterList", value = "课程章节")
    private List<@Valid ExamCourseChapterDTO> examCourseChapterList;

    @ApiModelProperty(name = "examCoursePaperList", value = "课程试卷")
    private List<@Valid ExamCoursePaperDTO> examCoursePaperList;
}
