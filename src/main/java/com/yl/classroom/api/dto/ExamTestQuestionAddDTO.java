package com.yl.classroom.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-09-14 15:34
 */
@Data
@ApiModel("ExamTestQuestionAddDTO")
public class ExamTestQuestionAddDTO  implements Serializable {

    /**
     *
     */
    @Valid
    @ApiModelProperty("试题")
    @NotEmpty
    private List<ExamPaperQuestionDTO> questionList;
    /**
     *
     */
    @Valid
    @ApiModelProperty("试卷")
    @NotNull
    private ExamPaperDTO paper;
}
