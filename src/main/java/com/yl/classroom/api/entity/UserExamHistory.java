package com.yl.classroom.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 个人考试历史日志对象 tab_user_exam_history
 *
 * <AUTHOR>
 * @date 2021-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tab_user_exam_history")
public class UserExamHistory implements Serializable{
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty("id")
    private Long id;
    /** 用户id */
    @ApiModelProperty("用户id")
    private Long userId;
    /** 课程id */
    @ApiModelProperty("课程id")
    private Long courseId;
    /** 课程名称 */
    @ApiModelProperty("课程名称")
    private String courseName;
    /** 试卷id */
    @ApiModelProperty("试卷id")
    private Long paperId;
    /** 试题数 */
    @ApiModelProperty("试题数")
    private Integer questionCount;
    /** 试卷名称 */
    @ApiModelProperty("试卷名称")
    private String paperName;
    /** 试卷分数 */
    @ApiModelProperty("试卷分数")
    private Integer score;
    /** 合格分数 */
    @ApiModelProperty("合格分数")
    private Integer passScore;
    /** 考试开始时间 */
    @ApiModelProperty("考试开始时间")
    private LocalDateTime startTime;
    /** 考试结束时间   */
    @ApiModelProperty("考试结束时间  ")
    private LocalDateTime endTime;
    /** 当前考试次数 */
    @ApiModelProperty("当前考试次数")
    private Integer examFrequency;
    /** 考试批次 */
    @ApiModelProperty("考试批次")
    private Long examBatch;
    /** 考试分数 */
    @ApiModelProperty("考试分数")
    private Integer grade;
    /** 考试人员ip */
    @ApiModelProperty("考试人员ip")
    private String examIp;
    /** 考试状态  1.通过 2未通过 3 代考 */
    @ApiModelProperty("考试状态  1.通过 2未通过 3 代考")
    private Integer examState;
    /** 用户姓名 */
    @ApiModelProperty("用户姓名")
    private String userName;
    /** 网点id */
    @ApiModelProperty("网点id")
    private Long networkId;
    /** 网点姓名 */
    @ApiModelProperty("网点姓名")
    private String networkName;
    /** 员工编号 */
    @ApiModelProperty("员工编号")
    private String staffNo;

}
