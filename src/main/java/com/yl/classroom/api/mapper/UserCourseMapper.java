package com.yl.classroom.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yl.classroom.api.entity.ExamPaperQuestion;
import com.yl.classroom.api.entity.UserCourse;
import com.yl.classroom.api.vo.UserCourseVO;
import com.yl.classroom.api.dto.UserCourseDTO;
import org.apache.ibatis.annotations.Param;

/**
 * 个人课程学习记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-08-19
 */
public interface UserCourseMapper  extends BaseMapper<UserCourse>
{
    /**
     * 查询个人课程学习记录
     * 
     * @param id 个人课程学习记录ID
     * @return 个人课程学习记录
     */
    public UserCourseVO selectUserCourseById(Long id);

    public UserCourseVO selectUserCourseByCoureseIdUserId(@Param("courseId") Long courseId,@Param("userId") Long userId);

    /**
     * 课程学习进度
     *
     * @param userId 用户id
     * @return 个人课程学习记录
     */
    public List<UserCourseVO> selectCourseStudyByUserId(@Param("userId") Long userId,@Param("courseIds") List<Long> courseIds);

    /**
     * 获取指定课程的学习记录
     * @param userId
     * @param courseId
     * @return
     */
    public UserCourseVO getCourseStudyByCourseId(@Param("courseId") Long courseId,@Param("userId") Long userId);

    /**
     * 查询个人课程学习记录列表
     * 
     * @param userCourse 个人课程学习记录
     * @return 个人课程学习记录集合
     */
    public List<UserCourseVO> selectUserCourseList(UserCourseDTO userCourse);

    /**
     * 新增个人课程学习记录
     * 
     * @param userCourse 个人课程学习记录
     * @return 结果
     */
    public int insertUserCourse(UserCourseDTO userCourse);

    /**
     * 修改个人课程学习记录
     * 
     * @param userCourse 个人课程学习记录
     * @return 结果
     */
    public int updateUserCourse(UserCourseDTO userCourse);

    /**
     * 修改个人课程学习记录
     *
     * @return 结果
     */
    public int updateLearnedSectionCount(@Param("courseId") Long courseId,@Param("userId") Long userId);

    /**
     * 删除个人课程学习记录
     *
     * @param userCourse 个人课程学习记录
     * @return 结果
     */
    public int deleteByUserCourse(UserCourseDTO userCourse);


    /**
     *
     * 
     * @param courseId 课程id
     * @return 结果
     */
    public int deleteUserCourse(Long courseId);
    public int deleteUserId(Long userId);
    /**
     * 批量删除个人课程学习记录
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteUserCourseByIds(Long[] ids);

    /**
     * 返回所有合格的用户id
     * @param courseIdList  所有已发布的必学的课程id
     * @param courseId     被下架的必学课程id
     * @param coursePassCount  必学的课程id的课程数
     * @return
     */
    public List<Long> getPassUserId(List<Long> courseIdList,Long courseId,Integer coursePassCount);
}
