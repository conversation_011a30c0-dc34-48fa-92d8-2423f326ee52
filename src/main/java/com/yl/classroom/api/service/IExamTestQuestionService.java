package com.yl.classroom.api.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.classroom.api.dto.ExamSubjectListDTO;
import com.yl.classroom.api.dto.ExamTestQuestionDTO;
import com.yl.classroom.api.vo.ExamTestQuestionVO;

/**
 * 试题Service接口
 * 
 * <AUTHOR>
 * @date 2021-08-18
 */
public interface IExamTestQuestionService
{
    /**
     * 查询试题
     * 
     * @param id 试题ID
     * @return 试题
     */
    public ExamTestQuestionVO selectExamTestQuestionById(Long id);
    /**
     * 查询试题
     *
     * @param ids 试题ID
     * @return 试题
     */
    public List<ExamTestQuestionVO> selectExamTestQuestionByIds(List<Long> ids);
    /**
     * 查询试题列表
     * 
     * @param examTestQuestion 试题
     * @return 试题集合
     */
    public Page<ExamTestQuestionVO> selectExamTestQuestionList(Page<ExamTestQuestionVO> page, ExamTestQuestionDTO examTestQuestion);

    /**
     * 新增试题
     * 
     * @param examTestQuestion 试题
     * @return 结果
     */
    public int insertExamTestQuestion(ExamTestQuestionDTO examTestQuestion);

    /**
     * 修改试题
     * 
     * @param dto 试题
     * @return 结果
     */
    public int updateQuestionByNumbers(ExamTestQuestionDTO dto);

    /**
     * 批量删除试题
     *
     * @param numbmers 试题编码
     * @return 结果
     */
    public int deleteQuestionByNumbers(List<Long> numbmers);

    /**
     * 计算试题分
     * @param ids
     * @return
     */
    Integer countScore(List<Long> ids);

    int insertBatch(List<ExamTestQuestionDTO> list);

    ExamTestQuestionDTO getBean(ExamSubjectListDTO question);
}
