package com.yl.classroom.api.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import com.yl.classroom.api.mapper.UserExamRecordMapper;
import com.yl.classroom.api.entity.UserExamRecord;
import com.yl.classroom.api.dto.UserExamRecordDTO;
import com.yl.classroom.api.vo.UserExamRecordVO;
import com.yl.classroom.api.service.IUserExamRecordService;

/**
 * 个人考试记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-08-18
 */
@Service
public class UserExamRecordServiceImpl implements IUserExamRecordService
{
    @Autowired
    private UserExamRecordMapper userExamRecordMapper;

    /**
     * 查询个人考试记录
     * 
     * @param id 个人考试记录ID
     * @return 个人考试记录
     */
    @Override
    public UserExamRecordVO selectUserExamRecordById(Long id)
    {
        return userExamRecordMapper.selectUserExamRecordById(id);
    }

    /**
     * 查询个人考试记录列表
     * 
     * @param userExamRecord 个人考试记录
     * @return 个人考试记录
     */
    @Override
    public Page<UserExamRecordVO> selectUserExamRecordList(Page<UserExamRecordVO> page, UserExamRecordDTO userExamRecord)
    {
        return userExamRecordMapper.selectUserExamRecordList(page,userExamRecord);
    }

    /**
     * 新增个人考试记录
     * 
     * @param userExamRecord 个人考试记录
     * @return 结果
     */
    @Override
    public int insertUserExamRecord(UserExamRecordDTO userExamRecord)
    {
        return userExamRecordMapper.insertUserExamRecord(userExamRecord);
    }
}
