package com.yl.classroom.api.vo;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 个人章节学习记录对象 个人章节学习记录
 * 
 * <AUTHOR>
 * @date 2021-09-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "UserChapterStudyVO", description = "个人章节学习记录")
public class UserChapterStudyVO implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty("id")
    private Long id;
    /** 章节编号(唯一) */
    @ApiModelProperty("章节编号(唯一)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long chapterNumber;
    /** 所属课程id */
    @ApiModelProperty("所属课程id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long courseId;
    /** 课程章节名称 */
    @ApiModelProperty("课程章节名称")
    private String chapterName;
    /** 章节学习开始时间 */
    @ApiModelProperty("章节学习开始时间")
    private LocalDateTime studyStartTime;
    /** 视频内容观看进度，当前播放位置  秒记 */
    @ApiModelProperty("视频内容观看进度，当前播放位置  秒记")
    private Integer videoViewProgress;
    /** 章节学习状态 1.已完成，2 学习中 3待学习 */
    @ApiModelProperty("章节学习状态 1.已完成，2 学习中 3待学习")
    private Integer status;
    /** 章节学习总时长(分钟) */
    @ApiModelProperty("章节学习总时长(分钟)")
    private Integer studyTotalTime;
    /** 章节学习最新时间 */
    @ApiModelProperty("章节学习最新时间")
    private LocalDateTime studyNewTime;
    /** 章节学习最新结束时间 */
    @ApiModelProperty("章节学习最新结束时间")
    private LocalDateTime studyEndTime;
    /** 课程学习当天时长 */
    @ApiModelProperty("课程学习当天时长")
    private Integer studyTodayTime;
    /** 用户id */
    @ApiModelProperty("用户id")
    private Long userId;

    /** 章节内容地址 */
    @ApiModelProperty("章节内容地址")
    private String contentPath;
    /**
     * 所属章节编号
     */
    @ApiModelProperty("所属章节编号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentNumber;

    /**
     * 章节类型(1目录 2小节)
     */
    @ApiModelProperty("章节类型(1目录 2小节)")
    private Integer chapterType;
    /**
     * 章节内容类型 1.视频 2文档
     */
    @ApiModelProperty("章节内容类型 1.视频 2文档")
    private Integer contentType;
    /**
     * 课程老师
     */
    @ApiModelProperty("课程老师")
    private String teacher;
    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String phone;
    /**
     * 排序，章以百位开始，小节个位开始
     */
    @ApiModelProperty("排序，章以百位开始，小节个位开始")
    private Integer sort;
    /**
     * 章节小节序号
     */
    @ApiModelProperty("章节小节序号")
    private String serialNumber;
}
