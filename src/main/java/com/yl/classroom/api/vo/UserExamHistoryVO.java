package com.yl.classroom.api.vo;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 个人考试历史日志对象 个人考试历史日志
 * 
 * <AUTHOR>
 * @date 2021-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "UserExamHistoryVO", description = "个人考试历史日志")
public class UserExamHistoryVO implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty("id")
    private Long id;
    /** 用户id */
    @ApiModelProperty("用户id")
    private Long userId;
    /** 课程id */
    @ApiModelProperty("课程id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long courseId;
    /** 课程名称 */
    @ApiModelProperty("课程名称")
    private String courseName;
    /** 试卷id */
    @ApiModelProperty("试卷id")
    private Long paperId;
    /** 试题数 */
    @ApiModelProperty("试题数")
    private Integer questionCount;
    /** 试卷名称 */
    @ApiModelProperty("试卷名称")
    private String paperName;
    /** 试卷分数 */
    @ApiModelProperty("试卷分数")
    private Integer score;
    /** 合格分数 */
    @ApiModelProperty("合格分数")
    private Integer passScore;
    /** 考试时长(分钟) */
    @ApiModelProperty("考试时长(分钟)")
    private Integer examTimeLength;
    /** 考试允许考试次数 */
    @ApiModelProperty("考试允许考试次数")
    private Integer examCount;
    /** 考试开始时间 */
    @ApiModelProperty("考试开始时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;
    /** 考试结束时间   */
    @ApiModelProperty("考试结束时间  ")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
    /** 当前考试次数 */
    @ApiModelProperty("当前考试次数")
    private Integer examFrequency;
    /** 考试批次 */
    @ApiModelProperty("考试批次")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long examBatch;
    /** 考试分数 */
    @ApiModelProperty("考试分数")
    private Integer grade;
    /** 考试人员ip */
    @ApiModelProperty("考试人员ip")
    private String examIp;
    /** 考试状态  1.通过 2未通过 3 代考 */
    @ApiModelProperty("考试状态  1.通过 2未通过 3 代考")
    private Integer examState;
    /** 用户姓名 */
    @ApiModelProperty("用户姓名")
    private String userName;
    /** 网点id */
    @ApiModelProperty("网点id")
    private Long networkId;
    /** 网点姓名 */
    @ApiModelProperty("网点姓名")
    private String networkName;
    /** 员工编号 */
    @ApiModelProperty("员工编号")
    private String staffNo;

}
