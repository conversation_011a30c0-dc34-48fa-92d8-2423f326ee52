package com.yl.classroom.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-08-20 15:34
 */
@Data
@ApiModel(value = "UserExamTestQuestionVO", description = "试题")
public class UserExamTestQuestionVO   implements Serializable {
    /** id */
    @ApiModelProperty("id")
    private Long id;
    /** 试题id */
    @ApiModelProperty("试题id")
    private Long questionId;
    /** 试题名称 */
    @ApiModelProperty("试题名称")
    private String questionName;
    /** 试题答案A */
    @ApiModelProperty("试题答案A")
    private String answerA;
    /** 试题答案B */
    @ApiModelProperty("试题答案B")
    private String answerB;
    /** 试题答案C */
    @ApiModelProperty("试题答案C")
    private String answerC;
    /** 试题答案D */
    @ApiModelProperty("试题答案D")
    private String answerD;
    /** 试题答案E */
    @ApiModelProperty("试题答案E")
    private String answerE;
    /** 试题答案F */
    @ApiModelProperty("试题答案F")
    private String answerF;
    /** 试题类型（1 选择题，2多选择题  3判断题） */
    @ApiModelProperty("试题类型（1 选择题，2多选择题  3判断题）")
    private Integer questionType;
    /** 答题分数 */
    @ApiModelProperty("答题分数")
    private Integer score;
    /** 试题正确答案 */
    @ApiModelProperty("试题正确答案")
    private String rightAnswers;
    @ApiModelProperty("排序")
    private Integer sort;
}
