//package com.yl.common.base.annotation;
//
//
//
//
//import com.yl.common.base.enums.CaseModeEnum;
//import com.yl.common.base.validator.UserCheckValidator;
//
//import javax.validation.Constraint;
//import javax.validation.Payload;
//import java.lang.annotation.*;
//
//import static java.lang.annotation.ElementType.*;
//import static java.lang.annotation.RetentionPolicy.RUNTIME;
//
//// 指定真正实现校验规则的类
//@Constraint(validatedBy = UserCheckValidator.class)
//@Target( {  ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE })
//@Retention(RetentionPolicy.RUNTIME)
//@Documented
//public @interface UserCheck {
//    String message() default "";
//
//    Class<?>[] groups() default { };
//
//    Class<? extends Payload>[] payload() default { };
//
//    CaseModeEnum value();
//
//    @Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER })
//    @Retention(RUNTIME)
//    @Documented
//    @interface List {
//        UserCheck[] value();
//    }
//
//}
