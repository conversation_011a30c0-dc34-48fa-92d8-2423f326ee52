package com.yl.common.base.config;

import com.yl.order.api.constant.PageCons;
import com.yl.order.api.enums.HeaderParamEnum;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 * 此拦截器会转发header中指定信息
 * <AUTHOR>
 * @since Created in 2019-08-08 17:44
 */
@Slf4j
public class RequestHeaderInterceptorConfiguration implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if(attributes != null){
            HttpServletRequest request = attributes.getRequest();
            String values = request.getHeader(HeaderParamEnum.OAUTH_AUTHTOKEN.getName());
            template.header(HeaderParamEnum.OAUTH_AUTHTOKEN.getName(), values);
            if(request.getParameter(PageCons.PAGE_CURRENT) != null) {
                template.query(PageCons.PAGE_CURRENT, request.getParameter(PageCons.PAGE_CURRENT));
            }
            if(request.getParameter(PageCons.PAGE_SIZE) != null) {
                template.query(PageCons.PAGE_SIZE, request.getParameter(PageCons.PAGE_SIZE));
            }
            log.debug("feign interceptor header:{}",template);
        }
    }
}