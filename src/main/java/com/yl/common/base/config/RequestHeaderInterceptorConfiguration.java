package com.yl.common.base.config;

import lombok.extern.slf4j.Slf4j;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 * 此拦截器会转发header中指定信息
 *
 * <AUTHOR>
 * @since Created in 2019-08-08 17:44
 */
@Slf4j
public class RequestHeaderInterceptorConfiguration {

    /*public static String getRemoteAddr() {
        try {
            String remoteAddr = getRequest().getHeader("X-Real-IP");
            if (StringUtils.isNotBlank(remoteAddr)) {
                remoteAddr = getRequest().getHeader("X-Forwarded-For");
            } else if (StringUtils.isNotBlank(remoteAddr)) {
                remoteAddr = getRequest().getHeader("Proxy-Client-IP");
            } else if (StringUtils.isNotBlank(remoteAddr)) {
                remoteAddr = getRequest().getHeader("WL-Proxy-Client-IP");
            }

            return remoteAddr != null ? remoteAddr : getRequest().getRemoteAddr();
        } catch (Exception var1) {
            return "";
        }
    }

    public static HttpServletRequest getRequest() {
        try {
            return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        } catch (Exception var1) {
            return null;
        }
    }

    @Override
    public void apply(RequestTemplate template) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            String values = request.getHeader(HeaderParamEnum.OAUTH_AUTHTOKEN.getName());
            template.header(HeaderParamEnum.OAUTH_AUTHTOKEN.getName(), values);

            String remoteAddr = getRemoteAddr();
            log.info("远程地址:{}", remoteAddr);
            if (StringUtils.isNotEmpty(remoteAddr)) {
                template.query("request_ip", new String[]{remoteAddr});
            }

            if (request.getParameter(PageCons.PAGE_CURRENT) != null) {
                if (CollectionUtils.isEmpty(template.queries().get(PageCons.PAGE_CURRENT))) {
                    template.query(PageCons.PAGE_CURRENT, request.getParameter(PageCons.PAGE_CURRENT));
                }
            }
            if (request.getParameter(PageCons.PAGE_SIZE) != null) {
                if (CollectionUtils.isEmpty(template.queries().get(PageCons.PAGE_SIZE))) {
                    template.query(PageCons.PAGE_SIZE, request.getParameter(PageCons.PAGE_SIZE));
                }
            }
            log.debug("feign interceptor header:{}", template);
        }
    }*/
}