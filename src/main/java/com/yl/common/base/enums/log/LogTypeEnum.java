package com.yl.common.base.enums.log;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * @Description: 日志类型枚举
 * @Project:
 * @CreateDate: Created in 2019/6/22 14:25
 * @Author: < a href="<EMAIL>">kaixuan.yin</ a>
 */
public enum LogTypeEnum {

    SYS_SELECT_TYPE(1, "查询"),
    SYS_ADD_TYPE(2, "新增"),
    SYS_UPDATE_TYPE(3, "修改"),
    SYS_DELETE_TYPE(4, "删除"),
    SYS_EXPORT_TYPE(5, "导出"),;

    private int code;
    private String name;

    LogTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
