package com.yl.common.base.enums.log.operation;

import java.util.Arrays;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 * <p>
 * 系统枚举
 *
 * <AUTHOR>
 * @since Created in 2019-12-20
 */
public enum SystemNameNewEnum {

    SYS_JMS_LMDM("BASICINFO", "基础资料"),

    SYS_JMS_SPMN("NETWORKINFO", "网点经营"),

    SYS_JMS_SPM("FINANCIALINFO", "财务结算"),

    ;
    private String code;
    private String name;

    SystemNameNewEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }


    public String getName() {
        return name;
    }


    /**
     * 将枚举转为集合
     *
     * @return
     */
    public static List<SystemNameNewEnum> getAll() {
        return Arrays.asList(values());
    }
}
