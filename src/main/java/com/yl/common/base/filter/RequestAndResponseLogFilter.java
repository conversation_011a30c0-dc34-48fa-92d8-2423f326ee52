package com.yl.common.base.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;
import org.springframework.web.util.WebUtils;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title RequestAndResponseLogFilter
 * @Package com.yl.edi.wph.base.filter
 * @Description 请求响应日志记录
 * @date 2021/5/5 2:54 下午
 */
@Slf4j
public class RequestAndResponseLogFilter extends OncePerRequestFilter {

    /**
     * 是否记录请求日志
     */
    private boolean needLogRequest = true;

    /**
     * 是否记录响应日志
     */
    private boolean needLogResponse = true;

    /**
     * 是否记录header
     */
    private boolean needLogHeader = true;

    /**
     * 是否记录参数
     */
    private boolean needLogPayload = true;

    /**
     * 记录的最大payload大小
     */
    private int maxPayloadLength = 2 * 1024 * 1024;

    AntPathMatcher antPathMatcher = new AntPathMatcher();

    /**
     * 不进行过滤的请求pattern
     */
    private List<String> excludeUrlPatterns = new ArrayList<>(Arrays.asList("/health"));

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String url = request.getServletPath();
        boolean matched = false;
        for (String pattern : excludeUrlPatterns) {
            matched = antPathMatcher.match(pattern, url);
            if (matched) {
                break;
            }
        }
        return matched;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        Date requestDate = new Date();
        boolean isFirstRequest = !isAsyncDispatch(request);

        //包装缓存requestBody信息
        HttpServletRequest requestToUse = request;
        if (isNeedLogPayload() && isFirstRequest && !(request instanceof ContentCachingRequestWrapper)) {
            requestToUse = new ContentCachingRequestWrapper(request, getMaxPayloadLength());
        }

        //包装缓存responseBody信息
        HttpServletResponse responseToUse = response;
        if (isNeedLogPayload() && !(response instanceof ContentCachingResponseWrapper)) {
            responseToUse = new ContentCachingResponseWrapper(response);
        }
        try {
            filterChain.doFilter(requestToUse, responseToUse);
        } finally {
            //记录请求日志
            if (isNeedLogRequest()) {
                logRequest(requestToUse, requestDate);
            }
            //记录响应日志
            if (isNeedLogResponse()) {
                logResponse(responseToUse);
                //把从response中读取过的内容重新放回response，否则客户端获取不到返回的数据
                resetResponse(responseToUse);
            }
        }
    }


    /**
     * @param request
     * @param requestDate
     * <AUTHOR> mz
     * @Description : 记录请求日志
     * @Date : 3:05 下午 2021/5/5
     */
    protected void logRequest(HttpServletRequest request, Date requestDate) throws IOException {
//        String payload = isNeedLogPayload() ? getRequestPayload(request) : "";
//        logger.info(createRequestMessage(request, payload, requestDate));
    }

    /**
     * @param response
     * <AUTHOR> mz
     * @Description : 记录响应日志
     * @Date : 3:04 下午 2021/5/5
     */
    protected void logResponse(HttpServletResponse response) {
//        String payload = isNeedLogPayload() ? getResponsePayload(response) : "";
//        logger.info(createResponseMessage(response, payload, new Date()));
    }

    /**
     * @param response
     * <AUTHOR> mz
     * @Description : 重新将响应参数设置到response中
     * @Date : 3:04 下午 2021/5/5
     */
    protected void resetResponse(HttpServletResponse response) throws IOException {
        ContentCachingResponseWrapper wrapper = WebUtils.getNativeResponse(response, ContentCachingResponseWrapper.class);
        if (wrapper != null) {
            wrapper.copyBodyToResponse();
        }
    }

    /**
     * @param request
     * @return : java.lang.String
     * <AUTHOR> mz
     * @Description : 获取请求体中参数
     * @Date : 3:04 下午 2021/5/5
     */
    protected String getRequestPayload(HttpServletRequest request) throws IOException {
        String payload = "";
        ContentCachingRequestWrapper wrapper =
                WebUtils.getNativeRequest(request, ContentCachingRequestWrapper.class);
        if (wrapper != null) {
            byte[] buf = wrapper.getContentAsByteArray();
            payload = getPayloadFromBuf(buf, wrapper.getCharacterEncoding());
        }
        return payload;
    }

    /**
     * @param response
     * @return : java.lang.String
     * <AUTHOR> mz
     * @Description : 获取响应体中参数
     * @Date : 3:04 下午 2021/5/5
     */
    protected String getResponsePayload(HttpServletResponse response) {
        String payload = "";
        ContentCachingResponseWrapper wrapper = WebUtils.getNativeResponse(response, ContentCachingResponseWrapper.class);
        if (wrapper != null) {
            byte[] buf = wrapper.getContentAsByteArray();
            payload = getPayloadFromBuf(buf, wrapper.getCharacterEncoding());
        }
        return payload;
    }

    /**
     * @param request
     * @param payload
     * @param requestDate
     * @return : java.lang.String
     * <AUTHOR> mz
     * @Description : 创建请求日志实际需要打印的内容
     * @Date : 3:04 下午 2021/5/5
     */
    protected String createRequestMessage(HttpServletRequest request, String payload, Date requestDate) {
        StringBuilder msg = new StringBuilder();
        msg.append("Inbound Message\n----------------------------\n");
        msg.append("Address: ").append(request.getRequestURL()).append("\n");
        msg.append("HttpMethod: ").append(request.getMethod()).append("\n");
        msg.append("QueryString: ").append(request.getQueryString()).append("\n");
        msg.append("Encoding: ").append(request.getCharacterEncoding()).append("\n");
        msg.append("Content-Type: ").append(request.getContentType()).append("\n");
        if (isNeedLogHeader()) {
            msg.append("Headers: ").append(new ServletServerHttpRequest(request).getHeaders()).append("\n");
        }
        if (isNeedLogPayload()) {

            HttpHeaders headers = new ServletServerHttpRequest(request).getHeaders();
            if(Objects.toString(headers,"").contains("urlencoded")) {
                try {
                    payload = URLDecoder.decode(payload,"UTF-8");
                } catch (UnsupportedEncodingException e) {

                }
            }
            int length = Math.min(payload.length(), getMaxPayloadLength());
            msg.append("Payload: ").append(payload.substring(0, length)).append("\n");
        }
        msg.append("----------------------------------------------");
        return msg.toString();
    }

    /**
     * @param response
     * @param payload
     * @param responseDate
     * @return : java.lang.String
     * <AUTHOR> mz
     * @Description : 创建响应日志实际需要打印的内容
     * @Date : 3:04 下午 2021/5/5
     */
    protected String createResponseMessage(HttpServletResponse response, String payload, Date responseDate) {
        StringBuilder msg = new StringBuilder();
        msg.append("Outbound Message\n----------------------------\n");
        msg.append("Encoding: ").append(response.getCharacterEncoding()).append("\n");
        msg.append("Content-Type: ").append(response.getContentType()).append("\n");
        if (isNeedLogHeader()) {
            msg.append("Headers: ").append(new ServletServerHttpResponse(response).getHeaders()).append("\n");
        }
        boolean needLogContentType = true;
        String contentType = response.getContentType();
        //是JSON格式的才输出
        needLogContentType = StringUtils.isEmpty(contentType) || contentType.toUpperCase().contains("JSON") || contentType.contains("text");
        if (isNeedLogPayload() && needLogContentType) {
            int length = Math.min(payload.length(), getMaxPayloadLength());
            msg.append("Payload: ").append(payload.substring(0, length)).append("\n");
        }
        msg.append("----------------------------------------------");
        return msg.toString();
    }

    /**
     * @param buf
     * @param characterEncoding
     * @return : java.lang.String
     * <AUTHOR> mz
     * @Description : 将byte[]参数转换为字符串用于输出
     * @Date : 3:05 下午 2021/5/5
     */
    protected String getPayloadFromBuf(byte[] buf, String characterEncoding) {
        String payload = "";
        if (buf.length > 0) {
            int length = Math.min(buf.length, getMaxPayloadLength());
            try {
                payload = new String(buf, 0, length, characterEncoding);
            } catch (UnsupportedEncodingException ex) {
                logger.error(ex.getMessage(), ex);
            }
        }
        return payload;
    }

    public boolean isNeedLogRequest() {
        return needLogRequest;
    }

    public void setNeedLogRequest(boolean needLogRequest) {
        this.needLogRequest = needLogRequest;
    }

    public boolean isNeedLogResponse() {
        return needLogResponse;
    }

    public void setNeedLogResponse(boolean needLogResponse) {
        this.needLogResponse = needLogResponse;
    }

    public boolean isNeedLogHeader() {
        return needLogHeader;
    }

    public void setNeedLogHeader(boolean needLogHeader) {
        this.needLogHeader = needLogHeader;
    }

    public boolean isNeedLogPayload() {
        return needLogPayload;
    }

    public void setNeedLogPayload(boolean needLogPayload) {
        this.needLogPayload = needLogPayload;
    }

    public int getMaxPayloadLength() {
        return maxPayloadLength;
    }

    public void setMaxPayloadLength(int maxPayloadLength) {
        this.maxPayloadLength = maxPayloadLength;
    }

    public List<String> getExcludeUrlPatterns() {
        return excludeUrlPatterns;
    }

    public void setExcludeUrlPatterns(List<String> excludeUrlPatterns) {
        this.excludeUrlPatterns = excludeUrlPatterns;
    }

}