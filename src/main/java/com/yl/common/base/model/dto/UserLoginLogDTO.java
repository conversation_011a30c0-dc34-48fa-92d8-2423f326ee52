package com.yl.common.base.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * @Description: 登陆日志查询DTO
 * @Project:
 * @CreateDate: Created in 2019-06-25 13:54
 * @Author: < a href="<EMAIL>">haibo.lin</ a>
 */
@Data
public class UserLoginLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工名称
     */
    private String staffName;

    /**
     * 开始时间
     */
//    @OperationParamLog(message = "开始时间")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startTime;

    /**
     * 结束时间
     */
//    @OperationParamLog(message = "结束时间")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endTime;
}
