package com.yl.common.base.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.yl.common.base.enums.IdEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019
 *
 * @Description: 使用雪花算法生成ID
 * @Project:
 * @CreateDate: Created in 2019/7/9 19:13
 * @Author: <a href="<EMAIL>">zijing.lin</a>
 */
public class GenerationIdUtil {

    private static Map<String, IdEnum> serviceMap = new ConcurrentHashMap<>();

    private GenerationIdUtil() {

        for (IdEnum idEnum : IdEnum.values()) {
            serviceMap.put(idEnum.getServiceName(), idEnum);
        }

    }

    /**
     * @return
     * <AUTHOR>
     * @Description 生成一个ID
     * @Date 2019/7/9 19:31
     * @Param
     **/
    public static Long getId() {

        return IdUtil.createSnowflake(RandomUtil.randomInt(0, 32), RandomUtil.randomInt(0, 32)).nextId() + LongAdderUtil.getLongValue();
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 根据服务名生成ID
     * @Date 2019/7/9 19:31
     * @Param
     **/
    public static Long getIdByServiceName(String serviceName) {
        if (serviceMap.get(serviceName) != null) {
            return IdUtil.createSnowflake((serviceMap.get(serviceName).getWorkerId()), serviceMap.get(serviceName).getDataCenterId()).nextId() + LongAdderUtil.getLongValue();
        } else {
            return IdUtil.createSnowflake(RandomUtil.randomInt(0, 32), RandomUtil.randomInt(0, 32)).nextId() + LongAdderUtil.getLongValue();
        }

    }


    /**
     * @return
     * <AUTHOR>
     * @Description 根据服务名批量生成ID
     * @Date 2019/7/9 19:31
     * @Param
     **/
    public static List<Long> batchIdByServiceName(String serviceName, int count) {

        List<Long> list = new ArrayList();
        if (serviceMap.get(serviceName) != null) {
            for (int i = 0; i < count; i++) {
                list.add(IdUtil.createSnowflake(serviceMap.get(serviceName).getWorkerId(), serviceMap.get(serviceName).getDataCenterId()).nextId() + LongAdderUtil.getLongValue());
            }
        } else {
            for (int i = 0; i < count; i++) {
                list.add(IdUtil.createSnowflake(RandomUtil.randomInt(0, 32), RandomUtil.randomInt(0, 32)).nextId() + LongAdderUtil.getLongValue());
            }
        }

        return list;
    }






}
