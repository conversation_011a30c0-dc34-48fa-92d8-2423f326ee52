package com.yl.common.base.util;

import javax.servlet.http.HttpServletRequest;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 * 获取真实的IP
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-07-08 9:13
 */
public class IpAddressUtil {

    public static String getIpAdrress(HttpServletRequest request) {
        //X-Real-IP，一般只记录真实发出请求的客户端IP
        String XRealIp = request.getHeader("X-Real-IP");
        //x_forwarded_for:  【用户经过代理时，代理会增加这个字段，nginx可用内置变量$http_x_forwarded_for取到这个字段，没有使用代理时，此字段为空】
        String XForwardedFor = request.getHeader("X-Forwarded-For");
        if(StringUtils.isNotEmpty(XForwardedFor) && !"unKnown".equalsIgnoreCase(XForwardedFor)){
            //多次反向代理后会出现多个ip值，但只有第一个ip才是真实ip。所以只拿第一个就好
            int index = XForwardedFor.indexOf(",");
            if(index != -1){
                return XForwardedFor.substring(0,index);
            }else{
                return XForwardedFor;
            }
        }
        XForwardedFor = XRealIp;
        if(StringUtils.isNotEmpty(XForwardedFor) && !"unKnown".equalsIgnoreCase(XForwardedFor)){
            return XForwardedFor;
        }
        if (StringUtils.isBlank(XForwardedFor) || "unknown".equalsIgnoreCase(XForwardedFor)) {
            XForwardedFor = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(XForwardedFor) || "unknown".equalsIgnoreCase(XForwardedFor)) {
            XForwardedFor = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(XForwardedFor) || "unknown".equalsIgnoreCase(XForwardedFor)) {
            XForwardedFor = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(XForwardedFor) || "unknown".equalsIgnoreCase(XForwardedFor)) {
            XForwardedFor = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (StringUtils.isBlank(XForwardedFor) || "unknown".equalsIgnoreCase(XForwardedFor)) {
            XForwardedFor = request.getRemoteAddr();
        }
        return XForwardedFor;
    }
}
