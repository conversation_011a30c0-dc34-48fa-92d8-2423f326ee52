package com.yl.common.base.util;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.dto.JwtUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 * 获取用户登录信息工具类
 *
 * @project: LoginUserUtils<br>
 * @since Created in 2019/12/01 10:40 上午
 */
@Slf4j
public class LoginUserUtils {

    public static final String LOGIN_USER = "LOGIN_USER";

    public static JwtUserDTO getJwtUser() {
        HttpServletRequest request = getRequest();
        String routeName = request.getHeader("routeName");
        String authToken = request.getHeader("authToken");
        String requestURI = request.getRequestURI();
        Object userInfo = request.getAttribute(LOGIN_USER);
        if (userInfo == null) {
            log.error("getJwtUser,获取用户信息为空,将返回:当前按钮没有访问权限, authToken:{} ,routeName:{}, requestURI:{}", authToken, routeName, requestURI);
            throw new ServiceException(ResultCodeEnum.PERMISSION_NO_ACCESS);
        }
        return (JwtUserDTO) userInfo;
    }

    private static HttpServletRequest getRequest() {
        return getServletRequestAttributes().getRequest();
    }

    private static ServletRequestAttributes getServletRequestAttributes() {
        return (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    }

}
