package com.yl.common.base.util;

import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019/9/4 11:37 <br>
 * @Author: <a href="<EMAIL>"><EMAIL></a>
 */
@Component
public class YlRetCodeUtil {
    //获取返回码
    public int getCode(String retCode){
        int ret = 90000;
        String regEx="[^0-9]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(retCode);
        try {
            ret =  Integer.parseInt(m.replaceAll("").trim());
        } catch (NumberFormatException e) {
//            log.error("非法返回码",e);
        }
        return ret;
    }


    //获取系统
    public String getSys(String retCode){
        String regEx = "(.*?)-";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(retCode);
        while (m.find()) {
            return m.group(1);
        }
        return "90000"; //非法返回码
    }


    //获取模块
    public String getModule(String retCode){
        String regEx = "-(.*?)-";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(retCode);
        while (m.find()) {
            return m.group(1);
        }
        return "90000"; //非法返回码
    }


    //获取系统与模块
    public String getSysModule(String retCode){
        String regEx="\\D+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(retCode);
        while (m.find()) {
            return m.group(0);
        }
        return "90000"; //非法返回码
    }

}
