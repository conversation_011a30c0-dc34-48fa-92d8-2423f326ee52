package com.yl.customerplatform.controller;

import com.yl.common.base.model.vo.Result;
import com.yl.customerplatform.service.IOrderInputService;
import com.yl.customerplatform.vo.OrderInputVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/orderInput")
public class OrderInputController {

    @Autowired
    private IOrderInputService orderInputService;

    @PostMapping("/save")
    public Result<List<OrderInputVo>> save(@RequestBody List<OrderInputVo> orderInputVoList) {
        return orderInputService.saveOrderInput(orderInputVoList);
    }


    @GetMapping(value = "/downloadTemplate")
    public void downloadTemplate() throws IOException {
        orderInputService.downloadTemplate();
    }


}
