package com.yl.customerplatform.dto.coupon;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021-11-20
 *
 * @Description: TODO
 * @Author: 张润
 * @Date: 2021-11-20 15:19
 */
@Data
@EqualsAndHashCode
public class CouponExcelDTO extends BaseRowModel {
    /**
     * 序号
     */
    @ExcelProperty(index = 0)
    private String rowNum;

    /**
     * 发放手机号
     */
    @ExcelProperty(index = 1)
    private String tel;

    /**
     * 发放事由
     */
    @ExcelProperty(index = 2)
    private String grantReasonStr;

    /**
     * 工单号
     */
    @ExcelProperty(index = 3)
    private String relatedWorkNo;

    /**
     * 发放备注
     */
    @ExcelProperty(index = 4)
    private String remark;


    /**
     * 发放事由类型
     */
    private Short grantReason;
}
