package com.yl.customerplatform.dto.coupon;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * YL_OMS_COUPON_BATCH
 * <AUTHOR>
@ApiModel(value="com.yl.customerplatform.dto.coupon.OmsCouponBatchUpdateDTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OmsCouponBatchUpdateDTO implements Serializable {



    @ApiModelProperty(value="id")
    @NotNull(message = "id不为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value="批次号", hidden = true)
    private String couponBatchNo;

    /**
     * 批次名称
     */
    @ApiModelProperty(value="批次名称")
    @NotEmpty(message = "批次名称不为空")
    private String couponBatchName;

    /**
     * 优惠券类型
     */
    @ApiModelProperty(value="优惠券类型")
    @NotEmpty(message = "优惠券类型不为空")
    private String couponType;

    /**
     * 有效期类型
     */
    @ApiModelProperty(value="有效期类型", example = "1、固定日期 ， 2、领取后x天")
    @NotNull(message = "有效期类型不为空")
    private Short validType;

    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value="有效期开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fixedStartDate;

    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value="有效期结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fixedEndDate;

    /**
     * 领取后x天
     */
    @ApiModelProperty(value="领取后x天")
    private Long receiveAfterDay;

    /**
     * 使用地区类型,1:州，2：市，3：区
     */
    @ApiModelProperty(value="使用地区类型,1:州，2：市，3：区")
    @NotNull(message = "使用地区类型不为空")
    private Short useRegionType;

    /**
     * 州code
     */
    @ApiModelProperty(value="州code")
    private String useProCode;

    /**
     * 市code
     */
    @ApiModelProperty(value="市code")
    private String useCityCode;

    /**
     * 区code
     */
    @ApiModelProperty(value="区code")
    private String useAreaCode;

    /**
     * 州名称
     */
    @ApiModelProperty(value="州名称")
    private String useProName;

    /**
     * 市名称
     */
    @ApiModelProperty(value="市名称")
    private String useCityName;

    /**
     * 区名称
     */
    @ApiModelProperty(value="区名称")
    private String useAreaName;

    /**
     * 使用渠道, 1、全部 2、APP 3、巴枪PDA 4、JMS 5、官网
     */
    @ApiModelProperty(value="使用渠道, 1、全部 2、APP 3、巴枪PDA 4、JMS 5、官网")
    @NotEmpty(message = "使用渠道不为空")
    private String useChannel;

    /**
     * 快递产品类型,1:标准快递，2：特快专递，3：同城配送
     */
    @ApiModelProperty(value="快递产品类型,1:标准快递，2：特快专递，3：同城配送")
    @NotEmpty(message = "快递产品类型不为空")
    private String expressType;

    /**
     * 快递产品类型名称
     */
    @ApiModelProperty(value="快递产品类型名称")
    private String expressName;

    /**
     * 费用类型,1：运费，2：保价费，3：COD服务费，4：包材费
     */
    @ApiModelProperty(value="费用类型,1：运费，2：保价费，3：COD服务费，4：包材费")
    @NotEmpty(message = "费用类型不为空")
    private String feeType;

    /**
     * 结算方式,1:寄付现结，2：寄付月结
     */
    @ApiModelProperty(value="结算方式,1:寄付现结，2：寄付月结")
    @NotEmpty(message = "结算方式不为空")
    private String settleMethod;

    /**
     * 生成数量
     */
    @ApiModelProperty(value="生成数量")
    private Long generatedQuantity;

    /**
     * 账号限领量
     */
    @ApiModelProperty(value="账号限领量")
    private Long limitedQuantity;

    /**
     * 预警值
     */
    @ApiModelProperty(value="预警值")
    private Long warnValue;

    /**
     * 预警电话
     */
    @ApiModelProperty(value="预警电话")
    @NotEmpty(message = "预警电话不为空")
    private String warnTel;

    /**
     * 使用说明
     */
    @ApiModelProperty(value="使用说明", hidden = true)
    private String instructions;

    /**
     * 使用说明
     */
    @ApiModelProperty(value="使用说明")
    @NotNull(message = "使用说明不为空")
    private List<String> instructionsList;

    /**
     * 费用承担部门
     */
    @ApiModelProperty(value="费用承担部门")
    @NotEmpty(message = "费用承担部门不为空")
    private String expenseDep;

    /**
     * 申请原因
     */
    @ApiModelProperty(value="申请原因")
    @NotEmpty(message = "申请原因不为空")
    private String applyReason;

    /**
     * 活动名称
     */
    @ApiModelProperty(value="活动名称")
    private String activityName;

    /**
     * 批次状态,1、待审核 2、启用中 3、已下架 4、审核未通过
     */
    @ApiModelProperty(value="批次状态,",example = "1、待审核 2、启用中 3、已下架 4、审核未通过 5、无库存 6、已过期", hidden = true)
    private Short batchStatus;

    @ApiModelProperty(hidden = true)
    private Date createTime;

    @ApiModelProperty(hidden = true)
    private String creator;
    /**
     * 费用承担部门
     */
    @ApiModelProperty(value="费用承担部门id")
    private String expenseDepId;
    /**
     * 费用承担部门
     */
    @ApiModelProperty(value="费用承担部门code")
    private String expenseDepCode;
    private static final long serialVersionUID = 1L;
}