package com.yl.customerplatform.feign;

import com.yl.common.base.model.vo.Result;
import com.yl.customerplatform.vo.AppBaseDataVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-10-13 13:37
 */

@FeignClient(
        name = "yllmdmapi",
        path = "/lmdmapi/app/baseData"
)
public interface AppBaseDataFeignClient {
    @GetMapping({"/list"})
    Result<AppBaseDataVO> getBaseDataList(@RequestParam("type") Integer var1);
}
