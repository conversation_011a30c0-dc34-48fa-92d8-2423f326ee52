package com.yl.customerplatform.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.model.vo.Result;
import com.yl.customerplatform.dto.coupon.*;
import com.yl.customerplatform.feign.fallback.OmsCouponBatchFallback;
import com.yl.customerplatform.vo.coupon.OmsCouponBatchSearchVO;
import com.yl.customerplatform.vo.coupon.OmsCouponBatchVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠券批次
 */
@Component
@FeignClient(name = "ylorderapi",
        path = "/orderapi" ,
        fallback = OmsCouponBatchFallback.class)
public interface OmsCouponBatchClient {

    /**
     * 优惠券批次 新增
     * @param dto
     * @return
     */
    @PostMapping(value = "/couponBatch/add")
    Result couponBatchAdd(@RequestBody(required = true) OmsCouponBatchSaveDTO dto);

    /**
     * 优惠券批次 修改
     * @param updateDTO
     * @return
     */
    @PostMapping(value = "/couponBatch/update")
    Result couponBatchUpdate(@RequestBody(required = true) OmsCouponBatchUpdateDTO updateDTO);

    /**
     * 状态修改
     * @param dto
     * @return
     */
    @PostMapping(value = "/couponBatch/updateStatus")
    Result couponBatchUpdateStatus(@RequestBody(required = true) OmsCouponBatchStatusDTO dto);

    /**
     * 优惠券批次查询
     * @param couponBatchPageQueryDTO
     * @return
     */
    @PostMapping({"/couponBatch/list"})
    public Result<Page<OmsCouponBatchVO>> getPages(@RequestBody CouponBatchPageQueryDTO couponBatchPageQueryDTO);


    /**
     * 优惠券批次查询
     * @param id
     * @return
     */
    @GetMapping({"/couponBatch/detail/{id}"})
    public Result<OmsCouponBatchVO> detail(@PathVariable(value = "id") Long id);

    /**
     * 优惠券批次搜索
     * @param searchDTO
     * @return
     */
    @PostMapping({"/couponBatch/search"})
    public Result<List<OmsCouponBatchSearchVO>> search(@RequestBody OmsCouponBatchSearchDTO searchDTO);


}