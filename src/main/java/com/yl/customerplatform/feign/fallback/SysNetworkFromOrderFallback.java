package com.yl.customerplatform.feign.fallback;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.customerplatform.feign.SysNetworkFromOrderFeignClient;
import com.yl.lmdm.api.vo.organizational.SysNetworkVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * <AUTHOR>
 * @description :
 * @date ：Created in 2021-02-03
 */
@Component
@Slf4j
public class SysNetworkFromOrderFallback implements SysNetworkFromOrderFeignClient {
    @Override
    public Result<List<SysNetworkVO>> proxyArea() {
        log.warn("查询全量的代理区失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<SysNetworkVO>> franchisee(String path) {
        log.warn("查询登录角色所属及下属的加盟商失败");
        return Result.error(ResultCodeEnum.FAIL);
    }
}
