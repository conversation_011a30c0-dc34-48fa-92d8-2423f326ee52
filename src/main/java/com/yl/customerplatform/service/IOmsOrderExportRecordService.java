package com.yl.customerplatform.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.model.vo.Result;
import com.yl.customerplatform.dto.ExportRecordQueryDTO;
import com.yl.customerplatform.vo.DownExcelVO;

/**
 * 订单综合查询Excel
 * Created by lwk on 2020年5月16日19:49:38
 */
public interface IOmsOrderExportRecordService {

    /**
     * 查询所以下载记录.
     */

    Result<Page<DownExcelVO>> queryOrderDetail(ExportRecordQueryDTO dto);

    /**
     * 删除下载excel记录
     *
     * @param id
     * @return
     */
    Result<Boolean> delLoadExcel(String id);

    /**
     * 下载完成
     *
     * @param id
     * @return
     */
    Result<Boolean> updateFinishDownLoad(String id);

}
