package com.yl.customerplatform.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.dto.JwtUserDTO;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.OrikaBeanMapper;
import com.yl.customerplatform.annotation.CustomColumn;
import com.yl.customerplatform.config.NetWorkPermissionHelper;
import com.yl.customerplatform.dto.CpProxySysNetworkDTO;
import com.yl.customerplatform.dto.OmsCustomColumnDTO;
import com.yl.customerplatform.dto.OmsCustomColumnNode;
import com.yl.customerplatform.enums.CustomColumnEnum;
import com.yl.customerplatform.enums.OmsErrorEnum;
import com.yl.customerplatform.feign.CpNetworkFeignClient;
import com.yl.customerplatform.helper.I18nHelper;
import com.yl.customerplatform.service.IOmsBaseDataService;
import com.yl.customerplatform.util.JsonUtils;
import com.yl.customerplatform.util.JwtUserUtils;
import com.yl.customerplatform.util.enums.RedisKeyEnum;
import com.yl.customerplatform.vo.CpProxySysNetworkVO;
import com.yl.customerplatform.vo.OmsCustomColumnNodeVO;
import com.yl.jms.ups.util.LoginUserUtils;
import com.yl.order.api.util.JacksonUtil;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import springfox.documentation.spring.web.json.Json;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.*;

/**
 * <p>
 * 派件任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-24
 */
@Service
@Slf4j
public class OmsOmsBaseDataServiceImpl implements IOmsBaseDataService {

    /**
     * 所有默认列的map
     */
    private static Map<Integer, List<OmsCustomColumnNodeVO>> customColumnMap = new HashMap<>();

    @Autowired
    private NetWorkPermissionHelper netWorkPermissionHelper;

    @Autowired
    private CpNetworkFeignClient cpNetworkFeignClient;

    @Autowired
    private I18nHelper ylI18nHelper;

    @Autowired
    protected HttpServletRequest request;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Override
    public void setCustomColumn(OmsCustomColumnDTO omsCustomColumnDTO) {
        List<OmsCustomColumnNode> columnList = omsCustomColumnDTO.getColumnList();
        if (CollectionUtils.isEmpty(columnList)) {
            return;
        }
        CustomColumnEnum customColumnEnum = CustomColumnEnum.of(omsCustomColumnDTO.getCode());
        if (customColumnEnum == null) {
            throw new BusinessException(OmsErrorEnum.OMS_CUSTOM_COLUMN_CODE_ERROR);
        }
        String tableName = customColumnEnum.getTableName();
        JwtUserDTO user = LoginUserUtils.getJwtUser();
        String redisKey = RedisKeyEnum.OMS_CUSTOM_COLUMN.keyBuilder(tableName + ":" + user.getId());
        RedisUtil.zRemoveRange(redisKey, 0, -1);
        int count = 1;
        for (OmsCustomColumnNode columnNode : columnList) {
                columnNode.setColumn(getColumn(customColumnEnum,columnNode.getName()));
            String value = JSONObject.toJSONString(columnNode);
            double score = Double.valueOf(String.valueOf(count++));
            RedisUtil.zAdd(redisKey, value, score);
        }
    }

    @Override
    public List<OmsCustomColumnNodeVO> getCustomColumn(Integer code) {
        CustomColumnEnum customColumnEnum = CustomColumnEnum.of(code);
        log.info("getCustomColumn code:{}",code);
        if (customColumnEnum == null) {
            throw new BusinessException(OmsErrorEnum.OMS_CUSTOM_COLUMN_CODE_ERROR);
        }
        String tableName = customColumnEnum.getTableName();
        log.info("getCustomColumn tableName:{}",tableName);
        JwtUserDTO user = LoginUserUtils.getJwtUser();
        Set<Object> columnSet = RedisUtil.zRange(RedisKeyEnum.OMS_CUSTOM_COLUMN.keyBuilder(tableName + ":" + user.getId()), 0, -1);
        log.info("getCustomColumn columnSet:{}", JsonUtils.toJson(columnSet));
        // 用户没有定义则取全部列
        return CollectionUtils.isEmpty(columnSet) ? this.getDefaultColumn(code) : this.getListBySet(columnSet);
    }

    private List<OmsCustomColumnNodeVO> getListBySet(Set<Object> columnSet) {
        List<OmsCustomColumnNodeVO> voList = new ArrayList<>();
        for (Object obj : columnSet) {
            OmsCustomColumnNodeVO vo = JacksonUtil.json2BeanByZoneTime(JSONObject.parseObject(obj.toString()).toJSONString(), OmsCustomColumnNodeVO.class);
            log.info("getListBySet OmsCustomColumnNodeVO:{}",JsonUtils.toJson(vo));
            vo.setColumn(ylI18nHelper.getI18nVal(vo.getColumn()));
            voList.add(vo);
        }
        log.info("getListBySet voList:{}",JsonUtils.toJson(voList));
        return voList;
    }

    @Override
    public List<OmsCustomColumnNodeVO> getDefaultColumn(Integer code) {
        log.info("getDefaultColumn code:{}",code);
        CustomColumnEnum customColumnEnum = CustomColumnEnum.of(code);
        log.info("getDefaultColumn customColumnEnum:{}",customColumnEnum);
        if (customColumnEnum == null) {
            throw new BusinessException(OmsErrorEnum.OMS_CUSTOM_COLUMN_CODE_ERROR);
        }
        if (customColumnMap.containsKey(code)) {
            List<OmsCustomColumnNodeVO> vos = customColumnMap.get(code);
            log.info("customColumnMap get vos:{}",JsonUtils.toJson(vos));
            List<OmsCustomColumnNodeVO> cvos = new ArrayList<>();
            for(OmsCustomColumnNodeVO vo : vos){
                OmsCustomColumnNodeVO node = orikaBeanMapper.map(vo,OmsCustomColumnNodeVO.class);
                node.setColumn(ylI18nHelper.getI18nVal(vo.getColumn()));
                cvos.add(node);
            }
            log.info("new customColumnMap get vos:{}",JsonUtils.toJson(cvos));
            return  cvos;
        }
        Class clazz = customColumnEnum.getClazz();
        Field[] fields = clazz.getDeclaredFields();
        List<OmsCustomColumnNodeVO> allColumn = new ArrayList<>();
        for (Field field : fields) {
            boolean fieldHasAnnotation = field.isAnnotationPresent(CustomColumn.class);
            if (fieldHasAnnotation) {
                CustomColumn fieldAnnotation = field.getAnnotation(CustomColumn.class);
                boolean show = fieldAnnotation.show();
                OmsCustomColumnNodeVO omsCustomColumnNodeVO = new OmsCustomColumnNodeVO(field.getName(), fieldAnnotation.name(), show, fieldAnnotation.position());
                if(!fieldAnnotation.hide()) {
                    log.info("omsCustomColumnNodeVO :{}",JsonUtils.toJson(omsCustomColumnNodeVO));
//                    omsCustomColumnNodeVO.setColumn(omsCustomColumnNodeVO.getColumn());
                    omsCustomColumnNodeVO.setColumn(getColumn(customColumnEnum,omsCustomColumnNodeVO.getName()));
                    log.info("new omsCustomColumnNodeVO :{}",JsonUtils.toJson(omsCustomColumnNodeVO));
                    allColumn.add(omsCustomColumnNodeVO);
                }
            }
        }
        allColumn.sort(Comparator.comparingInt(OmsCustomColumnNodeVO::getPosition));
        customColumnMap.put(code, allColumn);
        log.info("new allColumn :{}",JsonUtils.toJson(allColumn));
        return allColumn;
    }

    /**
     * 检查是否有权限
     * @param networkCode
     * @return
     */
    @Override
    public boolean checkPermission(String networkCode) {
//        JwtUserDTO jwtUserDTO = JwtUtil.getJwtUser();
        JwtUserDTO jwtUserDTO = LoginUserUtils.getJwtUser();
        List<String> userNetworkCodes = netWorkPermissionHelper.getNetWorkCode(jwtUserDTO.getNetworkId(), jwtUserDTO.getInstitutionalLevelId());
        return JwtUserUtils.isNotManager(jwtUserDTO) ? userNetworkCodes.contains(networkCode) : true;
    }

    @Override
    public Result<Page<CpProxySysNetworkVO>> getProxyNetworksByArea(CpProxySysNetworkDTO proxySysNetworkDTO, Long current, Long size) {
        return cpNetworkFeignClient.getProxyNetworksByArea(proxySysNetworkDTO,current,size);
    }

    private String getColumn(CustomColumnEnum customColumnEnum,String columnName){
        Class clazz = customColumnEnum.getClazz();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            if(fieldName.equals(columnName)) {
                boolean fieldHasAnnotation = field.isAnnotationPresent(CustomColumn.class);
                if (fieldHasAnnotation) {
                    CustomColumn fieldAnnotation = field.getAnnotation(CustomColumn.class);
                    return fieldAnnotation.name();
                }
            }
        }
        return columnName;
    }

}
