package com.yl.customerplatform.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class NetworkDashboardVo {

    private Integer current;

    private Integer size;

    private String senderName;

    private String senderMobilePhone;

    private String senderProvinceName;

    private String senderProvinceId;

    private String senderCityName;

    private String senderCityId;

    private String senderAreaName;

    private String senderAreaId;

    private String senderTownship;

    private String senderTownshipId;

    private String senderDetailedAddress;

    private Integer orderCount;

    private Integer packageNumberCount;

    private Integer customerCount;

    private BigDecimal packageTotalWeightSum;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inputTime;

    private String startDate;

    private String endDate;

    private String pickNetworkCode;

    private String pickStaffName;

    private String pickStaffCode;

    private Integer pickedCount;

    private Integer notPickedCount;

    private List<String> orderStatusCodeList;

    private Integer orderStatusCode;

}
