package com.yl.customerplatform.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;

import lombok.Data;

@ColumnWidth(25)
@Data
public class OrderAddressExcelWithErrorVO extends OrderAddressExcelVO {
    @ColumnWidth(50)
    @ExcelProperty(
            value = {"异常信息"},
            index = 14
    )
    @HeadFontStyle(
            color = 10
    )
    private String errorMsg;

    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    public OrderAddressExcelWithErrorVO() {
    }


    public String toString() {
        return "orderAddressExcelWithErrorVO(errorMsg=" + this.getErrorMsg() + ")";
    }
}
