package com.yl.customerplatform.vo.dispatchmonitor;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yl.common.base.constant.SaveGroup;
import com.yl.order.api.vo.OmsOrderMemberVO;
import com.yl.order.api.vo.OmsOrderThirdExtVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单请求实体
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OmsDispatchDetailVO请求对象", description = "调派明细请求实体")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OmsDispatchDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    @Null(message = "id不能有值", groups = {SaveGroup.class})
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "客户订单编号")
    @Size(max = 50, message = "客户订单编号不能超过50个字符")
    private String customerOrderId;

    @ApiModelProperty(value = "订单来源名称")
    @Size(max = 30, message = "订单来源名称不能超过30个字符")
    private String orderSourceName;

    @ApiModelProperty(value = "订单来源code")
    @Size(max = 30, message = "订单来源code不能超过30个字符")
    private String orderSourceCode;

    @ApiModelProperty(value = "订单类型code，1.费仓储订单 2.仓储订单 3.逆向物流订单")
    private Integer orderTypeCode;

    @ApiModelProperty(value = "订单类型，1.费仓储订单 2.仓储订单 3.逆向物流订单")
    private String orderTypeName;

    @ApiModelProperty(value = "运单号")
    private String waybillId;

    @ApiModelProperty(value = "寄件人姓名")
    @Size(max = 30, message = "寄件人姓名不能超过30个字符")
    private String senderName;

    @ApiModelProperty(value = "寄件人公司")
    @Size(max = 100, message = "寄件人公司不能超过100个字符")
    private String senderCompany;

    @ApiModelProperty(value = "寄件人手机号")
    @Size(max = 30, message = "寄件人手机号不能超过30个字符")
    private String senderMobilePhone;

    @ApiModelProperty(value = "寄件人座机")
    @Size(max = 30, message = "寄件人固话不能超过30个字符")
    private String senderTelphone;

    @ApiModelProperty(value = "寄件国家名称")
    @Size(max = 30, message = "寄件国家名称不能超过30个字符")
    private String senderCountryName;

    @ApiModelProperty(value = "寄件国家Id")
    private Integer senderCountryId;

    @ApiModelProperty(value = "寄件省份名称")
    @Size(max = 30, message = "寄件省份名称不能超过30个字符")
    private String senderProvinceName;

    @ApiModelProperty(value = "寄件省份id")
    private Integer senderProvinceId;

    @ApiModelProperty(value = "寄件城市名称")
    @Size(max = 30, message = "寄件城市名称不能超过30个字符")
    private String senderCityName;

    @ApiModelProperty(value = "寄件城市id")
    private Integer senderCityId;

    @ApiModelProperty(value = "寄件区域名称")
    @Size(max = 30, message = "寄件区域名称不能超过30个字符")
    private String senderAreaName;

    @ApiModelProperty(value = "寄件区域Id")
    private Integer senderAreaId;

    @ApiModelProperty(value = "寄件乡镇")
    @Size(max = 200, message = "寄件乡镇不能超过200个字符")
    private String senderTownship;

    @ApiModelProperty(value = "寄件街道")
    @Size(max = 200, message = "不能超过200个字符")
    private String senderStreet;

    @ApiModelProperty(value = "寄件邮编")
    @Size(max = 30, message = "寄件邮编不能超过30个字符")
    private String senderPostalCode;

    @ApiModelProperty(value = "收件人姓名")
    @Size(max = 30, message = "收件人姓名不能超过30个字符")
    private String receiverName;

    @ApiModelProperty(value = "收件人座机")
    @Size(max = 30, message = "收件人固话不能超过30个字符")
    private String receiverTelphone;

    @ApiModelProperty(value = "收件国家id")
    private Integer receiverCountryId;

    @ApiModelProperty(value = "收件省份名称")
    @Size(max = 30, message = "收件省份名称不能超过30个字符")
    private String receiverProvinceName;

    @ApiModelProperty(value = "收件省份id")
    private Integer receiverProvinceId;

    @ApiModelProperty(value = "收件城市名称")
    @Size(max = 30, message = "收件城市名称不能超过30个字符")
    private String receiverCityName;

    @ApiModelProperty(value = "收件城市id")
    private Integer receiverCityId;

    @ApiModelProperty(value = "收件区域名称")
    @Size(max = 30, message = "收件区域名称不能超过30个字符")
    @NotBlank(message = "收件区域名称不能为空")
    private String receiverAreaName;

    @ApiModelProperty(value = "收件区域id")
    private Integer receiverAreaId;

    @ApiModelProperty(value = "收件乡镇")
    @Size(max = 200, message = "收件乡镇不能超过200个字符")
    private String receiverTownship;

    @ApiModelProperty(value = "收件街道")
    @Size(max = 200, message = "收件街道不能超过200个字符")
    private String receiverStreet;

    @ApiModelProperty(value = "收件详细地址")
    @Size(max = 200, message = "收件详细地址不能超过200个字符")
    private String receiverDetailedAddress;

    @ApiModelProperty(value = "收件邮编")
    @Size(max = 30, message = "收件邮编不能超过30个字符")
    private String receiverPostalCode;

    @ApiModelProperty(value = "收件分拣码")
    @Size(max = 30, message = "收件分拣码不能超过30个字符")
    private String receiverSortingCode;

    @ApiModelProperty(value = "备注")
    @Size(max = 500, message = "备注不能超过500个字符")
    private String remarks;

    @ApiModelProperty(value = "快件类型code")
    @Size(max = 30, message = "快件类型编码不能超过30个字符")
    @NotBlank(message = "快件类型编码不能为空")
    private String expressTypeCode;

    @ApiModelProperty(value = "需要代收货款,1是，0否")
    @Min(value = 0, message = "是否需要代收货款只能为0或者1")
    @Max(value = 1, message = "是否需要代收货款只能为0或者1")
    @NotNull(message = "是否需要代收货款只能为0或者1")
    private Integer codNeed;

    @ApiModelProperty(value = "代收货款金额")
    @Digits(integer = 10, fraction = 2, message = "代收货款金额超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal codMoney;

    @ApiModelProperty(value = "最佳取件开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime bestPickTimeStart;

    @ApiModelProperty(value = "最佳取件结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime bestPickTimeEnd;

    @ApiModelProperty(value = "订单状态code")
    private Integer orderStatusCode;

    @ApiModelProperty(value = "订单状态名称")
    private String orderStatusName;

    @ApiModelProperty(value = "支付方式名称")
    @Size(max = 30, message = "支付方式名称不能超过30个字符")
    private String paidModeName;

    @ApiModelProperty(value = "支付方式code")
    @Size(max = 30, message = "支付方式code不能超过30个字符")
    private String paidModeCode;

    @ApiModelProperty(value = "寄件详细地址")
    @Size(max = 200, message = "寄件详细地址不能超过200个字符")
    private String senderDetailedAddress;

    @ApiModelProperty(value = "收件人公司")
    @Size(max = 100, message = "收件人公司不能超过100个字符")
    private String receiverCompany;

    @ApiModelProperty(value = "收件人手机号")
    @Size(max = 30, message = "收件人手机号不能超过30个字符")
    private String receiverMobilePhone;

    @ApiModelProperty(value = "收件国家名称")
    @Size(max = 30, message = "收件国家名称不能超过30个字符")
    private String receiverCountryName;

    @ApiModelProperty(value = "寄件服务方式名称")
    @NotBlank(message = "寄件服务方式名称不能为空", groups = {SaveGroup.class})
    @Size(max = 30, message = "寄件服务方式名称不能超过30个字符")
    private String sendName;

    @ApiModelProperty(value = "寄件服务方式code")
    @NotBlank(message = "寄件服务方式code不能为空", groups = {SaveGroup.class})
    @Size(max = 30, message = "寄件服务方式code不能超过30个字符")
    private String sendCode;

    @ApiModelProperty(value = "快件类型名称")
    @Size(max = 30, message = "快件类型名称不能超过30个字符")
    private String expressTypeName;

    @ApiModelProperty(value = "快件状态code")
    private Integer expressStatusCode;

    @ApiModelProperty(value = "快件状态名称")
    private String expressStatusName;

    @ApiModelProperty(value = "需要保价1是，0否")
    @Min(value = 0, message = "需要保价只能为0或者1")
    @Max(value = 1, message = "需要保价只能为0或者1")
    @NotNull(message = "需要保价只能为0或者1")
    private Integer insured;

    @ApiModelProperty(value = "保价金额")
    private String declaredValue;

    @ApiModelProperty(value = "保价费")
    private String insuredValue;

    @ApiModelProperty(value = "派件服务方式名称")
    @NotBlank(message = "派件服务方式名称不能为空", groups = {SaveGroup.class})
    @Size(max = 30, message = "派件服务方式名称不能超过30个字符")
    private String dispatchName;

    @ApiModelProperty(value = "派件服务方式code")
    @NotBlank(message = "派件服务方式code名称不能为空", groups = {SaveGroup.class})
    @Size(max = 30, message = "派件服务方式code不能超过30个字符")
    private String dispatchCode;

    @ApiModelProperty(value = "标准运费")
    @Digits(integer = 10, fraction = 2, message = "标准运费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal standardValue;

    @ApiModelProperty(value = "总费用")
    @Digits(integer = 10, fraction = 2, message = "总费用超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal totalFreight;

    @ApiModelProperty(value = "代收货款手续费")
    @Digits(integer = 10, fraction = 2, message = "代收货款手续费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal codFee;

    @ApiModelProperty(value = "包材规格")
    @Size(max = 30, message = "包材规格不能超过30个字符")
    private String boxStandardName;

    @ApiModelProperty(value = "包材数量")
    private Integer boxNumber;

    @ApiModelProperty(value = "包材规格id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long boxStandardId;

    @ApiModelProperty(value = "包材规格code")
    private String boxStandardCode;

    @ApiModelProperty(value = "包材单价")
    @Digits(integer = 10, fraction = 2, message = "包材单价超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal boxPrice;

    @ApiModelProperty(value = "代收货款币别名称")
    @Size(max = 30, message = "代收货款币别名称不能超过30个字符")
    private String codCurrencyTypeName;

    @ApiModelProperty(value = "代收货款币别code")
    @Size(max = 30, message = "代收货款币别编码不能超过30个字符")
    private String codCurrencyTypeCode;

    @ApiModelProperty(value = "物品类型名称")
    @Size(max = 30, message = "物品类型名称不能超过30个字符")
    @NotBlank(message = "物品类型名称不能为空")
    private String goodsTypeName;

    @ApiModelProperty(value = "物品类型id")
    private Integer goodsTypeId;

    @ApiModelProperty(value = "物品类型code")
    @Size(max = 30, message = "物品类型编码不能超过30个字符")
    @NotBlank(message = "物品类型编码不能为空")
    private String goodsTypeCode;

    @ApiModelProperty(value = "物品名称")
    private String goodsName;

    @ApiModelProperty(value = "件数,＞1表示子母件，如果业务上不支持子母件，则前端限制不显示，并默认为1")
    @Min(value = 1, message = "包裹数量不能小于1")
    @Max(value = 999, message = "包裹数量不能超过999")
    private Integer packageNumber;

    @ApiModelProperty(value = "包裹总长,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹总长超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal packageLength;

    @ApiModelProperty(value = "包裹总宽,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹总宽超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal packageWide;

    @ApiModelProperty(value = "包裹总高,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹总高超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal packageHigh;

    @ApiModelProperty(value = "包裹体积重,单位立方厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹体积重超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal packateVolume;

    @ApiModelProperty(value = "包裹计费重量,单位千克")
    @Digits(integer = 5, fraction = 2, message = "包裹计费重量,超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal packageChargeWeight;

    @ApiModelProperty(value = "包裹总重量,单位千克")
    @Digits(integer = 5, fraction = 2, message = "包裹总重量超出了允许范围(只允许在5位整数和2位小数范围内)")
    @DecimalMin(value = "0.01", message = "包裹总重量必须大于或等于0.01")
    private BigDecimal packageTotalWeight;

    @ApiModelProperty(value = "结算方式名称")
    @Size(max = 30, message = "结算方式名称不能超过30个字符")
    @NotBlank(message = "结算方式名称不能为空")
    private String paymentModeName;

    @ApiModelProperty(value = "结算方式code")
    @Size(max = 30, message = "结算方式编码不能超过30个字符")
    @NotBlank(message = "结算方式编码不能为空")
    private String paymentModeCode;

    /**
     * 结算重量
     */
    @ApiModelProperty(name = "settlementWeight", value = "结算重量")
    private BigDecimal settlementWeight;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "客户编号code")
    @Size(max = 30, message = "客户编号不能超过30个字符")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    @Size(max = 30, message = "客户名称不能超过30个字符")
    private String customerName;

    @ApiModelProperty(value = "结算货币类型")
    private String spmMoneyType;

    @ApiModelProperty(value = "客户下单时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime customerOrderTime;

    @ApiModelProperty(value = "取消订单原因")
    private String cancelReason;

    /**取消原因**/
    private String cancelExplain;

    @ApiModelProperty(value = "订单取消时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancelTime;

    @ApiModelProperty(value = "取件失败时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime pickFailTime;

    @ApiModelProperty(value = "取件失败原因")
    private String pickFailReason;

    @ApiModelProperty(value = "打印次数")
    private Integer printsNumber=0;

    @ApiModelProperty(value = "实际取件网点名称")
    private String realPickNetworkName;

    @ApiModelProperty(value = "实际取件网点code")
    private String realPickNetworkCode;

    @ApiModelProperty(value = "调度网点时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime dispatchNetworkTime;

    @ApiModelProperty(value = "取件网点名称")
    private String pickNetworkName;

    @ApiModelProperty(value = "取件网点code")
    private String pickNetworkCode;

    @ApiModelProperty(value = "取件网点code")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime dispatchStaffTime;

    @ApiModelProperty(value = "取件业务员名称")
    private String pickStaffName;

    @ApiModelProperty(value = "取件业务员code")
    private String pickStaffCode;

    @ApiModelProperty(value = "取件时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime pickTime;

    @ApiModelProperty(value = "上一次调度网点名称")
    private String lastDispatchNetworkName;

    @ApiModelProperty(value = "上一次调度网点code")
    private String lastDispatchNetworkCode;

    @ApiModelProperty(value = "上一次调度网点时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime lastDispatchNetworkTime;

    @ApiModelProperty(value = "调度网点原因")
    private String dispatchNetworkReason;

    @ApiModelProperty(value = "上一次调度业务员名称")
    private String lastDispatchStaffName;

    @ApiModelProperty(value = "上一次调度业务员code")
    private String lastDispatchStaffCode;

    @ApiModelProperty(value = "上一次调度业务员时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime lastDispatchStaffTime;

    @ApiModelProperty(value = "打回调度订单原因")
    private String backDispatchNetworkReason;

    @ApiModelProperty(value = "打回调度订单次数")
    private Integer backDispatchNetworkNumber;

    @ApiModelProperty(value = "打回调度订单时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime backDispatchNetworkTime;

    @ApiModelProperty(value = "物品价值")
    private BigDecimal goodsValue;

    @ApiModelProperty(value = "寄件人邮箱")
    private String senderEmail;

    @ApiModelProperty(value = "收件人邮箱")
    private String receiverEmail;

    @ApiModelProperty(value = "订单物品详情信息")
    private List<OmsOrderMemberVO> items;

    @ApiModelProperty(value = "取件网点id")
    private Long pickNetworkId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime inputTime;

    private Long lastDispatchNetworkId;

    private Integer isDelete;

    private Integer createBy;

    private String createByCode;

    private String createByName;

    private Integer updateBy;

    private String updateByName;

//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;

    /*
    * 订单表没有包材费

    private BigDecimal packageCost;
    */

    /**
     * 始发地id
     */
    @ApiModelProperty(value = "始发地id")
    private Integer originId;

    /**
     * 始发地code
     */
    @ApiModelProperty(value = "始发地code")
    private String originCode;

    /**
     * 始发地名字
     */
    @ApiModelProperty(value = "始发地名字")
    private String originName;

    /**
     * 目的地id
     */
    @ApiModelProperty(value = "目的地id")
    private Integer destinationId;

    /**
     * 目的地code
     */
    @ApiModelProperty(value = "目的地code")
    private String destinationCode;

    /**
     * 目的地名字
     */
    @ApiModelProperty(value = "目的地名字")
    private String destinationName;

    /**
     * 三段码
     **/

    private String terminalDispatchCode;

    private Integer memberId;

    private Integer dispatchNetworkId;

    /**代理区code**/
    private String proxyAreaCode;
    /**代理区name**/
    private String proxyAreaName;

    @ApiModelProperty(value = "调度代理区时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime dispatchProxyAreaTime;

    private boolean isRepeat;
    /***
     * 第三方运单号
     */
    private  String  customerWaybillNo;
    /**
     * 打印时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime printTime;

    @ApiModelProperty(value = "是否是商务件 1是 2不是")
    private Integer isBusiness;

    @ApiModelProperty(value = "商务件")
    private String isBusinessName;


    private OmsOrderThirdExtVO omsOrderThirdExtVO;

    /**
     * 订单异常信息
     */
    private String exceptionMsg;
    /**
     * 二段码命中原因
     */
    private String secondCodeHitDesc;

    /** 签回单 0否   1是 2回单标记 */
    private Integer signReceipt;

    /**
     * 回单费用
     */
    private BigDecimal receiptFreight;

    /**
     * 集包地编码
     */
    private String lastCenterCode;

    /**
     * 集包地名称
     */
    private String lastCenterName;

    @ApiModelProperty(value = "是否转寄(1否 2是)")
    private Integer isTransfer;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 证件号码
     */
    private String idNo;

    /**
     * 证件类型 DocumentTypeEnum
     */
    private Integer idNoType;

    /**
     * 1男,0女
     */
    private Integer sex;

    /** 隐私标记; 1:是, 0:否 */
    private Integer isPrivacy;

    /** 快递员手机号 */
    private String staffPhone;

    /** 是否需要调度 1需要 2不需要*/
    private Integer needDispatch;

    /** 子运单号 */
    private String subWaybillIds;

    /**
     * 签回单单号
     */
    private String receiptWaybillNo;

    @ApiModelProperty(value = "取件失败原因code")
    private String pickFailReasonCode;

    @ApiModelProperty(value = "取件失败网点id")
    private Long pickFailNetworkId;
    @ApiModelProperty(value = "取件失败网点code")
    private String pickFailNetworkCode;
    @ApiModelProperty(value = "取件失败网点name")
    private String pickFailNetworkName;
    @ApiModelProperty(value = "取件失败业务员id")
    private Integer pickFailStaffId;
    @ApiModelProperty(value = "取件失败业务员code")
    private String pickFailStaffCode;
    @ApiModelProperty(value = "取件失败业务员name")
    private String pickFailStaffName;
    private Integer ifTransfer;
    private String ifTransferStr;
    /**
     * 退转件人姓名
     */
    private String transferName;

    private String transferCompany;

    /**
     * 退转件人联系电话
     */
    private String transferMobilePhone;
    /**
     *退转件省份名称
     */
    private String transferProvinceName;
    /**
     *退转件省份id
     */
    private Integer transferProvinceId;
    /**
     *退转件城市名称
     */
    private String transferCityName;
    /**
     *收件城市id
     */
    private Integer transferCityId;
    /**
     *退转件区域名称
     */
    private String transferAreaName;
    /**
     *退转件区域id
     */
    private Integer transferAreaId;

    private String transferTownshipName;

    private Integer transferTownshipId;

    private String transferPostalCode;

    /**
     *退转件详细地址
     */
    private String transferDetailedAddress;

    private String transferDispatchCode;
}
