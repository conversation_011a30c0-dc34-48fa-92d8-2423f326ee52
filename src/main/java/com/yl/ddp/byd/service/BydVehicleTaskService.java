package com.yl.ddp.byd.service;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yl.ddp.byd.config.BydProper;
import com.yl.ddp.byd.utils.SignUtil;
import com.yl.ddp.common.util.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BydVehicleTaskService {

    @Autowired
    private BydProper bydProper;

    public void vehicleTaskSignIn(String msg) {
        String requestId = ThreadLocalUtil.getRequestId();
        Long timestamp = System.currentTimeMillis();
        JSONObject dataObject = JSONObject.parseObject(msg);
        JSONArray dataArray = new JSONArray();
        dataArray.add(dataObject);

        String rawData = dataArray.toJSONString() + bydProper.getSys() + timestamp + bydProper.getSalt();
        String sign = SignUtil.sha256Hex(rawData);
        JSONObject params = new JSONObject();
        params.put("data", dataArray.toJSONString());
        params.put("sign", sign);
        params.put("timestamp", timestamp);
        params.put("sys", bydProper.getSys());
        log.info("请求BYD参数：{}，{}", requestId, params.toJSONString());
        HttpResponse execute = HttpUtil.createPost(bydProper.getUrl())
                .header("x-Gateway-APIKey", bydProper.getApiKey())
                .body(params.toJSONString())
                .execute();
        log.info("请求BYD响应结果：{}，状态码：{}，body：{}", requestId, execute.getStatus(), execute.body());
    }

}
