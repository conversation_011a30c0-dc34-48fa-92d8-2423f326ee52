package com.yl.ddp.forward.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yl.common.base.model.vo.Result;
import com.yl.ddp.common.util.JWTUtils;
import com.yl.ddp.forward.constant.Constant;
import com.yl.ddp.forward.feign.OpenPlatformFeign;
import com.yl.ddp.forward.vo.DdpForwardVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping(Constant.externalControllerRequestMapping)
public class ForwardExternalController {


    @Autowired
    private OpenPlatformFeign openPlatformFeign;

    @PostMapping("/externalArray/**")
    public Result<Object> catchExternalPost(@RequestBody JSONArray jsonArray, HttpServletRequest request) {
        DdpForwardVo ddpForwardVo = new DdpForwardVo();
        String requestURI = request.getRequestURI().replaceAll("/+", "/");
        String code = requestURI.split("externalArray/")[1];
        ddpForwardVo.setData(jsonArray);
        ddpForwardVo.setServiceCode(code);
        return request(ddpForwardVo);
    }

    @PostMapping("/externalObj/**")
    public Result<Object> catchExternalPost(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        DdpForwardVo ddpForwardVo = new DdpForwardVo();
        String requestURI = request.getRequestURI().replaceAll("/+", "/");
        String code = requestURI.split("externalObj/")[1];
        ddpForwardVo.setData(jsonObject);
        ddpForwardVo.setServiceCode(code);
        return request(ddpForwardVo);
    }


    private Result<Object> request(DdpForwardVo ddpForwardVo){
        try {
            return openPlatformFeign.external(ddpForwardVo, JWTUtils.openSign());
        } catch (Exception e) {
            log.error("openPlatformFeign.external接口请求异常默认重试一次：", e);
            try {
                return openPlatformFeign.external(ddpForwardVo, JWTUtils.openSign());
            } catch (Exception ex) {
                log.error("openPlatformFeign.external接口重试失败：", ex);
                throw ex;
            }
        }
    }


}
