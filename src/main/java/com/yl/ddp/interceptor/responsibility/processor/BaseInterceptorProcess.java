package com.yl.ddp.interceptor.responsibility.processor;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.ddp.interceptor.responsibility.base.PathConstants;
import com.yl.ddp.interceptor.responsibility.base.Payload;
import com.yl.ddp.interceptor.responsibility.chain.HandleChain;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Component
// 当前类不能加Intercept注解，容器中以手动添加
public class BaseInterceptorProcess extends InterceptorHandlerAbstract {

    @Override
    public boolean preHandle(Payload payload, HandleChain handleChain) throws BusinessException {
        HttpServletRequest request = payload.getRequest();
        String requestURI = request.getRequestURI().replaceAll("/+", "/");
        if (!match(requestURI)) {
            log.info("基础拦截器开始执行 开始执行 ，处理不了抛异常:{}", payload.getUuid());
            throw new BusinessException(ResultCodeEnum.UNAUTHORIZED);
        }
        log.info("preHandle 基础拦截器开始执行 UUID:{}", payload.getUuid());
        return true;
    }

    @Override
    public void afterCompletion(Payload payload, HandleChain handleChain) throws Exception {
        log.info("afterCompletion 基础拦截器开始执行 UUID:{}", payload.getUuid());
    }

    @Override
    public void postHandle(Payload payload, HandleChain handleChain) throws Exception {
//        log.info("postHandle 基础拦截器开始执行 UUID:{}", payload.getUuid());
    }

    @Override
    String pattern() {
        return PathConstants.BASE_PATH;
    }
}
