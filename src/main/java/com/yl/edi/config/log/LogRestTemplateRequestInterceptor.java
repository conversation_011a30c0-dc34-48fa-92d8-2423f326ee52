package com.yl.edi.config.log;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title LogRestTemplateRequestInterceptor
 * @Package com.log.demo.http
 * @Description 记录请求
 * @date 2021/11/25 7:03 下午
 */
@Slf4j
public class LogRestTemplateRequestInterceptor implements ClientHttpRequestInterceptor {

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        long start = System.nanoTime();
        ClientHttpResponse response = new LogClientHttpResponseWrapper(execution.execute(request, body));
        long elapsedTime = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - start);
        // 打印日志
        trace(request, body, response, elapsedTime);
        return response;
    }

    private void trace(HttpRequest request, byte[] body, ClientHttpResponse response, long elapsedTime) throws IOException {
        try {
            // 记录日志
            String responseStr = IOUtils.toString(response.getBody(), StandardCharsets.UTF_8);
            StringBuilder stringBuilder = new StringBuilder("\n");
            stringBuilder.append("URI : {}").append("\n");
            stringBuilder.append("Method : {}").append("\n");
            stringBuilder.append("Headers : {}").append("\n");
            stringBuilder.append("Payload : {}").append("\n");
            stringBuilder.append("RespStatus : {} , {}ms").append("\n");
            stringBuilder.append("RespHeaders : {}").append("\n");
            stringBuilder.append("Response : {}").append("\n");
            log.info(stringBuilder.toString(), request.getURI(),
                    request.getMethod(), request.getHeaders(), new String(body, StandardCharsets.UTF_8),
                    response.getStatusCode(), elapsedTime, response.getHeaders(), responseStr);
        } catch (Exception e) {
            log.warn("logging rest-template error:{}", e);
        }
    }

}