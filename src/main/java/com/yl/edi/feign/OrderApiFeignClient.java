package com.yl.edi.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.model.vo.Result;
import com.yl.edi.dto.EdiAddressMapQueryDTO;
import com.yl.edi.dto.OmsOrderApiDTO;
import com.yl.edi.dto.OmsOrderExtInfoDTO;
import com.yl.edi.feign.fallback.OrderApiFeignClientFallbackFactory;
import com.yl.edi.vo.EdiAddressMapVO;
import com.yl.edi.vo.FeignResultVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title OrderApiFeignClient
 * @Package com.yl.edi.feign
 * @Description
 * @date 2021/10/26 7:24 下午
 */
@FeignClient(name = "ylordera<PERSON>", path = "/orderapi", fallbackFactory = OrderApiFeignClientFallbackFactory.class)
public interface OrderApiFeignClient {

    /**
     * @param orderApiDTO
     * @return : com.yl.edi.vo.FeignResultVO
     * <AUTHOR> mz
     * @Description : 更新订单
     * @Date : 4:38 下午 2021/10/29
     */
    @PostMapping("/edi/omsOrder/shopeeUpdate")
    FeignResultVO update(@RequestBody OmsOrderApiDTO orderApiDTO);


    /**
     * @param orderApiDTO
     * @return : com.yl.edi.vo.FeignResultVO
     * <AUTHOR> mz
     * @Description : 取消订单
     * @Date : 4:39 下午 2021/10/29
     */
    @PostMapping("/edi/omsOrder/shopeeCancel")
    FeignResultVO cancel(@RequestBody OmsOrderApiDTO orderApiDTO);

    /**
     * @param queryParam
     * @param
     * @return : com.yl.common.base.model.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.yl.customerplatform.vo.EdiAddressMapVO>>
     * <AUTHOR> mz
     * @Description : 分页查询(正向)
     * @Date : 5:58 下午 2021/6/14
     */
    @PostMapping(value = "/address/map/page")
    Result<Page<EdiAddressMapVO>> queryForPage(@RequestBody EdiAddressMapQueryDTO queryParam);

    @PostMapping(value = "/edi/omsOrder/addOrderExtInfo")
    public Result<Boolean> addOrderExtInfo(@RequestBody OmsOrderExtInfoDTO omsOrderExtInfoDTO);

    @PostMapping("/omsOrder/customer/orderId")
    Result<List<OmsOrderApiDTO>> getOrderByCustomerOrderId(@RequestBody OmsOrderApiDTO omsOrderApiDTO);


}