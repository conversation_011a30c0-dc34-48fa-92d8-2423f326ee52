package com.yl.edi.service;

import com.yl.edi.dto.AddressDTO;
import com.yl.edi.dto.BillCodeGetResultDTO;
import com.yl.edi.dto.OmsOrderApiDTO;
import com.yl.edi.dto.OrderSendDTO;
import com.yl.edi.vo.SegmentCodeResponseVo;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title OrderEntityConvertService
 * @Package com.yl.edi.service
 * @Description
 * @date 2021/10/29 11:12 上午
 */
public interface OrderEntityConvertService {

    /**
     * @param sendDTO
     * @param billCodeNos
     * @param dispatchCodeVO
     * @param receiverAddress
     * @param senderAddress
     * @return : com.yl.edi.dto.OmsOrderApiDTO
     * <AUTHOR> mz
     * @Description : 转换添加订单实体
     * @Date : 3:56 下午 2021/10/29
     */
    OmsOrderApiDTO convertForAdd(OrderSendDTO sendDTO, BillCodeGetResultDTO billCodeNos, SegmentCodeResponseVo dispatchCodeVO,
                                 AddressD<PERSON> receiverAddress, AddressDTO senderAddress);

    /**
     * @param sendDTO
     * @param dispatchCodeVO
     * @param receiverAddress
     * @param senderAddress
     * @return : com.yl.edi.dto.OmsOrderApiDTO
     * <AUTHOR> mz
     * @Description : 转换更新实体
     * @Date : 4:34 下午 2021/10/29
     */
    OmsOrderApiDTO convertForUpdate(OrderSendDTO sendDTO, SegmentCodeResponseVo dispatchCodeVO, AddressDTO receiverAddress,
                                    AddressDTO senderAddress);

}