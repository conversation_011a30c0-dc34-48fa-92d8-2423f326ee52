package com.yl.edi.service.impl;

import com.alibaba.fastjson.JSON;
import com.yl.edi.dto.AddressDTO;
import com.yl.edi.enums.ReasonCodeEnum;
import com.yl.edi.exception.BaseBusinessException;
import com.yl.edi.feign.LmdmFeignClient;
import com.yl.edi.service.AddressService;
import com.yl.edi.vo.FeignResultVO;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title AddressServiceImpl
 * @Package com.yl.edi.service.impl
 * @Description
 * @date 2021/12/7 3:55 下午
 */
@Service
public class AddressServiceImpl implements AddressService {


    @Autowired
    private RedisTemplate redisTemplate;

    private static final String CACHE_KEY = "EDI:JFS:SHOPEE:NETWORK:ADDRESS";

    private static final String NETWORK_MODIFY_TIP = "EDI:JFS:SHOPEE:NETWORK:MODIFY:TIP";

    @Autowired
    private LmdmFeignClient lmdmFeignClient;

    public void init() {
        redisTemplate.opsForValue().set(NETWORK_MODIFY_TIP, Boolean.FALSE);
    }


    /**
     * @param networkCode
     * @return : com.yl.edi.dto.AddressDTO
     * <AUTHOR> mz
     * @Description : 根据网点编码获取网点省市区数据
     * @Date : 4:02 下午 2021/12/7
     */
    @Override
    public AddressDTO getAddressByNetworkCode(String networkCode) {
        if (StringUtils.isBlank(networkCode)) {
            return new AddressDTO();
        }
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        String s = hashOperations.get(CACHE_KEY, networkCode);
        if (StringUtils.isNotBlank(s)) {
            return JSON.parseObject(s, AddressDTO.class);
        }
        FeignResultVO<AddressDTO> feignResultVO = lmdmFeignClient.queryNetworkAddress(networkCode);
        if (!feignResultVO.getCode().equals(NumberUtils.INTEGER_ONE)) {
            throw new BaseBusinessException(ReasonCodeEnum.SYSTEM_FAIL);
        }
        AddressDTO data = feignResultVO.getData();
        if (Objects.isNull(feignResultVO.getData())) {
            data = new AddressDTO();
        }
        hashOperations.put(CACHE_KEY, networkCode, JSON.toJSONString(data));
        return data;
    }


    /**
     * <AUTHOR> mz
     * @Description : 重置网点地址缓存变更标识
     * @Date : 4:39 下午 2021/12/7
     */
    @Override
    public void restNetworkAddressCache() {
        redisTemplate.opsForValue().set(NETWORK_MODIFY_TIP, Boolean.TRUE);
    }

    /**
     * <AUTHOR> mz
     * @Description : 定时任务检查缓存是否有修改 # 使用此方法防止 基础数据 短时间内频繁变更 导致 redis 短时间内来回的删除数据
     * @Date : 5:26 下午 2021/12/7
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void loopCheckStateAndDeleteCache() {
        ValueOperations<String, Boolean> valueOperations = redisTemplate.opsForValue();
        Boolean aBoolean = ObjectUtils.defaultIfNull(valueOperations.get(NETWORK_MODIFY_TIP), Boolean.TRUE);
        if (aBoolean) {
            redisTemplate.delete(CACHE_KEY);
            redisTemplate.opsForValue().set(NETWORK_MODIFY_TIP, Boolean.FALSE);
        }
    }

}