package com.yl.edi.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-01-07 15:49
 */
@Slf4j
public class SignUtils {

    public static String generateDigest(String plainText) {
        try {
            return Base64.encodeBase64URLSafeString(getSha512(plainText));
        } catch (Exception e) {
            log.error("签名异常", e);
        }
        return null;
    }

    public static byte[] getMD5(String plainText) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(plainText.getBytes("UTF-8"));
            return md5.digest();
        } catch (UnsupportedEncodingException | NoSuchAlgorithmException e) {
            log.error("签名异常", e);
        }
        return null;
    }

    public static byte[] getSha512(String plainText) {
        try {
            MessageDigest sha512 = MessageDigest.getInstance("SHA-512");
            sha512.update(plainText.getBytes("UTF-8"));
            return sha512.digest();
        } catch (UnsupportedEncodingException | NoSuchAlgorithmException e) {
            log.error("签名异常", e);
        }
        return null;
    }

    public static void main(String[] args) {
        // http://shopee.jtcargo.co.id/jfsshopeeorder/order/send
        // sign
        // timestamp
        long millis = System.currentTimeMillis();
//        String json = "{\"notify_list\":[{\"carrier_tn\":\"200004122662\",\"instruction\":1,\"notify_event\":\"lost\",\"reference_no\":\"ID2544641077544T\"}]}";
        String json = "{\"codValue\":0,\"cogsValue\":300000,\"customerId\":\"J0086024424\",\"expressType\":\"EZ\",\"goods\":[{\"category\":\"Souvenir & Party Supplies\",\"name\":\"pesanan\"}],\"goodsValue\":300000,\"isSchedule\":true,\"operations\":2,\"orderCode\":\"ID1751350231458\",\"priceCurrency\":\"IDR\",\"receiver\":{\"address\":\"Jalan Kampung Citundun Kempeng, RT.11/RW.3, Kp Citundun Kempeng, Citundun, Warunggunung, KAB. LEBAK, WARUNGGUNUNG, BANTEN, ID, 42352\",\"city\":\"KOTA CIMAHI\",\"district\":\"CIMAHI SELATAN\",\"mobile\":\"6287******1264\",\"name\":\"M***n\",\"phone\":\"6287******264\",\"postCode\":\"42352\",\"province\":\"JAWA BARAT\"},\"sender\":{\"address\":\"Jl panglima sudirman Sukolilo gg1 no 97 rt 2 rw 1, TUBAN, KAB. TUBAN, JAWA TIMUR\",\"city\":\"KAB. BANGLI\",\"district\":\"SUSUT\",\"mobile\":\"62856******458\",\"name\":\"o****n\",\"phone\":\"628******30458\",\"postCode\":\"62318\",\"province\":\"BALI\"},\"serviceType\":\"dropoff\",\"settlementType\":1,\"taxAmount\":0,\"totalQuantity\":5,\"weight\":86, \"billNo\":\"200002384127\"}";
//        String json = "{\"codValue\":0,\"cogsValue\":300000,\"customerId\":\"J0086024424\",\"expressType\":\"EZ\",\"goods\":[{\"category\":\"Souvenir & Party Supplies\",\"name\":\"pesanan\"}],\"goodsValue\":300000,\"isSchedule\":true,\"operations\":1,\"orderCode\":\"yn9999942001\",\"priceCurrency\":\"IDR\",\"receiver\":{\"address\":\"Jalan Kampung Citundun Kempeng, RT.11/RW.3, Kp Citundun Kempeng, Citundun, Warunggunung, KAB. LEBAK, WARUNGGUNUNG, BANTEN, ID, 42352\",\"city\":\"KOTA CIMAHI\",\"district\":\"CIMAHI SELATAN\",\"mobile\":\"6287******1264\",\"name\":\"Maman\",\"phone\":\"6287******264\",\"postCode\":\"42352\",\"province\":\"JAWA BARAT\"},\"sender\":{\"address\":\"Jl panglima sudirman Sukolilo gg1 no 97 rt 2 rw 1, TUBAN, KAB. TUBAN, JAWA TIMUR\",\"city\":\"KAB. BANGLI\",\"district\":\"SUSUT\",\"mobile\":\"62856******458\",\"name\":\"otibun3t4n\",\"phone\":\"628******30458\",\"postCode\":\"62318\",\"province\":\"BALI\"},\"serviceType\":\"dropoff\",\"settlementType\":1,\"taxAmount\":0,\"totalQuantity\":1,\"weight\":86,\"Sys_ID\":\"0MDRJxkJVN0N0kDOwMzNzQDMzIDRJB\"}";
        StringBuilder stringBuilder = new StringBuilder("body=");
        String signPlainText = stringBuilder.append(json).append("&").append("key=")
                .append("test")
//                .append("GZHwn63uBDpl")
                .append("&").append("timestamp=").append(millis).toString();
        System.out.println(SignUtils.generateDigest(signPlainText));

        System.out.println(millis);
        System.out.println(json);
    }

}
