package com.yl.edi.wms.common.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class JwtUtils {

    public static String sign(String account, String key, Map<String, Object> map) {
        Map<String, Object> accountMap = new HashMap<>();
        accountMap.put("account", account);
        JWTCreator.Builder builder = JWT.create();
        builder.withHeader(accountMap).withPayload(map);
        return builder.sign(Algorithm.HMAC256(key));

    }

    public static String sign(Map<String, Object> header, String key, Map<String, Object> map) {
        JWTCreator.Builder builder = JWT.create();
        builder.withHeader(header).withPayload(map);
        return builder.sign(Algorithm.HMAC256(key));

    }

    public static String sign(String key, Map<String, Object> map) {
        JWTCreator.Builder builder = JWT.create();
        builder.withPayload(map);
        return builder.sign(Algorithm.HMAC256(key));
    }

    public static String sign(Long userId, String secret) {
        Date nowDate = new Date();
        //过期时间
        Date expireDate = new Date(nowDate.getTime() + 1000L * 60L * 10);
        return JWT.create().withClaim("userId", userId)
                .withIssuedAt(nowDate)
                .withExpiresAt(expireDate)
                .sign(Algorithm.HMAC512(secret));
    }

    public static String ddpSign() {
        return sign(1L,"WgtqaT1HNTZPZNMDJu3k12k");
    }



    public static void main(String[] args) {
        System.out.println(sign(1L, "WgtqaT1HNTZPZNMDJu3k12k"));
    }

}
