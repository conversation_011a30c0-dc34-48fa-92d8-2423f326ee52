package com.yl.intelligent.dispatch.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.intelligent.dispatch.entity.Courier30daysStats;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

public interface Courier30daysStatsService extends IService<Courier30daysStats> {

    /**
     * 查询寄件人维度的30天汇总原始数据
     *
     * @param statDate  统计日期
     * @param startDate 开始日期
     * @return 游标对象，支持流式处理
     */
    Cursor<Courier30daysStats> querySender30DaysStatsData(@Param("statDate") String statDate, @Param("startDate") String startDate);

    /**
     * 查询寄件区域维度的30天汇总原始数据
     *
     * @param statDate  统计日期
     * @param startDate 开始日期
     * @return 游标对象，支持流式处理
     */
    Cursor<Courier30daysStats> queryArea30DaysStatsData(@Param("statDate") String statDate, @Param("startDate") String startDate);

    /**
     * 流式查询推荐数据
     *
     * @param statDate 统计日期
     * @param infoType 信息类型(1:寄件人维度，2:寄件区域维度)
     * @param rank     排名(通常为1)
     * @param isActive 是否活跃(通常为1)
     * @return 游标对象，支持流式处理
     */
    Cursor<Courier30daysStats> queryRecommendByCursor(String statDate, Integer infoType, Integer rank, Integer isActive);

    /**
     * 更新排名
     *
     * @param statDate 统计日期
     * @param infoType 信息类型
     * @return 影响的行数
     */
    int updateRank(@Param("statDate") String statDate, @Param("infoType") Integer infoType);

    /**
     * 更新近5天活跃状态
     *
     * @param statDate   统计日期
     * @param activeDate 活跃判断日期（5天前）
     * @return 影响的行数
     */
    int updateActiveStatus(@Param("statDate") String statDate, @Param("activeDate") String activeDate);

}
