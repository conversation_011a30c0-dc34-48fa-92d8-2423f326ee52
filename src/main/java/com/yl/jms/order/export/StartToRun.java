
package com.yl.jms.order.export;

import com.yl.jms.order.export.service.IOmsOrderExportService;
import com.yl.platform.i18n.helper.YlI18nHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-07-22 16:08
 */

@Component
@Slf4j
public class StartToRun implements CommandLineRunner {

    @Autowired
    private IOmsOrderExportService orderExportService;


    @Override
    public void run(String... strings) throws Exception {
    /*    SA sa = new SA();
        sa.setCode("test");
        sa.setName("sxg");
        sa.setSiteCode("888888");
        sa.setSiteName("总部");
//        PlatformOrderPickRateStatisDTO rateStatisDTO = new PlatformOrderPickRateStatisDTO();
        OmsOrderQueryDTO dto = new OmsOrderQueryDTO();
        dto.setSa(sa);
        dto.setStartDate("2021-03-06 00:00:00");
        dto.setEndDate("2021-03-12 15:15:17");
        dto.setSiteCode("320001");
        dto.setExportJson(json);
        orderExportService.createAndUploadDocument(dto);*/
    }

    private String json = "[   {     \"name\": \"id\",     \"column\": \"订单号\",     \"show\": true,     \"position\": null   },   {     \"name\": \"orderStatusName\",     \"column\": \"订单状态\",     \"show\": true,     \"position\": null   },   {     \"name\": \"orderSourceName\",     \"column\": \"订单来源\",     \"show\": true,     \"position\": null   },   {     \"name\": \"sendName\",     \"column\": \"寄件服务方式\",     \"show\": true,     \"position\": null   },   {     \"name\": \"pickNetworkName\",     \"column\": \"取件网点\",     \"show\": true,     \"position\": null   },   {     \"name\": \"pickStaffName\",     \"column\": \"收派员\",     \"show\": true,     \"position\": null   },   {     \"name\": \"customerName\",     \"column\": \"客户信息\",     \"show\": true,     \"position\": null   },   {     \"name\": \"inputTime\",     \"column\": \"订单录入时间\",     \"show\": true,     \"position\": null   },   {     \"name\": \"waybillId\",     \"column\": \"运单号\",     \"show\": true,     \"position\": null   },   {     \"name\": \"paymentModeName\",     \"column\": \"结算方式\",     \"show\": true,     \"position\": null   },   {     \"name\": \"codNeed\",     \"column\": \"需要代收货款\",     \"show\": true,     \"position\": null   },   {     \"name\": \"senderPostalCode\",     \"column\": \"寄件邮编\",     \"show\": true,     \"position\": null   },   {     \"name\": \"senderProvinceName\",     \"column\": \"寄件省份\",     \"show\": true,     \"position\": null   },   {     \"name\": \"senderCityName\",     \"column\": \"寄件城市\",     \"show\": true,     \"position\": null   },   {     \"name\": \"senderAreaName\",     \"column\": \"寄件区域\",     \"show\": true,     \"position\": null   },   {     \"name\": \"senderDetailedAddress\",     \"column\": \"寄件详细地址\",     \"show\": true,     \"position\": null   },   {     \"name\": \"senderName\",     \"column\": \"寄件人\",     \"show\": true,     \"position\": null   },   {     \"name\": \"senderMobilePhone\",     \"column\": \"寄件人手机\",     \"show\": true,     \"position\": null   },   {     \"name\": \"terminalDispatchCode\",     \"column\": \"三段码\",     \"show\": true,     \"position\": null   },   {     \"name\": \"phoneContact\",     \"column\": \"电联标识\",     \"show\": true,     \"position\": null   },   {     \"name\": \"orderTypeName\",     \"column\": \"订单类型\",     \"show\": true,     \"position\": null,     \"isShow\": false   },   {     \"name\": \"customerOrderId\",     \"column\": \"客户订单编号\",     \"show\": false,     \"position\": null   },   {     \"name\": \"senderCompany\",     \"column\": \"寄件人公司\",     \"show\": false,     \"position\": null   },   {     \"name\": \"senderTelphone\",     \"column\": \"寄件人座机\",     \"show\": false,     \"position\": null   },   {     \"name\": \"senderCountryName\",     \"column\": \"寄件国家\",     \"show\": false,     \"position\": null   },   {     \"name\": \"senderTownship\",     \"column\": \"寄件乡镇\",     \"show\": false,     \"position\": null   },   {     \"name\": \"senderStreet\",     \"column\": \"寄件街道\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverName\",     \"column\": \"收件人\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverCompany\",     \"column\": \"收件人公司\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverMobilePhone\",     \"column\": \"收件人手机\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverTelphone\",     \"column\": \"收件人座机\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverCountryName\",     \"column\": \"收件国家\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverProvinceName\",     \"column\": \"收件省份\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverCityName\",     \"column\": \"收件城市\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverAreaName\",     \"column\": \"收件区域\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverTownship\",     \"column\": \"收件乡镇\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverStreet\",     \"column\": \"收件街道\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverDetailedAddress\",     \"column\": \"收件详细地址\",     \"show\": false,     \"position\": null   },   {     \"name\": \"receiverPostalCode\",     \"column\": \"收件邮编\",     \"show\": false,     \"position\": null   },   {     \"name\": \"remarks\",     \"column\": \"备注\",     \"show\": false,     \"position\": null   },   {     \"name\": \"expressTypeName\",     \"column\": \"产品名称\",     \"show\": false,     \"position\": null   },   {     \"name\": \"insured\",     \"column\": \"需要保价\",     \"show\": false,     \"position\": null   },   {     \"name\": \"declaredValue\",     \"column\": \"保价金额\",     \"show\": false,     \"position\": null   },   {     \"name\": \"insuredValue\",     \"column\": \"保价费\",     \"show\": false,     \"position\": null   },   {     \"name\": \"codMoney\",     \"column\": \"代收货款金额\",     \"show\": false,     \"position\": null   },   {     \"name\": \"codFee\",     \"column\": \"代收货款手续费\",     \"show\": false,     \"position\": null   },   {     \"name\": \"codCurrencyTypeName\",     \"column\": \"代收货款币别\",     \"show\": false,     \"position\": null   },   {     \"name\": \"goodsTypeName\",     \"column\": \"物品类型\",     \"show\": false,     \"position\": null   },   {     \"name\": \"goodsName\",     \"column\": \"物品名称\",     \"show\": false,     \"position\": null   },   {     \"name\": \"packageNumber\",     \"column\": \"件数\",     \"show\": false,     \"position\": null   },   {     \"name\": \"packageLength\",     \"column\": \"包裹总长\",     \"show\": false,     \"position\": null   },   {     \"name\": \"packageWide\",     \"column\": \"包裹总宽\",     \"show\": false,     \"position\": null   },   {     \"name\": \"packageHigh\",     \"column\": \"包裹总高\",     \"show\": false,     \"position\": null   },   {     \"name\": \"packateVolume\",     \"column\": \"体积重量\",     \"show\": false,     \"position\": null   },   {     \"name\": \"packageChargeWeight\",     \"column\": \"计费重量\",     \"show\": false,     \"position\": null   },   {     \"name\": \"packageTotalWeight\",     \"column\": \"物品重量\",     \"show\": false,     \"position\": null   },   {     \"name\": \"boxPrice\",     \"column\": \"包材单价\",     \"show\": false,     \"position\": null   },   {     \"name\": \"standardValue\",     \"column\": \"标准运费\",     \"show\": false,     \"position\": null   },   {     \"name\": \"totalFreight\",     \"column\": \"总费用\",     \"show\": false,     \"position\": null   },   {     \"name\": \"paidModeName\",     \"column\": \"支付方式\",     \"show\": false,     \"position\": null   },   {     \"name\": \"customerCode\",     \"column\": \"客户编号\",     \"show\": false,     \"position\": null   },   {     \"name\": \"spmMoneyType\",     \"column\": \"结算货币类型\",     \"show\": false,     \"position\": null   },   {     \"name\": \"dispatchName\",     \"column\": \"派件服务方式\",     \"show\": false,     \"position\": null   },   {     \"name\": \"cancelReason\",     \"column\": \"取消订单原因\",     \"show\": false,     \"position\": null   },   {     \"name\": \"pickFailReason\",     \"column\": \"取件失败原因\",     \"show\": false,     \"position\": null   },   {     \"name\": \"printsNumber\",     \"column\": \"打印次数\",     \"show\": false,     \"position\": null   },   {     \"name\": \"realPickNetworkName\",     \"column\": \"实际取件网点\",     \"show\": false,     \"position\": null   },   {     \"name\": \"lastDispatchNetworkName\",     \"column\": \"上次调派网点\",     \"show\": false,     \"position\": null   },   {     \"name\": \"dispatchNetworkReason\",     \"column\": \"调派网点原因\",     \"show\": false,     \"position\": null   },   {     \"name\": \"lastDispatchStaffName\",     \"column\": \"上次调派收派员\",     \"show\": false,     \"position\": null   },   {     \"name\": \"goodsValue\",     \"column\": \"物品价值\",     \"show\": false,     \"position\": null   },   {     \"name\": \"updateByName\",     \"column\": \"更新人\",     \"show\": false,     \"position\": null   },   {     \"name\": \"updateTime\",     \"column\": \"更新时间\",     \"show\": false,     \"position\": null   },   {     \"name\": \"boxNumber\",     \"column\": \"包材数量\",     \"show\": false,     \"position\": null   },   {     \"name\": \"boxStandardName\",     \"column\": \"包材规格\",     \"show\": false,     \"position\": null   },   {     \"name\": \"isBusinessName\",     \"column\": \"商务件\",     \"show\": false,     \"position\": null   },   {     \"name\": \"customerWaybillNo\",     \"column\": \"第三方运单号\",     \"show\": false,     \"position\": null   },   {     \"name\": \"secondCodeHitDesc\",     \"column\": \"二段码命中原因\",     \"show\": false,     \"position\": null   },   {     \"name\": \"signReceipt\",     \"column\": \"是否签回单\",     \"show\": false,     \"position\": null   },   {     \"name\": \"isTransfer\",     \"column\": \"转寄/退回\",     \"show\": false,     \"position\": null   },   {     \"name\": \"cancelTime\",     \"column\": \"取消时间\",     \"show\": false,     \"position\": null   },   {     \"name\": \"pickTime\",     \"column\": \"取件时间\",     \"show\": false,     \"position\": null   },   {     \"name\": \"dispatchProxyAreaTime\",     \"column\": \"调度代理区时间\",     \"show\": false,     \"position\": null   },   {     \"name\": \"dispatchNetworkTime\",     \"column\": \"调度网点时间\",     \"show\": false,     \"position\": null   },   {     \"name\": \"dispatchStaffTime\",     \"column\": \"调派收派员时间\",     \"show\": false,     \"position\": null   },   {     \"name\": \"backDispatchNetworkTime\",     \"column\": \"打回调度订单时间\",     \"show\": false,     \"position\": null   },   {     \"name\": \"backDispatchNetworkReason\",     \"column\": \"打回订单原因\",     \"show\": false,     \"position\": null   },   {     \"name\": \"backDispatchNetworkNumber\",     \"column\": \"打回订单次数\",     \"show\": false,     \"position\": null   } ]";
}

