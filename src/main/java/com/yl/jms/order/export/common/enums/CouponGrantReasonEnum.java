package com.yl.jms.order.export.common.enums;

import java.util.Arrays;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:  发放事由 1:用户理赔，2：用户答谢， 3、其他
 * @Project:
 * @CreateDate: Created in 2019-09-19 19:37
 * @Author: <a href="<EMAIL>"><PERSON><PERSON><PERSON><PERSON></a>
 */
public enum CouponGrantReasonEnum {

    SETTLEMENT(Short.valueOf("1"), "用户理赔"),
    THANKS(Short.valueOf("2"), "用户答谢"),
    OTHER(Short.valueOf("3"), "其他"),

    ;
    private Short code;

    private String name;

    CouponGrantReasonEnum(Short code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CouponGrantReasonEnum of(Short code) {
        return Arrays.stream(CouponGrantReasonEnum.values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
    }

    public Short getCode() {
        return code;
    }

    public void setCode(Short code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }}