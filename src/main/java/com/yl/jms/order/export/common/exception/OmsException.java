package com.yl.jms.order.export.common.exception;


import com.yl.jms.order.export.common.enums.RespCodeEnum;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: oms异常类
 * @Project:
 * @CreateDate: Created in 2019-06-22 9:23
 * @Author: <a href="<EMAIL>">zhangchunlin</a>
 */
public class OmsException extends BusinessException {

    public OmsException(RespCodeEnum resultCode) {
        super(resultCode);
    }

    public OmsException(String msg) {
        super(msg);
    }

    public OmsException(Integer code, String msg) {
        super(code,msg);
    }

    public OmsException(String message, Throwable cause) {
        super(message, cause);
    }

    public OmsException(Throwable cause) {
        super(cause);
    }

}
