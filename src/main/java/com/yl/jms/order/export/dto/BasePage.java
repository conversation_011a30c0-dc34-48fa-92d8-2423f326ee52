package com.yl.jms.order.export.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class BasePage implements Serializable {

    private int current = 1;

    private int size = 20;

    public int getCurrent() {
        if (this.current <= 0) {
            return 1;
        }
        return current;
    }

    public int getSize() {
        if (this.size <= 0) {
            return 100;
        }
        return size;
    }
}
