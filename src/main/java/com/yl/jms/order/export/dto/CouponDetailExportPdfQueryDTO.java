package com.yl.jms.order.export.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021-11-18
 *
 * @Description: 优惠券明细查询DTO
 * @Author: 张润
 * @Date: 2021-11-18 15:14
 */
@Data
public class CouponDetailExportPdfQueryDTO extends BaseExportDto implements Serializable {



    /**
     * 优惠券码
     */
    @ApiModelProperty(value="优惠券码")
    @NotNull(message = "优惠券不为空")
    private List<String> couponDetailNos;

    /**
     * 员工所属网点编号
     */
    @ApiModelProperty(value="员工所属网点编号",hidden = true)
    private String siteCode;

    /**
     * uuid
     */
    @ApiModelProperty(value="uuid导出用的",hidden = true)
    private String uuid;

    /**
     * 默认-总部，1-代理区 2-网点
     */
    @ApiModelProperty(value="默认-总部，1-代理区 2-网点", hidden = true)
    private String codeType;

}
