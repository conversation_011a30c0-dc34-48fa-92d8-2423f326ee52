package com.yl.jms.order.export.service.impl;

import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yl.jms.order.export.common.enums.ResultCodeEnum;
import com.yl.jms.order.export.common.exception.BusinessException;
import com.yl.jms.order.export.common.utils.OmsExceptionUtils;
import com.yl.jms.order.export.dto.BaseExportDto;
import com.yl.jms.order.export.entity.DownExcel;
import com.yl.jms.order.export.mapper.DownExcelMapper;
import com.yl.jms.order.export.service.IPrintService;
import com.yl.jms.order.export.util.CollectionUtils;
import com.yl.jms.order.export.util.FileUtils;
import com.yl.jms.order.export.util.OSSUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.*;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 自定义文件导出
 * @author: xiongweibin
 * @create: 2020-07-22 14:18
 */
@Component
@Service
@Slf4j
public abstract class AbstractCustomPdfExportServiceImpl<T extends BaseExportDto, V extends BaseRowModel> {


    @Autowired
    private DownExcelMapper downExcelMapper;

    @Autowired
    private IPrintService printService;

    @Autowired
    private OSSUtil ossUtil;

    @Value("${file.upload.seviceIp}")
    private String fileUploadSeviceIp;

    private Class<V> voClass;

    private ExecutorService executor = new ThreadPoolExecutor(20, 20,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    protected AbstractCustomPdfExportServiceImpl() {
        Type type = getClass().getGenericSuperclass();
        Type trueType = ((ParameterizedType) type).getActualTypeArguments()[1];
        this.voClass = (Class<V>) trueType;
    }


    public void createAndUploadDocument(final T baseExportDto) {
        validateEntity(baseExportDto);
        //检查当前人当前任务是否重复操作
        this.doCreateAndUploadDocument(baseExportDto);
    }

    /**
     * 导出请求参数的校验 - 具体子类重写此方法，可作定制化校验，此处空实现
     *
     * @param t
     */
    public abstract void validateEntity(T t);


    /**
     * 获取导出文档信息
     *
     * @param t
     * @return
     */
    public abstract T obtainExportInformation(T t);

    /**
     * 创建并且上传文档
     *
     * @param baseExportDto
     */
    public void doCreateAndUploadDocument(final T baseExportDto) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        final T t = obtainExportInformation(baseExportDto);
        //创建下载任务
        String recordId = createDownLoadTask(baseExportDto);
        //执行导出操作
        executor.submit(exportThenUpdateTask(baseExportDto, uuid, t, recordId));
    }

    private Runnable exportThenUpdateTask(T baseExportDto, String uuid, T t, String recordId) {
        return new Runnable() {
            @Override
            public void run() {
                String fileName = t.getFileName();
                String moduleCode = t.getModuleName();
                String projectName = t.getProjectName();
                String pdfTemplateName = t.getPdfTemplateName();
                ByteArrayOutputStream out = new ByteArrayOutputStream(1024);
                InputStream inputStream = null;
                BufferedInputStream bis = null;
                File zipFile = null;
                //ExecutorService handlePdfExecutor = new ThreadPoolExecutor(10, 50, 1000 * 60,
                //TimeUnit.MILLISECONDS, new ArrayBlockingQueue<Runnable>(100), Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());
                ExecutorService handlePdfExecutor = new ThreadPoolExecutor(20, 20,
                        60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());
                try {


                    log.info("异步导出消息-requestId==>{},入参==>{}", uuid, JSON.toJSONString(baseExportDto));
                    long start = System.currentTimeMillis() / 1000;//单位秒

                    long start1 = System.currentTimeMillis();
                    long exportCount = queryExportCount(t);
                    log.info("setParameter耗时={}，count数：{}", System.currentTimeMillis() - start1, exportCount);
                    log.info(fileName + "-requestid==>{},条数==>{}", uuid, exportCount);

                    List<List<Map<String, Object>>> mapList = queryExportData(t);
                    List<File> files = new ArrayList<>();
                    CountDownLatch downLatch = new CountDownLatch(mapList.size());
                    String mkDir = UUID.randomUUID().toString();
                    mapList.forEach(maps -> {
                        handlePdfExecutor.submit(() -> {
                            try {
                                files.add(printService.printJrxml(pdfTemplateName, maps, mkDir));
                            } catch (Exception e) {
                                log.error("异步导出消息异常-requestid==>{},maps==>{},异常信息==>{}", uuid, JSON.toJSONString(maps), OmsExceptionUtils.getStackTrace(e));
                            } finally {
                                downLatch.countDown();
                            }
                        });
                    });
                    downLatch.await();
                    log.error("异步导出生成pdf消息异常-requestid==>{},files-size:{}", uuid, files.size());
                    if (CollectionUtils.isEmpty(files)) {
                        log.error("异步导出生成pdf消息异常-requestid==>{}", uuid);
                        return;
                    }
                    //生成pdf完成,打成压缩包
                     /*String targetPath = ResourceUtils.getFile("classpath:").getPath().concat(File.separator)
                             .concat(UUID.randomUUID().toString());*/

                    File targetFile = new File(ResourceUtils.getURL("classpath:").getPath());
                    if (!targetFile.exists()) {
                        targetFile = new File("");
                    }
                    File upload = new File(targetFile.getAbsolutePath(), UUID.randomUUID().toString());
                    if (!upload.exists()) {
                        upload.mkdirs();
                    }
                    log.error("异步导出生成pdf消息异常-requestid==>{},files.get(0).getParentFile().getPath():{}", uuid, files.get(0).getParentFile().getPath());
                    zipFile = FileUtils.compressToZip(files.get(0).getParentFile().getPath(), targetFile.getAbsolutePath(), fileName);

                    inputStream = new FileInputStream(zipFile);
                    bis = new BufferedInputStream(inputStream);
                    int date = -1;
                    while ((date = bis.read()) != -1) {
                        out.write(date);
                    }
                    long end = System.currentTimeMillis() / 1000;
                    final Long totalTime = end - start;
                    //下载完成更新下载状态
                    DownExcel downExcel = new DownExcel();
                    //文件上传oss
                    final String downloadSignedUrl = ossUtil.upload(fileName, moduleCode, projectName, out.toByteArray());
                    downExcel.setRecordId(recordId);
                    downExcel.setExportExcelUrl(downloadSignedUrl);
                    downExcel.setServiceIp(fileUploadSeviceIp);
                    updateDownLoadTask(downExcel);
                    log.info("异步导出消息-requestid==>{},数据条数==>{}，导出耗时==>{}秒", uuid, exportCount, totalTime);
                } catch (Exception e) {
                    log.error("异步导出消息异常-requestid==>{},异常信息==>{}", uuid, OmsExceptionUtils.getStackTrace(e));
                } finally {
                    try {
                        if (null != bis) {
                            bis.close();
                        }
                        if (null != inputStream) {
                            inputStream.close();
                        }
                        if (null != handlePdfExecutor) {
                            handlePdfExecutor.shutdown();
                        }
                        if (null != zipFile) {
                            zipFile.delete();
                        }
                        out.close();
                    } catch (IOException e) {
                        log.error("异步导出消息异常-requestId==>{},异常信息==>{}", uuid, OmsExceptionUtils.getStackTrace(e));
                    }
                }
            }
        };
    }

    /**
     * 创建下载任务
     *
     * @param exportDto
     */
    private String createDownLoadTask(T exportDto) {
        // 本次请求id
        String recordId = UUID.randomUUID().toString().replaceAll("-", "");
        Map paramsMap = Maps.newHashMap();
        paramsMap.put("recordId", recordId);
        paramsMap.put("siteName", exportDto.getSa().getSiteName());
        paramsMap.put("moduleName", exportDto.getModuleName());
        paramsMap.put("exportType", exportDto.getModuleType());
        paramsMap.put("userCode", exportDto.getSa().getCode());
        paramsMap.put("exportTime", LocalDateTime.now());
        try {
            downExcelMapper.insertInfo(paramsMap);
        } catch (Exception e) {
            log.error("创建下载任务异常", e);
            throw new BusinessException(ResultCodeEnum.CREATE_DOWNLOAD_TASK_ERROR);
        }
        return recordId;
    }

    /**
     * 更新下载状态
     *
     * @param downExcel
     */
    private void updateDownLoadTask(DownExcel downExcel) {
        downExcelMapper.updateDownInfo(downExcel);
    }

    /**
     * 获取导出条数
     *
     * @return
     */
    public abstract long queryExportCount(T t);

    /**
     * 获取导出数据
     *
     * @param t
     * @return
     */
    public abstract List<List<Map<String, Object>>> queryExportData(T t);
/*
    public static void main(String[] args) throws Exception {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("开始==============================");

        long start = System.currentTimeMillis() / 1000;//单位秒
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("typeName", "xxxxhuodong");
        map.put("money", "满100减20");
        map.put("disType", "youhuijuan");
        map.put("date", "2021/10/18-11/20");
        map.put("no", "No.00001");

        map.put("desc1", "1、xxxxxxxxxxxxxxxxx");
        map.put("desc2", "2、xxxxxxxxxxxxxxxxxxxx");
        map.put("desc3", "3、xxxxxxxxxxxx");
        map.put("desc4", "4、xxxxxxxxxxxxxx");
        map.put("desc5", "5、xxxxxxxxxxxxxxxxxxx");

        list.add(map);
        printJrxml("coupon", list);
        long end = System.currentTimeMillis() / 1000;
        final Long totalTime = end - start;
        stopWatch.stop();
        log.info("导出耗时==>{}秒",  totalTime);
    }


    private static void printJrxml(String templateName, List<Map<String, Object>> dataSource) throws Exception {
        JRBeanCollectionDataSource mainDS = new JRBeanCollectionDataSource(dataSource);

        String path = "tmp/jrxml/"+templateName+".jrxml";
        ClassPathResource classPathResource = new ClassPathResource(path);
        InputStream inputStream =classPathResource.getInputStream();
        JasperReport jreport = JasperCompileManager.compileReport(inputStream);

        JasperPrint jprint = JasperFillManager.fillReport(jreport, new HashMap(), mainDS);
        File pdf = new File(getAbsolutePath() + "/tmp/" + UUID.randomUUID() + ".pdf");
        if(!pdf.getParentFile().exists()){
            pdf.getParentFile().mkdirs();
        }
        JasperExportManager.exportReportToPdfFile(jprint, pdf.getAbsolutePath());
        System.out.println("build pdf success, pdf path : "+pdf.getAbsolutePath());
    }

    *//**
     * 获取文件保存路径
     *
     * @return
     *//*
    public static String getAbsolutePath() {
        String pdfPath = null;
        try {
            File classPath = new File(ResourceUtils.getURL("classpath:").getPath());
            if (!classPath.exists()) {
                classPath = new File("");
            }
            pdfPath = classPath.getAbsolutePath();
        } catch (FileNotFoundException e) {
            log.error("获取PDF路径异常" + e);
        }

        return pdfPath;
    }*/


}
