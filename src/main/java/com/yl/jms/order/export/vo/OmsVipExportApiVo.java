package com.yl.jms.order.export.vo;

import com.alibaba.excel.metadata.BaseRowModel;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since Created in 2019/10/14 9:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OmsWaybill导出对象", description = "运单表导出实体")
public class OmsVipExportApiVo extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "运单号")
    private String waybillNo;
    @ApiModelProperty(value = "子单号")
    private String subNo;
//    @ApiModelProperty(value = "运单状态code")
//
//    private Integer waybillStatusCode;

    @ApiModelProperty(value = "运单状态")
    private String waybillStatusName;
//    @ApiModelProperty(value = "客户名称")
//
//    private String customerName;

    @ApiModelProperty(value = "件数,＞1表示子母件，如果业务上不支持子母件，则前端限制不显示，并默认为1")
    private Integer packageNumber;
//    @ApiModelProperty(value = "寄件网点")
//
//    private String pickNetworkName;
//
//    @ApiModelProperty(value = "始发地")
//
//    private String originName;
//
//    @ApiModelProperty(value = "目的地")
//
//    private String destinationName;
//    @ApiModelProperty(value = "派件网点")
//
//    private String dispatchNetworkName;

    @ApiModelProperty(value = "总运费")
    private BigDecimal totalFreight;

    @ApiModelProperty(value = "运费")
    private BigDecimal freight;

    @ApiModelProperty(value = "其他费")
    private BigDecimal otherFee;

    @ApiModelProperty(value = "保价费")
    private BigDecimal insuredFee;

    @ApiModelProperty(value = "税金")
    private BigDecimal tax;

    @ApiModelProperty(value = "税后总费用")
    private BigDecimal afterTaxFreight;
//    @ApiModelProperty(value = "结算审核标识,1是，0否")
//
//    private Integer isSettlement;
//    @ApiModelProperty(value = "录入时间")
//
//    private LocalDateTime inputTime;

    @ApiModelProperty(value = "揽件时间")
    private LocalDateTime collectTime;
//    @ApiModelProperty(value = "物品类型id")
//
//    private Integer goodsTypeId;
//
//    @ApiModelProperty(value = "物品类型code")
//
//    private String goodsTypeCode;

    @ApiModelProperty(value = "物品类型")
    private String goodsTypeName;

    @ApiModelProperty(value = "物品名称")
    private String goodsName;

    @ApiModelProperty(value = "包裹总长,单位厘米")
    private BigDecimal packageLength;

    @ApiModelProperty(value = "包裹总宽,单位厘米")
    private BigDecimal packageWide;

    @ApiModelProperty(value = "包裹总高,单位厘米")
    private BigDecimal packageHigh;

    @ApiModelProperty(value = "包裹体积重,单位立方厘米")
    private BigDecimal packageVolume;

    @ApiModelProperty(value = "包裹总重量,单位千克")
    private BigDecimal packageTotalWeight;

    @ApiModelProperty(value = "包裹总体积,单位立方厘米")
    private BigDecimal packageTotalVolume;

    @ApiModelProperty(value = "包裹计费重量,单位千克")
    private BigDecimal packageChargeWeight;

    @ApiModelProperty(value = "包材规格code")
    private String boxStandardCode;

    @ApiModelProperty(value = "包材规格")
    private String boxStandardName;

    @ApiModelProperty(value = "包材数量")
    private Integer boxNumber;

    @ApiModelProperty(value = "箱子价格")
    private BigDecimal boxPrice;
//    @ApiModelProperty(value = "揽件业务员code")
//
//    private String collectStaffCode;
//
//    @ApiModelProperty(value = "揽件业务员")
//
//    private String collectStaffName;
//
//    @ApiModelProperty(value = "派件业务员code")
//
//    private String dispatchStaffCode;
//
//    @ApiModelProperty(value = "派件业务员")
//
//    private String dispatchStaffName;

    @ApiModelProperty(value = "派件时间")
    private LocalDateTime dispatchTime;
//    @ApiModelProperty(value = "派件网点code")
//
//    private String dispatchNetworkCode;

    @ApiModelProperty(value = "异常原因")
    private String exceptionReason;

    @ApiModelProperty(value = "签收标识,1是，0否")
    private Integer isSign;
//    @ApiModelProperty(value = "签收网点code")
//
//    private String signNetworkCode;

    @ApiModelProperty(value = "签收网点")
    private String signNetworkName;

    @ApiModelProperty(value = "签收时间")
    private LocalDateTime signTime;
//    @ApiModelProperty(value = "是否实名制,1是，0否")
//
//    private Integer isRealName;
//
//    @ApiModelProperty(value = "身份证号码")
//
//    private String idNo;
//    @ApiModelProperty(value = "问题件标识,1是，0否")
//
//    private Integer isAbnormal;
//
//    @ApiModelProperty(value = "问题件登记网点code")
//
//    private String abnormalRegNetworkCode;
//
//    @ApiModelProperty(value = "问题件登记网点")
//
//    private String abnormalRegNetworkName;
//
//    @ApiModelProperty(value = "问题件登记时间")
//
//    private LocalDateTime abnormalRegTime;
//
//    @ApiModelProperty(value = "问题件登记人code")
//
//    private String abnormalRegStaffCode;
//
//    @ApiModelProperty(value = "问题件登记人")
//
//    private String abnormalRegStaffName;
//
//    @ApiModelProperty(value = "班次id")
//
//    private Integer shiftId;
//
//    @ApiModelProperty(value = "班次名称")
//
//    private String shiftName;
//
//    @ApiModelProperty(value = "内部订单编号")
//
//    private Long orderId;
//
//    @ApiModelProperty(value = "客户订单编号")
//
    private String customerOrderId;
//
//    @ApiModelProperty(value = "运单来源code")
//
//    private String waybillSourceCode;
//
//    @ApiModelProperty(value = "运单来源")
//
//    private String waybillSourceName;
//
//    @ApiModelProperty(value = "寄件方式code")
//
//    private String sendCode;

    @ApiModelProperty(value = "寄件方式")
    private String sendName;
//    @ApiModelProperty(value = "派件方式code")
//
//    private String dispatchCode;

    @ApiModelProperty(value = "派件方式")
    private String dispatchName;
//    @ApiModelProperty(value = "是否作废件,1是，0否")
//
//    private Integer isVoid;

    @ApiModelProperty(value = "是否退件,1是，0否")
    private Integer isRefund;
//    @ApiModelProperty(value = "是否需要签回单,1是，0否")
//
//    private Integer isNeedReceipt;
//
//    @ApiModelProperty(value = "回单编号")
//
//    private String receiptNo;
//
//    @ApiModelProperty(value = "子单标识,1是，0否")
//
//    private Integer isSub;
//
//    @ApiModelProperty(value = "客户编号code")
//
//    private String customerCode;

    @ApiModelProperty(value = "寄件人姓名")
    private String senderName;

    @ApiModelProperty(value = "寄件人手机号")
    private String senderMobilePhone;

    @ApiModelProperty(value = "寄件人座机")
    private String senderTelphone;

    @ApiModelProperty(value = "寄件国家Id")
    private Integer senderCountryId;

    @ApiModelProperty(value = "寄件国家")
    private String senderCountryName;

    @ApiModelProperty(value = "寄件省份id")
    private Integer senderProvinceId;

    @ApiModelProperty(value = "寄件省份")
    private String senderProvinceName;

    @ApiModelProperty(value = "寄件城市id")
    private Integer senderCityId;

    @ApiModelProperty(value = "寄件城市")
    private String senderCityName;

    @ApiModelProperty(value = "寄件区域Id")
    private Integer senderAreaId;

    @ApiModelProperty(value = "寄件区域")
    private String senderAreaName;

    @ApiModelProperty(value = "寄件乡镇")
    private String senderTownship;

    @ApiModelProperty(value = "寄件街道")
    private String senderStreet;

    @ApiModelProperty(value = "寄件详细地址")
    private String senderDetailedAddress;

    @ApiModelProperty(value = "寄件邮编")
    private String senderPostalCode;

    @ApiModelProperty(value = "寄件邮箱")
    private String senderEmail;
//    @ApiModelProperty(value = "签收短信通知,1是，0否")
//
//    private Integer signSmsNotify;
//
//    @ApiModelProperty(value = "寄件短信通知,1是，0否")
//
//    private Integer senderSmsNotify;
//    @ApiModelProperty(value = "始发地id")
//
//    private Integer originId;
//
//    @ApiModelProperty(value = "始发地code")
//
//    private String originCode;
//
//    @ApiModelProperty(value = "寄件网点code")
//
//    private String pickNetworkCode;
//
//    @ApiModelProperty(value = "到付款")
//
//    private BigDecimal receivePayFee;
//
//    @ApiModelProperty(value = "寄件财务中心编号")
//
//    private String pickFinanceCode;
//
//    @ApiModelProperty(value = "寄件财务中心")
//
//    private String pickFinanceName;
//
//    @ApiModelProperty(value = "派件财务中心编号")
//
//    private String dispatchFinanceCode;
//
//    @ApiModelProperty(value = "派件财务中心")
//
//    private String dispatchFinanceName;
//
//    @ApiModelProperty(value = "产品类型id")
//
//    private Integer expressTypeId;
//
//    @ApiModelProperty(value = "产品类型code")
//
//    private String expressTypeCode;

    @ApiModelProperty(value = "产品类型")
    private String expressTypeName;
//    @ApiModelProperty(value = "是否需要保价,1是，0否")
//
//    private Integer insured;

    @ApiModelProperty(value = "保价金额")
    private BigDecimal insuredAmount;

    @ApiModelProperty(value = "是否需要代收货款,1是，0否")
    private Integer codNeed;

    @ApiModelProperty(value = "代收货款金额")
    private BigDecimal codMoney;

    @ApiModelProperty(value = "代收货款费用")
    private BigDecimal codFee;

    @ApiModelProperty(value = "结算方式id")
    private Integer settlementId;

    @ApiModelProperty(value = "结算方式")
    private String settlementName;

    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;

    @ApiModelProperty(value = "收件人公司")
    private String receiverCompany;

    @ApiModelProperty(value = "收件人手机号")
    private String receiverMobilePhone;

    @ApiModelProperty(value = "收件人座机")
    private String receiverTelphone;

    @ApiModelProperty(value = "收件国家id")
    private Integer receiverCountryId;

    @ApiModelProperty(value = "收件国家")
    private String receiverCountryName;

    @ApiModelProperty(value = "收件省份id")
    private Integer receiverProvinceId;

    @ApiModelProperty(value = "收件省份")
    private String receiverProvinceName;

    @ApiModelProperty(value = "收件城市id")
    private Integer receiverCityId;

    @ApiModelProperty(value = "收件城市")
    private String receiverCityName;

    @ApiModelProperty(value = "收件区域id")
    private Integer receiverAreaId;

    @ApiModelProperty(value = "收件区域")
    private String receiverAreaName;

    @ApiModelProperty(value = "收件乡镇")
    private String receiverTownship;

    @ApiModelProperty(value = "收件街道")
    private String receiverStreet;

    @ApiModelProperty(value = "收件详细地址")
    private String receiverDetailedAddress;

    @ApiModelProperty(value = "收件邮编")
    private String receiverPostalCode;

    @ApiModelProperty(value = "收件邮箱")
    private String receiverEmail;
//    @ApiModelProperty(value = "目的地id")
//
//    private Integer destinationId;
//
//    @ApiModelProperty(value = "目的地code")
//
//    private String destinationCode;
//
//    @ApiModelProperty(value = "派件分拣码")
//
//    private String receiverSortingCode;

    @ApiModelProperty(value = "寄件时间")
    private LocalDateTime deliveryTime;
//    @ApiModelProperty(value = "录入人code")
//
//    private String inputStaffCode;
//
//    @ApiModelProperty(value = "录入人")
//
//    private String inputStaffName;
//
//    @ApiModelProperty(value = "录入网点code")
//
//    private String inputNetworkCode;
//
//    @ApiModelProperty(value = "录入网点")
//
//    private String inputNetworkName;
//
//    @ApiModelProperty(value = "优惠券编号")
//
//    private String couponCode;
//
//    @ApiModelProperty(value = "优惠金额")
//
//    private BigDecimal couponAmount;

    @ApiModelProperty(value = "备注")
    private String remarks;
//    @ApiModelProperty(value = "打印次数")
//
//    private Integer printsNumber;
//
//    @ApiModelProperty(value = "发票编号")
//
//    private String invoiceNo;
//
//    @ApiModelProperty(value = "是否纸质运单,1是，0否")
//
//    private Integer isPaper;
//
//    @ApiModelProperty(value = "账单编号")
//
//    private String billNo;
//
//    @ApiModelProperty(value = "是否现金收款,1是，0否")
//
//    private Integer isCash;

    @ApiModelProperty(value = "COD收款标识,1是，0否")
    private Integer isCodReceive;
//    @ApiModelProperty(value = "发件标识,做了寄件网点发件扫描后标记是（控制是否允许作废),1是，0否")
//
//    private Integer isSend;
//
//
//    private LocalDateTime lastUpdateTime;
//
//
//    private String lastUpdateNetworkCode;
//
//
//    private String lastUpdateNetworkName;
//
//
//    private String lastUpdateStaffCode;
//
//
//    private String lastUpdateStaffName;

  /*  @CustomColumn(name = "同步ES更新时间", hide = true)
    @ApiModelProperty(value = "同步ES更新时间")

    private LocalDateTime lastUpdateTimeSync;*/
//    @ApiModelProperty(value = "是否删除,1未删除，2已删除")
//
//    private Integer isDelete;
//
//
//    private Integer paidModeId;
//
//
//    private String paidModeCode;

    private String paidModeName;

    @ApiModelProperty(value = "退件时间")
    private LocalDateTime refundTime;

    //订单来源code和名称
    private  String orderSourceCode;
    private  String orderSourceName;
    /**
     * 是否签回单,1是，0否
     */

    private Integer isNeedReceipt;

    /**
     * 关联单号
     */

    private String receiptNo;
    /**
     * 回单费
     */
    private BigDecimal receiptFee;
    private Integer receiptBusinessType;
}
