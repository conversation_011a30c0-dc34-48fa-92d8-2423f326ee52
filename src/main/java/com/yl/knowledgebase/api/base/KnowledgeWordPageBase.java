package com.yl.knowledgebase.api.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/12/14 23:45
 */
@Data
@Accessors(chain = true)
public class KnowledgeWordPageBase implements Serializable {

    private static final long serialVersionUID = 3477136224692338386L;

    @ApiModelProperty(name = "id", value = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(name = "readingQuantity", value = "阅读量")
    private Integer readingQuantity;

    @ApiModelProperty(name = "commentCount", value = "评论数")
    private Integer commentCount;

    @ApiModelProperty(name = "collectCount", value = "收藏数")
    private Integer collectCount;

    @ApiModelProperty(name = "giveLikeCount", value = "点赞数")
    private Integer giveLikeCount;/**/
    @ApiModelProperty(name = "downloadCount", value = "下载量")
    private int downloadCount;
}
