package com.yl.knowledgebase.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/12/21 15:37
 */
@Data
@Accessors(chain = true)
public class KnowledgeWordTitleOnlyQueryApiDTO implements Serializable {
    private static final long serialVersionUID = -5068763128416060367L;

    @ApiModelProperty(name = "firstCatalogueId", value = "所属模块id")
    private Long firstCatalogueId;
    @ApiModelProperty(name = "secondCatalogueId", value = "所属二级目录id")
    private Long secondCatalogueId;
    @ApiModelProperty(name = "threeCatalogueId", value = "所属三级目录id")
    private Long threeCatalogueId;
    @ApiModelProperty(name = "title", value = "文档名称")
    private String title;
}
