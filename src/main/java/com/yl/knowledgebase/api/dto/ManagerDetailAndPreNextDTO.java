package com.yl.knowledgebase.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ManagerDetailAndPreNextDTO {

    @ApiModelProperty(name = "id", value = "文档id")
    private Long id;

    @ApiModelProperty(name = "networkId", value = "网点id")
    private Long networkId;

    @ApiModelProperty(name = "createBy", value = "创建人id")
    private Long createBy;

    @ApiModelProperty(name = "selectType", value = "查询权限 1 全部，2 已发布")
    private Integer selectType;

    @ApiModelProperty(name = "institutionalLevelId", value = "机构级别,22:总部,334:代理区,335: 中心,336:一级网点,337:二级网点")
    private Integer institutionalLevelId;

    @ApiModelProperty(name = "currentNetworkId", value = "当前登陆人网点id")
    private Integer currentNetworkId;

    @ApiModelProperty(name = "currentNetworkName", value = "当前登陆人网点名称")
    private String currentNetworkName;
}
