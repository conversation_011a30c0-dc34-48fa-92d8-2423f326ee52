package com.yl.knowledgebase.api.enums;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019-08-10 15:43
 */
public enum HeaderParamEnum {
    OAUTH_AUTHTOKEN("authToken"),;

    private String name;

    HeaderParamEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
