package com.yl.knowledgebase.api.enums.exception;

import com.yl.common.base.enums.RespCodeEnum;

/**
 * @Description: 客户管理异常枚举
 * @Package: com.yl.post.station.api.enums.exception
 * @Author: zhangbin
 * @Date: 2020/11/12 11:54
 */
public enum KnowledgeCatalogueExceptionEnum implements RespCodeEnum {
    KNOW_NAME_ALREADY_EXISTS(149001110, "knowledgeCatalogue_already_exists", "目录名称已存在"),
    FAIL_TO_ADD(149001111,"fail_to_add","新增失败"),
    FAIL_TO_EDIT(149001112,"fail_to_edit","修改失败"),
    KNOW_NOT_EXISTS(149001113,"know_not_exists","该目录不存在"),
    SORT_ALREADY_EXISTS(149001114,"sort_already_exists","排序值重复，请重新填写"),
    FAIL_TO_DELETE(149001115,"fail_to_delete","该状态为启用,无法删除"),
    EXISTS_CATALOGUE_OR_WORD(149001116,"fail_to_delete","该模块存在文档,不可删除"),
    EXISTS_SECOND_CATALOGUE_WORD(149001117,"fail_to_delete","该目录存在文档,不可删除"),
    PICTUREURL_NOT_NULL(149001118,"fail_to_delete","一级目录图标不能为空"),
    KNOWDEDGE_CATALOGUE_SORT_IS_OVER(149001119,"knowdedge_catalogue_sort_is_over","模块数量已超额"),
    KNOW_CATALOGUE_NAME_ALREADY_EXISTS(149001120, "know_catalogue_name_already_exists", "模块名称已存在"),
    KNOW_IS_DELETE(149001121,"know_is_delete","该目录已删除"),
    LEVEL_IS_ERROR(149001122,"level_is_error","禁止目录往下拖拽"),
    KNOW_CATALOGUE_IS_NOT_EXISTS(149001123,"know_catalogue_is_not_exists","目录不存在,拖拽失败"),
    ;

    private int code;
    private String key;
    private String msg;

    KnowledgeCatalogueExceptionEnum(int code, String key, String msg) {
        this.code = code;
        this.key = key;
        this.msg = msg;
    }


    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public void setArgs(Object[] var1) {

    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
