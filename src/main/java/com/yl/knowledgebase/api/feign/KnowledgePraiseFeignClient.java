package com.yl.knowledgebase.api.feign;

import com.yl.common.base.model.vo.Result;
import com.yl.knowledgebase.api.base.BaseConstant;
import com.yl.knowledgebase.api.dto.KnowledgePraiseApiDTO;
import com.yl.knowledgebase.api.vo.KnowledgePraiseApiVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/12/8 17:52
 */
@Component
@FeignClient(
        name = BaseConstant.FEIGN_CLIENT_NAME,
        path = BaseConstant.FEIGN_CLIENT_PATH + "/tabKnowledgePraise",
        url = BaseConstant.FEIGN_CLIENT_URL,
        configuration = ApiKnowledgeFeignConfiguration.class)
public interface KnowledgePraiseFeignClient {

    @ApiOperation(value="点赞",notes="点赞")
    @PostMapping("/save")
    Result<Boolean> save(@RequestBody KnowledgePraiseApiDTO dto);

    @ApiOperation(value="取消点赞",notes="取消点赞")
    @PostMapping("/delete")
    Result<Boolean> delete(@RequestBody KnowledgePraiseApiDTO dto);

    @ApiOperation(value="是否已经点赞",notes="是否已经点赞")
    @PostMapping("/isPraised")
    Result<KnowledgePraiseApiVO> isPraised(@ApiParam(name = "wordId", value = "文档id") @RequestParam("wordId") Long wordId,
                                           @ApiParam(name = "networkId", value = "网点id") @RequestParam("networkId") Long networkId);
}
