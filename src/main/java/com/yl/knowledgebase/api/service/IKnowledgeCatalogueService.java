package com.yl.knowledgebase.api.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.knowledgebase.api.dto.*;
import com.yl.knowledgebase.api.entity.TabKnowledgeCatalogue;
import com.yl.knowledgebase.api.vo.KnowledgeCatalogueLinkageApiVO;
import com.yl.knowledgebase.api.vo.KnowledgeCatalogueTreeVo;
import com.yl.knowledgebase.api.vo.KnowledgeTreesApiVo;
import com.yl.knowledgebase.api.vo.TabKnowledgeCatalogueVo;

import java.util.List;


/**
 * <AUTHOR>
 * @Description:
 * @date 2020/12/2 17:33
 */
public interface IKnowledgeCatalogueService extends IService<TabKnowledgeCatalogue> {
    /**
     * 分页列表
     * @param dto
     * @return
     */
    Page<TabKnowledgeCatalogueVo> pageList(TabKnowledgeCatalogueDTO dto);

    /**
     * 添加
     * @param dto
     * @return
     */
    Boolean saveKnowledgeCatalogue(KnowledgeCatalogueApiDTO dto);

    /**
     * 修改
     * @param dto
     * @return
     */
    Boolean updateKnowledgeCatalogue(KnowledgeCatalogueApiDTO dto);

    Boolean delete(Long id);

    List<TabKnowledgeCatalogueVo> selectCatalogueNameList(Integer selectType);

    Boolean updateStatus(KnowledgeCatalogueApiDTO dto);

    Boolean updatePictureUrl(KnowledgeCatalogueApiDTO dto);

    Boolean updateSecondCatalogue(KnowledgeCatalogueDragApiDTO dto);

    Boolean updateCatalogue(KnowledgeCatalogueDragApiDTO dto);

    KnowledgeCatalogueTreeVo catalogueLinkage(Long parentId, Integer selectType);

    KnowledgeCatalogueTreeVo catalogueLinkageTwoAndThree(Long parentId, Integer selectType);

    Boolean deleteSecondCatalogue(Long id);

    Boolean deleteThreeCatalogue(Long id);

    List<KnowledgeCatalogueLinkageApiVO> catalogueWordLinkage(Long parentId, Integer selectType);

    Page<KnowledgeCatalogueLinkageApiVO> catalogueWordLinkageSelect(Long parentId, String name, Integer selectType, Page page);

    boolean addSecondCatalogue(KnowledgeSecondCatalogueAddApiDTO dto);

    boolean updateSecondCatalogueName(KnowledgeSecondCatalogueUpdateApiDTO dto);

    boolean addThreeCatalogue(KnowledgeSecondCatalogueAddApiDTO dto);

    boolean updateThreeCatalogueName(KnowledgeSecondCatalogueUpdateApiDTO dto);

    KnowledgeTreesApiVo loadCatalogues(Integer selectType);


}

