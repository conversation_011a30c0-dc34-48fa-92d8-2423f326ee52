package com.yl.knowledgebase.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.common.base.model.vo.Result;
import com.yl.knowledgebase.api.dto.KnowledgePraiseRecordDTO;
import com.yl.knowledgebase.api.dto.KnowledgeReadRecordDTO;
import com.yl.knowledgebase.api.entity.TabKnowledgePraiseRecord;
import com.yl.knowledgebase.api.entity.TabKnowledgeReadRecord;

/**
 * <AUTHOR>
 * @Description:
 * @date 2021/4/28 18:17
 */
public interface IKnowledgePraiseRecordService extends IService<TabKnowledgePraiseRecord> {

    Result save(KnowledgePraiseRecordDTO dto);

}
