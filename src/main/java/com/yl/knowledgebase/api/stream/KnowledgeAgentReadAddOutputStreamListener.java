package com.yl.knowledgebase.api.stream;

import com.yl.common.base.utils.JsonUtils;
import com.yl.knowledgebase.api.entity.TabKnowledgeWord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/12/14 16:56
 */
@Slf4j
@EnableBinding(KnowledgeOutputInterface.class)
@Component
public class KnowledgeAgentReadAddOutputStreamListener {

    @Autowired
    private KnowledgeOutputInterface knowledgeOutputInterface;

    public Boolean sendMessage(TabKnowledgeWord data) {
        boolean result = false;
        try{
            log.info("文档新增代理区阅读量发送小司开始。。。{}", JsonUtils.toJson(data));
            Message message = MessageBuilder.withPayload(data).build();
            result = knowledgeOutputInterface.knowledgeAgentReadAddOutput().send(message);
        } catch (Exception e){
            log.error("文档新增，代理区阅读量发送消息失败，{}", e.getMessage());
        }
        return result;
    }
}
