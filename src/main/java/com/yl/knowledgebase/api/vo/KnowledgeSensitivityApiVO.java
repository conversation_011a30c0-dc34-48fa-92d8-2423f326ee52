package com.yl.knowledgebase.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class KnowledgeSensitivityApiVO implements Serializable {
    @ApiModelProperty(name = "id", value = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(name = "sensitivityName", value = "敏感字名称")
    private String sensitivityName;

    @ApiModelProperty(name = "createBy", value = "创建人id")
    private Long createBy;

    @ApiModelProperty(name = "createByName", value = "创建人")
    private String createByName;

    @ApiModelProperty(name = "updateBy", value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(name = "updateByName", value = "更新人")
    private String updateByName;

    @ApiModelProperty(name = "createTime", value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(name = "updateTime", value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(name = "isEnable", value = "是否启动 1 启用 2 禁用")
    private Integer isEnable;

    @ApiModelProperty(name = "version", value = "")
    private Integer version;

    private static final long serialVersionUID = 1L;
}