package com.yl.knowledgebase.controller;

import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import com.yl.knowledgebase.dto.classroom.ExamTestQuestionAddDTO;
import com.yl.knowledgebase.dto.classroom.ExamTestQuestionResetDTO;
import com.yl.knowledgebase.service.IExamPaperQuestionService;
import com.yl.knowledgebase.vo.classroom.ExamPaperQuestionVO;
import com.yl.knowledgebase.vo.classroom.ExamTestQuestionVO;
import com.yl.knowledgebase.vo.classroom.QuestionTypeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * ExamPaperQuestionFeign
 *
 * <AUTHOR>
 * @date 2021-09-14 14:44:52
 */
@RestController
@RequestMapping(value="/paperQuestion")
@Api(value = "ExamPaperQuestionController", tags = {"试卷试题操作类"})
public class ExamPaperQuestionController  extends BaseController{

   @Autowired
   private IExamPaperQuestionService service;
  /**
   * 获取试卷试题详细信息
   *
   * @param id id
   * @return Result<ExamPaperQuestionVO>
   * <AUTHOR>
   * @date 2021-09-14 14:44:52
   */
  @ApiOperation(value="获取试卷试题详细信息",notes="获取试卷试题详细信息")
  @RequestMapping(value = "/getInfo", method = RequestMethod.GET)
  public Result<ExamPaperQuestionVO> getInfo(@RequestParam("id") Long id){
    return success(service.getInfo(id));
  }

  /**
   * 返回试卷试题与试题库比对消息，返回是否有改动
   *
   * @param paperId paperId
   * @return Result<List<ExamTestQuestionVO>>
   * <AUTHOR>
   * @date 2021-09-14 14:44:52
   */
  @ApiOperation(value="getThanVersionQuestion",notes="返回试卷试题与试题库比对消息")
  @RequestMapping(value = "/getThanVersionQuestion", method = RequestMethod.POST)
  public Result<List<ExamTestQuestionVO>> getThanVersionQuestion(@RequestParam("paperId") Long paperId){
    return success(service.getThanVersionQuestion(paperId));
  }

  /**
   * 新增试卷试题
   *
   * @return Result<Integer>
   * <AUTHOR>
   * @date 2021-09-14 14:44:52
   */
  @ApiOperation(value="add",notes="新增试卷试题")
  @RequestMapping(value = "add", method = RequestMethod.POST)
  public Result<Integer> add(@RequestBody ExamTestQuestionAddDTO dto){
    return success(service.add(dto));
  }

  /**
   * 
   *
   * @param ids ids
   * @param paperId paperId
   * @return Result<Integer>
   * <AUTHOR>
   * @date 2021-09-14 14:44:52
   */
  @ApiOperation(value="remove",notes="删除试卷试题")
  @RequestMapping(value = "/remove", method = RequestMethod.POST)
  public Result<Integer> remove(@RequestParam("ids") List<Long> ids, @RequestParam("paperId") Long paperId){
    return success(service.remove(ids, paperId));
  }

  /**
   * 查询试卷试题列表
   *
   * @return Result<List<ExamPaperQuestionVO>>
   * <AUTHOR>
   * @date 2021-09-14 14:44:52
   */
  @ApiOperation(value="list",notes="查询试卷试题列表")
  @RequestMapping(value = "/list", method = RequestMethod.GET)
  public Result<Map<Integer, QuestionTypeVO>> list(@RequestParam Long paperId){
    return success(service.list(paperId));
  }

  /**
   * 重置试卷试题
   *
   * @return Result<Integer>
   * <AUTHOR>
   * @date 2021-09-14 14:44:52
   */
  @ApiOperation(value="reset",notes="重置试卷试题")
  @RequestMapping(value = "reset", method = RequestMethod.POST)
  public Result<Integer> reset(@RequestBody ExamTestQuestionResetDTO dto){
    return success(service.reset(dto));
  }

}
