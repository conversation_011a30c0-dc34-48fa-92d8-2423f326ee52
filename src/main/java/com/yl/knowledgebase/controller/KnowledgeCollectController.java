package com.yl.knowledgebase.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import com.yl.knowledgebase.dto.KnowledgeCollectCancelDTO;
import com.yl.knowledgebase.dto.KnowledgeCollectDTO;
import com.yl.knowledgebase.dto.KnowledgeCollectQueryDTO;
import com.yl.knowledgebase.service.impl.KnowledgeCollectServiceImpl;
import com.yl.knowledgebase.valid.group.Insert;
import com.yl.knowledgebase.valid.group.Update;
import com.yl.knowledgebase.vo.KnowledgeCollectPageVO;
import com.yl.knowledgebase.vo.KnowledgeCollectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(tags="文档收藏")
@RestController
@RequestMapping(value="/tabKnowledgeCollect")
public class KnowledgeCollectController extends BaseController {

    @Autowired
    private KnowledgeCollectServiceImpl knowledgeCollectService;

    @ApiOperation(value="文档收藏",notes="文档收藏")
    @PostMapping("/save")
    public Result<Boolean> save(@Validated(Insert.class) @RequestBody KnowledgeCollectDTO dto) {
        return success(knowledgeCollectService.save(dto));
    }

    @ApiOperation(value="文档取消收藏",notes="文档取消收藏")
    @PostMapping("/delete")
    public Result<Boolean> delete(@Validated(Update.class) @RequestBody KnowledgeCollectCancelDTO dto) {
        return success(knowledgeCollectService.delete(dto));
    }

    @ApiOperation(value="修改",notes="修改")
    @PostMapping("/update")
    public Result<Boolean> update() {
        return success(null);
    }

    @ApiOperation(value="列表查询",notes="列表查询")
    @GetMapping("/page")
    public Result<Page<KnowledgeCollectPageVO>> page(KnowledgeCollectQueryDTO query, Page page) {
        return success(knowledgeCollectService.page(query, page));
    }

    @ApiOperation(value="详情查看",notes="详情查看")
    @GetMapping("/detail")
    public Result<KnowledgeCollectVO> detail() {
        return success(null);
    }
}