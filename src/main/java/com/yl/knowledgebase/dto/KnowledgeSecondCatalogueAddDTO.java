package com.yl.knowledgebase.dto;

import com.yl.knowledgebase.base.KnowledgeBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/12/23 15:09
 */
@Data
public class KnowledgeSecondCatalogueAddDTO extends KnowledgeBaseDTO {

    private static final long serialVersionUID = -3170229025952384643L;

    @ApiModelProperty(name = "parentId", value = "上级目录id", required = true)
    private Long parentId;

    @ApiModelProperty(name = "level", value = "目录层级 1 一级 2 二级 3 三级 .....", required = true)
    private Integer level;

    @ApiModelProperty(name = "catalogueName", value = "目录名称", required = true)
    private String catalogueName;

    @ApiModelProperty(name = "sort", value = "排序字段")
    private Integer sort;
}
