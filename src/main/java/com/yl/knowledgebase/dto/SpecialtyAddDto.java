package com.yl.knowledgebase.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 专业课程对应表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SpecialtyAddDto", description = "专业课程对应表")
public class SpecialtyAddDto implements Serializable {


    private Integer id;

    @ApiModelProperty(value = "专业名称")
    private String specialtyName;

    @ApiModelProperty(value = "创建人")
    private Long createId;

    @ApiModelProperty(value = "创建人名称")
    private String createName;


    @ApiModelProperty(value = "标签名称")
    private String tagName;

    @ApiModelProperty(value = "标签Code")
    private String tagCode;

    @ApiModelProperty(value = "是否发布 1 发布 2不发布")
    private Integer isIssue;

    @ApiModelProperty(value = "修改人")
    private Long updateId;

    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    @ApiModelProperty(value = "课程数")
    private Integer courseCount;

    @ApiModelProperty(value = "是否管理员")
    private Integer isAdmin;

    @ApiModelProperty(value = "是否新人")
    private Integer isNew;

    @ApiModelProperty(value = "是否标签人员")
    private Integer isTag;

    @ApiModelProperty(value = "课程列表")
    private List<SpecialtyCourseDto>  specialtyCourseDtos;

}
