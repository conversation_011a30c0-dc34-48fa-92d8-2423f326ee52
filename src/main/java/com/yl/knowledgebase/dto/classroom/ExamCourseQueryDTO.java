package com.yl.knowledgebase.dto.classroom;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ExamCourseQueryDTO", description = "课程列表")
public class ExamCourseQueryDTO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty("课程id")
    private Long id;

    /**
     * 课程名称
     */
    @ApiModelProperty("课程名称")
    private String courseName;

    /**
     * 是否必学(1必学 2不必)
     */
    @ApiModelProperty("是否必学(1必学 2不必)")
    private Integer must;

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

    /**
     * 页码
     */
    @NotNull(message = "数量不能为空")
    private Long size = 20L;


    @ApiModelProperty("课程状态:1已发布 2待发布 3删除课程")
    private Integer status;
}
