package com.yl.knowledgebase.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum ChooseTypeEnum {
    /**
     * 选项A、B、C、D
     */
    CHOOSE_A(1, "A"),
    CHOOSE_B(2, "B"),
    CHOOSE_C(3, "C"),
    CHOOSE_D(4, "D");

    private int code;
    private String name;

    ChooseTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ChooseTypeEnum getByCode(int code) {
        return Arrays.asList(values()).stream().filter(q -> q.getCode() == code).findFirst().orElse(null);
    }

    public static List<String> getNameList() {
        return Arrays.stream(values()).map(ChooseTypeEnum::getName).collect(Collectors.toList());
    }
}
