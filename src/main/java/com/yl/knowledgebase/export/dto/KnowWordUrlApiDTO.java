package com.yl.knowledgebase.export.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/12/26 15:26
 */
@Data
public class KnowWordUrlApiDTO implements Serializable {

    private static final long serialVersionUID = -1429528382575978971L;

    @ApiModelProperty(name = "name", value = "文件名")
    private String name;

    @ApiModelProperty(name = "accessoryUrl", value = "上传地址")
    private String accessoryUrl;
}