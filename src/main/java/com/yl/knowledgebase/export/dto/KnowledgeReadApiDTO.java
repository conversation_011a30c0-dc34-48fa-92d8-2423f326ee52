package com.yl.knowledgebase.export.dto;

import com.yl.knowledgebase.export.valid.group.Insert;
import com.yl.knowledgebase.export.valid.group.Update;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class KnowledgeReadApiDTO implements Serializable {
    @NotNull(groups={Update.class})
    @Max(groups={Update.class},value = 8888888888888888888L, message = "超出最大值")
    @ApiModelProperty(name = "id", value = "")
    private Long id;

    @NotNull(groups={Update.class, Insert.class})
    @Max(groups={Update.class,Insert.class},value = 8888888888888888888L, message = "文档id超出最大值")
    @ApiModelProperty(name = "knowledgeWordId", value = "文档id")
    private Long knowledgeWordId;

    @NotNull(groups={Update.class,Insert.class})
    @Max(groups={Update.class,Insert.class},value = 8888888888888888888L, message = "网点id超出最大值")
    @ApiModelProperty(name = "networkId", value = "网点id")
    private Long networkId;

    @Length(groups={Update.class,Insert.class},max = 60, message = "网点名称长度不能超过60")
    @ApiModelProperty(name = "networkName", value = "网点名称")
    private String networkName;

    @Max(groups={Update.class,Insert.class},value = 8888888888888888888L, message = "超出最大值")
    @ApiModelProperty(name = "createBy", value = "")
    private Long createBy;

    @Length(groups={Update.class,Insert.class},max = 60, message = "长度不能超过60")
    @ApiModelProperty(name = "createByName", value = "")
    private String createByName;

    @ApiModelProperty(name = "createTime", value = "")
    private LocalDateTime createTime;

    @ApiModelProperty(name = "updateTime", value = "")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}