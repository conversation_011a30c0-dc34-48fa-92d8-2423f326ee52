package com.yl.knowledgebase.export.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class KnowledgeReadRecordDTO implements Serializable {

    @ApiModelProperty(name = "id", value = "")
    private Long id;

    @ApiModelProperty(name = "knowledgeWordId", value = "文档id")
    private Long knowledgeWordId;

    @ApiModelProperty(name = "networkId", value = "网点id")
    private Long networkId;

    @ApiModelProperty(name = "networkName", value = "网点名称")
    private String networkName;

    @ApiModelProperty(name = "createBy", value = "")
    private Long createBy;

    @ApiModelProperty(name = "createByName", value = "")
    private String createByName;

    @ApiModelProperty(name = "createTime", value = "")
    private LocalDateTime createTime;

    @ApiModelProperty(name = "updateTime", value = "")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}