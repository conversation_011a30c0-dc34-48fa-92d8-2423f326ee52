package com.yl.knowledgebase.export.dto;


import com.yl.knowledgebase.export.base.KnowledgeBaseDTO;
import com.yl.knowledgebase.export.valid.group.Insert;
import com.yl.knowledgebase.export.valid.group.Update;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class KnowledgeSensitivityApiDTO extends KnowledgeBaseDTO {
    @NotNull(groups={Update.class})
    @Max(groups={Update.class},value = 8888888888888888888L, message = "超出最大值")
    @ApiModelProperty(name = "id", value = "")
    private Long id;

    @Length(groups={Update.class,Insert.class},max = 20, message = "敏感字名称长度不能超过20")
    @ApiModelProperty(name = "sensitivityName", value = "敏感字名称")
    @NotBlank(groups={Update.class,Insert.class},message = "敏感字不能为空")
    private String sensitivityName;

    /*@Max(groups={Update.class,Insert.class},value = 8888888888888888888L, message = "创建人id超出最大值")
    @ApiModelProperty(name = "createBy", value = "创建人id")
    private Long createBy;

    @Length(groups={Update.class,Insert.class},max = 50, message = "创建人长度不能超过50")
    @ApiModelProperty(name = "createByName", value = "创建人")
    private String createByName;

    @Max(groups={Update.class,Insert.class},value = 8888888888888888888L, message = "更新人id超出最大值")
    @ApiModelProperty(name = "updateBy", value = "更新人id")
    private Long updateBy;

    @Length(groups={Update.class, Insert.class},max = 50, message = "更新人长度不能超过50")
    @ApiModelProperty(name = "updateByName", value = "更新人")
    private String updateByName;

    @ApiModelProperty(name = "createTime", value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(name = "updateTime", value = "更新时间")
    private LocalDateTime updateTime;*/

    @Max(groups={Update.class,Insert.class},value = 8888888888L, message = "是否启动 1 启用 2 禁用超出最大值")
    @ApiModelProperty(name = "isEnable", value = "是否启动 1 启用 2 禁用")
    private Integer isEnable;

    @Max(groups={Update.class,Insert.class},value = 8888888888L, message = "超出最大值")
    @ApiModelProperty(name = "version", value = "")
    private Integer version;

    private static final long serialVersionUID = 1L;
}