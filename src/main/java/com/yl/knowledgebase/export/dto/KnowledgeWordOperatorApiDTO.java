package com.yl.knowledgebase.export.dto;

import com.yl.knowledgebase.export.base.KnowledgeBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/12/8 14:51
 */
@Data
@Accessors(chain = true)
public class KnowledgeWordOperatorApiDTO extends KnowledgeBaseDTO {

    private static final long serialVersionUID = -3183459525987154502L;

    @ApiModelProperty(name = "id", value = "文档id")
    private Long id;

    /*@ApiModelProperty(name = "operatorType", value = "操作类型 1 发布 2 删除 3 禁用")
    private Integer operatorType;*/
}
