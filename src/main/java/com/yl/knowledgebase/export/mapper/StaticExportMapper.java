package com.yl.knowledgebase.export.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.model.vo.Result;
import com.yl.knowledgebase.export.dto.KnowledgeStatisticsDTO;
import com.yl.knowledgebase.export.vo.KnowledgeStatisticsExportVO;
import com.yl.knowledgebase.export.vo.KnowledgeStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description: 报表文档导出
 * @Author: 陈绪军
 * @Date: 2021-09-08 9:08
 */
@Mapper
public interface StaticExportMapper {
    Page<KnowledgeStatisticsExportVO> queryPage(Page page,@Param("query") KnowledgeStatisticsDTO query);
}
