package com.yl.knowledgebase.export.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.common.base.model.vo.Result;
import com.yl.knowledgebase.export.dto.ExportRecordDTO;
import com.yl.knowledgebase.export.entity.ExportRecord;
import com.yl.knowledgebase.export.vo.ExportRecordVO;


/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: wwx
 * @create: 2021-5-5 17:11
 */
public interface IExportRecordService extends IService<ExportRecord> {

    Result<Page<ExportRecordVO>> page(ExportRecordDTO dto);

    Result<Boolean> delete(ExportRecordDTO dto);
}
