package com.yl.knowledgebase.export.utils;

import com.yl.common.base.enums.RespCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.knowledgebase.export.enums.exception.lmdm.CommonCodeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * Class Description
 *
 * <AUTHOR>
 * @since 2020/9/4 16:17
 */
@Slf4j
public class FeignExceptionUtil {

    public static <R> R tryCatch(Supplier<R> func, RespCodeEnum codeEnum){
        try {
            return func.get();
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            throw new BusinessException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ServiceException(codeEnum);
        }
    }

    /**
     * 基础数据服务异常
     * @param func
     * @param <R>
     * @return
     */
    public static <R> R catchLmdmException(Supplier<R> func) {
        return tryCatch(func, CommonCodeEnum.LMDM_ERROR);
    }

}
