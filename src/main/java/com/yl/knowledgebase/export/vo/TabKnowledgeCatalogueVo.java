package com.yl.knowledgebase.export.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> xuyangyang
 * @Date : 2020/12/4 16:24
 */
@Data
@ApiModel(value="模块管理返回对象", description="模块管理返回对象")
public class TabKnowledgeCatalogueVo {
    @ApiModelProperty(name = "id", value = "id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(name = "catalogueName", value = "目录名称")
    private String catalogueName;

    @ApiModelProperty(name = "catalogueName", value = "目录名称")
    private String catalogueCode;

    @ApiModelProperty(name = "createBy", value = "创建人id")
    private Long createBy;

    @ApiModelProperty(name = "createByName", value = "创建人")
    private String createByName;

    @ApiModelProperty(name = "createTime", value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(name = "updateByName", value = "修改人")
    private String updateByName;

    @ApiModelProperty(name = "updateTime", value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(name = "isEnable", value = "状态")
    private String isEnable;

    @ApiModelProperty(name = "sort", value = "排序")
    private Integer sort;

    @ApiModelProperty(name = "picture_url", value = "一级目录图标url")
    private String pictureUrl;

    @ApiModelProperty(name = "version", value = "版本号")
    private String version;

    @ApiModelProperty(name = "level", value = "目录层级 1 一级 2 二级 3 三级 .....")
    private Integer level;

    @ApiModelProperty(name = "parentId", value = "上级目录id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    @ApiModelProperty(name = "accessoryUrlList", value = "处理后的图片")
    private List<String> pictureUrlList;

}
