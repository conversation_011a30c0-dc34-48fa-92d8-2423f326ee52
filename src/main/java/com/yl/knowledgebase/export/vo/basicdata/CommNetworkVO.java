package com.yl.knowledgebase.export.vo.basicdata;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "CommNetworkVO", description = "社区查询网点响应对象")
public class CommNetworkVO implements Serializable {

    @ApiModelProperty(name = "proxyNetworkId", value = "代理网点id")
    private Integer proxyNetworkId;

    @ApiModelProperty(name = "proxyNetworkCode", value = "代理网点编码")
    private String proxyNetworkCode;

    @ApiModelProperty(name = "proxyNetworkName", value = "代理网点名称")
    private String proxyNetworkName;

    @ApiModelProperty(name = "parentNetworkId", value = "所属网点ID")
    private Integer parentNetworkId;

    @ApiModelProperty(name = "parentNetworkCode", value = "所属网点编码")
    private String parentNetworkCode;

    @ApiModelProperty(name = "parentNetworkName", value = "所属网点名称")
    private String parentNetworkName;

    @ApiModelProperty(name = "id", value = "客户id")
    private Integer id;

    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    @ApiModelProperty(name = "code", value = "编码")
    private String code;

}
