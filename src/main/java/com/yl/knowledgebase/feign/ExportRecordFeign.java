package com.yl.knowledgebase.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.model.vo.Result;

import com.yl.knowledgebase.base.BaseConstant;
import com.yl.knowledgebase.dto.ExportRecordDTO;
import com.yl.knowledgebase.feign.fallback.ExportRecordFeignFallBack;
import com.yl.knowledgebase.vo.ExportRecordVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 功能描述:
 * 下载中心feign
 * @since: 1.0.0
 * @Author:wwx
 * @Date: 2021-05-6 11:10
 */
@Component
@FeignClient(
        name = BaseConstant.FEIGN_CLIENT_NAME,
        path = BaseConstant.FEIGN_CLIENT_PATH + "/exportRecord",
        url = BaseConstant.FEIGN_CLIENT_URL,
        configuration = ApiKnowledgeFeignConfiguration.class,
        fallback = ExportRecordFeignFallBack.class)
public interface ExportRecordFeign {

    /**
     * 导出下载中心
     * @param dto
     * @return
     */
    @PostMapping("/queryExportRecordPage")
    @ApiOperation(value = "导出下载中心查询", notes = "导出下载中心查询")
    Result<Page<ExportRecordVO>> queryExportRecordPage(@RequestBody ExportRecordDTO dto);

    /**
     * 导出下载中心删除
     * @param dto
     * @return
     */
    @PostMapping("/deleteExportRecord")
    @ApiOperation(value = "导出下载中心删除", notes = "导出下载中心删除")
    Result deleteExportRecord(@RequestBody ExportRecordDTO dto);

}
