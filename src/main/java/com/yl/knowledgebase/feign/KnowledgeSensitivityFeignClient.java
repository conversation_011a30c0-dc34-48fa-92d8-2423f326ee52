package com.yl.knowledgebase.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.model.vo.Result;


import com.yl.knowledgebase.base.BaseConstant;
import com.yl.knowledgebase.dto.KnowledgeSensitivityApiDTO;
import com.yl.knowledgebase.dto.TabKnowledgeSensitivityDTO;
import com.yl.knowledgebase.vo.KnowledgeSensitivityApiVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/12/4 15:37
 */
@Component
@FeignClient(
        name = BaseConstant.FEIGN_CLIENT_NAME,
        path = BaseConstant.FEIGN_CLIENT_PATH + "/tabKnowledgeSensitivity",
        url = BaseConstant.FEIGN_CLIENT_URL,
        configuration = ApiKnowledgeFeignConfiguration.class)
public interface KnowledgeSensitivityFeignClient {
    @ApiOperation(value="新增",notes="新增")
    @PostMapping("/save")
    Result<Boolean> save(@RequestBody KnowledgeSensitivityApiDTO dto);

    @ApiOperation(value="删除",notes="删除")
    @GetMapping("/delete")
    Result<Boolean> delete(@RequestParam("id") Long id);

    @ApiOperation(value="修改",notes="修改")
    @PostMapping("/update")
    Result<Boolean> update(@RequestBody KnowledgeSensitivityApiDTO dto);

    @ApiOperation(value="列表查询",notes="列表查询")
    @PostMapping("/page")
    Result<Page<KnowledgeSensitivityApiVO>> page(@RequestBody TabKnowledgeSensitivityDTO dto);

    @ApiOperation(value="详情查看",notes="详情查看")
    @PostMapping("/detail")
    Result<KnowledgeSensitivityApiVO> detail();
}
