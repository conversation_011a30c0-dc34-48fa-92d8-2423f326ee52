package com.yl.knowledgebase.service.impl;

import com.alibaba.fastjson.JSON;
import com.yl.jms.ups.dto.UpsUser;
import com.yl.jms.ups.util.LoginUserUtils;
import com.yl.knowledgebase.dto.classroom.ExamPaperDTO;
import com.yl.knowledgebase.feign.classroom.ExamPaperFeign;
import com.yl.knowledgebase.service.IExamPaperService;
import com.yl.knowledgebase.vo.classroom.ExamPaperVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ExamPaperFeign
 *
 * <AUTHOR>
 * @date 2021-09-14 14:44:52
 */
@Slf4j
@Service
public class ExamPaperServiceImpl  implements IExamPaperService {

    @Autowired
    private ExamPaperFeign fingh;
  /**
   * 获取试卷详细信息
   *
   * @param id id
   * @return Result<ExamPaperVO>
   * <AUTHOR>
   * @date 2021-09-14 14:44:52
   */
   @Override
   public ExamPaperVO getInfo(Long id){
      return fingh.getInfo(id).result();
   }

  /**
   * 修改试卷
   *
   * @param examPaper examPaper
   * @return Result<Integer>
   * <AUTHOR>
   * @date 2021-09-14 14:44:52
   */
   @Override
   public Integer edit(ExamPaperDTO examPaper){
      UpsUser upsUser = LoginUserUtils.getUpsUser();
       examPaper.setUpdateId(upsUser.getId().longValue())
               .setUpdateBy(upsUser.getName())
               .setUpdateTime(LocalDateTime.now());
       log.info("修改试卷==>{}", JSON.toJSONString(examPaper));
      return fingh.edit(examPaper).result();
   }

  /**
   * 新增试卷
   *
   * @param examPaper examPaper
   * @return Result<Integer>
   * <AUTHOR>
   * @date 2021-09-14 14:44:52
   */
   @Override
   public Integer add(ExamPaperDTO examPaper){
      UpsUser upsUser = LoginUserUtils.getUpsUser();
       examPaper.setCreateId(upsUser.getId().longValue())
               .setCreateBy(upsUser.getName())
               .setCreateTime(LocalDateTime.now())
               .setUpdateId(upsUser.getId().longValue())
               .setUpdateBy(upsUser.getName())
               .setUpdateTime(LocalDateTime.now());
         log.info("新增试卷==>{}", JSON.toJSONString(examPaper));
       return fingh.add(examPaper).result();
   }

  /**
   * 
   *
   * @param ids ids
   * @return Result<Integer>
   * <AUTHOR>
   * @date 2021-09-14 14:44:52
   */
   @Override
   public Integer remove(Long[] ids){
       log.info("删除试卷==>{}", JSON.toJSONString(ids));
      return fingh.remove(ids).result();
   }

  /**
   * 查询试卷列表
   *
   * @param examPaper examPaper
   * @return Result<List<ExamPaperVO>>
   * <AUTHOR>
   * @date 2021-09-14 14:44:52
   */
   @Override
   public List<ExamPaperVO> list(ExamPaperDTO examPaper){
      return fingh.list(examPaper).result();
   }

}
