package com.yl.knowledgebase.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-09-18 16:57
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExportRecordVO implements Serializable {
    /** 导出记录主键 **/
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /** 导出时间 **/
    @ApiModelProperty(value = "导出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime exportTime;
    /** 是否完成 0:未完成 1:已完成 **/
    @ApiModelProperty(name = "firstCatalogueName", value = "是否完成 0:未完成 1:已完成")
    private Integer isfinished;
    /** 模块code **/
    @ApiModelProperty(name = "moduleCode", value = "模块code")
    private String moduleCode;
    /** 模块code **/
    @ApiModelProperty(name = "moduleName", value = "模块Name")
    private String moduleName;
    /** 导出excel url **/
    @ApiModelProperty(name = "exportExcelUrl", value = "导出excel url")
    private String exportExcelUrl;
    /** 用户code **/
    @ApiModelProperty(name = "userCode", value = "userCode")
    private String userCode;
}
