package com.yl.knowledgebase.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> xuyangyang
 * @Date : 2020/12/15 15:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class KnowledgeCatalogueLinkageVo implements Serializable {
    private static final long serialVersionUID = 1851199468228195819L;
    @ApiModelProperty(name = "id", value = "目录id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(name = "catalogueCode", value = "目录CODE")
    private String catalogueCode;

    @ApiModelProperty(name = "name", value = "目录名称")
    private String name;

    @ApiModelProperty(name = "parentId", value = "上级目录id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    @ApiModelProperty(name = "sort", value = "序号")
    private Integer sort;

    @ApiModelProperty(name = "level", value = "目录层级 1 一级 2 二级 3 三级 .....")
    private Integer level;

    @ApiModelProperty(name = "isEnable", value = "是否启动 1 启用 2  不启用")
    private Integer isEnable;

    @ApiModelProperty(name = "hasChild", value = "是否包含子集 1 包含 2  不包含")
    private Integer hasChild;
}
