package com.yl.knowledgebase.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 知识库树结构
 */
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class KnowledgeTreesVo implements Serializable {


    @ApiModelProperty(name = "id", value = "目录或文档id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(name = "catalogueCode", value = "目录CODE")
    private String catalogueCode;

    @ApiModelProperty(name = "name", value = "目录名称")
    private String name;

    @ApiModelProperty(name = "parentId", value = "上级目录id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    @ApiModelProperty(name = "sort", value = "序号")
    private Integer sort;

    @ApiModelProperty(name = "level", value = "目录层级 1 一级 2 二级 3 三级 .....")
    private Integer level;

    @ApiModelProperty(name = "type", value = "类型：1 目录  2 文档")
    private Integer type;

    @ApiModelProperty(name = "isEnable", value = "是否启动 1 启用 2  不启用")
    private Integer isEnable;

    @ApiModelProperty(name = "hasChild", value = "是否包含子集 1 包含 2  不包含")
    private Integer hasChild;

    @ApiModelProperty(name = "children", value = "子集")
    private List<KnowledgeTreesVo> children;



}
