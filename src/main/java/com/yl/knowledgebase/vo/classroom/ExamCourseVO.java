package com.yl.knowledgebase.vo.classroom;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 课程对象 课程
 *
 * <AUTHOR>
 * @date 2021-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ExamCourseVO", description = "课程")
public class ExamCourseVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("id")
    private Long id;
    /**
     * 课程名称
     */
    @ApiModelProperty("课程名称")
    private String courseName;
    /**
     * 课程图片地址
     */
    @ApiModelProperty("课程图片地址")
    private String picturePath;
    /**
     * 课程章数
     */
    @ApiModelProperty("课程章数")
    private Integer chapterCount;
    /**
     * 课程小节数
     */
    @ApiModelProperty("课程小节数")
    private Integer sectionCount;
    /**
     * 课程老师
     */
    @ApiModelProperty("课程老师")
    private String teacher;
    /**
     * 课程开始时间
     */
    @ApiModelProperty("课程开始时间")
    private LocalDateTime startTime;
    /**
     * 课程结束时间
     */
    @ApiModelProperty("课程结束时间")
    private LocalDateTime endTime;
    /**
     * 课程类别
     */
    @ApiModelProperty("课程类别")
    private Integer courseType;
    /**
     * 课程状态(1已发布 2待发布)
     */
    @ApiModelProperty("课程状态(1已发布 2待发布)")
    private Integer status;
    /**
     * 是否必学(1必学 2不必)
     */
    @ApiModelProperty("是否必学(1必学 2不必)")
    private Integer must;
    /**
     * 考试是否已通过(1通过 2未通过)
     */
    @ApiModelProperty("考试是否已通过(1通过 2未通过)")
    private Integer passExam;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    private Long createId;
    /**
     * 更新
     */
    @ApiModelProperty("更新")
    private String updateBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
    /**
     * 更新人id
     */
    @ApiModelProperty("更新人id")
    private Long updateId;
    /**
     * 课程简介
     */
    @ApiModelProperty("课程简介")
    private String introduction;
    /**
     * 试卷数
     */
    @ApiModelProperty("试卷数")
    private Integer paperCount;
}
