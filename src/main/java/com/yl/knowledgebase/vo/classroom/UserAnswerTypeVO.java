package com.yl.knowledgebase.vo.classroom;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 个人试卷答题记录对象 个人试卷答题记录
 * 
 * <AUTHOR>
 * @date 2021-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "UserAnswerTypeVO", description = "个人试卷答题记录")
public class UserAnswerTypeVO implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("题型")
    private Integer questionType;
    @ApiModelProperty("题数")
    private Integer questionCount;
    @ApiModelProperty("题总分")
    private Integer questionScore;
    @ApiModelProperty("试题列表")
    private List<UserAnswerRecordVO> questionVOList;

    public UserAnswerTypeVO() {
    }

    public UserAnswerTypeVO(Integer questionType, Integer questionCount, Integer questionScore, List<UserAnswerRecordVO> questionVOList) {
        this.questionType = questionType;
        this.questionCount = questionCount;
        this.questionScore = questionScore;
        this.questionVOList = questionVOList;
    }
}
