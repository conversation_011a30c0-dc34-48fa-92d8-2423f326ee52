package com.yl.lmdm.api.organizational;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class SysCustomerCacheVo implements Serializable {

    private Integer id;

    /**
     * 客户编码
     */

    private String code;

    /**
     * 客户名称
     */

    private String name;

    /**
     * 简称
     */

    private String abbreviation;

    /**
     * 行业类型
     */

    private Integer industryId;

    /**
     * 客户类型
     */

    private Integer typeId;

    /**
     * 城市ID
     */

    private Integer provinceId;

    /**
     * 城市ID
     */

    private Integer cityId;

    /**
     * 区域ID
     */

    private Integer areaId;

    /**
     * 地址
     */

    private String address;

    /**
     * 客户性质:国有、民营、股份制、外资、社会组织机构
     */

    private Integer natureId;

    /**
     * 主要寄托物
     */

    private String mainDeposit;

    /**
     * 联系人姓名
     */

    private String contactName;

    /**
     * 联系人手机
     */

    private String contactMobile;

    /**
     * 联系人邮箱
     */

    private String contactEmail;

    /**
     * 财务对接人姓名
     */

    private String financialName;

    /**
     * 财务对接人手机
     */

    private String financialMobile;

    /**
     * 财务对接人邮箱
     */

    private String financialEmail;

    /**
     * 邮编
     */

    private String zipCode;

    /**
     * 身份证号码
     */

    private String idCardNumber;

    /**
     * 统一信用代码
     */

    private String unifiedCreditCode;

    /**
     * 组织机构代码
     */

    private String organizationCode;

    /**
     * 税务登记号
     */

    private String taxRegistrationCode;

    /**
     * 企业注册号
     */

    private String businessRegistrationCode;

    /**
     * 营业执照图片
     */

    private String businessLicenseImgUrl;

    /**
     * 组织机构代码证图片
     */

    private String organizationImgUrl;

    /**
     * 税务登记证图片
     */

    private String taxRegistrationImgUrl;

    /**
     * 身份证正面图片
     */

    private String idCardFrontImgUrl;

    /**
     * 身份证反面图片
     */

    private String idCardBackImgUrl;

    /**
     * 客户所属网点
     */

    private Integer networkId;

    /**
     * 业务员ID;关联员工表
     */

    private Integer staffId;

    /**
     * 业务员手机号
     */

    private String staffMobile;

    /**
     * 客户等级：VIP、普通
     */

    private Integer customerLevelId;

    /**
     * 是否开通VIP系统账户:1-是， 2-否
     */

    private Integer isVipAccount;

    /**
     * 共享客户：全网共享、下级共享、不共享
     */

    private Integer customerShareId;

    /**
     * 信用额度:1-999999
     */

    private Integer creditAmount;

    /**
     * 是否开通COD:1-是;2-否
     */

    private Integer isOpenCod;

    /**
     * 单笔COD限额:1-99999
     */

    private Integer singleCodLimit;

    /**
     * 返款周期:3、7、15、30天
     */

    private Integer rebatePeriodId;

    /**
     * 代收结算账户开户行
     */

    private String collectionSettlementBankName;

    /**
     * 代收结算账号
     */

    private String collectionSettlementBankAccount;

    /**
     * 代收结算账户
     */

    private String collectionSettlementBankAccountName;

    /**
     * 账单类型:纸质账单、电子账单
     */

    private Integer billId;

    /**
     * 接收账单的email账号
     */

    private String billEmial;

    /**
     * 结算方式：寄付月结、寄付现结
     */

    private Integer settlementId;

    /**
     * 付款方式：现金、转账、支票
     */

    private Integer payId;

    /**
     * 付款期限:1-999
     */

    private Integer prompt;

    /**
     * 发票类型：增值税发票、增值税普票、普通电子发票
     */

    private Integer invoiceId;

    /**
     * 纳税人名称
     */

    private String taxpayerName;

    /**
     * 纳税人识别号
     */

    private String taxpayerNumber;

    /**
     * 纳税人注册地址
     */

    private String taxpayerRegistrationAddress;

    /**
     * 纳税人联系电话
     */

    private String taxpayerPhone;

    /**
     * 纳税人开户银行名称
     */

    private String taxpayerBankName;

    /**
     * 纳税人开户银行账号
     */

    private String taxpayerBankAccount;

    /**
     * 发票接收人姓名
     */

    private String invoiceRecipientName;

    /**
     * 发票接收人电话
     */

    private String invoiceRecipientPhone;

    /**
     * 发票接收人邮箱
     */

    private String invoiceRecipientEmail;

    /**
     * 发票接收人地址
     */

    private String invoiceRecipientAddress;

    /**
     * 国家ID
     */

    private Integer countryId;

    /**
     * 国家代码
     */

    private String countryCode;

    /**
     * 是否启用:1启用,2不启用
     */

    private Integer isEnable;

    /**
     * 是否删除:1未删除,2已删除
     */

    private Integer isDelete;

    /**
     * 版本号
     */

    private String version;

    /**
     * 排序
     */

    private Integer sort;
    /** 2019 11月份新需求，给客户新加几个字段，用于对接第三方客户**/
    /**
     * 商家ID
     */

    private String mallId;

    /**
     * 商家ID
     */

    private String mallName;

    /**
     * 父客户
     */

    private String parentCustomer;

    /**
     * 客户来源
     */

    private Integer sourceId;

    /**
     * 计泡参数
     */

    private Integer meterBubble;

    //(2019-12-20新产品需求，1，把账单日从客户合同迁移到客户资料里面，2，客户资料要做成有层级关系，父客户ID默认为0)

    private Integer parentId;

    /**
     * 账单日：1-31天
     */

    private Integer billingDay;

    /**
     * 账期类型：月结、周结
     */

    private Integer accountPeriodId;


    /**
     * 客户报价方式：标准报价、定制报价
     */

    private Integer quoteId;

    /**
     * 电子面单对象：空/网点/客户
     */
    private Integer essObject;

    /**
     * 计费重量
     */
    private String billingWeight;

    private Integer createBy;

    @ApiModelProperty(name = "updateBy", value = "最后更新人ID")
    private Integer updateBy;

    @ApiModelProperty(name = "createByName", value = "创建人名称")
    private String createByName;

    @ApiModelProperty(name = "updateByName", value = "最后修改人名称")
    private String updateByName;

    @ApiModelProperty(name = "createTime", value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty(name = "updateTime", value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;


}
