package com.yl.looseorder.api.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.Assert;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-08-28 10:06 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */
public enum OmsapiRedisKeyEnum {


    OMSAPI_LMDM_SETTLEMENTDESTINATION_INFO("OMSAPI","LMDM","SETTLEMENTDESTINATION","SYNC","结算目的地"),
    /**
     * 需要更新的key放下面
     */
    OMSAPI_LMDM_NETWORK_INFO("OMSAPI","LMDM","NETWORKINFO","SYNC","网点信息，key为省市区id"),
    OMSAPI_LMDM_NETWORK_INFO_BY_ID("OMSAPI","LMDM","NETWORKINFOBYID","SYNC","网点信息，key为网点id"),
    OMSAPI_LMDM_NETWORK_INFO_BY_CODE("OMSAPI","LMDM","NETWORKINFOBYCODE","SYNC","网点信息，key为网点编码"),
    OMSAPI_LMDM_PICK_NETWORK_INFO("OMSAPI","LMDM","PICKNETWORKINFO","SYNC","寄件网点信息"),
    OMSAPI_LMDM_DISPATCH_NETWORK_INFO("OMSAPI","LMDM","DISPATCHNETWORKINFO","SYNC","派件网点信息"),
    OMSAPI_LMDM_CUSTOMER_INFO("OMSAPI","LMDM","CUSTOMERINFO","v1","客户信息，key为客户id"),
    OMSAPI_LMDM_CUSTOMER_INFO_BY_CODE("OMSAPI","LMDM","CUSTOMERINFOBYCODE","v1","客户信息，key为客户编码"),
    OMSAPI_LMDM_PRODUCT_TYPE_INFO("OMSAPI","LMDM","PRODUCTTYPEINFO","SYNC","产品类型"),
    OMSAPI_LMDM_PAY_TYPE_INFO("OMSAPI","LMDM","PAYTYPEINFO","SYNC","支付类型"),
    OMSAPI_LMDM_ARTICLE_TYPE_INFO("OMSAPI","LMDM","ARTICLETYPEINFO","SYNC","物品类型"),
    OMSAPI_LMDM_SERVICE_INFO("OMSAPI", "LMDM", "SENDSERVICEINFO", "SYNC", "寄件服务类型"),
    OMSAPI_LMDM_PAYMENT_MANNER_INFO("OMSAPI", "LMDM", "PAYMENTMANNERINFO", "SYNC", "派件服务类型"),
            ;


    public String keyBuilder() {
        return checkAndGetValue(key);
    }

    public String keyBuilder(String key) {
        return checkAndGetValue(key);
    }

    private String checkAndGetValue(String key) {
        Assert.notNull(keyPrefix, "RedisKeyEnum: keyPrefix can not be null");
        Assert.notNull(module, "RedisKeyEnum: module can not be null");
        Assert.notNull(func, "RedisKeyEnum: func can not be null");
        Assert.notNull(key, "RedisKeyEnum: key can not be null");
        return keyPrefix + ":" + module + ":" + func + ":" + key;
    }

    OmsapiRedisKeyEnum(String keyPrefix, String module, String func, String remark) {
        this.keyPrefix = keyPrefix;
        this.module = module;
        this.func = func;
        this.remark = remark;
    }

    OmsapiRedisKeyEnum(String keyPrefix, String module, String func, String key, String remark) {
        this.keyPrefix = keyPrefix;
        this.module = module;
        this.func = func;
        this.key = key;
        this.remark = remark;
    }

    /**
     * 系统标识
     */
    private final String keyPrefix;
    /**
     * 模块名称
     */
    private final String module;
    /**
     * 方法名称
     */
    private final String func;
    /**
     * key
     */
    private String key;
    /**
     * 描述
     */
    private final String remark;

    /***
     * Redis常量key在此枚举中申明
     *
     * <AUTHOR>
     * @date 2019/6/11
     */
    @Getter
    @AllArgsConstructor
    enum ConstantKeyEnum {
        /**
         * 常量Key样例
         */
        LMDM_TOKEN_EXPIRE_TIME("LMDM:TOKEN:EXPIRE:TIME", "token过期时间"),
        LMDM_SMS_CODE_EXPIRE("LMDM:SMS:CODE:EXPIRE", "短信验证码过期时间");


        String key;
        String remark;
    }

   public interface NoData{
        String VALUE = "NO_DATA";
    }
}
