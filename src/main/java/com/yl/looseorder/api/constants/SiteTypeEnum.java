package com.yl.looseorder.api.constants;

public enum SiteTypeEnum {

    // 网点类型-代理区 code:17
    NT_AGENT_AREA(334, "代理区"),

    // 网点类型-中心 code:18
    NT_CENTER(335, "中心"),

    // 网点类型-一级网点 code:19
    NT_FIRST_LEVEL(336, "一级网点"),

    // 网点类型-二级网点 code:20
    NT_SECOND_LEVEL(337, "二级网点"),
    ;

    SiteTypeEnum(Integer typeId, String name) {
        this.typeId = typeId;
        this.name = name;
    }

    private final Integer typeId;

    private final String name;

    public Integer getTypeId() {
        return typeId;
    }

    public String getName() {
        return name;
    }
}
