package com.yl.looseorder.api.dto;






import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 订单请求实体
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="OmsOrder取件失败请求实体", description="OmsOrder取件失败请求实体")
public class OmsOrderPickFailDTO implements Serializable {

     private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "订单编号集合")
    @NotBlank(message = "订单号不能为空")
    private List<Long> ids;

    @ApiModelProperty(value = "取件失败原因code")
    @NotBlank(message = "取件失败原因code不能为空")
    private String reasonCode;

    @ApiModelProperty(value = "取件失败原因")
    private String reason;

    @ApiModelProperty(value = "取件失败网点id")
    private Long pickFailNetworkId;
    @ApiModelProperty(value = "取件失败网点code")
    private String pickFailNetworkCode;
    @ApiModelProperty(value = "取件失败网点name")
    private String pickFailNetworkName;
    @ApiModelProperty(value = "取件失败业务员id")
    private Integer pickFailStaffId;
    @ApiModelProperty(value = "取件失败业务员code")
    private String pickFailStaffCode;
    @ApiModelProperty(value = "取件失败业务员name")
    private String pickFailStaffName;
    @ApiModelProperty(value = "取件失败图片")
    private String pickFailPicUrls;
    @ApiModelProperty(value = "取件时间")
    private String pickTime;
    @ApiModelProperty(value = "操作来源 1JMS 2巴枪APP")
    private Integer operatorSource;


}
