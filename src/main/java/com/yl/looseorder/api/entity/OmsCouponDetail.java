package com.yl.looseorder.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * YL_OMS_COUPON_DETAIL
 * <AUTHOR>
@Data
@TableName(value = "YL_OMS_COUPON_DETAIL")
public class OmsCouponDetail implements Serializable {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 优惠券码
     */
    @ApiModelProperty(value="优惠券码")
    private String couponDetailNo;


    /**
     * 核销码
     */
    @ApiModelProperty(value="核销码")
    private String realCouponDetailNo;

    /**
     * 优惠券批次号
     */
    @ApiModelProperty(value="优惠券批次号")
    private String couponBatchNo;

    /**
     * 生效日期
     */
    @ApiModelProperty(value="生效日期")
    private Date invalidStartDate;

    /**
     * 失效日期
     */
    @ApiModelProperty(value="失效日期")
    private Date invalidEndDate;

    /**
     * 状态
     */
    @ApiModelProperty(value="状态", example = "1、待使用 2、已使用 3、已撤回 4、已失效 5、待生效")
    private Short status;

    /**
     * 发放账户id
     */
    @ApiModelProperty(value="发放账户id")
    private Long userId;

    /**
     * 发放手机号
     */
    @ApiModelProperty(value="发放手机号")
    private String tel;

    /**
     * 发放途径1:发放至账号，2：发放至手机号
     */
    @ApiModelProperty(value="发放途径1:发放至账号，2：发放至手机号")
    private Short grantChannel;

    /**
     * 发放事由 1:用户理赔，2：用户答谢， 3、其他
     */
    @ApiModelProperty(value="发放事由 1:用户理赔，2：用户答谢， 3、其他")
    private Short grantReason;

    /**
     * 关联工单
     */
    @ApiModelProperty(value="关联工单")
    private String relatedWorkNo;

    /**
     * 发放备注
     */
    @ApiModelProperty(value="发放备注")
    private String remark;

    /**
     * 使用时间
     */
    @ApiModelProperty(value="使用时间")
    private Date useTime;

    /**
     * 撤回时间
     */
    @ApiModelProperty(value="撤回时间")
    private Date withdrawTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    private String creator;


    /**
     * 是否锁定
     */
    @ApiModelProperty(value="是否锁定", example = "0:未锁定，1：已锁定")
    private Integer isValid;

    private static final long serialVersionUID = 1L;
}