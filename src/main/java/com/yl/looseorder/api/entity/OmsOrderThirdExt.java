package com.yl.looseorder.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_OMS_ORDER_THIRD_EXT")
public class OmsOrderThirdExt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 订单id
     */

    private Long orderId;

    /**
     * 客户订单编号
     */

    private String customerOrderId;

    /**
     * 1.菜鸟
     */

    private Integer type;

    /**
     * 内容
     */

    private String context;

    /**
     * 录入时间
     */

    private LocalDateTime inputTime;

    /**
     * 是否删除,1未删除，2已删除
     */

    private Integer isDelete;

    /**
     * 三段码状态 0.未知（可能是历史数据） 1.有三段码 2.无三段码
     */
    private Integer terminalDispatchCodeStatus;

    private String datoubi;


    /**
     * 第三方配送方式
     */
    private String thirdShipmentTypeCode;


    /**
     * 外部商家标识
     */
    private String outerMallId;


    /**
     * 包裹提⽰码
     */
    private String packageHintCode;

    /** 云打印任务ID */
    private String printTaskId;

    /** 微信TOKEN */
    private String token;

    /**
     * 云打印设备id
     */
    private Integer printerEquipmentId;
}
