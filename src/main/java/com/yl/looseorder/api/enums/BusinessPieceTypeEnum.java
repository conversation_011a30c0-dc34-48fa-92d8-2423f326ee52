package com.yl.looseorder.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 *   客户商务件类型，1商务件 2非商务件
 * <AUTHOR>
 * @since Created in 2020/04/01
 */
@AllArgsConstructor
@Getter
public enum BusinessPieceTypeEnum {
    /**
     * 客户商务件类型，1商务件 2非商务件
     */
    YES(1,"是"),
    NO(2,"否");

    /**
     * code
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String name;

    public static String getNameByCode(Integer code) {
        BusinessPieceTypeEnum first = Arrays.stream(BusinessPieceTypeEnum.values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
        if (null == first) {
            return "";
        }
        return first.getName();
    }

}
