package com.yl.looseorder.api.enums;

import java.util.Arrays;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021<br>
 *
 * @Description: 发送短信模板
 */
public enum CouponSMSTypeEnum {
    COUPON_SMS(1, "coupon_issuance_%s"),
    EARLY_WARNING_SMS(2, "coupon_alert_%s"),
    SOURCE(3,"SM03"),
    ;
    private Integer code;

    private String msg;

    CouponSMSTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static CouponSMSTypeEnum of(Integer code) {
        return Arrays.stream(CouponSMSTypeEnum.values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }}