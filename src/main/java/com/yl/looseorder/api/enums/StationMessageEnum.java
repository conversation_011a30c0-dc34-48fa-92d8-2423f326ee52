package com.yl.looseorder.api.enums;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2020-08-13
 * @Author: xpj
 */
public enum StationMessageEnum {
    NEW_ORDER(1, "Pesanan baru","Anda memiliki pesanan baru yang belum ditugaskan, mohon segera proses!"),
    SOON_OVERTIME_NO_CONVENTION(2, "Akan lewat batas waktu & belum dijadwalkan","Anda memiliki pesanan dengan kode pesanan %s yang akan melewati batas waktu & belum dijadwalkan, mohon segera proses!"),
    SOON_OVERTIME_NO_PICK1(3, "Akan lewat batas waktu & belum pickup","Anda memiliki pesanan dengan kode pesanan %s yang akan melewati batas waktu & belum pickup, mohon segera proses!"),
    SOON_OVERTIME_NO_PICK2(4, "Akan lewat batas waktu & belum pickup","Anda memiliki pesanan dengan kode pesanan %s yang akan melewati batas waktu & belum ditugaskan, mohon segera proses!"),
    OVERTIME_NO_CONVENTION(5, "Melewati batas waktu & belum dijadwalkan","Anda memiliki pesanan dengan kode pesanan %s yang melewati batas waktu & belum dijadwalkan, mohon segera proses!"),
    OVERTIME_NO_PICK(6, "Melewati batas waktu & belum pickup","Anda memiliki pesanan dengan kode pesanan %s yang melewati batas waktu & belum pickup, mohon segera proses!");


    /**
     *  "新订单",您有新订单待调派，请及时处理！
     *   "Pesanan baru", Anda memiliki pesanan baru yang belum ditugaskan, mohon segera proses!
     *
     *   "即将超时未预约","您有订单号为%s的订单即将超时未预约，请及时预约"
     *   "Akan lewat batas waktu & belum dijadwalkan", "Anda memiliki pesanan dengan kode pesanan %s yang akan melewati batas waktu & belum dijadwalkan, mohon segera proses!"
     *
     *   "即将超时未取件","您有订单号为%s的订单即将超时未取件，请及时取件"
     *   "Akan lewat batas waktu & belum pickup", "Anda memiliki pesanan dengan kode pesanan %s yang akan melewati batas waktu & belum pickup, mohon segera proses!"
     *
     *   "即将超时未调派","您有订单号为%s的订单即将超时未调派，请及时调派"
     *   "Akan lewat batas waktu & belum ditugaskan", "Anda memiliki pesanan dengan kode pesanan %s yang akan melewati batas waktu & belum ditugaskan, mohon segera proses!"
     *
     *   "超时未预约","您有订单号为%s的订单超时未预约，请及时预约"
     *   "Melewati batas waktu & belum dijadwalkan","Anda memiliki pesanan dengan kode pesanan %s yang melewati batas waktu & belum dijadwalkan, mohon segera proses!"
     *
     *   "超时未取件","您有订单号为%s的订单超时未取件，请及时取件"
     *   "Melewati batas waktu & belum pickup","Anda memiliki pesanan dengan kode pesanan %s yang melewati batas waktu & belum pickup, mohon segera proses!"
     *
     *   "订单调派提醒","您有%s条新订单待调派，请及时处理！"
     *   "Peringatan penugasan pesanan","Anda memiliki %s pesanan baru yang belum ditugaskan, mohon segera proses!"
     *
     *   "订单调派提醒","您有%s条订单待调派，请及时处理！"
     *   "Peringatan penugasan pesanan","Anda memiliki %s pesanan yang belum ditugaskan, mohon segera proses!"
     *
     *   "订单超时提醒","您有%s条订单未预约，请及时通知业务员处理！"
     *   "Peringatan pesanan lewat batas waktu", "Anda memiliki %s pesanan yang belum dijadwalkan, mohon segera beri tahu staff untuk proses!"
     *
     *   "即将超时提醒","您有%s条订单即将超时未取件，请及时通知业务员处理！"
     *   "Peringatan akan lewat batas waktu", "Anda memiliki %s pesanan yang akan melewati batas waktu & belum pickup, mohon segera beri tahu staff untuk proses!"
     *
     *   "订单超时提醒","您有%s条订单未取件，请及时通知业务员处理！"
     *   "Peringatan pesanan lewat batas waktu", "Anda memiliki %s pesanan yang belum pickup, mohon segera beri tahu staff untuk proses!"
     *
     *   "平台订单未揽收提醒","您有%s个客户的%s条订单已超过6小时未揽收，请及时揽收。"
     *   "Peringatan pesanan platform yang belum diambil", “Anda memiliki %s pelanggan dengan %s pesanan yang telah melewati batas 6 jam & belum diambil, mohon segera melakukan pengambilan."
     */

    private Integer id;
    private String desc;
    private String message;


    private StationMessageEnum(Integer id, String desc, String message) {
        this.id = id;
        this.desc = desc;
        this.message = message;
    }

    public Integer getId() {
        return this.id;
    }

    public String getDesc() {
        return this.desc;
    }

    public String getMessage() {
        return this.message;
    }

    public static StationMessageEnum getById(Integer id) {
        if (id == null) {
            return null;
        }
        for (StationMessageEnum codeEnum : StationMessageEnum.values()) {
            if (id.equals(codeEnum.getId())) {
                return codeEnum;
            }
        }
        return null;
    }
}
