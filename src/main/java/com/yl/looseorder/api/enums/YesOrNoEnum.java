package com.yl.looseorder.api.enums;

import java.util.Arrays;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: 优惠券批次有效期类型,1、固定日期 ， 2、领取后x天
 * @Project:
 * @CreateDate: Created in 2019-09-19 19:37
 * @Author: <a href="<EMAIL>">zhangchunlin</a>
 */
public enum YesOrNoEnum {
    NO(0, "否"),
    YES(1, "是"),

    ;
    private Integer code;

    private String name;

    YesOrNoEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static YesOrNoEnum of(Integer code) {
        return Arrays.stream(YesOrNoEnum.values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }
    public Short getShorCode() {
        return code.shortValue();
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }}

