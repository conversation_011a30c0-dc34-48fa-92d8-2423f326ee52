package com.yl.looseorder.api.feign;

import com.yl.looseorder.api.constant.ApiConstant;
import com.yl.looseorder.api.feign.fallback.OmsOrderApiQueryNewFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-08-06 13:58 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */
@FeignClient(name = ApiConstant.LOOSEORDERAPI_NAME, path = ApiConstant.LOOSEORDERAPI_PATH + "/omsOrder", fallbackFactory = OmsOrderApiQueryNewFallback.class)
public interface OmsOrderGateWayQueryNewFeign extends OmsOrderQueryNewClient {
}
