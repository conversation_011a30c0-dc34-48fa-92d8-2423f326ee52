package com.yl.looseorder.api.feign;

import com.yl.common.base.model.vo.Result;
import com.yl.lmdm.api.dto.customer.SysCustomerAddressDTO;
import com.yl.looseorder.api.feign.fallback.SysCustomerAddressFeignClientNFallBack;
import com.yl.looseorder.api.vo.lmdm.SysNetworkVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-15 10:57 <br>
 * @Author: zhipeng.liu
 */

@Component
@FeignClient(
        name = "yllmdmapi",
        path = "/lmdmapi/sysCustomerAddress",
        fallback = SysCustomerAddressFeignClientNFallBack.class)
public interface SysCustomerAddressFeignClient {

    /**
     * 接口根据客户编码，省市区id查询对应的揽件网点信息
     * 若没有维护对应的客户地址则返回为null
     *
     * @param
     * @return
     */
    @PostMapping("/getNetByCustomerAddress")
    Result<SysNetworkVO> getNetByCustomerAddress(@RequestBody SysCustomerAddressDTO sysCustomerAddressDTO);


}
