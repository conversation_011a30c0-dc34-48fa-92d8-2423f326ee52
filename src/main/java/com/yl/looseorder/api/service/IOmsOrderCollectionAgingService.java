package com.yl.looseorder.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.common.base.model.vo.Result;
import com.yl.order.api.entity.OmsOrderCollectionAging;
import com.yl.order.api.vo.OmsOrderCollectionAgingVo;

import java.util.List;

public interface IOmsOrderCollectionAgingService extends IService<OmsOrderCollectionAging> {

    Result<Boolean> saveOrderCollectionAging(OmsOrderCollectionAgingVo omsOrderCollectionAgingVo);

    Result<Boolean> updateOrderCollectionAging(OmsOrderCollectionAgingVo omsOrderCollectionAgingVo);

    Result<Boolean> batchSaveOrderCollectionAging(List<OmsOrderCollectionAgingVo> batchSaveList);
}
