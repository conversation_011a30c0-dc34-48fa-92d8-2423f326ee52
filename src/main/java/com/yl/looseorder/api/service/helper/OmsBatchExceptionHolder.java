package com.yl.looseorder.api.service.helper;

import com.yl.order.api.dto.OmsOrderApiDTO;
import com.yl.order.api.vo.OmsBatchSaveOrderInfoVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * vip异常订单的持有者（重复规则：客户订单号当天重复的订单）
 */
public class OmsBatchExceptionHolder {
    private static final ThreadLocal<OmsBatchSaveOrderInfoVo> HOLDER = new ThreadLocal<>();

    public static void set(OmsBatchSaveOrderInfoVo data){
        HOLDER.set(data);
    }

    public static OmsBatchSaveOrderInfoVo get(){
        OmsBatchSaveOrderInfoVo omsBatchSaveOrderInfoVo = HOLDER.get();
        if(Objects.isNull(omsBatchSaveOrderInfoVo)){
            omsBatchSaveOrderInfoVo = new OmsBatchSaveOrderInfoVo();
            omsBatchSaveOrderInfoVo.setErrorOrders(new ArrayList<>());
            omsBatchSaveOrderInfoVo.setRepeatOrders(new ArrayList<>());
            HOLDER.set(omsBatchSaveOrderInfoVo);
        }
        return omsBatchSaveOrderInfoVo;
    }

    public static void remove (){
        HOLDER.remove();
    }



    public static List<OmsOrderApiDTO> getErrorOrders(){
        return get().getErrorOrders();
    }
}
