package com.yl.looseorder.api.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.id.IdGenerator;
import com.yl.looseorder.api.config.OrikaBeanMapper;
import com.yl.looseorder.api.dto.coupon.OmsCouponTypeDTO;
import com.yl.looseorder.api.dto.coupon.OmsCouponTypeSaveDTO;
import com.yl.looseorder.api.dto.coupon.OmsCouponTypeStatusDTO;
import com.yl.looseorder.api.entity.OmsCouponType;
import com.yl.looseorder.api.enums.CouponDiscountTypeEnum;
import com.yl.looseorder.api.enums.CouponTypeStatusEnum;
import com.yl.looseorder.api.enums.ResultCodeEnum;
import com.yl.looseorder.api.enums.YesOrNoEnum;
import com.yl.looseorder.api.exception.BusinessException;
import com.yl.looseorder.api.mapper.OmsCouponTypeMapper;
import com.yl.looseorder.api.service.IOmsCouponTypeService;
import com.yl.looseorder.api.util.SnowflakeIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2020-03-10 19:13 <br>
 * @Author:laiguihao
 */

@Service
@Slf4j
public class OmsCouponTypeServiceImpl extends ServiceImpl<OmsCouponTypeMapper, OmsCouponType> implements IOmsCouponTypeService {

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private OmsCouponTypeMapper omsCouponTypeMapper;

    @Autowired
    private IdGenerator<Long> idGenerator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(OmsCouponTypeSaveDTO saveDTO) {
        //校验业务数据
        checkData(saveDTO);
        saveDTO.setStatus(CouponTypeStatusEnum.USING.getCode().shortValue());
        saveDTO.setCreateUserId(saveDTO.getUser().getId().longValue());
        saveDTO.setCreator(saveDTO.getUser().getName());
        saveDTO.setStaffCode(saveDTO.getUser().getStaffNo());
        int id = snowflakeIdGenerator.nextId();
        saveDTO.setCouponType("L" + String.format("%012d", id));
        if (Objects.isNull(saveDTO.getId())) {
            saveDTO.setId(idGenerator.generate());
        }

        omsCouponTypeMapper.insert(saveDTO);
        log.info("添加优惠券类型:{}", JSON.toJSONString(saveDTO));

        //如果是叠加，更新对应被叠加的优惠券的叠加类型
        if (YesOrNoEnum.YES.getShorCode().equals(saveDTO.getIsSuperposition())) {

            LambdaQueryWrapper<OmsCouponType> couponTypeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            couponTypeLambdaQueryWrapper.eq(OmsCouponType::getCouponType, saveDTO.getSuperpositionType());
            OmsCouponType superpositionCouponType = omsCouponTypeMapper.selectOne(couponTypeLambdaQueryWrapper);

            List<Short> discountTypes = Stream.of(saveDTO.getDiscountType(), superpositionCouponType.getDiscountType()).collect(Collectors.toList());
            if (!(discountTypes.contains(CouponDiscountTypeEnum.AUTO_GRANT.getCode()) &&
                    (discountTypes.contains(CouponDiscountTypeEnum.USER_GET.getCode())
                            || discountTypes.contains(CouponDiscountTypeEnum.MANUAL_GRANT.getCode())))) {
                throw new BusinessException(ResultCodeEnum.COUPON_SUPERPOSE_ERROR);
            }
            OmsCouponType couponType = omsCouponTypeMapper.selectById(saveDTO.getId());
            superpositionCouponType.setIsSuperposition(YesOrNoEnum.YES.getShorCode());
            superpositionCouponType.setSuperpositionType(couponType.getCouponType());
            omsCouponTypeMapper.updateById(superpositionCouponType);
        }
        return true;
    }

    private void checkData(OmsCouponTypeSaveDTO saveDTO) {
        LambdaQueryWrapper<OmsCouponType> batchLambdaQueryWrapper = new LambdaQueryWrapper<>();
        batchLambdaQueryWrapper.eq(OmsCouponType::getCouponTypeName, saveDTO.getCouponTypeName());
        OmsCouponType exitsBatch = this.getOne(batchLambdaQueryWrapper);
        //校验该优惠券类型名称重复
        if (null != exitsBatch) {
            throw new BusinessException(ResultCodeEnum.COUPON_TYPE_NAME_EXIT);
        }

        //如果是叠加
        if (YesOrNoEnum.YES.getShorCode().equals(saveDTO.getIsSuperposition())) {

            log.info("校验叠加方式:{}", JSON.toJSONString(saveDTO));
            if (StringUtils.isBlank(saveDTO.getSuperpositionType())) {
                throw new BusinessException(ResultCodeEnum.COUPON_SUPERPOSITION_EMPTY_ERROR);
            }

            //查询被叠加过的优惠券类型
            batchLambdaQueryWrapper = new LambdaQueryWrapper<>();
            batchLambdaQueryWrapper.eq(OmsCouponType::getIsSuperposition, 1);
            batchLambdaQueryWrapper.eq(OmsCouponType::getStatus, CouponTypeStatusEnum.USING.getCode());
            List<OmsCouponType> superCouponTypes = this.list(batchLambdaQueryWrapper);
            List<String> superpositions = new ArrayList<>(superCouponTypes.size() * 2);
            superCouponTypes.forEach(superCouponType -> {
                superpositions.add(superCouponType.getCouponType());
                superpositions.add(superCouponType.getSuperpositionType());
            });

            if (superpositions.contains(saveDTO.getSuperpositionType())) {
                throw new BusinessException(ResultCodeEnum.COUPON_SUPERPOSITION_ERROR);
            }
        }
        //校验折扣类型
        if (saveDTO.getDiscountType().equals(CouponDiscountTypeEnum.AUTO_GRANT.getCode())) {
            if (Objects.isNull(saveDTO.getCouponValue())) {
                throw new BusinessException(ResultCodeEnum.COUPON_VALUE_EMPTY);
            }
        } else if (saveDTO.getDiscountType().equals(CouponDiscountTypeEnum.USER_GET.getCode())) {
            if (Objects.isNull(saveDTO.getCouponFullValue()) || Objects.isNull(saveDTO.getCouponReduceValue())) {
                throw new BusinessException(ResultCodeEnum.COUPON_VALUE_EMPTY);
            }
        } else if (saveDTO.getDiscountType().equals(CouponDiscountTypeEnum.MANUAL_GRANT.getCode())) {
            if (Objects.isNull(saveDTO.getCouponFullValue())
                    || Objects.isNull(saveDTO.getCouponReduceValue())
                    || Objects.isNull(saveDTO.getCouponHighestReduce())) {
                throw new BusinessException(ResultCodeEnum.COUPON_VALUE_EMPTY);
            }
        } else {
            throw new BusinessException(ResultCodeEnum.DISCOUNT_TYPE_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(OmsCouponTypeStatusDTO statusDTO) {

        OmsCouponTypeDTO couponType = orikaBeanMapper.map(statusDTO, OmsCouponTypeDTO.class);
        return omsCouponTypeMapper.updateByPrimaryKeySelective(couponType) > 0 ? true : false;
    }
}
