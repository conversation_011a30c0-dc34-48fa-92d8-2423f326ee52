package com.yl.looseorder.api.trace;


import cn.hutool.core.util.IdUtil;
import org.slf4j.MDC;

/**
 * Trace 工具类
 *
 * <AUTHOR>
 * @date 2021-12-27  9:42
 */
public class TraceUtils {

    public static final String UNIQUE_ID = "TRACE_ID";


    /**
     * 生成 TraceId
     *
     * @return TraceId
     */
    public static String generateTraceId() {
        return IdUtil.simpleUUID();
    }

    /**
     * 获取当前线程 tranceId
     *
     * @return tranceId
     */
    public static String getCurrentTranceId() {
        String tranceId = MDC.get(UNIQUE_ID);
        return tranceId == null ? "--" : tranceId;
    }
}
