package com.yl.official.admin.client;

import com.yl.common.base.model.vo.Result;
import com.yl.official.admin.vo.OssUrlSignVO;
import com.yl.platform.file.dto.OssUrlSignDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@FeignClient(
        name = "ylfile",
        path = "/file/oss"
)
public interface OssFeignClient {

    @PostMapping({"/getDownloadSignedUrl"})
    Result<List<String>> getDownloadSignedUrl(@RequestBody List<String> paths);

    @PostMapping({"/getDownloadSignedUrlDetail"})
    Result<Map<String, String>> getDownloadSignedUrlDetail(@RequestBody List<String> paths);

    @PostMapping({"/getUploadSignedUrl"})
    Result<List<OssUrlSignVO>> getUploadSignedUrl(@RequestBody @Valid List<OssUrlSignDTO> list);

    @PostMapping(
            value = {"/upload"},
            produces = {"application/json;charset=UTF-8"},
            consumes = {"multipart/form-data"}
    )
    Result<String> upload(@RequestParam("projectName") @NotBlank(message = "Item name cannot be empty") String projectName, @RequestParam("moduleName") @NotBlank(message = "Module name cannot be empty") String moduleName, @RequestPart @NotNull(message = "文件不能为空") MultipartFile file);
}
