package com.yl.official.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import com.yl.official.admin.annotation.ExcludeInterceptor;
import com.yl.official.admin.dto.CareersAddDto;
import com.yl.official.admin.dto.CareersDto;
import com.yl.official.admin.dto.CareersQueryDto;
import com.yl.official.admin.service.ICareersService;
import com.yl.official.admin.vo.CareersDictVo;
import com.yl.official.admin.vo.CareersVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 招贤纳士表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@RestController
@Api(value = "CareersController", tags = {"招贤纳士表"})
@RequestMapping("/careers")
@Validated
public class CareersController extends BaseController {
    @Autowired
    private ICareersService careersService;

    /**
     * 查询招贤纳士表
     */
    @PostMapping("/searchCareersList")
    @ApiOperation(value = "searchCareersList", notes = "查询招贤纳士列表")
    @ExcludeInterceptor
    public Result<List<CareersVo>> selectCareersList(@RequestBody CareersDto dto) {
        List<CareersVo> values = careersService.selectCareersList(dto);
        return Result.success(values);
    }

    /**
     * 分页查询
     */
    @PostMapping("/selectPageCareers")
    @ApiOperation(value = "selectPageCareers", notes = "分页查询招贤纳士表")
    public Result<Page<CareersVo>> selectPageCareers(@RequestBody CareersQueryDto dto) {
        Page<CareersVo> values = careersService.selectPageCareers(dto, dto.getPage(), dto.getSize());
        return Result.success(values);
    }

    /**
     * 新增数据
     */
    @PostMapping("/recruitAdd")
    @ApiOperation(value = "recruitAdd", notes = "新增招贤纳士表")
    public Result<Boolean> addBatch(@RequestBody @Valid List<CareersAddDto> dtoList) {
        return Result.success(careersService.addBatch(dtoList));
    }

    /**
     * 根据id修改数据
     */
    @PostMapping("/editCareers")
    @ApiOperation(value = "editCareers", notes = "根据id修改数据")
    public Result<Boolean> editBatch(@RequestBody @Valid List<CareersDto> dtoList) {
        return Result.success(careersService.editBatch(dtoList));
    }

    /**
     * 根据infoNumber删除数据
     */
    @GetMapping("/deleteCareers")
    @ApiOperation(value = "deleteCareers", notes = "删除数据")
    public Result<Integer> deleteCareers(@RequestParam("infoNumber") Long infoNumber) {
        return Result.success(careersService.deleteCareers(infoNumber));
    }

    /**
     * 根据id获取详情
     */
    @GetMapping("/detailAdminCareers")
    @ApiOperation(value = "detailAdminCareers", notes = "根据infoNumber获取详情")
    public Result<List<CareersVo>> getInfo(@RequestParam("infoNumber") Long infoNumber) {
        return Result.success(careersService.getInfo(infoNumber));
    }

    @GetMapping("/detailCareers")
    @ApiOperation(value = "detailCareers", notes = "跟据字典code查询列表")
    public Result<List<CareersDictVo>> getCareersDictBy(@RequestParam("dictCode") String dictCode) {
        return Result.success(careersService.getCareersDictBy(dictCode));
    }

}

