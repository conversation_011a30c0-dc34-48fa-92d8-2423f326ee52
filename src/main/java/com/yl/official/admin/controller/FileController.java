package com.yl.official.admin.controller;

import com.alibaba.fastjson.JSON;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.StringUtils;
import com.yl.official.admin.client.OssFeignClient;
import com.yl.official.admin.constant.BaseConstants;
import com.yl.official.admin.enums.exception.CommonErrorCodeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("file")
@Api(value = "图片上传下载", tags = {"图片上传下载"})
@Slf4j
public class FileController extends BaseController {
    @Autowired
    private OssFeignClient ossFeignClient;
    /**
     * 项目
     */
    public static final String PROJECT_NAME = "openplatform";
    /**
     * 模块
     */
    public static final String MUDULE_NAME = "file";

    /**
     * 图片上传
     * @param file
     * @return
     */
    @PostMapping("/uploadImg")
    @ApiOperation(value = "图片上传", notes = "图片上传")
    public Result<String> uploadImg(@RequestParam("file") MultipartFile file) {
        log.warn("uploadImg start");
        try{
            if (file.isEmpty()) {
                throw new BusinessException(CommonErrorCodeEnum.FILE_ISNULL);
            }
            String[] aa = file.getOriginalFilename().split("\\.");
            if(StringUtils.isEmpty(aa)|| aa.length < 2 || !BaseConstants.image_set.contains(aa[aa.length-1])){
                throw new BusinessException(CommonErrorCodeEnum.FILE_ISNULL);
            }
            log.warn("uploadImg ossFeignClient start");
            Result<String> result = ossFeignClient.upload(PROJECT_NAME,MUDULE_NAME,file);
            log.info("uploadImg result:{}", JSON.toJSON(result));
            return success(ossFeignClient.upload(PROJECT_NAME,MUDULE_NAME,file).result());
        }catch (Exception e){
            log.warn("uploadImg error:",e);
        }
        return Result.success("");
    }

    /**
     * 获取图片地址
     * @param path
     * @return
     */
    @GetMapping("/getImgUrl")
    @ApiOperation(value = "获取图片地址", notes = "获取图片地址")
    public Result<List<String>> getImgUrl(@RequestParam("path") String path) {
        String []pathArr = path.split(",");
        if(pathArr.length == 0){
            throw new BusinessException(CommonErrorCodeEnum.PARAM_ERROR);
        }
        log.info("getImgUrl req:{}",JSON.toJSONString(pathArr));
        List<String> result = ossFeignClient.getDownloadSignedUrl(Arrays.asList(pathArr)).result();
        log.info("getImgUrl res:{}",JSON.toJSONString(result));
        List<String> newStr = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            String s = result.get(i);
            String s1 = s.replaceAll("&amp;","&");
            newStr.add(s1);
        }
        return success(newStr);
    }
}
