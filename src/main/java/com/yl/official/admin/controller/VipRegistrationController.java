package com.yl.official.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import com.yl.official.admin.annotation.ExcludeInterceptor;
import com.yl.official.admin.dto.VipRegistrationDTO;
import com.yl.official.admin.dto.VipRegistrationQueryDto;
import com.yl.official.admin.service.VipRegistrationService;
import com.yl.official.admin.utils.ExcelUtil;
import com.yl.official.admin.vo.VipRegistrationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 字典表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-9
 */
@RestController
@Api(value = "VipRegistrationController", tags = {"大客户登记"})
@RequestMapping("/vipRegistration")
public class VipRegistrationController extends BaseController {
    @Autowired
    private VipRegistrationService vipRegistrationService;

    private static final String VIPREGISTRATION = "vipRegistration.xlsx";

    /**
     * 分页查询
     */
    @PostMapping("/selectPage")
    @ApiOperation(value = "selectPage", notes = "分页查询大客户登记")
    public Result<Page<VipRegistrationVo>> selectPage(@RequestBody VipRegistrationQueryDto dto){
        Page<VipRegistrationVo> values = vipRegistrationService.selectPage(dto,dto.getPage(),dto.getSize());
        return Result.success(values);
    }

    /**
     *  新增大客户登记信息数据
     */
    @PostMapping("/addVipRegistration")
    @ApiOperation(value = "addVipRegistration", notes = "新增大客户登记信息")
    @ExcludeInterceptor
    public Result<Integer> addVipRegistration(@RequestBody @Valid VipRegistrationDTO dto){
        return Result.success(vipRegistrationService.add(dto));
    }

    /**
     * 导出Excel 1000行  前端传数据
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    public void export(@RequestBody VipRegistrationQueryDto dto) throws Exception {
        XSSFWorkbook xssfWorkbook = vipRegistrationService.vipRegistrationExcel(dto);
        ExcelUtil.buildExcelFile(VIPREGISTRATION, xssfWorkbook);
        ExcelUtil.buildExcelDocument(VIPREGISTRATION, xssfWorkbook, response);
    }

}
