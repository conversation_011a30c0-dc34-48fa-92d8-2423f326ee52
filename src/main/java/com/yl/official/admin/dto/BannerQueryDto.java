package com.yl.official.admin.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * banner表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BannerQueryDto", description = "banner表")
public class BannerQueryDto implements Serializable {

    @ApiModelProperty(value = "banner标题")
    private String title;

    @ApiModelProperty(value = "banner状态；1 待发布   2已发布 3已下线  4已删除")
    private Integer bannerStatus;


    @ApiModelProperty(value = "发布开始时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "发布结束时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    private String createTimeStartStr;

    private String createTimeEndStr;

    @NotNull
    private Long page;

    @NotNull
    private Long size;

    public void setBeginTime(LocalDateTime beginTime){
        this.beginTime = beginTime;
        this.createTimeStartStr = LocalDateTimeUtil.format(beginTime, DatePattern.NORM_DATETIME_PATTERN);
    }

    public void setEndTime(LocalDateTime endTime){
        this.endTime=endTime;
        this.createTimeEndStr = LocalDateTimeUtil.format(endTime, DatePattern.NORM_DATETIME_PATTERN);
    }
}
