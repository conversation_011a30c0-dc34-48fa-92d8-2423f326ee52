package com.yl.official.admin.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.exception.BusinessException;
import com.yl.official.admin.dto.DictAddDto;
import com.yl.official.admin.dto.DictDto;
import com.yl.official.admin.dto.DictQueryDto;
import com.yl.official.admin.entity.Dict;
import com.yl.official.admin.enums.exception.CommonErrorCodeEnum;
import com.yl.official.admin.mapper.DictMapper;
import com.yl.official.admin.service.IDictService;
import com.yl.official.admin.utils.SessionUtil;
import com.yl.official.admin.vo.DictVo;
import com.yl.official.admin.vo.UserSessionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@Service
@Slf4j
public class DictServiceImpl extends ServiceImpl<DictMapper, Dict> implements IDictService {

    @Autowired
    private DictMapper dictMapper;
    @Autowired
    private SessionUtil sessionUtil;
    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    /**
     * 查询字典表
     */
    @Override
    public List<DictVo> selectDictList(DictDto dto) {
        log.info("查询字典表入参:{}", JSON.toJSONString(dto));
        return dictMapper.selectDictList(dto);
    }

    /**
     * 分页查询字典表
     */
    @Override
    public Page<DictVo> selectPageDict(DictQueryDto dto, Long current, Long size) {
        log.info("分页查询字典表入参:{}", JSON.toJSONString(dto));
        Page<DictVo> page = new Page<>();
        page.setCurrent(current);
        page.setSize(size);
        return dictMapper.selectPageDict(page, dto);
    }

    /**
     * 分页查询字典表
     */
    @Override
    public Page<DictVo> getPageDict(DictDto dto, Long current, Long size) {
        Page<DictVo> page = new Page<>();
        page.setCurrent(current);
        page.setSize(size);
        QueryWrapper<DictVo> qw = new QueryWrapper<>();
        return dictMapper.getPageDict(page, qw);
    }

    /**
     * 新增数据
     */
    @Override
    public Boolean addBatch(List<DictAddDto> dtoList) {
        log.info("新增职位类型入参:{}", JSON.toJSONString(dtoList));
        Set<String> countrySet = new HashSet<>();
        Long number = IdUtil.getSnowflake().nextId();
        List<Dict> list = new ArrayList<>();
        int i = 0;
        for (DictAddDto dto : dtoList) {
            DictVo dictVo = dictMapper.getInfoByName(dto.getDictName(),dto.getCountry());
            if (dictVo != null) {
                throw new BusinessException(CommonErrorCodeEnum.DICT_NAME_ERROR);
            }
            i++;
            String code = "JT" + String.format("%010d", dictMapper.countAll() + i);
            dto.setDictCode(code);
            Dict vDto = orikaBeanMapper.map(dto, Dict.class);
            UserSessionVo userSessionVo = sessionUtil.getUser();
            vDto.setCreateTime(LocalDateTime.now());
            vDto.setCreateUser(userSessionVo.getUserName());
            vDto.setUpdateTime(LocalDateTime.now());
            vDto.setUpdateUser(userSessionVo.getUserName());
            vDto.setInfoNumber(number);
            countrySet.add(dto.getCountry());
            list.add(vDto);
        }
        if (countrySet.size() != dtoList.size()) {
            throw new BusinessException(CommonErrorCodeEnum.COUNTRY_ERROR);
        }
        log.info("新增职位类型:{}", JSON.toJSONString(list));
        return this.saveBatch(list);
    }

    /**
     * 根据id修改数据
     */
    @Override
    public Boolean editBatch(List<DictDto> dtoList) {
        log.info("修改职位类型入参:{}", JSON.toJSONString(dtoList));
        Set<String> countrySet = new HashSet<>();
        List<Dict> updateList = new ArrayList<>();

        UserSessionVo userSessionVo = sessionUtil.getUser();
        for (DictDto dto : dtoList) {
            if (dto.getInfoNumber() == null) {
                throw new BusinessException(CommonErrorCodeEnum.NUMBER_ERROR);
            }
            DictVo dictVo = dictMapper.getInfoByName(dto.getDictName(),dto.getCountry());
            if (dictVo != null && !dictVo.getId().equals(dto.getId())) {
                throw new BusinessException(CommonErrorCodeEnum.DICT_NAME_ERROR);
            }
            Dict vDto = orikaBeanMapper.map(dto, Dict.class);
            vDto.setUpdateTime(LocalDateTime.now());
            vDto.setUpdateUser(userSessionVo.getUserName());
            updateList.add(vDto);
            countrySet.add(dto.getCountry());
        }
        if (countrySet.size() != dtoList.size()) {
            throw new BusinessException(CommonErrorCodeEnum.COUNTRY_ERROR);
        }
        return this.updateBatchById(updateList);
    }

    /**
     * 删除数据
     */
    @Override
    public Integer deleteDict(Long infoNumber) {
        log.info("删除职位类型入参:{}", JSON.toJSONString(infoNumber));
        return dictMapper.deleteDict(infoNumber);
    }

    /**
     * 删除数据
     */
    @Override
    public Integer delById(Long id) {
        return dictMapper.delById(id);
    }

    /**
     * 根据id数据
     */
    @Override
    public List<DictVo> getInfo(Long infoNumber) {
        log.info("详情接口职位类型入参:{}", JSON.toJSONString(infoNumber));
        return dictMapper.getInfo(infoNumber);
    }
}
