package com.yl.official.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.exception.BusinessException;
import com.yl.official.admin.dto.JoinCooperationDto;
import com.yl.official.admin.dto.JoinCooperationQueryDto;
import com.yl.official.admin.entity.JoinCooperation;
import com.yl.official.admin.enums.exception.CommonErrorCodeEnum;
import com.yl.official.admin.mapper.JoinCooperationMapper;
import com.yl.official.admin.service.ChapterCheckService;
import com.yl.official.admin.service.JoinCooperationService;
import com.yl.official.admin.utils.ExcelUtil;
import com.yl.official.admin.utils.StringUtils;
import com.yl.official.admin.vo.JoinCooperationExcelVo;
import com.yl.official.admin.vo.JoinCooperationVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 * 字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@Service
@Slf4j
public class JoinCooperationServiceImpl implements JoinCooperationService {

    @Autowired
    private JoinCooperationMapper joinCooperationMapper;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private ChapterCheckService chapterCheckService;

    /**
     * 分页查询字典表
     */
    @Override
    public Page<JoinCooperationVo> selectPage(JoinCooperationQueryDto dto, Long current, Long size) {
        if (dto.getStartTime() != null && dto.getEndTime() != null) {
            boolean timeCheck = dto.getEndTime().isBefore(dto.getStartTime());
            if (timeCheck) {
                throw new BusinessException(CommonErrorCodeEnum.DATE_START_ERROR);
            }
        }
        Page<JoinCooperationVo> page = new Page<>();
        page.setCurrent(current);
        page.setSize(size);
        return joinCooperationMapper.selectPage(page, dto);
    }

    @Override
    public XSSFWorkbook vipRegistrationExcel(JoinCooperationQueryDto dto) {
        String requestId = UUID.randomUUID().toString().replace("-", "");//本次请求id
        log.info("加盟合作导出-requestId==>{}, 入参==>{}", requestId, JSON.toJSONString(dto));

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("加盟合作导出列表");
        List<JoinCooperationExcelVo> joinCooperationExcelVos = joinCooperationMapper.selectList(dto);
        for (JoinCooperationExcelVo joinCooperationExcelVo : joinCooperationExcelVos) {
            if (StringUtils.isNotEmpty(joinCooperationExcelVo.getProvinceName()) && StringUtils.isNotEmpty(joinCooperationExcelVo.getCityName()) && StringUtils.isNotEmpty(joinCooperationExcelVo.getAreaName())) {
                StringBuilder stringBuilder = new StringBuilder();
                String provinceCityAreaName = stringBuilder.append(joinCooperationExcelVo.getProvinceName()).append("-").append(joinCooperationExcelVo.getCityName()).append("-").append(joinCooperationExcelVo.getAreaName()).toString();
                joinCooperationExcelVo.setProvinceCityAreaName(provinceCityAreaName);
            }
            if ("2".equals(joinCooperationExcelVo.getExperience())) {
                joinCooperationExcelVo.setExperience("否");
            } else if ("1".equals(joinCooperationExcelVo.getExperience())) {
                joinCooperationExcelVo.setExperience("是");
            }
            if ("1".equals(joinCooperationExcelVo.getLogisticsIndustry())) {
                joinCooperationExcelVo.setLogisticsIndustry("是");
            }else if ("2".equals(joinCooperationExcelVo.getLogisticsIndustry())){
                joinCooperationExcelVo.setLogisticsIndustry("否");
            }

            if ("1".equals(joinCooperationExcelVo.getCustomerResources())) {
                joinCooperationExcelVo.setCustomerResources("是");
            }else if ("2".equals(joinCooperationExcelVo.getCustomerResources())){
                joinCooperationExcelVo.setCustomerResources("否");
            }

            joinCooperationExcelVo.setInvestmentAmount(conversionInvestmentAmount(joinCooperationExcelVo.getInvestmentAmount()));
        }
        stopWatch.stop();

        stopWatch.start("加盟合作总数");
        Integer total = joinCooperationExcelVos.size();
        stopWatch.stop();

        if (total.intValue() > 10000) {
            throw new BusinessException(CommonErrorCodeEnum.EXCEL_MAX_SIZE_ERROR);
        }

        log.info("加盟合作-requestId==>{}, 总数==>{}, 返回列表==>{}, 总耗时==>{}s, 耗时明细==>{}", requestId, total, JSON.toJSONString(joinCooperationExcelVos), stopWatch.getTotalTimeSeconds(), stopWatch.prettyPrint());
        XSSFWorkbook workbook = ExcelUtil.exportExcel(dto.getSort(), dto.getColumnNames(), joinCooperationExcelVos);
        return workbook;
    }

    private String conversionInvestmentAmount(String investmentAmount) {
        if (StringUtils.isEmpty(investmentAmount)) {
            return "";
        }
        switch (investmentAmount) {
            case "1":
                return "100条以下";
            case "2":
                return "100-200条";
            case "3":
                return "200-500条";
            case "4":
                return "500条以上";
        }
        return "";
    }

    /**
     * 新增数据
     */
    @Override
    public Integer add(JoinCooperationDto dto) {
        JoinCooperation vDto = orikaBeanMapper.map(dto, JoinCooperation.class);
        vDto.setCreateTime(LocalDateTime.now());
        return joinCooperationMapper.insert(vDto);
    }
}
