package com.yl.official.admin.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * banner表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "反馈信息对象", description = "FeedBack表")
public class FeedBackVo implements Serializable {

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "问题id")
    private String questionId;

    @ApiModelProperty(value = "问题名称")
    private String questionName;

    @ApiModelProperty(value = "问题选项1")
    private String option1;

    @ApiModelProperty(value = "问题选项2")
    private String option2;

    @ApiModelProperty(value = "问题选项3")
    private String option3;

    @ApiModelProperty(value = "问题选项4")
    private String option4;

    @ApiModelProperty(value = "问题选项5")
    private String option5;

    @ApiModelProperty(value = "问题选项6")
    private String option6;

    @ApiModelProperty(value = "问题选项7")
    private String option7;

    @ApiModelProperty(value = "问题选项8")
    private String option8;

    @ApiModelProperty(value = "问题选项9")
    private String option9;

    @ApiModelProperty(value = "问题选项10")
    private String option10;

    @ApiModelProperty(value = "图片")
    private String imgUrl;

    @ApiModelProperty(value = "用户答案")
    private String answer;

    @ApiModelProperty(value = "问题类型（1 单选题，2多选择题  3问答题 4图片题）")
    private Integer questionType;

    @ApiModelProperty(value = "提交人")
    private String createBy;
    @ApiModelProperty(value = "提交人手机号码")
    private String createPhone;
    @ApiModelProperty(value = "提交时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "提交人id")
    private Long createId;

    @ApiModelProperty(value = "问卷id")
    private String examId;

    @ApiModelProperty(value = "记录id")
    private String recordId;
}
