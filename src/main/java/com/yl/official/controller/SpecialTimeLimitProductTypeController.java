package com.yl.official.controller;

import com.yl.common.base.model.vo.Result;
import com.yl.official.dto.SpecialTimeLimitProductType;
import com.yl.official.feign.SpecialTimeLimitProductTypeFeignClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(value = "地址管理", tags = {"地址管理"})
@RestController
@RequestMapping("/specialTimeLimitProductType")
@Slf4j
public class SpecialTimeLimitProductTypeController {
    @Autowired
    private SpecialTimeLimitProductTypeFeignClient specialTimeLimitProductTypeFeignClient;

    @GetMapping("/getList")
    @ApiOperation(value = "查询预估时效以及产品类型")
    public Result<List<SpecialTimeLimitProductType>> getList(@RequestParam(value = "sendCityId") Integer sendCityId,
                                                      @RequestParam(value = "dispatchCityId") Integer dispatchCityId,
                                                      @RequestParam(value = "networkCode", required = false) String networkCode){
        return specialTimeLimitProductTypeFeignClient.getList(sendCityId,dispatchCityId,networkCode);
    }

}
