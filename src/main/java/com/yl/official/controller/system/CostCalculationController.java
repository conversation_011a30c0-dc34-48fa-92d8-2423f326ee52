package com.yl.official.controller.system;

import com.alibaba.fastjson.JSON;
import com.yl.common.base.annotation.ExcludeInterceptor;
import com.yl.common.base.model.vo.Result;
import com.yl.official.dto.SpmCommonComCostDTO;
import com.yl.official.service.ICostCalculationService;
import com.yl.official.vo.SpmComCostVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description: 保价费查询
 * @Project:
 * @CreateDate: Created in 2019-08-09 14:17 <br>
 * @Author: zhipeng.liu
 */
@Api(value = "计费", tags = {"计费"})
@RestController
@RequestMapping("/costCalculation")
@Slf4j
public class CostCalculationController extends AppBaseController {

    @Autowired
    private ICostCalculationService costCalculationService;


//    /**
//     * 保价费计算
//     *
//     * @param spmApiInsuranceTrialDTO
//     * @return
//     */
//    @PostMapping("/insuranceFee/computationCost")
//    @ApiOperation(value = "保价费计算", notes = "保价费计算")
//    public Result<BigDecimal> computationCost(@RequestBody SpmApiInsuranceTrialDTO spmApiInsuranceTrialDTO) {
//        log.info("保价费计算,请求参数：【{}】", JSON.toJSONString(spmApiInsuranceTrialDTO));
//        spmApiInsuranceTrialDTO.setProductTypeCode(ExpressTypeEnum.ORDINARY_EXPRESS.getCode());
//        spmApiInsuranceTrialDTO.setDateTime(LocalDateTime.now());
//        return success(costCalculationService.computationCost(spmApiInsuranceTrialDTO));
//    }

    /**
     * 运费计算
     *
     * @param spmCommonComCostDTO
     * @return
     */
    @ApiOperation(value = "运费计算", notes = "运费计算")
    @PostMapping("/spmStandardShippingQuote/comCost")
    @ExcludeInterceptor
    public Result<SpmComCostVO> computationCost(@RequestBody SpmCommonComCostDTO spmCommonComCostDTO) {
        log.info("运费计算,请求参数：【{}】", JSON.toJSONString(spmCommonComCostDTO));
        SpmComCostVO spmComCostVO = costCalculationService.comCost(spmCommonComCostDTO);
        log.info("computationCost.resp:{}",JSON.toJSONString(spmComCostVO));
        return success(spmComCostVO);
    }

}
