package com.yl.official.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 * 算费DTO
 *
 * @project: SpmApiTrialDTO<br>
 * @since  Created in 2019-07-30 18:16
 * <AUTHOR> ou
 */
@Data
@ToString
@ApiModel(description = "算费DTO")
public class SpmCommonComCostDTO extends SpmOmsCommonComCostDTO implements Serializable {

    private static final long serialVersionUID = -7002670393069665933L;

    @NotNull(message = "计费类型不能为空")
    @ApiModelProperty(name = "comType", value = "计费类型(1:计算客户报价,2:计算标准报价,3:计算客户报价+保价,4:计算标准报价+保价)")
    private Integer comCostType;

    @ApiModelProperty(name = "productTypeId", value = "产品类型id")
    private Integer productTypeId;

    @ApiModelProperty(name = "productTypeCode", value = "产品类型CODE")
    private String productTypeCode;

    @ApiModelProperty(name = "serviceMethodCode", value = "服务方式CODE")
    private String serviceMethodCode;

    @ApiModelProperty(name = "goodsTypeId", value = "产品类型id")
    private Integer goodsTypeId;

    @ApiModelProperty(name = "goodsTypeCode", value = "产品类型Code")
    private String goodsTypeCode;

    @ApiModelProperty(value = "所属财务中心id")

    private Integer financialCenterId;


    @ApiModelProperty(name = "startPointId", value = "始发区域ID")
    private Integer startPointId;


    @ApiModelProperty(name = "terminalPointId", value = "目的区域ID")
    private Integer terminalPointId;

    @ApiModelProperty(name = "value", value = "数值")
    private BigDecimal value;

    @ApiModelProperty(name = "packageNumber", value = "下单件数")
    private BigDecimal packageNumber;

    @ApiModelProperty(name = "value", value = "保价费物品价值")
    private BigDecimal goodsValue;

    @NotNull(message = "计算时间不能为空")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "dateTime", value = "计算时间")
    private LocalDateTime dateTime;

    @ApiModelProperty(name = "networkId", value = "网点ID可空,空时直接获取总部标准报价")
    private Integer networkId;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "运单ID")
    private Long waybillId;

    @ApiModelProperty(value = "结算方式(1:寄件现结,2寄付月结,3:到付现结)")
    private Integer smMode;


    @ApiModelProperty(value = "始发省份ID")
    private Integer startProviderId;

    @ApiModelProperty(
            value = "始发城市ID",
            name = "cityId",
            required = true
    )
    private Integer startCityId;

    @ApiModelProperty(
            value = "始发区县ID",
            name = "startAreaId",
            required = true
    )
    private Integer startAreaId;

    @ApiModelProperty(value = "目的省份ID")
    private Integer endProviderId;

    @ApiModelProperty(
            value = "目的城市ID",
            name = "cityId",
            required = true
    )
    private Integer endCityId;

    @ApiModelProperty(
            value = "目的区县ID",
            name = "endAreaId",
            required = true
    )
    private Integer endAreaId;

    @ApiModelProperty(value = "包裹总长,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "The total length of the parcel exceeds the allowed range (only 5 integers and 2 decimal places are allowed)")
    private BigDecimal packageLength;

    @ApiModelProperty(value = "包裹总宽,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "The total width of the parcel is out of the allowed range (only within the range of 5 integers and 2 decimals)")
    private BigDecimal packageWide;

    @ApiModelProperty(value = "包裹总高,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "The total height of the parcel exceeds the allowed range (only within 5 integers and 2 decimal places)")
    private BigDecimal packageHigh;


    /*滑动验证码参数*/
    @ApiModelProperty(value = "前端回调函数返回的用户验证票据")
    private String ticket;

    /*滑动验证码参数*/
    @ApiModelProperty(value = "前端回调函数返回的随机字符串")
    private String randstr;
    @ApiModelProperty(value = "是否手机端登录访问")
    private Integer isApp;

}
