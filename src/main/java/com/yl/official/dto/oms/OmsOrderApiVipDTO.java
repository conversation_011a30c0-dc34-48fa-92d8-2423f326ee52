package com.yl.official.dto.oms;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.yl.common.base.model.dto.BaseApiDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel(
        value = "OmsOrder请求对象",
        description = "VIP订单请求实体"
)
@JsonInclude(Include.NON_NULL)
@Data
@ToString
public class OmsOrderApiVipDTO extends BaseApiDTO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("客户订单编号")
    @Size(
            max = 50,
            message = "客户订单编号不能超过50个字符"
    )
    private String customerOrderId;
    @ApiModelProperty("客户编号code")
    @Size(
            max = 30,
            message = "客户编号不能超过30个字符"
    )
    @NotBlank(
            message = "客户编号code不能为空"
    )
    private String customerCode;
    @ApiModelProperty("订单编号")
    @Null(
            message = "编号不能有值"
    )
    private Long id;
    @ApiModelProperty("运单号")
    @Size(
            max = 30,
            message = "运单号不能超过30个字符"
    )
    private String waybillId;
    @ApiModelProperty("订单类型code，1.散客 2.月结")
    private Integer orderTypeCode;
    @ApiModelProperty("订单来源名称")
    @Size(
            max = 30,
            message = "订单来源名称不能超过30个字符"
    )
    private String orderSourceName;
    @ApiModelProperty("订单来源code")
    @NotBlank(
            message = "订单来源code不能为空"
    )
    @Size(
            max = 30,
            message = "订单来源code不能超过30个字符"
    )
    private String orderSourceCode;
    @ApiModelProperty("最佳取件开始时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private LocalDateTime bestPickTimeStart;
    @ApiModelProperty("最佳取件结束时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private LocalDateTime bestPickTimeEnd;
    @ApiModelProperty("支付方式名称")
    @Size(
            max = 30,
            message = "支付方式名称不能超过30个字符"
    )
    private String paidModeName;
    @ApiModelProperty("支付方式code")
    @Size(
            max = 30,
            message = "支付方式code不能超过30个字符"
    )
    private String paidModeCode;
    @ApiModelProperty("支付方式id")
    private Integer paidModeId;
    @ApiModelProperty("寄件人姓名")
    @Size(
            max = 100,
            message = "寄件人姓名不能超过100个字符"
    )
    @NotBlank(
            message = "寄件人姓名不能为空"
    )
    private String senderName;
    @ApiModelProperty("寄件人公司")
    @Size(
            max = 100,
            message = "寄件人公司不能超过100个字符"
    )
    private String senderCompany;
    @ApiModelProperty("寄件人手机号")
    @Size(
            max = 30,
            message = "寄件人手机号不能超过30个字符"
    )
    @NotBlank(
            message = "寄件人手机号不能为空"
    )
    private String senderMobilePhone;
    @ApiModelProperty("寄件人座机")
    @Size(
            max = 30,
            message = "寄件人固话不能超过30个字符"
    )
    private String senderTelphone;
    @ApiModelProperty("寄件国家名称")
    @Size(
            max = 60,
            message = "寄件国家名称不能超过60个字符"
    )
    private String senderCountryName;
    @ApiModelProperty("寄件国家Id")
    private Integer senderCountryId;
    @ApiModelProperty("寄件省份名称")
    @Size(
            max = 60,
            message = "寄件省份名称不能超过60个字符"
    )
    @NotBlank(
            message = "寄件省份名称不能为空"
    )
    private String senderProvinceName;
    @ApiModelProperty("寄件省份id")
    @NotNull(
            message = "寄件省份id不能为空"
    )
    private Integer senderProvinceId;
    @ApiModelProperty("寄件城市名称")
    @Size(
            max = 60,
            message = "寄件城市名称不能超过60个字符"
    )
    @NotBlank(
            message = "寄件城市名称不能为空"
    )
    private String senderCityName;
    @ApiModelProperty("寄件城市id")
    @NotNull(
            message = "寄件城市id不能为空"
    )
    private Integer senderCityId;
    @ApiModelProperty("寄件区域名称")
    @Size(
            max = 60,
            message = "寄件区域名称不能超过60个字符"
    )
    @NotBlank(
            message = "寄件区域名称不能为空"
    )
    private String senderAreaName;
    @ApiModelProperty("寄件区域Id")
    @NotNull(
            message = "寄件区域Id不能为空"
    )
    private Integer senderAreaId;
    @ApiModelProperty("寄件乡镇")
    @Size(
            max = 200,
            message = "寄件乡镇不能超过200个字符"
    )
    private String senderTownship;
    @ApiModelProperty("寄件街道")
    @Size(
            max = 200,
            message = "不能超过200个字符"
    )
    private String senderStreet;
    @ApiModelProperty("寄件详细地址")
    @Size(
            max = 255,
            message = "寄件详细地址不能超过255个字符"
    )
    @NotBlank(
            message = "寄件详细地址不能为空"
    )
    private String senderDetailedAddress;
    @ApiModelProperty("寄件邮编")
    @Size(
            max = 60,
            message = "寄件邮编不能超过60个字符"
    )
    private String senderPostalCode;
    @ApiModelProperty("寄件人邮箱")
    @Size(
            max = 150,
            message = "寄件人邮箱不能超过150个字符"
    )
    private String senderEmail;
    @ApiModelProperty("签收短信通知,1需要，0不需要")
    @Min(
            value = 0L,
            message = "签收短信通知只能为0或者1"
    )
    @Max(
            value = 1L,
            message = "签收短信通知只能为0或者1"
    )
    private Integer signSmsNotify;
    @ApiModelProperty("寄件短信通知,1需要，0不需要")
    @Min(
            value = 0L,
            message = "寄件短信通知只能为0或者1"
    )
    @Max(
            value = 1L,
            message = "寄件短信通知只能为0或者1"
    )
    private Integer senderSmsNotify;
    @ApiModelProperty("寄件服务方式名称")
    @Size(
            max = 30,
            message = "寄件服务方式名称不能超过30个字符"
    )
    private String sendName;
    @ApiModelProperty("寄件服务方式code")
    @NotBlank(
            message = "寄件服务方式code不能为空"
    )
    @Size(
            max = 30,
            message = "寄件服务方式code不能超过30个字符"
    )
    private String sendCode;
    @ApiModelProperty("收件人姓名")
    @Size(
            max = 100,
            message = "收件人姓名不能超过100个字符"
    )
    @NotBlank(
            message = "收件人姓名不能为空"
    )
    private String receiverName;
    @ApiModelProperty("收件人公司")
    @Size(
            max = 100,
            message = "收件人公司不能超过100个字符"
    )
    private String receiverCompany;
    @ApiModelProperty("收件人手机号")
    @Size(
            max = 30,
            message = "收件人手机号不能超过30个字符"
    )
    @NotBlank(
            message = "手机号不能为空"
    )
    private String receiverMobilePhone;
    @ApiModelProperty("收件人座机")
    @Size(
            max = 30,
            message = "收件人固话不能超过30个字符"
    )
    private String receiverTelphone;
    @ApiModelProperty("收件国家名称")
    @Size(
            max = 60,
            message = "收件国家名称不能超过60个字符"
    )
    private String receiverCountryName;
    @ApiModelProperty("收件国家id")
    private Integer receiverCountryId;
    @ApiModelProperty("收件省份名称")
    @Size(
            max = 60,
            message = "收件省份名称不能超过60个字符"
    )
    @NotBlank(
            message = "收件省份名称不能为空"
    )
    private String receiverProvinceName;
    @ApiModelProperty("收件省份id")
    @NotNull(
            message = "收件省份id不能为空"
    )
    private Integer receiverProvinceId;
    @ApiModelProperty("收件城市名称")
    @Size(
            max = 60,
            message = "收件城市名称不能超过60个字符"
    )
    @NotBlank(
            message = "收件城市名称不能为空"
    )
    private String receiverCityName;
    @ApiModelProperty("收件城市id")
    @NotNull(
            message = "收件城市id不能为空"
    )
    private Integer receiverCityId;
    @ApiModelProperty("收件区域名称")
    @Size(
            max = 60,
            message = "收件区域名称不能超过60个字符"
    )
    @NotBlank(
            message = "收件区域名称不能为空"
    )
    private String receiverAreaName;
    @ApiModelProperty("收件区域id")
    @NotNull(
            message = "收件区域id不能为空"
    )
    private Integer receiverAreaId;
    @ApiModelProperty("收件乡镇")
    @Size(
            max = 200,
            message = "收件乡镇不能超过200个字符"
    )
    private String receiverTownship;
    @ApiModelProperty("收件街道")
    @Size(
            max = 200,
            message = "收件街道不能超过200个字符"
    )
    private String receiverStreet;
    @ApiModelProperty("收件详细地址")
    @Size(
            max = 255,
            message = "收件详细地址不能超过255个字符"
    )
    @NotBlank(
            message = "收件详细地址不能为空"
    )
    private String receiverDetailedAddress;
    @ApiModelProperty("收件邮编")
    @Size(
            max = 60,
            message = "收件邮编不能超过60个字符"
    )
    private String receiverPostalCode;
    @ApiModelProperty("收件人邮箱")
    private String receiverEmail;
    @ApiModelProperty("派件服务方式名称")
    @Size(
            max = 60,
            message = "派件服务方式名称不能超过60个字符"
    )
    private String dispatchName;
    @ApiModelProperty("派件服务方式code")
    @Size(
            max = 30,
            message = "派件服务方式code不能超过30个字符"
    )
    private String dispatchCode;
    @ApiModelProperty("收件分拣码")
    @Size(
            max = 30,
            message = "收件分拣码不能超过30个字符"
    )
    private String receiverSortingCode;
    @ApiModelProperty("备注")
    @Size(
            max = 200,
            message = "备注不能超过200个字符"
    )
    private String remarks;
    @ApiModelProperty("快件类型id")
    private Integer expressTypeId;
    @ApiModelProperty("快件类型code")
    @Size(
            max = 30,
            message = "快件类型编码不能超过30个字符"
    )
    private String expressTypeCode;
    @ApiModelProperty("快件类型名称")
    @Size(
            max = 30,
            message = "快件类型名称不能超过30个字符"
    )
    private String expressTypeName;
    @ApiModelProperty("需要保价1是，0否")
    @Min(
            value = 0L,
            message = "需要保价只能为0或者1"
    )
    @Max(
            value = 1L,
            message = "需要保价只能为0或者1"
    )
    @NotNull(
            message = "需要保价不能为空"
    )
    private Integer insured;
    @ApiModelProperty("保价金额")
    @Digits(
            integer = 10,
            fraction = 2,
            message = "保价金额超出了允许范围(只允许在10位整数和2位小数范围内)"
    )
    private BigDecimal declaredValue;
    @ApiModelProperty("保价费")
    @Digits(
            integer = 10,
            fraction = 2,
            message = "保价费超出了允许范围(只允许在10位整数和2位小数范围内)"
    )
    private BigDecimal insuredValue;
    @ApiModelProperty("标准运费")
    @Digits(
            integer = 10,
            fraction = 2,
            message = "标准运费超出了允许范围(只允许在10位整数和2位小数范围内)"
    )
    private BigDecimal standardValue;
    @ApiModelProperty("总费用")
    @Digits(
            integer = 10,
            fraction = 2,
            message = "总费用超出了允许范围(只允许在10位整数和2位小数范围内)"
    )
    private BigDecimal totalFreight;
    @ApiModelProperty("需要代收货款,1是，0否")
    @Min(
            value = 0L,
            message = "是否需要代收货款只能为0或者1"
    )
    @Max(
            value = 1L,
            message = "是否需要代收货款只能为0或者1"
    )
    private Integer codNeed;
    @ApiModelProperty("代收货款金额")
    @Digits(
            integer = 10,
            fraction = 2,
            message = "代收货款金额超出了允许范围(只允许在10位整数和2位小数范围内)"
    )
    private BigDecimal codMoney;
    @ApiModelProperty("代收货款手续费")
    @Digits(
            integer = 10,
            fraction = 2,
            message = "代收货款手续费超出了允许范围(只允许在10位整数和2位小数范围内)"
    )
    private BigDecimal codFee;
    @ApiModelProperty("包材规格名称")
    @Size(
            max = 60,
            message = "包材规格名称不能超过60个字符"
    )
    private String boxStandardName;
    @ApiModelProperty("包材数量")
    private Integer boxNumber;
    @ApiModelProperty("包材规格id")
    private Long boxStandardId;
    @ApiModelProperty("包材规格code")
    private String boxStandardCode;
    @ApiModelProperty("包材单价")
    @Digits(
            integer = 10,
            fraction = 2,
            message = "包材单价超出了允许范围(只允许在10位整数和2位小数范围内)"
    )
    private BigDecimal boxPrice;
    @ApiModelProperty("代收货款币别名称")
    @Size(
            max = 32,
            message = "代收货款币别名称不能超过32个字符"
    )
    private String codCurrencyTypeName;
    @ApiModelProperty("代收货款币别code")
    @Size(
            max = 32,
            message = "代收货款币别编码不能超过32个字符"
    )
    private String codCurrencyTypeCode;
    @ApiModelProperty("物品类型名称")
    @Size(
            max = 60,
            message = "物品类型名称不能超过60个字符"
    )
    @NotBlank(
            message = "物品类型名称不能为空"
    )
    private String goodsTypeName;
    @ApiModelProperty("物品类型id")
    private Integer goodsTypeId;
    @ApiModelProperty("物品类型code")
    @Size(
            max = 30,
            message = "物品类型编码不能超过30个字符"
    )
    @NotBlank(
            message = "物品类型编码不能为空"
    )
    private String goodsTypeCode;
    @ApiModelProperty("物品名称")
    @Size(
            max = 200,
            message = "物品名称不能超过200个字符"
    )
    private String goodsName;
    @ApiModelProperty("应收运费")
    private BigDecimal receivableFreight;
    @ApiModelProperty("件数,＞1表示子母件，如果业务上不支持子母件，则前端限制不显示，并默认为1")
    @Min(
            value = 1L,
            message = "包裹数量不能小于1"
    )
    @Max(
            value = 99L,
            message = "包裹数量不能超过99"
    )
    @NotNull(
            message = "件数不能为空"
    )
    private Integer packageNumber;
    @ApiModelProperty("包裹总长,单位厘米")
    @Digits(
            integer = 5,
            fraction = 2,
            message = "包裹总长超出了允许范围(只允许在5位整数和2位小数范围内)"
    )
    private BigDecimal packageLength;
    @ApiModelProperty("包裹总宽,单位厘米")
    @Digits(
            integer = 5,
            fraction = 2,
            message = "包裹总宽超出了允许范围(只允许在5位整数和2位小数范围内)"
    )
    private BigDecimal packageWide;
    @ApiModelProperty("包裹总高,单位厘米")
    @Digits(
            integer = 5,
            fraction = 2,
            message = "包裹总高超出了允许范围(只允许在5位整数和2位小数范围内)"
    )
    private BigDecimal packageHigh;
    @ApiModelProperty("包裹体积重,单位立方厘米")
    @Digits(
            integer = 5,
            fraction = 2,
            message = "包裹体积重超出了允许范围(只允许在5位整数和2位小数范围内)"
    )
    private BigDecimal packateVolume;
    @ApiModelProperty("包裹计费重量,单位千克")
    @Digits(
            integer = 5,
            fraction = 2,
            message = "包裹计费重量,超出了允许范围(只允许在5位整数和2位小数范围内)"
    )
    private BigDecimal packageChargeWeight;
    @ApiModelProperty("包裹总重量,单位千克")
    @Digits(
            integer = 5,
            fraction = 2,
            message = "包裹总重量超出了允许范围(只允许在5位整数和2位小数范围内)"
    )
    @DecimalMin(
            value = "0.01",
            message = "包裹总重量必须大于或等于0.01"
    )
    @NotNull(
            message = "包裹总重量不能为空"
    )
    private BigDecimal packageTotalWeight;
    @ApiModelProperty("结算方式名称")
    @Size(
            max = 60,
            message = "结算方式名称不能超过60个字符"
    )
    @NotBlank(
            message = "结算方式名称不能为空"
    )
    private String paymentModeName;
    @ApiModelProperty("结算方式id")
    private Integer paymentModeId;
    @ApiModelProperty("结算方式code")
    @Size(
            max = 30,
            message = "结算方式编码不能超过30个字符"
    )
    @NotBlank(
            message = "结算方式编码不能为空"
    )
    private String paymentModeCode;
    @ApiModelProperty("客户id")
    @NotNull(
            message = "客户id不能为空"
    )
    private Integer customerId;
    @ApiModelProperty("客户名称")
    @Size(
            max = 60,
            message = "客户名称不能超过60个字符"
    )
    private String customerName;
    @ApiModelProperty("寄件国家三字码")
    private String senderCountryCode;
    @ApiModelProperty("收件国家三字码")
    private String receiverCountryCode;
    @ApiModelProperty("商品信息")
    private List<OmsOrderMemberDTO> items;
    @ApiModelProperty("取件网点code")
    private String pickNetworkCode;
    @ApiModelProperty("取件网点id")
    private Long pickNetworkId;
    @ApiModelProperty("取件网点名称")
    private String pickNetworkName;
    @ApiModelProperty("调度网点时间")
    private LocalDateTime dispatchNetworkTime;
    @ApiModelProperty("录入人ID")
    private Integer createBy;
    @ApiModelProperty("录入人编码")
    private String createByCode;
    @ApiModelProperty("录入人名称")
    private String createByName;
    @ApiModelProperty("客户下单时间")
    private LocalDateTime customerOrderTime;
    @ApiModelProperty("VIP客户自定义订单id")
    private Long formIndex;
    @ApiModelProperty("下单错误信息")
    private String errorMsg;
    @ApiModelProperty("结算重量,单位千克")
    private BigDecimal settlementWeight;
    @ApiModelProperty("是否有网点信息")
    private boolean hasNetWorkInfo;
    @ApiModelProperty("是否需要算费")
    private boolean needCalculationTotalFreight = true;
    @JsonIgnore
    private boolean goodsNameIsTooLong;
    private String realName;
    private String idNo;
    private Integer idNoType;
    /** 签回单 0否   1是  */
    private Integer signReceipt;

    /** 回单金额 */
    private BigDecimal receiptFreight;

    /**
     * 是否隐私面单 0:否 1:是
     */
    private Integer isPrivacy;
}
