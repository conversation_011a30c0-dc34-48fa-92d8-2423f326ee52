package com.yl.official.enums;
/**
 *
 * @return
 * <AUTHOR>
 */

/**
 * @program:
 *
 * @description: 应用模块名称常量
 *
 * @author: gzh
 *
 * @create: 2021-07-26 20:26
 **/
public enum ContextConstant {

    /**
     * 用户信息
     */
    USER_INFO("userInfo"),

    /**
     * 是否验签
     */
    ISSIGN("isSign"),

    /**
     * 签名
     */
    SIGN("sign"),

    /**
     * uri
     */
    URI("uri"),

    /**
     * 签名密钥
     */
    SIGNSECRET("signSecret");

    public String attr;

    ContextConstant(String attr){
        this.attr = attr;
    }
}
