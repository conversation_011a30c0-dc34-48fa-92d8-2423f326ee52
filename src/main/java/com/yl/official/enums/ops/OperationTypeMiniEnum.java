package com.yl.official.enums.ops;

import lombok.AllArgsConstructor;
import lombok.Getter;


@AllArgsConstructor
@Getter
public enum OperationTypeMiniEnum {

    COLLECTED,                  //已揽收  已取件
    TRANSPORTATIONED,           //运送中  运输中
    DELIVERYED,                 //派送中
    SIGNED,                     //已签收
    REMAIN,                     //留仓件
    REJECTION,                  //问题件客户拒收
    CANCEL,                     //取消
    ;


}
