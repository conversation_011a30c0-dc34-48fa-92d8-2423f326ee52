package com.yl.official.feign;

import com.yl.common.base.model.vo.Result;
import com.yl.official.dto.lmdm.NetworkQueryDTO;
import com.yl.official.feign.config.FeignConfiguration;
import com.yl.official.vo.lmdm.OrderNetworkAreaInfoVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(
        name = "ylnetworkapi",
        path = "/networkapi",
//        url="***********:31753",
        configuration = {FeignConfiguration.class})
public interface SysNetworkFeignClient {

    /**
     * 查询网点
     *
     * @param networkQueryDTO
     * @return
     */
    @PostMapping({"/web/order/network/queryNetworkByAreaInfo"})
    Result<List<OrderNetworkAreaInfoVO>> queryNetworkByAreaInfo(NetworkQueryDTO networkQueryDTO);


    @GetMapping("/web/vip/network/getToPaymentLimit")
    @ApiOperation(value = "查询到付款限额")
    Result<Long> getToPaymentLimit(@RequestParam(value = "code") String code);
}
