package com.yl.official.feign.fallback;

import com.yl.common.base.model.vo.Result;
import com.yl.official.feign.ImgconfigFeignClient;
import com.yl.official.vo.ImgConfigVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class ImgconfigFeignClientFallBack implements ImgconfigFeignClient {
    @Override
    public Result<List<ImgConfigVo>> queryOnline() {
        return null;
    }
}
