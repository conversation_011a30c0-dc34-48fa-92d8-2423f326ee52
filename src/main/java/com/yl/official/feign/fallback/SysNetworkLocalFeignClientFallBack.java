package com.yl.official.feign.fallback;

import com.yl.common.base.model.vo.Result;
import com.yl.official.feign.SysNetworkLocalFeignClient;
import com.yl.official.vo.lmdm.SysNetworkVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.List;

@Slf4j
@Component
public class SysNetworkLocalFeignClientFallBack implements SysNetworkLocalFeignClient {

    @Override
    public Result<SysNetworkVO> getDetailByCode(String var1) {
        return null;
    }

    @Override
    public Result<List<SysNetworkVO>> getDetailByCodes(List<String> var1) {
        return null;
    }

    @Override
    public Result<List<SysNetworkVO>> getByName(String name) {
        return null;
    }

    @Override
    public Result<List<SysNetworkVO>> getNearbySysNetworkVo(double latitude,double longitude){
        return null;
    }
}

