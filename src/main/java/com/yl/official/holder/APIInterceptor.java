package com.yl.official.holder;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-07-07 17:46
 */

import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.util.IpAddressUtil;
import com.yl.official.annotation.AccessLimit;
import com.yl.official.enums.exception.OfficialExceptionEnum;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @createDate :2020/7/18 6:54 下午
 * @desc : 接口拦截器
 */
@Component
@SuppressWarnings("all")
@Slf4j
public class APIInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private RedisUtil redisUtil;

    @Value("${verificationCode.expiration:600}")
    private Long expiration;

    @Value("${verificationCode.seconds:5}")
    private Long seconds;

    @Value("${verificationCode.maxCount:5}")
    private Long maxCount;
    /**
     * 预处理回调方法，实现处理器的预处理(如登陆检查/判断同一对象短时间内是否重复调用接口等) 第三个参数为相应的处理器即controller
     * f返回true表示流程继续，调用下一个拦截器或者处理器，返回false表示流程中断，通过response产生响应
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        //判断同一用户短时间内是否重复请求接口
//        log.info("=======request path====="+ request.getRequestURI());
        //判断请求是否属于方法的请求
        if (handler instanceof HandlerMethod) {
            HandlerMethod hm = (HandlerMethod) handler;
            //获取方法中的注解,看是否有该注解
            AccessLimit accessLimit = hm.getMethodAnnotation(AccessLimit.class);
            if (accessLimit == null) {
                return true;
            }
            String ip = IpAddressUtil.getIpAdrress(request);
            log.info("===accessLimit========" + accessLimit);
            //统计该IP请求次数间隔时间
            String timeKey = "ORDER_OFFICEIAL:VERIFYCODE:REQUEST:" + request.getServletPath() + ":" + ip;
            //统计今天该IP请求了多少次
            String countKey = "ORDER_OFFICEIAL:VERIFYCODE:REQUEST:" + request.getServletPath() + ":" + ip + "：count";
            Object timeObj = redisUtil.get(timeKey);
            Object countTObj = redisUtil.get(countKey);
            log.info("===重复请求:{}:{}", request.getServletPath(), ip);

            Integer count = 0;
            if (timeObj != null) {
                throw new ServiceException(OfficialExceptionEnum.OP_REPETITION_ERR);
            }
            if (countTObj != null) {
                count = Integer.valueOf(countTObj.toString());
            }
            if (count < maxCount) {
                count = count + 1;
                redisUtil.setEx(timeKey, "1", seconds);
                redisUtil.setEx(countKey, count + "", expiration);
                return true;
            } else {
                throw new ServiceException(OfficialExceptionEnum.OP_REPETITION_TIME_ERR);
            }
        }
        return super.preHandle(request, response, handler);
    }

    /**
     * 当请求进行处理之后，也就是controller方法调用之后执行，但是他会在DispatcherServlet进行视图渲染之前被调用
     * 此时我们可以通过modelAndView对模型数据进行处理或对视图进行处理
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {

    }

    /**
     * 方法将在整个请求结束之后，也就是DispatcheServlet进行视图渲染之后执行，这个方法的主要作用是对资源的清理工作
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {

    }
}