package com.yl.official.utils;

/**
 * @program: yl-jms-ibk-customer-schedule
 *
 * @description:
 *
 * @author: gzh
 *
 * @create: 2021-07-26 20:27
 **/
public class ContextUtils {

    private static ThreadLocal<Context> currentLocalContext = new InheritableThreadLocal<>();


    public static Context get() {
        return currentLocalContext.get();
    }

    public static void set(Context context) {
        currentLocalContext.set(context);
    }

    public static void unset() {
        currentLocalContext.remove();
    }

    public Object getGlobalVariable(String key) {
        Object result = null;

        Context context = get();
        if (context != null) {
            result = context.getGlobalVariable(key);
        }

        return result;
    }
}
