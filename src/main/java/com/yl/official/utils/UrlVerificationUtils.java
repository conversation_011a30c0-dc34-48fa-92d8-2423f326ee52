package com.yl.official.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description: Url 默认白名单校验
 * @Project:
 * @CreateDate: Created in 2019-08-31 17:51 <br>
 * @Author: zhipeng.liu
 */

public class UrlVerificationUtils {

    private static List<String> whiteList = new ArrayList<>();

    static {
//        whiteList.add("/official/waybill/getMainSubBillCode");
//        whiteList.add("/official/waybill/trackingCustomerByWaybillNo");
    }

    /**
     *   通用权限白名单，每个角色都有得权限,但是需要token
     * @param url
     * @return
     */
    public static Boolean checkUrl(String url){
        if(whiteList.contains(url)){
            return true;
        }
        return false;
    }


}
