package com.yl.official.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-10-15 11:41 <br>
 * @Author: zhipeng.liu
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(description= "返回响应数据")
public class UserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private Integer id;

    @ApiModelProperty(value = "主账号用户id")
    private Integer parentId;

    @ApiModelProperty(value = "账号")
    private String account;
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "网点id")
    private Integer networkId;

    /**
     * 客户code
     */
    @ApiModelProperty(value = "客户code")
    private String customerCode;

    @ApiModelProperty(value = "状态 1启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "名称")
    private String customerName;

    private Integer stockCount;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "联系人手机")
    private String contactMobile;

    @ApiModelProperty(value = "网点名称")
    private String networkName;

    @ApiModelProperty(value = "面单格式 1:不默认 2:一联 3:二联")
    private Integer surfaceFormat;

    @ApiModelProperty(value = "目标打印机")
    private String targetPrinter;

    @ApiModelProperty(value = "是否隐私面单 0:否 1:是")
    private Integer isPrivacy;

    @ApiModelProperty(value = "运费状态：0隐藏，1显示")
    private Integer freightStatus;
    /**
     * 客户类型：1普通客户,2：惠民
     */
    @ApiModelProperty(value = "客户类型：1普通客户,2：惠民")
    private Integer customerType;

    private Boolean isHuimin;

}
