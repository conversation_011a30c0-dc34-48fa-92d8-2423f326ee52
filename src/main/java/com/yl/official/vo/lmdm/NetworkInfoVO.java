package com.yl.official.vo.lmdm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019/7/11 19:45
 * @Author: <a href="<EMAIL>">chunlin.zhang</a>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="OrderNetworkAreaInfoVO", description="网点基础数据响应对象")
public class NetworkInfoVO implements Serializable {

    /**
     * 网点名称
     */
    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(name = "code", value = "编号")
    private String code;

    @ApiModelProperty(value = "详细地址", name = "address")
    private String address;

    @ApiModelProperty(name = "businessPrincipal", value = "业务负责人")
    private String businessPrincipal;

    @ApiModelProperty(name = "businessTelephone", value = "业务电话")
    private String businessTelephone;

    @ApiModelProperty(name = "startBusinessTime", value = "开始营业时间")
    private String startBusinessTime;

    @ApiModelProperty(name = "endBusinessTime", value = "结束营业时间")
    private String endBusinessTime;

    @ApiModelProperty(name = "providerDesc", value = "省份描述")
    private String providerDesc;

    @ApiModelProperty(name = "cityDesc", value = "城市描述")
    private String cityDesc;

    @ApiModelProperty(name = "areaDesc", value = "区/县描述")
    private String areaDesc;

    @ApiModelProperty(name = "functionTypeId", value = "功能类型ID")
    private Integer functionTypeId;

    @ApiModelProperty(name = "functionTypeDesc", value = "功能类型描述")
    private String functionTypeDesc;

    @ApiModelProperty(name = "typeId", value = "网点类型ID")
    private Integer typeId;

    @ApiModelProperty(name = "typeDesc", value = "网点类型描述")
    private String typeDesc;

    @ApiModelProperty(name = "doorheadPhotoUrl", value = "门头照url")
    private String doorheadPhotoUrl;
}