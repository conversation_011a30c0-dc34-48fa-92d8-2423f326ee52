package com.yl.official.vo.ops;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * <AUTHOR>
 * @date ：Created in 2019-12-20 10:49
 * @description：
 * @modified By：
 * @version: $version$
 */

@Data
public class PodTrackingListVO implements Serializable {
    /**
     * 运单号
     */
    private String keyword;

    private List<PodTrackingVO> details;

    /**
     * 寄件城市名称
     */
    private String senderCityName;
    /**
     * 收件城市名称
     */
    private String receiverCityName;
}
