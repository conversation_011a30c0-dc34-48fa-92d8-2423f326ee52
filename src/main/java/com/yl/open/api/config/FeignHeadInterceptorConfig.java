package com.yl.open.api.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description:
 * @Author: luobin
 * @Date: 2021/7/26 10:29 上午
 */
@Configuration
public class FeignHeadInterceptorConfig {

    private final static String LANG_TYPE = "langType";

    @Bean("requestInterceptor")
    public RequestInterceptor requestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                //1、使用RequestContextHolder拿到刚进来的请求数据
                ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (requestAttributes != null) {
                    //老请求
                    HttpServletRequest request = requestAttributes.getRequest();
                    //2、同步请求头的数据（主要是cookie）
                    //把老请求的cookie值放到新请求上来，进行一个同步
                    String langType = request.getHeader(LANG_TYPE);
                    template.header(LANG_TYPE, langType);
                }
            }
        };
    }
}
