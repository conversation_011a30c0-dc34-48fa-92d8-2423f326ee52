package com.yl.open.api.constant;

import java.util.Arrays;
import java.util.List;

/***
 * Method Description
 * 常用变量
 * <AUTHOR>
 * @date 2021-02-02
 */
public class BaseConstants {

    public static final List<String> image_set = Arrays.asList("jpg","JPG","png","PNG","JPEG","jpeg");

    public static final String DECRYPT_KEY = "ZniHsGZunPD43fKx";

    /**
     * 总部网点编码
     */
    public static final String HEADQUARTERS_CODE = "888888";
    /**
     * 总部网点名称
     */
    public static final String HEADQUARTERS_NAME = "总部";
    /**
     * 中东国家区号
     */
    public static final int JMSME_COUNTRY_CODE = 971;
    public static final int CHINA_COUNTRY_CODE = 86;
    /**
     * 短信验证码内容模板
     */
    public static final String TEMPLATE_VERIFICATION = "【极兔速递】您的验证码:%s,请于%s分钟内输入,如非本人操作请忽略!";

    /**
     * 中东短信密码重置模板
     */
    public static final String TEMPLATE_PWD_RESET = "Please use %s as your verification code";
    /**
     * 国内短信密码重置模板
     */
    public static final String CH_TEMPLATE_PWD_RESET = "您的注册验证码为：%s";

    /**
     * 短信来源编码
     */
    public static final String SMS_SOURCE_CODE = "A03";
}
