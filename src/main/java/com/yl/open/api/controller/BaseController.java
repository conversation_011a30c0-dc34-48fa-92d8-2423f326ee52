package com.yl.open.api.controller;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.filter.AntiSQLFilter;
import com.yl.common.base.model.vo.ApiResult;
import com.yl.common.base.model.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 基础的controller，提供了获取默认的分页和默认返回信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-05-31 11:07
 */
@CrossOrigin
public class BaseController {
    @Autowired
    protected HttpServletRequest request;

    @Autowired
    protected HttpServletResponse response;

    /*最大导出行数*/
    protected final Integer exportMaxRows = 50000;

    /**
     * 成功返回
     *
     * @param object
     * @return
     */
    public <T> Result<T> success(T object) {
        return Result.success(object);
    }

    /**
     * web,app 端请求成功返回-针对api
     *
     * @param object
     * @param <T>
     * @return
     */
    public <T> ApiResult<T> success_api(T object, String version) {
        return ApiResult.success(object, version);
    }

    /**
     * web,app 端请求成功返回-针对api
     * @param obj
     * @param <T>
     * @return
     */
    public <T> ApiResult<T> success_api(Map<Object, String> obj) {
        return ApiResult.success(obj);
    }

    /**
     * 成功返回
     *
     * @return
     */
    public Result<Void> success() {
        return Result.success();
    }

    /**
     * 成功返回
     *
     * @param code
     * @param msg
     * @return
     */
    public Result<Void> success(Integer code, String msg) {
        return Result.success(code, msg);
    }

    /**
     * 失败返回,自定义错误信息
     *
     * @return
     */
    public Result<Void> fail(Integer code, String msg) {
        return Result.error(code, msg);
    }

    /**
     * 失败返回，默认系统内部错误
     *
     * @return
     */
    public Result<Void> fail() {
        return Result.error(ResultCodeEnum.SYSTEM_INNER_ERROR);
    }


    /**
     * 获取安全参数(SQL ORDER BY 过滤)
     *
     * @param parameter
     * @return
     */
    protected String[] getParameterSafeValues(String parameter) {
        return AntiSQLFilter.getSafeValues(request.getParameterValues(parameter));
    }
}
