package com.yl.open.api.controller;


import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import com.yl.open.api.dto.YlOpRealNameDTO;
import com.yl.open.api.dto.YlOpRealNameSaveOrUpdateDto;
import com.yl.open.api.feign.YlOpRealNameFeignClient;
import com.yl.open.api.service.IYlOpRealNameService;
import com.yl.open.api.vo.YlOpRealNameVO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 实名制信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
@RestController
@RequestMapping("/realName")
@Slf4j
public class YlOpRealNameController extends BaseController implements YlOpRealNameFeignClient {

    @Autowired
    private IYlOpRealNameService service;

    @ApiOperation(value = "新增或更新实名制信息")
    @PostMapping("/saveOrUpdate")
    @Override
    public Result<Boolean> saveOrUpdate(@Validated @RequestBody YlOpRealNameSaveOrUpdateDto dto) {
        log.info("YlOpRealNameController saveOrUpdate  dto :{}", dto);
        return success(service.saveOrUpdate(dto));
    }

    @Override
    @PostMapping("/getRealName")
    @ApiOperation(value = "获取实名制信息")
    public Result<List<YlOpRealNameVO>> getRealName(@Validated @RequestBody YlOpRealNameDTO dto) {
        return success(service.getRealName(dto));
    }

}
