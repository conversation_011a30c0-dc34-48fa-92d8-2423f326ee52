package com.yl.open.api.controller.jointDebugging;

import com.alibaba.fastjson.JSON;
import com.yl.common.base.model.vo.Result;
import com.yl.open.api.constant.RedisKeyConstants;
import com.yl.open.api.dto.JointDebuggingDTO;
import com.yl.open.api.feign.AdminUserClient;
import com.yl.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:修改联调状态
 * @Project:
 * @CreateDate: Created in 2021-03-03
 * @Author: xpj
 */
@RestController
@RequestMapping("/jointDebugging")
@Slf4j
@Api(value = "/jointDebugging", description = "联调测试")
public class JointDebuggingController {

    @Autowired
    private AdminUserClient adminUserClient;

    /***
     * Method Description
     * 修改联调状态
     * @param jointDebuggingDTO
     * @return com.yl.common.base.model.vo.Result<java.lang.Boolean>
     * <AUTHOR>
     * @date 2021-03-03
     */
    @PostMapping(value = "/changeStatus")
    @ApiOperation(value = "修改联调状态", notes = "修改联调状态")
    public Result<Boolean> changeJointDebuggingStatus(@RequestBody @Valid JointDebuggingDTO jointDebuggingDTO){
        log.info("修改联调状态入参：{}", JSON.toJSONString(jointDebuggingDTO));
        String key = RedisKeyConstants.OPENPLATFORM_UUID_USERID + jointDebuggingDTO.getUuid();
        Object o = RedisUtil.get(key);
        log.info("修改联调状态redis key:{}",o);
        if(o == null){
            return Result.success(Boolean.FALSE);
        }
        jointDebuggingDTO.setUserId(Long.valueOf(o.toString()));
        adminUserClient.changeJointDebuggingStatus(jointDebuggingDTO);
        return Result.success(Boolean.TRUE);
    }
}
