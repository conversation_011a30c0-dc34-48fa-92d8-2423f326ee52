package com.yl.open.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2020-09-03
 * @Author: xpj
 */
@Data
@Accessors(chain = true)
@ApiModel(value="业务短信对象DTO", description="业务短信对象请求实体")
public class SmsBizMsgDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty( name = "templateCode",value = "短信模板code,门店自取：template_collect_network、代理点自取：template_collect_proxy")
    @NotNull
    private String templateCode;

    /** 批次号，系统内部生成*/
    private Long batchNum;

    /**
     * 业务信息
     */
    @ApiModelProperty( name = "list",value = "业务信息")
    @NotNull
    List<@Valid SmsBizMsg> list;

    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "业务短信实体", description = "业务短信实体")
    public static class SmsBizMsg implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty( name = "orderType",value = "订单类型 0拼多多  1非拼多多")
        @NotNull
        private Integer orderType;

        @ApiModelProperty( name = "phoneNumber",value = "接收短信的手机号，示例：18585858585")
        private String phoneNumber;

        @ApiModelProperty( name = "code",value = "取件码，示例：696969")
        private String code = " ";

        @ApiModelProperty( name = "networkCode",value = "扫描网点编码")
        private String networkCode;

        @ApiModelProperty( name = "networkName",value = "扫描网点名称")
        private String networkName;

        @ApiModelProperty( name = "staffNo",value = "员工号")
        private String staffNo;

        @ApiModelProperty( name = "staffName",value = "员工姓名")
        private String staffName;

        @ApiModelProperty( name = "waybillNo",value = "运单号，示例：JT123123123")
        private String waybillNo;

        @ApiModelProperty( name = "address",value = "地址，示例：粤美特大厦丰巢快递柜")
        private String address = " ";

        @ApiModelProperty( name = "mobile",value = "联系方式，示例：0755-58585858")
        private String mobile = " ";
        /**
         * 模板参数
         */
        private List<String> templateParam;

    }

}

