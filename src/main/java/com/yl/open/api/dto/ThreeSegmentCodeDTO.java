package com.yl.open.api.dto;

import com.yl.common.base.enums.MsgCodeEnum;
import com.yl.open.api.exception.BaseException;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 三段码数据对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/4/23 8:44
 */
@Data
public class ThreeSegmentCodeDTO {

    /**
     * 寄件信息对象
     */

    private SendReceiveInfo sender;

    /**
     * 收件信息对象
     */
    private SendReceiveInfo receiver;

    public void verification() {
        if (sender == null || StringUtils.isBlank(sender.getAddress()) || StringUtils.isBlank(sender.getProv()) || StringUtils.isBlank(sender.getCity())
                 || StringUtils.isBlank(sender.getCountryCode())) {
            throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM_ORIGIN);
        }

        if (receiver == null || StringUtils.isBlank(receiver.getAddress()) || StringUtils.isBlank(receiver.getProv()) || StringUtils.isBlank(receiver.getCity())
                || StringUtils.isBlank(receiver.getCountryCode())) {
            throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM_DESTINATION);
        }
    }
}
