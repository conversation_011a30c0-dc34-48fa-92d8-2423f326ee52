package com.yl.open.api.dto;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 开发平台实名制信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class YlOpRealNameDTO implements Serializable {


    /**
     * 个人在公安户籍管理部门正式登记注册的姓名
     */
    @ApiModelProperty(notes = "个人在公安户籍管理部门正式登记注册的姓名")
    @NotBlank(message = "Name cannot be empty")
    private String name;

    /**
     * 个人有效联系电话
     */
    @ApiModelProperty(notes = "个人有效联系电话")
    @NotBlank(message = "phone number cannotbe empty")
    private String mobileNumber;

    /**
     * 个人固定联系电话，包括区号、
     * 电话号码以及分机号，中间用 - 分隔
     */
    @ApiModelProperty(notes = "个人固定联系电话")
    @NotBlank(message = "Fixed contact phone number cannot be empty")
    private String phone;

    @ApiModelProperty(notes = "apiAccount")
    @NotBlank(message = "apiAccount cannotbe empty")
    private String apiAccount;


}
