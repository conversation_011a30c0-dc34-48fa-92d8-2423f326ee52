package com.yl.open.api.enums;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public enum ComCostTypeEnum {
    /***/
    CUSTOMER_COST(1, "计算客户报价"),
    STANDARD_COST(2, "计算标准报价"),
    CUSTOMER_INSURANCE_COST(3, "计算客户报价+保价"),
    STANDARD_INSURANCE_COST(4, "计算标准报价+保价"),
    ;
    private final Integer code;
    private final String name;

    ComCostTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(Integer code) {
        Optional<ComCostTypeEnum> first = Arrays.stream(values()).filter(e -> e.code.equals(code)).findFirst();
        if(first.isPresent()){
            return first.get().name;
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}