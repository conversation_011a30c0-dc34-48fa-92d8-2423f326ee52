package com.yl.open.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 响应码枚举：
 * code=0表示成功，code<0表示出错，-1到-9为系统保留异常码
 *
 * <AUTHOR>
 * @createTime 2019-07-31
 */
@Getter
@AllArgsConstructor
@ToString
public enum TypeCodeEnum {

    /**
     * 处理成功
     */
    EXPRESSTYPE_EZ("EZ", "普通快递"),

//1、散客；    2、月结；
    ORDERTYPE01("1", "散客"),
    ORDERTYPE2("2", "月结"),


    SERVICETYPE02("02", "门店寄件"),
    SERVICETYPE01("01", "上门取件"),


    DELIVERYTYPE06("06", "代收点自提"),
    DELIVERYTYPE05("05", "快递柜自提"),
    DELIVERYTYPE04("04", "站点自提"),
    DELIVERYTYPE03("03", "派送上门"),


    GOODSTYPE01("bm000001", "物品"),
    GOODSTYPE02("bm000002", "文件"),


    PAYMETHOD_PP_CASH("PP_CASH", "寄付现结"),
    PAYMETHOD_CC_CASH("CC_CASH", "到付现结"),
    PAYMETHOD_PP_PM("PP_PM", "寄付月结"),


    SYSTEM_ERROR("E5000", "服务出错");

    private Object code;

    private String msg;


}
