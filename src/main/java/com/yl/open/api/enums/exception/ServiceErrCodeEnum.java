package com.yl.open.api.enums.exception;

import com.yl.common.base.enums.RespCodeEnum;

/**
 * 业务错误代码
 */
public enum ServiceErrCodeEnum implements RespCodeEnum {

    WAYBILLNO_ISNULL(149010001, "waybillno_isnull", "The waybill number cannot be empty!"),
    PARAM_ERROR(149010002, "oauth_param_error", "Wrong parameters!"),
    COMMAND_ISNULL(149010003, "command_isnull", "Operation type cannot be empty!"),
    MODULENAME_ISNULL(149010004, "modulename_isnull", "Module name cannot be empty!"),
    FILETYPE_ISNULL(149010005, "filetype_isnull", "File type cannot be empty!"),
    DOCUMENTATION_ISNULL(149010006, "documentation_isnull", "Business type cannot be empty!"),
    FILE_ISNULL(149010007, "file_isnull", "File cannot be empty!"),
    OAUTH_NO_ACCESS(149010008, "OAUTH_NO_ACCESS", "No access rights!"),
    DETAILEDADDRESS_EQUALS(149010009, "detailedaddress_equals", "The sending details and receiving details cannot be the same!"),
    DECLAREDVALUE_ISNULL(149010010, "declaredvalue_isnull", "The value of the item cannot be empty!"),
    INSUREDVALUE_ISNULL(149010011, "insuredvalue_isnull", "保价费不能为空！"),
    ORDEREXCELSIZE_ERROR(*********, "orderexcelsize_error", "The number of imported orders cannot exceed 1000!"),
    ORDERLIST_SIZE_ERROR(*********, "orderlist_size_error", "The number of uploaded orders cannot exceed 1000!"),
    SELECT_WAYBILL_ERRPR(*********, "select_waybill_errpr", "Inquiring the material ID of the e-Bill failed!"),
    VERIFYCODE_TYPE_ERROR(*********, "verifycode_type_error", "The verification code type is wrong!"),
    MOBILE_UNEQUAL(*********, "mobile_unequal", "Phone number bound by the main account should be consistent with the phone number of the customer information!"),
    MOBILE_ALREADY_BIND(*********, "mobile_already_bind", "Phone number has been bound by other accounts!"),
    VERIFYCODE_ERROR(*********, "verifycode_error", "The verification code is wrong!"),
    ACCOUNT_ALREADY_BIND_MOBILE(*********, "account_already_bind_mobile", "The account has been bound to a cell phone number!"),
    MOBILE_NOT_BIND(*********, "mobile_not_bind", "The phone number is not bound by the account!"),
    MOBILE_ERROR(*********, "mobile_error", "Phone number is wrong!"),
    MOBILE_ALREADY_BIND_CONTACT_NETWORK(*********, "mobile_already_bind_contact_network", "The phone number has been bound by other accounts, please confirm with the contracted station!"),
    SENDER_ADDRESS_ERROR(*********, "sender_address_error", "This sending address is not yet available, coming soon"),
    RECEIVER_ADDRESS_ERROR(*********, "receiver_address_error", "This receiving address is not yet available, coming soon"),
    CC_CASH_INSURED_NOT_SUPPORT(*********, "cc_cash_insured_not_support", "FOD cannot be insured."),
    NO_OPERATE_AUTHORITY(*********, "no_operate_authority", "You have no authority to operate., please contact the station to confirm."),
    NO_ADD_ENABLE_ACCOUNT_AUTHORITY(*********, "no_add_enable_account_authority", "A main account with the status of disabled cannot add a sub-account with the status of enabled!"),
    NO_EDIT_ENABLE_ACCOUNT_AUTHORITY(*********, "no_edit_enable_account_authority", "Disabled sub-accounts cannot be enabled by a disabled main account!"),
    ADDR_LIST_SIZE_ERROR(*********, "addr_list_size_error", "上传的地址数量不能超过800！"),
    SMS_TYPE_ERROR(*********, "sms_type_error", "The number of uploaded addresses cannot exceed 800!"),
    PRINT_WAYBILL_ERROR(*********, "printWaybillError", "Printing failed！"),
    OPERATION_FREQUENTLY_ERROR(*********,"operation_frequently_error", "Operation too frequently!"),
    ;

    private int code;

    private String key;

    private String msg;

    ServiceErrCodeEnum(int code, String key, String msg) {
        this.code = code;
        this.key = key;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getKey() {
        return getKey();
    }



    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public void setArgs(Object[] var1) {

    }


}
