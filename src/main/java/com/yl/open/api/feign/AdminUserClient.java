package com.yl.open.api.feign;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yl.common.base.model.vo.Result;
import com.yl.open.api.dto.*;
import com.yl.open.api.vo.AdminUserVO;
import com.yl.open.api.vo.UserInterfaceVO;
import com.yl.open.api.vo.YlOpCertificationInfoVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(name = "openplatformapi",
        path = "/openplatformapi/admin/user")
public interface AdminUserClient {

    /**
     * 用户列表
     * @param adminUserPageQuery
     * @return
     */
    @PostMapping(value = "/page")
    Result<IPage<AdminUserVO>> page(@RequestBody  AdminUserPageQuery adminUserPageQuery);

    /**
     * 获取用户详情
     * @param userId
     * @return
     */
    @GetMapping(value = "/detail")
    Result<AdminUserVO> getUserById(@RequestParam("userId") long userId);


    /**
     * 用户管理 用户编辑
     * @param adminUseEditDTO
     * @return
     */
    @PostMapping(value = "/useEdit")
    Result<Boolean> useEdit(@RequestBody AdminUseEditDTO adminUseEditDTO);

    /**
     * 用户管理 接口审核
     * @param auditCertificationDTO
     * @return
     */
    @PostMapping(value = "/cer/Edit")
    Result<Boolean> cerEdit(@RequestBody AuditCertificationDTO auditCertificationDTO);

    /**
     * 认证列表
     * @param adminCerPageQuery
     * @return
     */
    @PostMapping(value = "/cer/page")
    Result<IPage<YlOpCertificationInfoVO>> cerPage(@RequestBody AdminCerPageQuery adminCerPageQuery);

    /**
     * 用户管理密码重置
     * @param
     * @return
     */
    @PostMapping(value = "/resetPwd")
    Result<Boolean> resetPwd(@RequestBody AdminResetPwdDTO userInterAuditDTO);



    /**
     * 接口列表
     * @param adminInterPageQuery
     * @return
     */
    @PostMapping(value = "/inter/page")
    Result<IPage<UserInterfaceVO>> interPage(@RequestBody AdminInterPageQuery adminInterPageQuery);


    /**
     * 接口编辑
     * @param adminUserInterEditDTO
     * @return
     */
    @PostMapping(value = "/inter/edit")
    Result<Boolean> interEdit(@RequestBody AdminUserInterEditDTO adminUserInterEditDTO);

    /**
     * 接口编辑
     * @param adminUserInterEditDTO
     * @return
     */
    @GetMapping(value = "/inter/detele")
    Result<Void> interDetele(@RequestParam("id") long id);

    /**
     * 接口新增
     * @param ylOpUserInterfaceDTO
     * @return
     */
    @PostMapping(value = "/inter/save")
    Result<Void> interSave(@RequestBody YlOpUserInterfaceDTO ylOpUserInterfaceDTO);

    /***
     *校验订单号是否存在
     * @param
     * @return
     */
    @GetMapping(value = "/checkOrderSource")
    Result<Boolean> checkOrderSource(@RequestParam("orderCode") String orderCode);

    /***
     * Method Description
     * 修改联调状态
     * @param jointDebuggingDTO
     * @return com.yl.common.base.model.vo.Result<java.lang.Void>
     * <AUTHOR>
     * @date 2021-03-04
     */
    @PostMapping(value = "/changeJointDebuggingStatus")
    Result<Void> changeJointDebuggingStatus(@RequestBody JointDebuggingDTO jointDebuggingDTO);

    /***
     * Method Description
     * 初始化联调状态
     * @param jointDebuggingDTO
     * @return com.yl.common.base.model.vo.Result<java.lang.Void>
     * <AUTHOR>
     * @date 2021-03-06
     */
    @PostMapping(value = "/initJointDebuggingStatus")
    Result<Void> initJointDebuggingStatus(@RequestBody JointDebuggingDTO jointDebuggingDTO);

    /***
     * Method Description
     * 30天后初始化联调状态
     * @return com.yl.common.base.model.vo.Result<java.lang.Void>
     * <AUTHOR>
     * @date 2021-03-09
     */
    @GetMapping(value = "/initJointDebuggingStatusJob")
    Result<Void> initJointDebuggingStatusJob();

}
