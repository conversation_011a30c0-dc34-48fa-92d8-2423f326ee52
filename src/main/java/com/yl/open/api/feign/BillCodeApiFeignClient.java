package com.yl.open.api.feign;

import com.yl.common.base.model.vo.Result;
import com.yl.open.api.dto.BillCodeGetDTO;
import com.yl.open.api.dto.BillCodeGetResultDTO;
import com.yl.open.api.dto.GetChildBillCodeDTO;
import com.yl.open.api.dto.YlBillCodeApiDTO;
import com.yl.open.api.feign.fallback.BillCodeApiFeignClientFallbackFactory;
import com.yl.open.api.vo.BillCodeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title BillCodeApiFeignClient
 * @Package com.yl.edi.feign.fallback
 * @Description 面单接口
 * @date 2021/10/26 5:36 下午
 */
@FeignClient(name = "ylbillcodeapi", path = "/billcodeapi", fallbackFactory = BillCodeApiFeignClientFallbackFactory.class)
public interface BillCodeApiFeignClient {

    /**
     * @param getDTO
     * @return : com.yl.edi.dto.BillCodeGetResultDTO
     * <AUTHOR> mz
     * @Description : 接口运单号获取
     * @Date : 5:44 下午 2021/10/26
     */
    @PostMapping("/materialNumber/manyTicketGenerate")
    Result<BillCodeGetResultDTO> getNo(BillCodeGetDTO getDTO);

    /**
     * 根据主单号查询子单号
     *
     * @param dto
     * @return
     */
    @PostMapping("/materialNumber/getChildBillCodeByParent")
    Result<BillCodeVO> getChildBillCodeByParent(@RequestBody GetChildBillCodeDTO dto);

    /**
     * 生成电子单号
     *
     * @param dto
     * @return
     */
    @PostMapping("/materialNumber/getBillCodeToOrder")
    Result<List<String>> getBillCodeToOrder(@RequestBody YlBillCodeApiDTO dto);

}