package com.yl.open.api.feign;

import com.yl.common.base.model.vo.Result;
import com.yl.open.api.feign.fallback.SysNetworkFeignClientFallBack;
import com.yl.open.api.vo.SysNetworkVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
//@FeignClient(
//        name = "yllmdmapi",
//        path = "/lmdmapi",
//        fallback = LmdmFeignClientFallBack.class
//)
@FeignClient(name = "yltmsqueryapi", path = "/tmsqueryapi/sysNetwork", fallback = SysNetworkFeignClientFallBack.class)
public interface SysNetworkFeignClient {
    @GetMapping("/getSysNetworkByCode")
    Result<SysNetworkVO> getSysNetworkByCode(@RequestParam String code);
}
