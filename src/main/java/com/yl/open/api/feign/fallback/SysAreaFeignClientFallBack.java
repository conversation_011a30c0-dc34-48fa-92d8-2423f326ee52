package com.yl.open.api.feign.fallback;

import com.yl.common.base.model.vo.Result;
import com.yl.open.api.entity.SysArea;
import com.yl.open.api.feign.SysAreaFeignClient;
import com.yl.open.api.vo.SysAreaNativeNameVo;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SysAreaFeignClientFallBack implements SysAreaFeignClient {


    @Override
    public Result<List<SysArea>> list(SysAreaNativeNameVo areaVo) {
        return null;
    }
}
