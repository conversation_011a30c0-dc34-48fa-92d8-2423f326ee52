package com.yl.open.api.feign.lmdm;


import com.yl.common.base.model.vo.Result;
import com.yl.open.api.constant.BaseConstants;
import com.yl.open.api.dto.lmdm.CommonQueryDTO;
import com.yl.open.api.vo.Page;
import com.yl.open.api.vo.lmdm.GeneralEdiNetworkVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 通用edi网关接口
 *
 * <AUTHOR>
 */
@Component
@FeignClient(
		name = "ylnetworkapi",
        path = BaseConstants.WEB_FEIGN_CLIENT_NAME + BaseConstants.PLATFORM_GENERAL_EDI + "/network")
public interface GeneralEdiNetworkFeignClient {

    /**
     * 通用edi分页获取网点信息
     *
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "通用edi分页获取网点信息", notes = "通用edi分页获取网点信息")
    Result<Page<GeneralEdiNetworkVO>> page(@RequestBody CommonQueryDTO dto,
                                           @RequestParam(value = "current", required = false, defaultValue = "1") Long current,
                                           @RequestParam(value = "size", required = false, defaultValue = "20") Long size);
}
