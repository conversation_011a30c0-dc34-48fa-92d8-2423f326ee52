package com.yl.open.api.feign.lmdm;



import com.yl.common.base.model.vo.Result;
import com.yl.open.api.constant.BaseConstants;
import com.yl.open.api.dto.lmdm.CommonQueryDTO;
import com.yl.open.api.vo.GeneralEdiStaffVO;
import com.yl.open.api.vo.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2020年5月22日14:50:54
 * @Author: zhuzhengjie
 */
@Component
@FeignClient(
		name = "ylnetworkapi",
		//url = "devapigw.jms.com",
        path = BaseConstants.WEB_FEIGN_CLIENT_NAME + BaseConstants.PLATFORM_GENERAL_EDI + "/sysStaff")
public interface GeneralEdiStaffFeignClient {

    @PostMapping("/page")
    @ApiOperation(value = "通用edi分页获取员工信息", notes = "通用edi分页获取员工信息")
    Result<Page<GeneralEdiStaffVO>> page(@RequestBody CommonQueryDTO dto,
                                         @RequestParam(value = "current", required = false, defaultValue = "1") Long current,
                                         @RequestParam(value = "size", required = false, defaultValue = "20") Long size);

}