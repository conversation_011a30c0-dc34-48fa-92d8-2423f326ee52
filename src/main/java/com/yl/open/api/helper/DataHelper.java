package com.yl.open.api.helper;

import com.yl.open.api.enums.MsgCodeEnum;
import com.yl.open.api.exception.BaseException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019/8/5 11:53 <br>
 * @Author: <a href="<EMAIL>"><EMAIL></a>
 */
@Component
public class DataHelper {


    public void isEmpty(List<String> args) {
        for (String v : args) {
            if (StringUtils.isEmpty(v)) {
                throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM);
            }
        }

    }

    public void isNull(Integer... args) {
        for (Integer v : args) {
            if (v == null) {
                throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM);
            }
        }

    }

    public void isNull(Long... args) {
        for (Long v : args) {
            if (v == null) {
                throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM);
            }
        }

    }

    public void maxLen(String[] args, int len) {
        if (args.length == 0 || args.length > len) {
            throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM);
        }

    }

    public void maxLen(List<String> args, int len) {
        if (args.isEmpty() || args.size() > len) {
            throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM);
        }

    }

    public void isEmpty(String... args) {
        isEmpty(Arrays.asList(args));
    }

    public void isOrderNum(String arg) {
        if(!StringUtils.isEmpty(arg)){
            Pattern p = Pattern.compile("^[a-zA-Z]{2}\\d{13}$"); // 前面两位字母，后面13位数据
            Matcher m = p.matcher(arg); // 操作的字符串
            boolean b = m.matches(); //返回是否匹配的结果
            if (!b) {
                throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM_BILLCODE);
            }
        }

    }

//    public static void main(String[] args) {
//        String ret = getSys("oms-order-1243345");
//        System.out.println(ret);
//        String module = getModule("oms-order-1243345");
//        System.out.println(module);
//        String sysModule = getSysModule("oms-order-1243345");
//        System.out.println(sysModule);
//        int code = getCode("oms-order-1243345");
//        System.out.println(code);
//    }

    public int getCode(String retCode){
        int ret = 90000;
        String regEx="[^0-9]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(retCode);
        try {
            ret =  Integer.parseInt(m.replaceAll("").trim());
        } catch (NumberFormatException e) {
//            log.error("非法返回码",e);
        }
        return ret;
    }


    public String getSys(String retCode){
        String regEx = "(.*?)-";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(retCode);
        while (m.find()) {
            return m.group(1);
        }
        return "90000"; //非法返回码
    }

    public String getModule(String retCode){
        String regEx = "-(.*?)-";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(retCode);
        while (m.find()) {
            return m.group(1);
        }
        return "90000"; //非法返回码
    }


    public String getSysModule(String retCode){
        String regEx="\\D+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(retCode);
        while (m.find()) {
            return m.group(0);
        }
        return "90000"; //非法返回码
    }


}
