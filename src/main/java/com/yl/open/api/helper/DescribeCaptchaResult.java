package com.yl.open.api.helper;

import com.tencentcloudapi.captcha.v20190722.CaptchaClient;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultRequest;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultResponse;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.util.JsonUtils;
import com.yl.open.api.enums.exception.LoginExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.Inet4Address;
import java.net.InetAddress;

@Slf4j
@Component
public class DescribeCaptchaResult {

    @Value("${secretId}")
    private String secretId;

    @Value("${secretKey}")
    private String secretKey;

    @Value("${captchaAppId}")
    private String captchaAppId;

    @Value("${appSecretKey}")
    private String appSecretKey;

    public void sendCaptchaCode(String randStr, String ticket) {

        Credential cred = new Credential(secretId, secretKey);

        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("captcha.tencentcloudapi.com");

        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);

        CaptchaClient client = new CaptchaClient(cred, "", clientProfile);

        DescribeCaptchaResultRequest req = new DescribeCaptchaResultRequest();
        req.setTicket(ticket);
        req.setRandstr(randStr);

        String ip = "127.0.0.1";
        try {
            InetAddress ip4 = Inet4Address.getLocalHost();
            ip = ip4.getHostAddress();
        } catch (Exception e) {
            log.warn("获取ip错误", e);
        }
        req.setUserIp(ip);
        req.setCaptchaType(9L);
        req.setAppSecretKey(appSecretKey);
        req.setCaptchaAppId(Long.valueOf(captchaAppId));

        DescribeCaptchaResultResponse resp = null;
        try {
            resp = client.DescribeCaptchaResult(req);
        } catch (TencentCloudSDKException e) {
            throw new BusinessException(LoginExceptionEnum.CAPTCHA_ERROR);
        }

        log.info("【腾讯滑块验证码校验】json={}", JsonUtils.toJson(resp));
        if (1 != resp.getCaptchaCode()) {
            throw new BusinessException(Integer.valueOf(resp.getCaptchaCode() + ""), resp.getCaptchaMsg());
        }
    }


    public static void main(String[] args) {
        try {
            String secretId = "2034935631";
            String secretKey = "0sEc5GdaAmfIPquN4jDyZNQ**";
            String Ticket = "";
            String Randstr = "";
            Credential cred = new Credential(secretId, secretKey);

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("captcha.tencentcloudapi.com");

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            CaptchaClient client = new CaptchaClient(cred, "", clientProfile);

            DescribeCaptchaResultRequest req = new DescribeCaptchaResultRequest();
            req.setTicket(Ticket);
            req.setRandstr(Randstr);

            String ip = "127.0.0.1";
            try {
                InetAddress ip4 = Inet4Address.getLocalHost();
                ip = ip4.getHostAddress();
            } catch (Exception e) {
                log.warn("获取ip错误", e);
            }
            req.setUserIp(ip);
            req.setCaptchaType(9L);
            req.setAppSecretKey(secretKey);
            req.setCaptchaAppId(Long.valueOf(secretId));

            DescribeCaptchaResultResponse resp = client.DescribeCaptchaResult(req);

            System.out.println(DescribeCaptchaResultResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }

    }

}