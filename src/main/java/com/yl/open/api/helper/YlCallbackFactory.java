package com.yl.open.api.helper;


import com.yl.open.api.service.CallbackService;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Component
public class YlCallbackFactory {

    private static final Map<Integer, CallbackService> beanMap = new ConcurrentHashMap<>(3);

    public void register(Integer type, CallbackService callbackService){
        beanMap.put(type, callbackService);
    }

    public CallbackService getCallbackService(Integer type){
        return beanMap.get(type);
    }
}
