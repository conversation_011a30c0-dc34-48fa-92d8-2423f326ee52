package com.yl.open.api.service.api;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yl.common.base.enums.MsgCodeEnum;
import com.yl.open.api.dto.ReqSubscribeDTO;
import com.yl.open.api.utils.TraceUtils;
import com.yl.open.api.vo.BaseResponse;
import com.yl.open.api.vo.trace.ResSubscribe;
import com.yl.open.api.vo.trace.ResSubscribeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 *  物流LogisticsServiceImpl
 * <AUTHOR>
 * @since Created in 2020年5月30日15:13:19
 */
@Service
@Slf4j
public class LogisticsService {


    public BaseResponse<ResSubscribeVO> subscribe(ReqSubscribeDTO dto) {
        //运单订阅
        ResSubscribeVO resOrderVO = new ResSubscribeVO();
        List<ResSubscribe> list = dto.getList().stream().filter(l->(l.getWaybillCode().contains("JT") || l.getWaybillCode().contains("UT"))).map(reqOrder->{
            String replace = reqOrder.getWaybillCode().replace("JT", "").replace("UT", "");
            reqOrder.setWaybillCode(replace);
            List<ResSubscribe> resSubscribeList = new ArrayList<>();
            ResSubscribe resOrder = new ResSubscribe();
            resOrder.setId(dto.getId());
            resOrder.setWaybillCode(reqOrder.getWaybillCode());
            resOrder.setTraceNode(reqOrder.getTraceNode());
            try {
                //设置有效期为15天
                String sublist = TraceUtils.get(reqOrder.getWaybillCode());
                if(StrUtil.isNotEmpty(sublist)) {
                    resSubscribeList = JSON.parseArray(sublist, ResSubscribe.class);
                }
                //针对一个单号多家商户订阅场景
                resSubscribeList.add(resOrder);
                TraceUtils.put(reqOrder.getWaybillCode(), JSON.toJSONString(resSubscribeList),15*24*60*60);
                resOrder.setIsSuccess(Boolean.TRUE);
            } catch (Exception e) {
                log.error("订阅失败,订阅参数:",e);
                resOrder.setReason(e.getMessage());
                resOrder.setIsSuccess(Boolean.FALSE);
            }
            return resOrder;
        }).collect(Collectors.toList());
        resOrderVO.setList(list);
        return new BaseResponse<>(MsgCodeEnum.SUCCESS,resOrderVO);
    }
   
}
