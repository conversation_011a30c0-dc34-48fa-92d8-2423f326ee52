/*
 * 云路供应链科技有限公司 版权所有 © Copyright 2019
 */
package com.yl.open.api.service.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yl.basicservices.entity.BasicSysNetwork;
import com.yl.basicservices.service.IBasicNetworkService;
import com.yl.common.base.enums.MsgCodeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.open.api.dto.DispatchCodeRequestDTO;
import com.yl.open.api.dto.DotInformationDto;
import com.yl.open.api.dto.LmdmSysAreaDTO;
import com.yl.open.api.entity.ApiAccount;
import com.yl.open.api.exception.BaseException;
import com.yl.open.api.feign.DispatchCodeFeignClient;
import com.yl.open.api.feign.OpenPlatformFeignClient;
import com.yl.open.api.utils.BigCodeAreaUtils;
import com.yl.open.api.utils.NetWorkUtils;
import com.yl.open.api.vo.BigCodeVO;
import com.yl.open.api.vo.DispatchCodeResponseVO;
import com.yl.open.api.vo.LmdmSysAreaVO;
import com.yl.open.api.vo.SysCommonAreaVO;
import com.yl.open.api.vo.lmdm.GeneralEdiNetworkVO;
import com.yl.open.api.vo.location.NetworkVO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static cn.hutool.core.util.StrUtil.isBlank;
import static java.util.Objects.isNull;


/**
 * 网点信息查询
 *
 * <AUTHOR>
 * @date 2020年4月21日
 */
@Slf4j
@Service
public class NetworkService {

    /**
     * 基础服务service
     */
    @Autowired
    private IBasicNetworkService iBasicNetworkService;


    @Autowired
    private DispatchCodeFeignClient newDispatchCodeClient;
    @Resource
    private OpenPlatformFeignClient openPlatformFeignClient;


    public NetworkVO getNetworkInfo(String bizContent, ApiAccount apiAccount) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        NetworkVO networkVO = new NetworkVO();
        log.info("开放平台网点信息查询，入参{}", bizContent);
        DotInformationDto dotInformationDto;
        SysCommonAreaVO sysCommonAreaVO = new SysCommonAreaVO();
        List<DispatchCodeRequestDTO> list = new ArrayList<>();
        DispatchCodeRequestDTO threeVO = new DispatchCodeRequestDTO();

        try {
            dotInformationDto = JSONObject.parseObject(bizContent, DotInformationDto.class);
        } catch (Exception e) {
            throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM);
        }

        if (isBlank(dotInformationDto.getProvince())) {
            throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM_PROV);
        }

        if (isBlank(dotInformationDto.getCity())) {
            throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM_CITY);
        }

        if (isBlank(dotInformationDto.getArea())) {
            throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM_AREA);
        }

        if (isBlank(dotInformationDto.getDetails())) {
            throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM_ADDR);
        }

        //配置中初始化数据中有rediskey的值说明有定制化地址映射平台
        if (StringUtil.isNotEmpty(apiAccount.getRedisKey())) {
            try {
                LmdmSysAreaDTO senderDTO = new LmdmSysAreaDTO();
                senderDTO.setCityName(dotInformationDto.getCity());
                senderDTO.setProvName(dotInformationDto.getProvince());

                Result<LmdmSysAreaVO> senderResult = openPlatformFeignClient.getInfoByName(senderDTO);
                log.info("getNetworkInfo查询发件省市名称返回结果：{}", JSON.toJSONString(senderResult));

                //redisResult = (String) RedisUtil.hGet(apiAccount.getRedisKey(), dotInformationDto.getCity()+dotInformationDto.getArea());
                //sysCommonAreaVO = JSON.parseObject(redisResult, SysCommonAreaVO.class);
                if (senderResult.isSucc()) {
                    if (senderResult.getData() != null) {

                        LmdmSysAreaVO senderVO = senderResult.getData();
                        //threeVO.setArea(sysCommonAreaVO.getLocalAreaName());
                        //threeVO.setAreaId(sysCommonAreaVO.getLocalAreaId());
                        threeVO.setCity(dotInformationDto.getCity());
                        threeVO.setCityId(Integer.valueOf(senderVO.getCityCode()));
                        threeVO.setProvince(dotInformationDto.getProvince());
                        threeVO.setProvinceId(Integer.valueOf(senderVO.getProvCode()));
                        threeVO.setDetails(dotInformationDto.getDetails());
                    }
                }
            } catch (Exception e) {
                throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM_REDIS);
            }
            threeVO.setUniqueSequence(uuid);
            list.add(threeVO);
        } else {//默认为本地地址查询
            LmdmSysAreaDTO senderDTO = new LmdmSysAreaDTO();
            senderDTO.setCityName(dotInformationDto.getCity());
            senderDTO.setProvName(dotInformationDto.getProvince());

            Result<LmdmSysAreaVO> senderResult = openPlatformFeignClient.getInfoByName(senderDTO);
            log.info("getNetworkInfo查询省市名称返回结果：{}", JSON.toJSONString(senderResult));
            //receiverBigCode = addOrderService.getNewBigCodeVO(dotInformationDto.getArea(), dotInformationDto.getCity(),"1");
            if (senderResult.isSucc()) {
                if (senderResult.getData() == null) {
                    threeVO.setArea(dotInformationDto.getArea());
                    threeVO.setCity(dotInformationDto.getCity());
                    threeVO.setProvince(dotInformationDto.getProvince());
                    threeVO.setDetails(dotInformationDto.getDetails());
                } else {

                    LmdmSysAreaVO senderVO = senderResult.getData();

                    threeVO.setArea(dotInformationDto.getArea());
                    // threeVO.setAreaId(receiverBigCode.getLocalAreaId());
                    threeVO.setCity(dotInformationDto.getCity());
                    threeVO.setCityId(Integer.valueOf(senderVO.getCityCode()));
                    threeVO.setProvince(dotInformationDto.getProvince());
                    threeVO.setProvinceId(Integer.valueOf(senderVO.getProvCode()));
                    threeVO.setDetails(dotInformationDto.getDetails());

                    if (StringUtils.isEmpty(dotInformationDto.getProvince())
                            || "N/A".equals(dotInformationDto.getProvince())) {
                        threeVO.setProvince(dotInformationDto.getProvince());
                    }

                    if (StringUtils.isEmpty(dotInformationDto.getCity())
                            || "N/A".equals(dotInformationDto.getCity())) {
                        threeVO.setCity(dotInformationDto.getCity());
                    }
                    if (StringUtils.isEmpty(dotInformationDto.getArea())
                            || "N/A".equals(dotInformationDto.getArea())) {
                        threeVO.setArea(dotInformationDto.getArea());
                    }
                }
            }

            threeVO.setUniqueSequence(uuid);
            list.add(threeVO);
        }
        // 1.根据地址查三段码

        if (Objects.nonNull(threeVO) || Objects.nonNull(sysCommonAreaVO)) {
            log.info("获取三段码请求参数：{}", JSON.toJSONString(list));
            Result<DispatchCodeResponseVO> dispatchCodeResponseVOResult = newDispatchCodeClient.fetchCodes(list.get(0));
            DispatchCodeResponseVO dispatchCodeResponseVO = dispatchCodeResponseVOResult.getData();
            String deliverNetworkId = String.valueOf(dispatchCodeResponseVO.getDeliverNetworkId());
            networkVO.setNetworkName(dispatchCodeResponseVO.getDeliverNetworkName());
            networkVO.setNetworkAddress(dispatchCodeResponseVO.getDetails());
            networkVO.setFull(dispatchCodeResponseVO.getFull().replace(",", "  "));
            if (StringUtil.isEmpty(deliverNetworkId)) {
                log.info("网点信息为空！{}", JSON.toJSONString(list));
//				  throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM_UNDATA);
            } else {
                String network = NetWorkUtils.getNetWork(deliverNetworkId);
                if (StringUtils.isEmpty(network)) {
                    BasicSysNetwork basicSysNetwork = iBasicNetworkService.getById(Integer.parseInt(deliverNetworkId));
                    if (Objects.isNull(basicSysNetwork)) {
                        log.info("获取网点信息失败！{}", JSON.toJSONString(list));
//					  throw new BaseException(MsgCodeEnum.ILLEGAL_PARAM_UNDATA);
                    } else {
                        networkVO.setMobile(basicSysNetwork.getMobile());
                    }
                    return networkVO;
                }
                GeneralEdiNetworkVO generalEdiNetworkVO = JSON.parseObject(network, GeneralEdiNetworkVO.class);
                networkVO.setMobile(generalEdiNetworkVO.getMobile());
            }
        }
        return networkVO;
    }


    /**
     * 从缓存中查询省市区和大头笔信息
     *
     * <AUTHOR>
     * @version 1.0.0
     * @since 2020-02-23
     */
    private BigCodeVO getBigCodeVO(String area, String city) {
        String name = city + area;

        // 先从区查询，按拼多多市区名称
        String bigCodeStr = BigCodeAreaUtils.get(name);

        // 如果查不到，再按本地市区二次查询
        if (isNull(bigCodeStr)) {
            bigCodeStr = BigCodeAreaUtils.getLocal(name);
        }

        // 按拼多多其他区查询
        if (isNull(bigCodeStr)) {
            bigCodeStr = BigCodeAreaUtils.get(city + "其他区");
        }

        // 如果查不到，再按其它区查询
        if (isNull(bigCodeStr)) {
            bigCodeStr = BigCodeAreaUtils.getLocal(city + "其它区");
        }
        return JSON.parseObject(bigCodeStr, BigCodeVO.class);

    }

}
