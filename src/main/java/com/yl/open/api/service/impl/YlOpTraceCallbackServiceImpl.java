package com.yl.open.api.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.util.CollectionUtils;
import com.yl.open.api.dto.CallbackDTO;
import com.yl.open.api.dto.CallbackFailDTO;
import com.yl.open.api.dto.CallbackQueryDTO;
import com.yl.open.api.dto.TypeBase;
import com.yl.open.api.entity.YlOpTraceCallback;
import com.yl.open.api.enums.CallbackTypeEnum;
import com.yl.open.api.helper.YlCallbackFactory;
import com.yl.open.api.mapper.YlOpTraceCallbackMapper;
import com.yl.open.api.service.CallbackService;
import com.yl.open.api.service.IYlOpTraceCallbackService;
import com.yl.open.api.vo.YlOpCallbackVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 物流轨迹回调记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-30
 */
@Service
@Slf4j
public class YlOpTraceCallbackServiceImpl extends ServiceImpl<YlOpTraceCallbackMapper, YlOpTraceCallback> implements IYlOpTraceCallbackService , CallbackService, InitializingBean {

    @Autowired
    private YlCallbackFactory ylCallbackFactory;

    @Autowired
    protected OrikaBeanMapper orikaBeanMapper;



    @Override
    public void saveBatch(List<CallbackDTO> list) {
        saveBatch(orikaBeanMapper.mapAsList(list, YlOpTraceCallback.class));
    }

    @Override
    public void updateBatchCallback(CallbackQueryDTO callbackQueryDTO) {
        if(CollectionUtils.isNotEmpty(callbackQueryDTO.getIds())){
            if(CollectionUtils.isNotEmpty(callbackQueryDTO.getIds())){
                baseMapper.updateStatus(callbackQueryDTO);
            }
        }
    }

    @Override
    public List<YlOpCallbackVO> queryBatchCallback() {
        try{

            List<YlOpCallbackVO> list = baseMapper.queryBatchTraceCallback();
            if(list.size() > 0){
                CallbackQueryDTO callbackQueryDTO = new CallbackQueryDTO();
                callbackQueryDTO.setIds(list.stream().map(YlOpCallbackVO::getId).collect(Collectors.toList()));
                callbackQueryDTO.setRequestStatus(3);
                //锁定记录
                this.updateBatchCallback(callbackQueryDTO);
            }
            return list;

        }catch (Exception e){
            log.info("物流轨迹分布式锁获取取失败:",e);
        }
        return Lists.newArrayList();

    }

    @Override
    public void updateCallback(CallbackDTO callbackDTO) {
        CallbackQueryDTO callbackQueryDTO = new CallbackQueryDTO();
        callbackQueryDTO.setIds(Lists.newArrayList(callbackDTO.getId()));
        callbackQueryDTO.setRequestStatus(callbackDTO.getRequestStatus());
        callbackQueryDTO.setResResult(callbackDTO.getResResult());
        baseMapper.updateStatus(callbackQueryDTO);
    }

    @Override
    public void deleteCallback(CallbackDTO callbackDTO) {
        baseMapper.deleteById(callbackDTO.getId());
    }

    @Override
    public IPage<YlOpCallbackVO> queryBatchFailCallback(CallbackFailDTO callbackFailDTO) {
        IPage<YlOpCallbackVO> iPage = new Page<>();
        iPage.setCurrent(callbackFailDTO.getCurrentPage());
        iPage.setSize(1000);
        IPage<YlOpCallbackVO> result = baseMapper.queryBatchFailTraceCallback(iPage, callbackFailDTO);
        return result;
    }

    @Override
    public void deleteBatchCallback(TypeBase typeBase) {
        baseMapper.deleteBatchCallback();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ylCallbackFactory.register(CallbackTypeEnum.TRACE.getCode(),this);
    }
}
