/*
 * 云路供应链科技有限公司 版权所有 © Copyright 2019
 */
package com.yl.open.api.stream;

import com.alibaba.cloud.stream.binder.rocketmq.constant.RocketMQConst;
import com.alibaba.fastjson.JSON;
import com.yl.open.api.dto.OmsOrderApiDTO;
import com.yl.open.api.stream.channel.AddOrderSource;
import com.yl.open.api.vo.BillCodeCallbackVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.messaging.support.MessageBuilder;

/**
 * 新增订单发送消息到MQ
 * 
 * <AUTHOR>
 * @since 2020-02-22
 * @version 1.0.0
 */
@Slf4j
@EnableBinding(AddOrderSource.class)
public class AddOrderPublisher {

    @Autowired
    private AddOrderSource source;

    public void send(Object message) {
        log.info("新增订单发送消息参数，{}", JSON.toJSONString(message));
        source.output().send(MessageBuilder.withPayload(message).build());
        log.info("新增订单发送消息成功，{}", JSON.toJSONString(message));
    }

    public void waybillCallback(OmsOrderApiDTO omsOrder){
        BillCodeCallbackVo vo = new BillCodeCallbackVo();
        vo.setOrderId(omsOrder.getCustomerOrderId());
        vo.setBillCode(omsOrder.getWaybillId());
        vo.setItem(omsOrder.getPackageNumber());
        vo.setSource(omsOrder.getOrderSourceCode());
        source.waybillCallbackOutput().send(MessageBuilder.withPayload(vo)
                .setHeader(RocketMQConst.Headers.KEYS, omsOrder.getWaybillId())
                .build());
    }

}
