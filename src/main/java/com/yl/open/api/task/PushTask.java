package com.yl.open.api.task;

import com.yl.open.api.dto.CallbackFailDTO;
import com.yl.open.api.dto.TypeBase;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:推送执行器接口
 * @Project:
 * @CreateDate: Created in 2021-01-11
 * @Author: xpj
 */
public interface PushTask {

    /***
     * Method Description
     * 推送正常的数据
     * @return void
     * <AUTHOR>
     * @date 2021-01-14
     */
    public void execute();

    /***
     * Method Description
     * 推送异常的数据
     * @param callbackFailDTO
     * @return void
     * <AUTHOR>
     * @date 2021-01-14
     */
    public void executeFail(CallbackFailDTO callbackFailDTO);

    /***
     * Method Description
     * 删除30天以前记录
     * @return void
     * <AUTHOR>
     * @date 2021-04-01
     */
    public void executeDelete();


}
