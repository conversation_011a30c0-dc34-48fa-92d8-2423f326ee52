package com.yl.open.api.track.push.msg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yl.open.api.track.push.msg.track.*;
import com.yl.track.core.common.entity.PreTrackMsg;
import com.yl.track.core.data.push.ext.PushMsgExtProcessor;
import com.yl.track.framework.constant.Constant;
import com.yl.track.setting.entity.PushApiConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("pushTrackMsgExtProcessorImpl")
public class PushTrackMsgExtProcessorImpl implements PushMsgExtProcessor {

    @Autowired
    private ShopeeTrackMapperImpl shopeeMapper;

    @Autowired
    private BliBliTrackMapperImpl bliBliTrackMapper;

    @Override
    public PreTrackMsg mapperBefore(PreTrackMsg preTrackMsg, PushApiConfig pushApiConfig) {
        switch (pushApiConfig.getCode()) {
            case Constant.ENCRYPTION_CODE_TIKTOK:
                return TikTokTrackMapperImpl.mapperBefore(preTrackMsg, pushApiConfig);
            case Constant.ENCRYPTION_CODE_LAZADA:
                return LazadaTrackMapperImpl.mapperBefore(preTrackMsg, pushApiConfig);
            case Constant.ENCRYPTION_CODE_BLIBLI:
                return bliBliTrackMapper.mapperBefore(preTrackMsg, pushApiConfig);
            case Constant.ENCRYPTION_CODE_TOKOPEDIA:
                return TokopediaTrackMapperImpl.mapperBefore(preTrackMsg, pushApiConfig);
        }

        return preTrackMsg;
    }

    @Override
    public Object extMapper(PreTrackMsg preTrackMsg, PushApiConfig pushApiConfig) {
        if ("DaQin".equals(pushApiConfig.getCode())) {
            return DaQinTrackMapperImpl.extMapper(preTrackMsg, pushApiConfig);
        }

        return null;
    }


    @Override
    public Object mapperAfter(Object bodyObj, PreTrackMsg preTrackMsg, PushApiConfig pushApiConfig) {
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(bodyObj));
        switch (pushApiConfig.getCode()) {
            case Constant.ENCRYPTION_CODE_LAZADA:
                return LazadaTrackMapperImpl.mapperAfter(jsonObject, preTrackMsg);
            case Constant.ENCRYPTION_CODE_TIKTOK:
                return TikTokTrackMapperImpl.mapperAfter(jsonObject, preTrackMsg);
            case Constant.ENCRYPTION_CODE_BLIBLI:
                return bliBliTrackMapper.mapperAfter(jsonObject, preTrackMsg);
            case Constant.ENCRYPTION_CODE_SHOPEE_TRACK:
                return shopeeMapper.mapperAfter(jsonObject, preTrackMsg);
            case Constant.ENCRYPTION_CODE_TOKOPEDIA:
                return TokopediaTrackMapperImpl.mapperAfter(jsonObject, preTrackMsg);
        }

        return jsonObject;
    }
}
