package com.yl.open.api.track.push.msg.track;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yl.common.base.util.StringUtils;
import com.yl.track.core.common.dao.PreTrackMsgDao;
import com.yl.track.core.common.entity.PreTrackMsg;
import com.yl.track.core.common.enums.TrackNodeEnum;
import com.yl.track.framework.util.DateUtils;
import com.yl.track.setting.entity.PushApiConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;


@Component
public class BliBliTrackMapperImpl {

    @Autowired
    private PreTrackMsgDao preTrackMsgDao;

    public PreTrackMsg mapperBefore(PreTrackMsg preTrackMsg, PushApiConfig pushApiConfig) {
        if (TrackNodeEnum.SIGN.getCode() != preTrackMsg.getScanCode()) {
            preTrackMsg.setSigner(null);
            preTrackMsg.setSignName(null);
        }
        if (StringUtils.isNotEmpty(preTrackMsg.getScanTime())) {
            Long milli = DateUtils.esDateToEpochMilli(preTrackMsg.getScanTime());
            preTrackMsg.setScanTime(String.valueOf(milli));
        }
        return preTrackMsg;
    }


    public JSONArray mapperAfter(JSONObject jsonObject, PreTrackMsg preTrackMsg) {

        if (StringUtils.isNotEmpty(preTrackMsg.getOrderInputTime())
                && (TrackNodeEnum.DISPATCH_NETWORK.getCode() == preTrackMsg.getScanCode()
                || TrackNodeEnum.CANCEL_ORDER.getCode() == preTrackMsg.getScanCode())) {
            jsonObject.put("airwayBillPrintDate", DateUtils.esDateToEpochMilli(preTrackMsg.getOrderInputTime()));
        } else if (StringUtils.isNotEmpty(preTrackMsg.getScanTime()) && TrackNodeEnum.COLLECTION.getCode() == preTrackMsg.getScanCode()) {
            jsonObject.put("airwayBillPrintDate", Long.valueOf(preTrackMsg.getScanTime()));
            jsonObject.put("actualPickupTimestamp", Long.valueOf(preTrackMsg.getScanTime()));
        } else if (StringUtils.isNotEmpty(preTrackMsg.getScanTime()) && TrackNodeEnum.PICK_FAIL.getCode() == preTrackMsg.getScanCode()) {
            jsonObject.put("airwayBillPrintDate", Long.valueOf(preTrackMsg.getScanTime()));
        } else {
            PreTrackMsg pickData = getPickData(preTrackMsg);
            if (Objects.nonNull(pickData) && StringUtils.isNotEmpty(pickData.getScanTime())) {
                Long milli = DateUtils.esDateToEpochMilli(pickData.getScanTime());
                jsonObject.put("airwayBillPrintDate", milli);
                jsonObject.put("actualPickupTimestamp", milli);
            }
        }


        JSONArray status = Optional.ofNullable(jsonObject.getJSONArray("status")).orElse(new JSONArray());
        JSONObject statusJSONObject = Optional.ofNullable(status.getJSONObject(0)).orElse(new JSONObject());
        setPicUrl(preTrackMsg, statusJSONObject);

        return getObject(jsonObject);
    }

    private JSONArray getObject(JSONObject jsonObject) {
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(jsonObject);
        return jsonArray;
    }

    private void setPicUrl(PreTrackMsg preTrackMsg, JSONObject statusJSONObject) {
        List<String> picUrl = preTrackMsg.getPicUrl();
        if (CollectionUtil.isNotEmpty(picUrl)) {
            statusJSONObject.put("receiverSignaturePictureUrl", picUrl.get(0));
            if (picUrl.size() > 1) {
                statusJSONObject.put("receiverPictureUrl", picUrl.get(1));
            }
        }
    }

    private PreTrackMsg getPickData(PreTrackMsg preTrackMsg) {
        return preTrackMsgDao.getByBillCode(preTrackMsg.getBillCode(), TrackNodeEnum.COLLECTION.getCode());
    }


}
