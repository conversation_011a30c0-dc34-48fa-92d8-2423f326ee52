package com.yl.open.api.track.push.msg.track;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.yl.common.base.model.vo.Result;
import com.yl.track.core.common.entity.PreTrackMsg;
import com.yl.track.core.common.enums.TrackNodeEnum;
import com.yl.track.core.common.feign.OmsOrderFeign;
import com.yl.track.core.common.vo.OmsOrderApiVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class ShopeeTrackMapperImpl {

    @Autowired
    private OmsOrderFeign omsOrderFeign;

    public Object mapperAfter(JSONObject jsonObject, PreTrackMsg preTrackMsg) {
        if (TrackNodeEnum.COLLECTION.getCode() == preTrackMsg.getScanCode()) {
            setWeight(jsonObject, preTrackMsg);
        }
        if (TrackNodeEnum.PICK_FAIL.getCode() == preTrackMsg.getScanCode()) {
            setPickFailPicture(jsonObject, preTrackMsg);
        }
        if (TrackNodeEnum.SIGN.getCode() == preTrackMsg.getScanCode()) {
            setSignPicture(jsonObject, preTrackMsg);
        }
        return jsonObject;
    }

    private void setSignPicture(JSONObject jsonObject, PreTrackMsg preTrackMsg) {
        List<JSONObject> picUrlsList = getPicUrlsList(preTrackMsg);
        jsonObject.put("epod_details", picUrlsList);
    }

    private void setPickFailPicture(JSONObject jsonObject, PreTrackMsg preTrackMsg) {
        List<JSONObject> picUrlsList = getPicUrlsList(preTrackMsg);
        jsonObject.put("epop_details", picUrlsList);
    }

    private List<JSONObject> getPicUrlsList(PreTrackMsg preTrackMsg) {
        List<JSONObject> picUrlsList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(preTrackMsg.getPicUrl())) {
            for (int i = 0; i < Math.min(preTrackMsg.getPicUrl().size(), 4); i++) {
                String picUrl = preTrackMsg.getPicUrl().get(i);
                JSONObject picUrlObj = new JSONObject();
                picUrlObj.put("url", picUrl);
                picUrlsList.add(picUrlObj);
            }
        }
        return picUrlsList;
    }

    private void setWeight(JSONObject jsonObject, PreTrackMsg preTrackMsg) {
        Result<OmsOrderApiVO> result = omsOrderFeign.getOrderInfoByWaybillId(preTrackMsg.getBillCode());
        if (result.isSucc() && Objects.nonNull(result.getData())) {
            OmsOrderApiVO omsOrderApiVO = result.getData();

//            BigDecimal weight = StringUtils.isNotEmpty(preTrackMsg.getWeight()) ? new BigDecimal(preTrackMsg.getWeight()) : BigDecimal.ZERO;
//            BigDecimal packageChargeWeight = ObjectUtils.defaultIfNull(omsOrderApiVO.getPackageChargeWeight(), BigDecimal.ZERO);
//            BigDecimal packageTotalWeight = ObjectUtils.defaultIfNull(omsOrderApiVO.getPackageTotalWeight(), BigDecimal.ZERO);
//            BigDecimal packageVolume = ObjectUtils.defaultIfNull(omsOrderApiVO.getPackateVolume(), BigDecimal.ZERO);
//            double max = NumberUtils.max(packageChargeWeight.doubleValue(), packageTotalWeight.doubleValue(), packageVolume.doubleValue(), weight.doubleValue());
            jsonObject.put("weight", preTrackMsg.getWeight());
            jsonObject.put("insurance", omsOrderApiVO.getInsuredValue());
        }
    }
}
