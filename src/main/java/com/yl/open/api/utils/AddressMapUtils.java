package com.yl.open.api.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.yl.open.api.constant.RedisKeyConstants;
import com.yl.open.api.service.AddressMapService;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

@Slf4j
@Component
public class AddressMapUtils {

    @Autowired
    private AddressMapService addressMapService;

    private static Map<Object, Object> localCache = Maps.newConcurrentMap();

    //key为 省市区名称拼接
    public static void putAddressMapArea(Map<String, String> maps) {
        RedisUtil.hPutAll(RedisKeyConstants.OPEN_ADDRESS_MAP_AREA, maps);
    }

    public static void putAddressMapAreaOne(String key, String value) {
        RedisUtil.hPut(RedisKeyConstants.OPEN_ADDRESS_MAP_AREA, key, value);
    }

    public static void delAddressMapAreaOne(String key) {
        RedisUtil.hDelete(RedisKeyConstants.OPEN_ADDRESS_MAP_AREA, key);
    }

    public static String getAddressMapArea(String name) {
        String redisStr = null;
        try {
            redisStr = (String) RedisUtil.hGet(RedisKeyConstants.OPEN_ADDRESS_MAP_AREA, name);
        } catch (Exception e) {
            log.error("获取地址映射数据Redis异常：{}", name, e);
        }
        if (StringUtils.isNotEmpty(redisStr)) {
            log.info("获取地址映射数据Redis缓存命中：{},{}", name, redisStr);
            return redisStr;
        }
        log.error("获取地址映射数据Redis缓存为空：{}", name);
        String localStr = (String) localCache.get(name);
        if (StringUtils.isNotEmpty(localStr)) {
            log.info("获取地址映射数据LocalMap缓存命中：{},{}", name, localStr);
            return localStr;
        }
        log.error("获取地址映射数据LocalMap缓存为空：{}", name);
        return null;
    }


    public void init() {
        initAndRefreshLocalCache();
    }

    @Scheduled(cron = "0 0 3 * * ? ")
    public void scheduledRefresh() {
        initAndRefreshLocalCache();
    }

    private void initAndRefreshLocalCache() {
        try {
            Map<Object, Object> redisMap = RedisUtil.hGetAll(RedisKeyConstants.OPEN_ADDRESS_MAP_AREA);

            if (CollectionUtil.isEmpty(redisMap)) {
                log.info("Redis缓存为空，开始初始化地址映射数据");
                addressMapService.initAddress("4,3,2,1");
                return;
            }

            // 创建新的Map并复制数据，避免直接引用
            Map<Object, Object> newCache = Maps.newConcurrentMap();
            newCache.putAll(redisMap);

            // 原子性地替换本地缓存
            localCache = newCache;

            log.info("本地缓存更新成功，当前缓存大小: {}", localCache.size());
        } catch (Exception e) {
            log.error("更新本地缓存失败", e);
            // 如果更新失败，保留原有缓存
        }
    }


}
