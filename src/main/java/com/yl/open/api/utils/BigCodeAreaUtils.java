/*
 * 云路供应链科技有限公司 版权所有 © Copyright 2019
 */
package com.yl.open.api.utils;

import com.yl.open.api.constant.RedisKeyConstants;
import com.yl.redis.util.RedisUtil;

import java.util.Map;

/**
 * 大头笔区数据Redis读取工具类
 * 
 * <AUTHOR>
 * @since 2020-02-07
 * @version 1.0.0
 */
public class BigCodeAreaUtils {

    public static void put(Map<String, String> maps) {
        RedisUtil.delete(RedisKeyConstants.BIG_PDD_PROV_CITY_AREA);
        RedisUtil.hPutAll(RedisKeyConstants.BIG_PDD_PROV_CITY_AREA, maps);
    }
    
    public static void putArea(Map<String, String> maps) {
    	RedisUtil.delete(RedisKeyConstants.BIG_PDD_AREA);
        RedisUtil.hPutAll(RedisKeyConstants.BIG_PDD_AREA, maps);
    }
    public static String getArea(String name) {
    	 return (String) RedisUtil.hGet(RedisKeyConstants.BIG_PDD_AREA, name);
    }
    public static String get(String name) {
        return (String) RedisUtil.hGet(RedisKeyConstants.BIG_PDD_PROV_CITY_AREA, name);
    }

    public static void putLocal(Map<String, String> maps) {
        RedisUtil.hPutAll(RedisKeyConstants.BIG_LOCAL_AREA, maps);
    }

    public static String getLocal(String name) {
        return (String) RedisUtil.hGet(RedisKeyConstants.BIG_LOCAL_AREA, name);
    }

    public static void putLocalAreaId(Map<String, String> maps) {
        RedisUtil.hPutAll(RedisKeyConstants.BIG_LOCAL_AREA_ID, maps);
    }

    public static String getLocalAreaId(String name) {
        return (String) RedisUtil.hGet(RedisKeyConstants.BIG_LOCAL_AREA_ID, name);
    }

    public static void putLocalNetworkId(Map<String, String> maps) {
        RedisUtil.hPutAll(RedisKeyConstants.BIG_LOCAL_NETWORK_ID, maps);
    }

    public static String getLocalNetworkId(String name) {
        return (String) RedisUtil.hGet(RedisKeyConstants.BIG_LOCAL_NETWORK_ID, name);
    }

}
