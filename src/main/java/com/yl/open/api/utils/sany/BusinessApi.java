package com.yl.open.api.utils.sany;

import com.alibaba.fastjson.JSON;
import com.yl.open.api.vo.sany.CommonPlainHead;
import com.yl.open.api.vo.sany.CommonReq;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class BusinessApi {

    public static Map<String, Object> postSany(String plainText, String url, Map<String, String> headers) {
        // 1、获取APP_Token 用于业务接口调用
        // 此处示例直接调用开发者认证接口，对于实际项目中应当从缓存中获取，不需要每次业务
        // 请求都进行开发者认证
        ApproveDev.approve(headers);

        // 2、按照业务接口定义构建请求报文明文结构,每个接口都拥有自己的请求报文和响应报文结构
        // 业务接口定义可以参考集成平台--门户--产品中心或者由业务系统直接提供接口文档
        // 目前集成平台只支持 restful 风格的请求，请求 Content-Type:application/json
        log.info("Sany业务接口请求报文明文为：{}", plainText);

        // 3、生成 randomKey 用于加密请求报文，基于安全性考虑，每次请求randomKey需要不一样
        // 同时 randomKey会用于解密响应报文
        String randomKey = SanyStringUtils.getRandomKey();
        log.info("randomKey ===== {}", randomKey);

        // 4、使用 AES 加密请求报文明文
        String scrtData = CryptoUtils.encryptByAes(plainText, randomKey);
        log.info("Sany业务接口请求报文加密后的密文为:{}", scrtData);

        // 5、 对请求报文进行加签
        String cntrKey = ApproveDev.CNTR_KEY;
        String scrtSgn = CryptoUtils.signBySha256(plainText, cntrKey);
        log.info("Sany业务接口请求报文加签后的值为：{}", scrtSgn);

        // 6、对 randomKey进行加密，加密采用AES
        String syncKey = ApproveDev.SYNC_KEY;
        String scrtKey = CryptoUtils.encryptByAes(randomKey, syncKey);
        log.info("Sany业务接口加密密钥randomKey加密后的值为：{}", scrtKey);

        // 7、构造请求集成平台报文
        CommonReq commonReq = CommonUtils.buildCommonReq(scrtData, scrtKey, scrtSgn);

        // 8、构建请求 header
        CommonPlainHead commonPlainHead = HeadUtils.buildHead(headers.get("appKey"));
        commonPlainHead.setAppToken(ApproveDev.APP_TOKEN);
        Map<String, String> httpHeaders = HeadUtils.buildHttpHeaders(commonPlainHead);

        // 9、构建业务接口url，业务接口url规则为 产品ID(productId)+接口ID(serviceId)
//        String productId = "0107101101";
//        String servcieId = "0107101101";

        // 10、请求接口
        return HttpUtils.doPost(url, commonReq, httpHeaders, randomKey, headers.get("publicKey"));
    }


    public static void main(String[] args) {
        String plainText = "{\n" +
                "        \"billCode\": \"200000971263\",\n" +
                "        \"damageLostType\": \"damag\",\n" +
                "        \"details\": [\n" +
                "            {\n" +
                "                \"billCode\": \"200000971263\",\n" +
                "                \"customerOrderId\": \"HPP24061400001\",\n" +
                "                \"desc\": \"【SOREANG】paket sudah diterima!penerima adalah【diterima sendiri】，jika ada pertanyaan silakan hubungi 13366669999，jika ada masalah atau pengaduan silakan hubungi nomor telepon outlet 66548745121|cs\",\n" +
                "                \"orderId\": 460022290906615829,\n" +
                "                \"picUrl\": [\n" +
                "                    \"https://demof.jtcargo.co.id/lite-ylappbc/SIGNING_SCAN_LIST/afcef7dcc84f461782bb87600bc0785e.jpg?Expires=1667911034&OSSAccessKeyId=LTAI5tFz5SZkarAKQLRKZYcf&Signature=z0Z665MjeudcRQ4D5LenzNnONzM%3D&response-content-disposition=inline%3B%20filename%3Dafcef7dcc84f461782bb87600bc0785e.jpg&response-content-encoding=UTF-8&response-content-type=image%2Fjpeg\",\n" +
                "                    \"https://demof.jtcargo.co.id/lite-ylappbc/SIGNING_SCAN_LIST/2963439843da44bbb4df05b206f14bd5.jpg?Expires=1667911034&OSSAccessKeyId=LTAI5tFz5SZkarAKQLRKZYcf&Signature=mPVFE0MTdxiA%2BuzfR6H5uzHJKvI%3D&response-content-disposition=inline%3B%20filename%3D2963439843da44bbb4df05b206f14bd5.jpg&response-content-encoding=UTF-8&response-content-type=image%2Fjpeg\"\n" +
                "                ],\n" +
                "                \"scanCode\": 10,\n" +
                "                \"scanNetworkArea\": \"MAJALAYA-SOG\",\n" +
                "                \"scanNetworkCity\": \"SOREANG\",\n" +
                "                \"scanNetworkId\": 159,\n" +
                "                \"scanNetworkName\": \"TestWD1J\",\n" +
                "                \"scanNetworkProvince\": \"JAWA BARAT\",\n" +
                "                \"scanNetworkTypeName\": \"PCI001A \",\n" +
                "                \"scanTime\": \"2022-11-14 16:10:28\",\n" +
                "                \"scanType\": \"tanda terima\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }";
        Map<String, String> headers = new HashMap<>();
        headers.put("appKey", "453cb39c769f4f45a9bc68d654a75109");
        headers.put("privateKey", "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCjiyp2PT0Nzj8YkHNgnDfAoaEl4Cbj+N8R+qxwjxeSD5bRqHJT8viOcQNn6V78nQyEniFu5wemYeHQjuZOzvit0mC/YWQvAD+MKVnWCybK4jxjx2R2HOkHCpe5h2vz8GTwZxFPwWc7QdWdtjHe/scGxxvvswRj2RQiu++hSSWEkevn00OXeBG7BvOqCD9abhZnoBq1kQ1rc65gaWZE2sJbdOu9yy/QWkW81O1nKFg8/Ju7ghqnsunJV2A13K6KogsofD1xKDiAUxIXLFJ6S/DTKGOffoZ7b329jxjmAl1Q0DxaxNsEljBREaV7cYm2dt9E3xLtftfhjsVtLzSciwflAgMBAAECggEAOygp0d9Q6F+EX0RRF88qM5nDisnMB4aMWhqMqaJ46jKNYYVG5arr0cp7auj1nPbKAmaYvV8AW2Too+Is2LDxLx78M/3smTvDwNVsU4pxG849/fGwGzFV8WAZhdjAadq5CGSf1HOm/b5QoN24qGsMXx1Cs6/TlLJ/IBlPEs48rfjucU+M7OEu7oPylMYHlZqnupH+Zp/b6d27o/ziykNBPeRX35b2ZJ632gC7yyMwtQeF1ne3BJBAgqlmt0n8CvX8KjJhn1VHmN+QkIK6XADX2g1m2ojWNVNcj8rpahTbY+VunfkRdHoRTMMfUevwRTLifS8aPraN7YoJCaqlxe3hQQKBgQDU4NRE9CB6FfHBWFdl4li2OYAjLdwCZNwuHNzEIAROYHscnUUovz1ncwmi9KBBUFucEyTIM4IJOpz0MGKMl+isRoPffyYY/xJO8UJU6mDf9pMvyQHVNZRllUW8rM8CeMGJjd2oYEVEHgnO9oM8cJRtuoHVNEKeSqsJKfrSO7aK7QKBgQDErAEQxpEyLl8HKKBo3I3ru40MJkEvox2blpZuMEM0MHpy4hMxFEwZDGKLDFHWWB6ELrreQQ4SRBu/tLF+yY7loUzWna4pdbqPGKkZ2PiQ7XV3IzpQZ42ftwhm/XU2a/j8q/K3NviW8YUEv8WF3msINO1kRx/3zRnJ6mW91tK52QKBgQDJLMetekAO4Bodq8y8XhGT8/ciR755DLP/aAtB1Tf8kbUGr6GiBkiTMOXUd/XSg4xvjK/f+r1S129oXSlk0FJ0LBeH6PMqADNfIySadvyh6rXiHMV8pivdbBoBWhYvscrEoart6jzLSM/gJJqVTZcXs3/q6n5idRH5kRCWCRRTaQKBgHg3m78hYYeHCNdbYhI3HxlFX4ZZq+bukh6VAgcAwVOZfn/S6WBKw+lbxLCyKyQWYTDzT46jvXI057FbRlP16L88xbc6U0dMi2kE7fi6w9XzEKZuhiq34b4LJ7usmoVVorDwekC7/WBm6aRphNe7iTlPAP0W7NeQK39dJCvSsKShAoGAY4bpzogevOX1RrrznqtJGe9+TarBu8uHZIBVj+D65LGqDnGB5Rf1THZJDNd7E8XNb5gW7yAVEbzPnd4wBylaC0+PlqnBQVI7xtHQTKD4rBMc9k7EUDjJCNEThImDe9X+KH+diJzVc8riQsve0yvBxIaemZZU8HwaJqraSmUnjeg=");
        headers.put("publicKey", "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwe7zmBJbtcxVeMz/GzZrdCVMIFOPuQMOOmnLsiTrP0QKexuJpeNhtpz5GdAQW97rAYHGamZq+Ob3iVVROjKvdFJ6rqOL+ihu/tKiP8oGI94BCBixvFmABrrRqOdlw+ZEbNE7KbKTswU1AOrvIwknyy1AM6A/FsTyqulPsa94KGBJBE2Vp3xS+Ku8sxdWte69PBzTEwZivezbWGU0mb0/HSXN0MRS5r09j7wzO4ft76FLdz7M1UqOMvP5S9G2hhVdzQraEghHa1oIyN00SMvMUqXEweDn12dr+Bb5al852mk6hkmXiM4uzrTG0bIVIEZ0hUR+JLadV4OAhlRybnWYawIDAQAB");
        String url = "https://ipaas-openapi-test.sany.com.cn/api/0107101101/0107101101";
        Map<String, Object> map = postSany(plainText, url, headers);
        log.info("调用Sany轨迹推送接口响应：{}", JSON.toJSONString(map.get("body")));
    }

}
