package com.yl.open.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description: TODO
 * @Author: mac
 * @Date: 2021/6/29 4:40 下午
 */
@Data
public class BillCodeVO implements Serializable {
    /**
     * 主单号
     */
    private String billCode;
    /**
     * 子单号（如果件数为1，则只返回主单号，该属性是没有值的）
     */
    private List<String>  childBillCode;

}
