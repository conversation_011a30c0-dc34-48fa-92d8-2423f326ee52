package com.yl.open.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2020-08-31
 * @Author: xpj
 */
@Data
public class CertificationInfoVO implements Serializable {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 客户账号
     */
    private String account;

    /**
     * 企业全称
     */
    private String enterpriseName;

    /**
     * 秘钥账号
     */
    private String apiAccount;
    /**
     * 秘钥
     */
    private String privateKey;


    /**
     * 订单来源
     */
    private String src;

    /**
     * 订单来源名称
     */
    private String name;

    /**
     * 结算账单回调url
     */
    private String freightUrl;

    /**
     * 订单状态回调url
     */
    private String orderStatusUrl;

    /**
     * 物流轨迹回调url
     */
    private String traceUrl;

    /**
     * 可用接口列表
     */
    List<UserInterfaceVO> userInterfaceVOList;


}
