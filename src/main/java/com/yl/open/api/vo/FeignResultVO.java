package com.yl.open.api.vo;

import com.yl.common.base.enums.ResultCodeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title FeignResultVO
 * @Package com.yl.edi.vo
 * @Description
 * @date 2021/10/26 5:45 下午
 */
@Data
public class FeignResultVO<T> {

    /**
     * 响应码
     */
    private Integer code;
    /**
     * 响应信息
     */
    private String msg;
    /**
     * 响应业务数据体
     */
    private T data;

    private Boolean succ;

    private Boolean fail;

    public boolean isSucc(){
        return this.code != null && ResultCodeEnum.SUCCESS.getCode() == this.code;
    }

    public boolean isFail(){
        return !isSucc();
    }
}