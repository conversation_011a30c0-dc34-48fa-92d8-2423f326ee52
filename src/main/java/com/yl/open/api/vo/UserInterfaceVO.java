package com.yl.open.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class UserInterfaceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(notes = "id")
    private Long id;

    /**
     * 用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(notes = "用户id")
    private Long userId;

    /**
     * 接口id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(notes = "接口id")
    private Long interfaceId;

    /**
     * 0:未申请，1：已申请,2:已上线3：停用
     */
    @ApiModelProperty(notes = "状态-0:未申请，1：已申请,2:已上线3：停用")
    private Integer status;

    /**
     * 备注信息
     */
    @ApiModelProperty(notes = "备注信息")
    private String remark;

    /**
     * 接口说明
     */
    @ApiModelProperty(notes = "接口说明")
    private String detail;


    /**
     * 接口名称
     */
    @ApiModelProperty(notes = "接口名称")
    private String name;

    /**
     * 创建时间
     */
    @ApiModelProperty(notes = "创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(notes = "更新时间")
    private LocalDateTime updatedTime;

    /**
     * 接口路径
     */
    @ApiModelProperty(notes = "接口路径")
    private String url;
    /**
     * 企业全称
     */
    @ApiModelProperty(notes = "企业全称")
    private String enterpriseName;

    /**
     * 更新人
     */
    @ApiModelProperty(notes = "更新人")
    private String updatedBy;

    /**
     * 账号
     */
    @ApiModelProperty(notes = "账号")
    private String account;

    /**
     * 接口类型:0普通接口1回调接口
     */
    @ApiModelProperty(notes = "接口类型:0普通接口1回调接口")
    private Integer interfaceType;

    /**
     * 回调接口url
     */
    @ApiModelProperty(notes = "回调接口url")
    private String urlCallback;

    /**
     * 联调的url
     */
    @ApiModelProperty(notes = "联调的url")
    private String testUrl;

    /**
     * UAT联调的url
     */
    @ApiModelProperty(notes = "UAT联调的url")
    private String uatTestUrl;
    @ApiModelProperty(notes = "UAT联调结果默认false")
    private boolean uatTestResult;

    /**
     * 联调成功的次数
     */
    @ApiModelProperty(notes = "联调成功的次数")
    private Integer testNum;

    /**
     * 联调成功的时间
     */
    @ApiModelProperty(notes = "联调成功的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime testTime;
}
