package com.yl.open.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-10-15 11:41 <br>
 * @Author: zhipeng.liu
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(notes = "id")
    private Long id;

    /**
     * 账号
     */
    @ApiModelProperty(notes = "账号")
    private String account;

    /**
     * 密码
     */
    @ApiModelProperty(notes = "密码")
    private String password;

    /**
     * 邮箱
     */
    @ApiModelProperty(notes = "邮箱")
    private String email;

    /**
     * 手机号
     */
    @ApiModelProperty(notes = "手机号")
    private String phone;

    /**
     * 角色0客户，1系统管理员
     */
    @ApiModelProperty(notes = "角色0客户，1系统管理员")
    private Integer role;

    /**
     * 禁用启用
     */
    @ApiModelProperty(notes = "状态")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty(notes = "创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @ApiModelProperty(notes = "更新人")
    private String updatedBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(notes = "更新时间")
    private LocalDateTime updatedTime;

    /***
     * 系统生成token
     */
    @ApiModelProperty(notes = "系统生成token")
    private String token;

    /***
     * 系统生成token
     */
    @ApiModelProperty(notes = "系统生成token")
    private String apiAccount;

    /**
     * 秘钥
     */
    @ApiModelProperty(notes = "秘钥")
    private String privateKey;

}
