package com.yl.open.api.vo.baseinfo;




import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 系统区域表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-06-03
 */
@Data
@Accessors(chain = true)

public class SysAreaIdVO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 省Id的集合
     */
    private List<Long> provinceIds;
    /**
     * 市Id的集合
     */
    private List<Long> cityIds;
    /**
     * 区Id的集合
     */
    private List<Long> areaIds;

}
