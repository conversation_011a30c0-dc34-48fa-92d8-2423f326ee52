package com.yl.open.api.vo.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @description oms_order_original_address
 * <AUTHOR>
 * @date 2024-05-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OmsOrderOriginalAddressVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 寄件省份名称
    */
    private String senderProvinceName;

    /**
    * 寄件城市名称
    */
    private String senderCityName;

    /**
    * 寄件区域名称
    */
    private String senderAreaName;

    /**
    * 寄件乡镇
    */
    private String senderTownship;

    /**
    * 收件省份名称
    */
    private String receiverProvinceName;

    /**
    * 收件城市名称
    */
    private String receiverCityName;

    /**
    * 收件区域名称
    */
    private String receiverAreaName;

    /**
    * 收件乡镇
    */
    private String receiverTownship;

    /**
     * 寄件经度
     */
    private String sendLongitude;

    /**
     * 寄件纬度
     */
    private String sendLatitude;

    /**
     * 收件经度
     */
    private String receiverLongitude;

    /**
     * 收件纬度
     */
    private String receiverLatitude;


}