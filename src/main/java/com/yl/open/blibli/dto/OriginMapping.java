/**
  * Copyright 2023 json.cn 
  */
package com.yl.open.blibli.dto;

/**
 * Auto-generated: 2023-06-13 15:58:19
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class OriginMapping {

    private String extBranchCode;
    private String extProvince;
    private String extCity;
    private String extDistrict;
    private String extSubDistrict;
    private String extZipcode;
    private String cityCode;
    public void setExtBranchCode(String extBranchCode) {
         this.extBranchCode = extBranchCode;
     }
     public String getExtBranchCode() {
         return extBranchCode;
     }

    public void setExtProvince(String extProvince) {
         this.extProvince = extProvince;
     }
     public String getExtProvince() {
         return extProvince;
     }

    public void setExtCity(String extCity) {
         this.extCity = extCity;
     }
     public String getExtCity() {
         return extCity;
     }

    public void setExtDistrict(String extDistrict) {
         this.extDistrict = extDistrict;
     }
     public String getExtDistrict() {
         return extDistrict;
     }

    public void setExtSubDistrict(String extSubDistrict) {
         this.extSubDistrict = extSubDistrict;
     }
     public String getExtSubDistrict() {
         return extSubDistrict;
     }

    public void setExtZipcode(String extZipcode) {
         this.extZipcode = extZipcode;
     }
     public String getExtZipcode() {
         return extZipcode;
     }

    public void setCityCode(String cityCode) {
         this.cityCode = cityCode;
     }
     public String getCityCode() {
         return cityCode;
     }

}