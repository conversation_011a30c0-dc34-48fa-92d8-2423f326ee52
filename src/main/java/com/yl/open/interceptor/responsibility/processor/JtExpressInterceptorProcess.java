package com.yl.open.interceptor.responsibility.processor;

import com.alibaba.fastjson.JSON;
import com.yl.common.base.enums.MsgCodeEnum;
import com.yl.open.api.constant.Constants;
import com.yl.open.api.encrypt.EncryptHelper;
import com.yl.open.api.entity.ApiAccount;
import com.yl.open.api.service.IAccountService;
import com.yl.open.api.vo.CertificationInfoVO;
import com.yl.open.interceptor.responsibility.base.Intercept;
import com.yl.open.interceptor.responsibility.base.PathConstants;
import com.yl.open.interceptor.responsibility.base.Payload;
import com.yl.open.interceptor.responsibility.chain.HandleChain;
import com.yl.open.jtExepress.dto.JtExpressAddOrderDTO;
import com.yl.open.jtExepress.enums.JtExpressResultEnum;
import com.yl.open.jtExepress.exception.JtExpressException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * JtExepress 拦截器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/13 14:33
 */
@Slf4j
@Component
@Intercept
public class JtExpressInterceptorProcess extends InterceptorHandlerAbstract {


    @Autowired
    private IAccountService accountService;

    @Override
    public boolean preHandle(Payload payload, HandleChain handleChain) throws Exception {

        HttpServletRequest request = payload.getRequest();
        String requestURI = request.getRequestURI().replaceAll("/+", "/");
        if (!match(requestURI)) {
            return handleChain.preHandle(payload);
        }
        log.info("JtExpressInterceptorProcess开始执行 UUID:{}", payload.getUuid());
        log.info("jtExepress interceptor:{}", requestURI);
        // 数据签名
        String digestHeader = request.getHeader(Constants.DIGEST);
        // 客户标识
        String apiAccountHeader = request.getHeader(Constants.API_ACCOUNT);

        String param = request.getParameter(Constants.DATA_PARAM);
        log.info("请求参数：requestURI:{},apiAccountId:{}, digestHeader:{}, param:{}", requestURI, apiAccountHeader, digestHeader, param);
        if (StringUtils.isBlank(apiAccountHeader)) {
            throw new JtExpressException(JtExpressResultEnum.RESULT_15_CODE.getIdMsg());
        }
        if (StringUtils.isBlank(digestHeader)) {
            throw new JtExpressException(JtExpressResultEnum.RESULT_16_CODE.getIdMsg());
        }
        //校验参数合法
        try {
            JSON.parseObject(param, JtExpressAddOrderDTO.class);
        } catch (Exception e) {
            throw new JtExpressException(JtExpressResultEnum.RESULT_10_CODE.getIdMsg());
        }
        //校验API account
        CertificationInfoVO account = accountService.getAccount(apiAccountHeader);
        if (account == null) {
            throw new JtExpressException(MsgCodeEnum.API_ACCOUNT_NOT_EXIST.getMsg());
        }
        ApiAccount apiAccount = new ApiAccount();
        //校验签名
        String encrypt = EncryptHelper.encrypt(0, param, account.getPrivateKey());
        if (!digestHeader.equalsIgnoreCase(encrypt)) {
            log.warn("签名错误，请求签名：{}, 内部签名：{}", digestHeader, encrypt);
            throw new JtExpressException(JtExpressResultEnum.RESULT_03_CODE.getIdMsg());
        }
        apiAccount.setId(account.getApiAccount());
        apiAccount.setPrivateKey(account.getPrivateKey());
        apiAccount.setFullName(account.getName());
        apiAccount.setShortName(account.getSrc());
        apiAccount.setExchangeAddress("1");
        apiAccount.setAutoScheduling("1");
        apiAccount.setIsCancelOrder("0");
        request.setAttribute("apiAccount", apiAccount);
        return true;
    }

    @Override
    String pattern() {
        return PathConstants.JT_EXPRESS_PATH;
    }


}
