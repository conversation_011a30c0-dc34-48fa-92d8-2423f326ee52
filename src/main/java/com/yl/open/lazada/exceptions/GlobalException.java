package com.yl.open.lazada.exceptions;


import com.yl.open.lazada.model.vo.OutputBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 云路信息科技公司 版权所有 © Copyright 2020
 *
 * @Description: LAZADA全局异常处理
 * @Author: 梁飞
 * @Date: 2020-12-31 18:12
 */
@RestControllerAdvice
@Slf4j
@Order(value = 1)
public class GlobalException {

    public final static String RESPONSE_DATA = "responseData";
    public final static String RESPONSE_ERROR = "error";


    @ExceptionHandler(LazadaException.class)
    public OutputBase handleLazadaException(LazadaException ex) {
        OutputBase outputBase = new OutputBase(ex.getJtsResultEnum().getCode(), ex.getJtsResultEnum().getEnMsg(), ex.getErrors());
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();//TODO 配合链路日志
        request.setAttribute(RESPONSE_DATA, outputBase);
        //最后监听错误
        request.setAttribute(RESPONSE_ERROR, outputBase);
        return outputBase;
    }

//    @ExceptionHandler(Exception.class)
//    public OutputBase handleGlobalException(Exception ex) {
//        log.error("系统异常:", ex);
//        List<ErrorOutput> errors = new ArrayList<>();
//        errors.add(new ErrorOutput(JtsResultEnum.RESULT_S07_CODE.getCode(), JtsResultEnum.RESULT_S07_CODE.getEnMsg()));
//        return OutputBase.errors(errors);
//    }

}
