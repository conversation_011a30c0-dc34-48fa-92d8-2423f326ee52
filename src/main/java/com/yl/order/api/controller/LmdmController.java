package com.yl.order.api.controller;

import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import com.yl.lmdm.api.vo.organizational.SysNetworkVO;
import com.yl.order.api.service.LmdmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * <AUTHOR>
 * @description :
 * @date ：Created in 2021-02-02
 */
@RestController
@RequestMapping(value = "/lmdm")
public class LmdmController extends BaseController {

    @Autowired
    private LmdmService lmdmService;

    @GetMapping("/proxyArea")
    public Result<List<SysNetworkVO>> proxyArea() {
        return success(lmdmService.proxyArea());
    }

//    @PostMapping("/franchisee")
//    public Result<List<SysNetworkVO>> franchisee(@RequestBody String path) {
//        return success(lmdmService.franchisee(path));
//    }


}
