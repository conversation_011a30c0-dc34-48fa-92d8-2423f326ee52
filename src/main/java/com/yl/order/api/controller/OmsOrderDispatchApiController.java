package com.yl.order.api.controller;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import com.yl.order.api.dto.*;
import com.yl.order.api.entity.OmsOrderPickFail;
import com.yl.order.api.feign.LooseOrderDispatchClient;
import com.yl.order.api.service.IOmsOrderDispatchService;
import com.yl.order.api.service.IOmsOrderPickFailService;
import com.yl.order.api.vo.dispatch.OmsOrderDispatchListApiVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 订单调度 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-16
 */
@RestController
@RequestMapping("/omsOrderDispatch")
@Api(tags = "订单调度")
@Slf4j
public class OmsOrderDispatchApiController extends BaseController {

    private final IOmsOrderDispatchService omsOrderDispatchService;

    OmsOrderDispatchApiController(IOmsOrderDispatchService omsOrderDispatchService) {
        this.omsOrderDispatchService = omsOrderDispatchService;
    }

    @Autowired
    private LooseOrderDispatchClient looseOrderDispatchClient;

    @Autowired
    private IOmsOrderPickFailService omsOrderPickFailService;

    /**
     * 分页查询清单
     */
    @PostMapping(value = "/getPages")
    public Result<IPage<OmsOrderDispatchListApiVO>> getPages(@RequestBody OmsOrderDispatchQueryApiDTO dto) {
        return success(omsOrderDispatchService.listByPage(dto));
    }

    /**
     * 批量调度网点
     */
    @PostMapping(value = "/dispatchOrder")
    public Result dispatchOrder(@RequestBody(required = false) OmsOrderDispatchOperateApiDTO dto) {
        log.info("批量调度网点:{}", dto.toString());
        return looseOrderDispatchClient.dispatchOrder(dto);
    }

    /**
     * 批量调度代理区
     */
    @PostMapping(value = "/dispatchProxyArea")
    public Result dispatchProxyArea(@RequestBody OmsOrderDispatchProxyAreaApiDTO dto) {
        log.info("批量调度代理区:{}", dto.toString());
        return looseOrderDispatchClient.dispatchProxyArea(dto);
    }

    /**
     * 批量打回调度网点订单
     */
    @PostMapping(value = "/backDispatchOrder")
    public Result backDispatchOrder(@RequestBody OmsOrderDispatchOperateApiDTO dto) {
        log.info("批量打回调度网点订单:{}", dto.toString());
        return looseOrderDispatchClient.backDispatchOrder(dto);
    }

    /**
     * 批量调度业务员
     */
    @PostMapping(value = "/dispatchSales")
    public Result dispatchSales(@RequestBody(required = false) OmsOrderDispatchOperateApiDTO dto) {
        log.info("批量调度业务员:{}", dto.toString());
        return looseOrderDispatchClient.dispatchSales(dto);
    }


    /**
     * 批量取件失败
     */
    @PostMapping(value = "/pickupFail")
    public Result pickupFail(@RequestBody(required = false) OmsOrderPickFailDTO dto) {
        log.info("批量取件失败:{}", JSON.toJSONString(dto));
        return looseOrderDispatchClient.pickupFail(dto);
    }

    /**
     * 获取订单调度的详细记录
     *
     * @param logsParamDTO
     * @return
     */
    @PostMapping(value = "/getDispatchLogs")
    public Result getDispatchLogs(@RequestBody DispatchLogsParamDTO logsParamDTO) {
        return success(omsOrderDispatchService.getDispatchLogs(logsParamDTO));
    }

//    /**
//     * 调度监控分页查询
//     * @param dto
//     * @return
//     */
//    @PostMapping(value = "/monitor/getPages")
//    public Result getMonitorPages(@RequestBody DispatchMonitorQueryDTO dto) {
//        return success(omsOrderDispatchMonitorService.query(dto));
//    }

    /**
     * 查询取件失败记录
     *
     * @param orderId
     * @return
     */
    @GetMapping(value = "/queryOmsOrderPickFailList")
    public Result<List<OmsOrderPickFail>> queryOmsOrderPickFailList(@RequestParam("orderId") Long orderId) {
        return omsOrderPickFailService.queryOmsOrderPickFailList(orderId);
    }
}

