package com.yl.order.api.dto;






import com.yl.common.base.valid.dto.BaseApiDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单请求实体
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OmsOrder请求对象", description = "订单调度请求实体")
public class OmsOrderDispatchApiDTO extends BaseApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(value = "寄件人姓名")
    @Size(max = 60, message = "寄件人姓名不能超过60个字符")
    @NotBlank(message = "寄件人姓名不能为空")
    private String senderName;

    @ApiModelProperty(value = "寄件人公司")
    @Size(max = 100, message = "寄件人公司不能超过100个字符")
    private String senderCompany;

    @ApiModelProperty(value = "寄件人手机号")
    @Size(max = 30, message = "寄件人手机号不能超过30个字符")
    private String senderMobilePhone;

    @ApiModelProperty(value = "寄件人座机")
    @Size(max = 30, message = "寄件人固话不能超过30个字符")
    private String senderTelphone;

    @ApiModelProperty(value = "寄件国家名称")
    @Size(max = 60, message = "寄件国家名称不能超过60个字符")
    private String senderCountryName;

    @ApiModelProperty(value = "寄件国家Id")
    private Integer senderCountryId;

    @ApiModelProperty(value = "寄件省份名称")
    @Size(max = 60, message = "寄件省份名称不能超过60个字符")
    @NotBlank(message = "寄件省份名称不能为空")
    private String senderProvinceName;

    @ApiModelProperty(value = "寄件省份id")
    private Integer senderProvinceId;

    @ApiModelProperty(value = "寄件城市名称")
    @Size(max = 60, message = "寄件城市名称不能超过60个字符")
    @NotBlank(message = "寄件城市名称不能为空")
    private String senderCityName;

    @ApiModelProperty(value = "寄件城市id")
    private Integer senderCityId;

    @ApiModelProperty(value = "寄件区域名称")
    @Size(max = 60, message = "寄件区域名称不能超过60个字符")
    @NotBlank(message = "寄件区域名称不能为空")
    private String senderAreaName;

    @ApiModelProperty(value = "寄件区域Id")
    private Integer senderAreaId;

    @ApiModelProperty(value = "寄件乡镇")
    @Size(max = 200, message = "寄件乡镇不能超过200个字符")
    private String senderTownship;

    @ApiModelProperty(value = "寄件街道")
    @Size(max = 200, message = "不能超过200个字符")
    private String senderStreet;

    @ApiModelProperty(value = "寄件详细地址")
    @Size(max = 200, message = "寄件详细地址不能超过200个字符")
    @NotBlank(message = "寄件详细地址不能为空")
    private String senderDetailedAddress;

    @ApiModelProperty(value = "寄件邮编")
    @Size(max = 30, message = "寄件邮编不能超过30个字符")
    private String senderPostalCode;

    @ApiModelProperty(value = "收件人姓名")
    @Size(max = 60, message = "收件人姓名不能超过60个字符")
    @NotBlank(message = "收件人姓名不能为空")
    private String receiverName;

    @ApiModelProperty(value = "收件人公司")
    @Size(max = 100, message = "收件人公司不能超过100个字符")
    private String receiverCompany;

    @ApiModelProperty(value = "收件人手机号")
    @Size(max = 30, message = "收件人手机号不能超过30个字符")
    private String receiverMobilePhone;

    @ApiModelProperty(value = "收件人座机")
    @Size(max = 30, message = "收件人固话不能超过30个字符")
    private String receiverTelphone;

    @ApiModelProperty(value = "收件国家名称")
    @Size(max = 60, message = "收件国家名称不能超过60个字符")
    private String receiverCountryName;

    @ApiModelProperty(value = "收件国家id")
    private Integer receiverCountryId;

    @ApiModelProperty(value = "收件省份名称")
    @Size(max = 60, message = "收件省份名称不能超过60个字符")
    @NotBlank(message = "收件省份名称不能为空")
    private String receiverProvinceName;

    @ApiModelProperty(value = "收件省份id")
    private Integer receiverProvinceId;

    @ApiModelProperty(value = "收件城市名称")
    @Size(max = 60, message = "收件城市名称不能超过60个字符")
    @NotBlank(message = "收件城市名称不能为空")
    private String receiverCityName;

    @ApiModelProperty(value = "收件城市id")
    private Integer receiverCityId;

    @ApiModelProperty(value = "收件区域名称")
    @Size(max = 60, message = "收件区域名称不能超过60个字符")
    @NotBlank(message = "收件区域名称不能为空")
    private String receiverAreaName;

    @ApiModelProperty(value = "收件区域id")
    private Integer receiverAreaId;

    @ApiModelProperty(value = "收件乡镇")
    @Size(max = 200, message = "收件乡镇不能超过200个字符")
    private String receiverTownship;

    @ApiModelProperty(value = "收件街道")
    @Size(max = 200, message = "收件街道不能超过200个字符")
    private String receiverStreet;

    @ApiModelProperty(value = "收件详细地址")
    @Size(max = 200, message = "收件详细地址不能超过200个字符")
    @NotBlank(message = "收件详细地址不能为空")
    private String receiverDetailedAddress;

    @ApiModelProperty(value = "收件邮编")
    @Size(max = 30, message = "收件邮编不能超过30个字符")
    private String receiverPostalCode;

    @ApiModelProperty(value = "收件分拣码")
    @Size(max = 30, message = "收件分拣码不能超过30个字符")
    private String receiverSortingCode;

    @ApiModelProperty(value = "备注")
    @Size(max = 500, message = "备注不能超过500个字符")
    private String remarks;

    @ApiModelProperty(value = "快件类型id")
    private Integer expressTypeId;

    @ApiModelProperty(value = "快件类型code")
    @Size(max = 30, message = "快件类型编码不能超过30个字符")
    @NotBlank(message = "快件类型编码不能为空")
    private String expressTypeCode;

    @ApiModelProperty(value = "快件类型名称")
    @Size(max = 30, message = "快件类型名称不能超过30个字符")
    @NotBlank(message = "快件类型名称不能为空")
    private String expressTypeName;

    @ApiModelProperty(value = "物品类型名称")
    @Size(max = 60, message = "物品类型名称不能超过60个字符")
    @NotBlank(message = "物品类型名称不能为空")
    private String goodsTypeName;

    @ApiModelProperty(value = "物品类型id")
    private Integer goodsTypeId;

    @ApiModelProperty(value = "物品类型编码")
    @Size(max = 30, message = "物品类型编码不能超过30个字符")
    @NotBlank(message = "物品类型编码不能为空")
    private String goodsTypeCode;

    @ApiModelProperty(value = "物品名称")
    @Size(max = 200, message = "物品名称不能超过200个字符")
    private String goodsName;

    @ApiModelProperty(value = "件数,＞1表示子母件，如果业务上不支持子母件，则前端限制不显示，并默认为1")
    @Min(value = 1, message = "包裹数量不能小于1")
    @Max(value = 99, message = "包裹数量不能超过99")
    private Integer packageNumber;

    @ApiModelProperty(value = "包裹总重量,单位千克")
    @Digits(integer = 5, fraction = 2, message = "包裹总重量超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal packageTotalWeight;

    @ApiModelProperty(value = "结算方式名称")
    @Size(max = 60, message = "结算方式名称不能超过60个字符")
    @NotBlank(message = "结算方式名称不能为空")
    private String paymentModeName;

    @ApiModelProperty(value = "结算方式id")
    private Integer paymentModeId;

    @ApiModelProperty(value = "结算方式编码")
    @Size(max = 30, message = "结算方式编码不能超过30个字符")
    @NotBlank(message = "结算方式编码不能为空")
    private String paymentModeCode;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "客户编号编码")
    @Size(max = 30, message = "客户编号不能超过30个字符")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    @Size(max = 60, message = "客户名称不能超过60个字符")
    private String customerName;

    /**
     * 更新人
     */

    private Integer updateBy;

    /**
     * 更新人
     */

    private String updateByName;

    /**
     * 更新时间
     */

    private LocalDateTime updateTime;

}
