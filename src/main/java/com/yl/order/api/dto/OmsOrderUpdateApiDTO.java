package com.yl.order.api.dto;


import com.yl.common.base.valid.dto.BaseApiDTO;
import com.yl.common.base.valid.group.EdiUpdateGroup;
import com.yl.common.base.valid.group.JmsUpdateGroup;
import com.yl.order.api.entity.OmsOrderOriginalAddress;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单请求实体
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OmsOrder请求对象", description = "订单请求实体")
public class OmsOrderUpdateApiDTO extends BaseApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户编号")
    @Size(max = 30, message = "客户编号不能超过30个字符")
    @NotBlank(message = "客户编号不能为空", groups = {EdiUpdateGroup.class, JmsUpdateGroup.class})
    private String customerCode;

    @ApiModelProperty(value = "客户订单编号")
    @Size(max = 50, message = "客户订单编号不能超过50个字符")
    @NotBlank(message = "客户订单编号不能为空", groups = {EdiUpdateGroup.class})
    private String customerOrderId;

    @ApiModelProperty(value = "订单编号")
    @NotNull(message = "订单编号不能为空", groups = {JmsUpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "订单类型code，1.散客 2.月结")
    private Integer orderTypeCode;

    @ApiModelProperty(value = "订单来源名称")
    @NotBlank(message = "订单来源名称不能为空", groups = {JmsUpdateGroup.class})
    @Size(max = 30, message = "订单来源名称不能超过30个字符")
    private String orderSourceName;

    @ApiModelProperty(value = "订单来源code")
    @NotBlank(message = "订单来源code不能为空", groups = {JmsUpdateGroup.class})
    @Size(max = 30, message = "订单来源code不能超过30个字符")
    private String orderSourceCode;

    @ApiModelProperty(value = "最佳取件开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bestPickTimeStart;

    @ApiModelProperty(value = "最佳取件结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bestPickTimeEnd;

    @ApiModelProperty(value = "订单状态code")
    @Null
    //暂不允许赋值
    private Integer orderStatusCode;

    @ApiModelProperty(value = "支付方式名称")
    @NotBlank(message = "支付方式名称不能为空", groups = {JmsUpdateGroup.class})
    @Size(max = 30, message = "支付方式名称不能超过30个字符")
    private String paidModeName;

    @ApiModelProperty(value = "支付方式code")
    @NotBlank(message = "支付方式code不能为空", groups = {JmsUpdateGroup.class})
    @Size(max = 30, message = "支付方式code不能超过30个字符")
    private String paidModeCode;

    @ApiModelProperty(value = "寄件人姓名")
    @Size(max = 100, message = "寄件人姓名不能超过100个字符")
    private String senderName;

    @ApiModelProperty(value = "寄件人公司")
    @Size(max = 100, message = "寄件人公司不能超过100个字符")
    private String senderCompany;

    @ApiModelProperty(value = "寄件人手机号")
    @Size(max = 30, message = "寄件人手机号不能超过30个字符")
    private String senderMobilePhone;

    @ApiModelProperty(value = "寄件人座机")
    @Size(max = 30, message = "寄件人固话不能超过30个字符")
    private String senderTelphone;

    @ApiModelProperty(value = "寄件国家名称")
    @Size(max = 30, message = "寄件国家名称不能超过30个字符")
    private String senderCountryName;

    @ApiModelProperty(value = "寄件国家Id")
    private Integer senderCountryId;

    @ApiModelProperty(value = "寄件省份名称")
    @Size(max = 30, message = "寄件省份名称不能超过30个字符")
    @NotBlank(message = "寄件省份名称不能为空", groups = {JmsUpdateGroup.class})
    private String senderProvinceName;

    @ApiModelProperty(value = "寄件省份id")
    private Integer senderProvinceId;

    @ApiModelProperty(value = "寄件城市名称")
    @Size(max = 30, message = "寄件城市名称不能超过30个字符")
    @NotBlank(message = "寄件城市名称不能为空", groups = {JmsUpdateGroup.class})
    private String senderCityName;

    @ApiModelProperty(value = "寄件城市id")
    private Integer senderCityId;

    @ApiModelProperty(value = "寄件区域名称")
    @Size(max = 30, message = "寄件区域名称不能超过30个字符")
    @NotBlank(message = "寄件区域名称不能为空", groups = {JmsUpdateGroup.class})
    private String senderAreaName;

    @ApiModelProperty(value = "寄件区域Id")
    private Integer senderAreaId;

    @ApiModelProperty(value = "寄件乡镇")
    @Size(max = 200, message = "寄件乡镇不能超过200个字符")
    private String senderTownship;

    @ApiModelProperty(value = "寄件街道")
    @Size(max = 200, message = "不能超过200个字符")
    private String senderStreet;

    @ApiModelProperty(value = "寄件详细地址")
    @Size(max = 200, message = "寄件详细地址不能超过200个字符")
    @NotBlank(message = "寄件详细地址不能为空", groups = {JmsUpdateGroup.class})
    private String senderDetailedAddress;

    @ApiModelProperty(value = "寄件邮编")
    @Size(max = 30, message = "寄件邮编不能超过30个字符")
    private String senderPostalCode;

    @ApiModelProperty(value = "寄件服务方式名称")
    @NotBlank(message = "寄件服务方式名称不能为空", groups = {JmsUpdateGroup.class})
    @Size(max = 30, message = "寄件服务方式名称不能超过30个字符")
    private String sendName;

    @ApiModelProperty(value = "寄件服务方式code")
    @NotBlank(message = "寄件服务方式code不能为空", groups = {JmsUpdateGroup.class})
    @Size(max = 30, message = "寄件服务方式code不能超过30个字符")
    private String sendCode;

    @ApiModelProperty(value = "收件人姓名")
    @Size(max = 100, message = "收件人姓名不能超过100个字符")
    @NotBlank(message = "收件人姓名不能为空", groups = {JmsUpdateGroup.class})
    private String receiverName;

    @ApiModelProperty(value = "收件人公司")
    @Size(max = 100, message = "收件人公司不能超过100个字符")
    private String receiverCompany;

    @ApiModelProperty(value = "收件人手机号")
    @Size(max = 30, message = "收件人手机号不能超过30个字符")
    private String receiverMobilePhone;

    @ApiModelProperty(value = "收件人座机")
    @Size(max = 30, message = "收件人固话不能超过30个字符")
    private String receiverTelphone;

    @ApiModelProperty(value = "收件国家名称")
    @Size(max = 30, message = "收件国家名称不能超过30个字符")
    private String receiverCountryName;

    @ApiModelProperty(value = "收件国家id")
    private Integer receiverCountryId;

    @ApiModelProperty(value = "收件省份名称")
    @Size(max = 30, message = "收件省份名称不能超过30个字符")
    @NotBlank(message = "收件省份名称不能为空", groups = {JmsUpdateGroup.class})
    private String receiverProvinceName;

    @ApiModelProperty(value = "收件省份id")
    private Integer receiverProvinceId;

    @ApiModelProperty(value = "收件城市名称")
    @Size(max = 30, message = "收件城市名称不能超过30个字符")
    @NotBlank(message = "收件城市名称不能为空", groups = {JmsUpdateGroup.class})
    private String receiverCityName;

    @ApiModelProperty(value = "收件城市id")
    private Integer receiverCityId;

    @ApiModelProperty(value = "收件区域名称")
    @Size(max = 30, message = "收件区域名称不能超过30个字符")
    @NotBlank(message = "收件区域名称不能为空", groups = {JmsUpdateGroup.class})
    private String receiverAreaName;

    @ApiModelProperty(value = "收件区域id")
    private Integer receiverAreaId;

    @ApiModelProperty(value = "收件乡镇")
    @Size(max = 200, message = "收件乡镇不能超过200个字符")
    private String receiverTownship;

    @ApiModelProperty(value = "收件街道")
    @Size(max = 200, message = "收件街道不能超过200个字符")
    private String receiverStreet;

    @ApiModelProperty(value = "收件详细地址")
    @Size(max = 200, message = "收件详细地址不能超过200个字符")
    @NotBlank(message = "收件详细地址不能为空", groups = {JmsUpdateGroup.class})
    private String receiverDetailedAddress;

    @ApiModelProperty(value = "收件邮编")
    @Size(max = 30, message = "收件邮编不能超过30个字符")
    private String receiverPostalCode;

    @ApiModelProperty(value = "派件服务方式名称")
    @NotBlank(message = "派件服务方式名称不能为空", groups = {JmsUpdateGroup.class})
    @Size(max = 30, message = "派件服务方式名称不能超过30个字符")
    private String dispatchName;

    @ApiModelProperty(value = "派件服务方式code")
    @NotBlank(message = "派件服务方式code名称不能为空", groups = {JmsUpdateGroup.class})
    @Size(max = 30, message = "派件服务方式code不能超过30个字符")
    private String dispatchCode;

    @ApiModelProperty(value = "收件分拣码")
    @Size(max = 30, message = "收件分拣码不能超过30个字符")
    private String receiverSortingCode;

    @ApiModelProperty(value = "备注")
    @Size(max = 500, message = "备注不能超过500个字符")
    private String remarks;

    @ApiModelProperty(value = "快件类型id")
    private Integer expressTypeId;

    @ApiModelProperty(value = "快件类型code")
    @Size(max = 30, message = "快件类型编码不能超过30个字符")
    @NotBlank(message = "快件类型编码不能为空", groups = {JmsUpdateGroup.class})
    private String expressTypeCode;

    @ApiModelProperty(value = "快件类型名称")
    @Size(max = 30, message = "快件类型名称不能超过30个字符")
    @NotBlank(message = "快件类型名称不能为空", groups = {JmsUpdateGroup.class})
    private String expressTypeName;

    @ApiModelProperty(value = "需要保价1是，0否")
    @Min(value = 0, message = "需要保价只能为0或者1")
    @Max(value = 1, message = "需要保价只能为0或者1")
    @NotNull(message = "是否需要保价不能为空", groups = {JmsUpdateGroup.class})
    private Integer insured;

    @ApiModelProperty(value = "保价金额")
    @Digits(integer = 10, fraction = 2, message = "保价金额超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal declaredValue;

    @ApiModelProperty(value = "保价费")
    @Digits(integer = 10, fraction = 2, message = "保价费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal insuredValue;

    @ApiModelProperty(value = "标准运费")
    @Digits(integer = 10, fraction = 2, message = "标准运费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal standardValue;

    @ApiModelProperty(value = "总费用")
    @Digits(integer = 10, fraction = 2, message = "总费用超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal totalFreight;

    @ApiModelProperty(value = "需要代收货款,1是，0否")
    @Min(value = 0, message = "是否需要代收货款只能为0或者1")
    @Max(value = 1, message = "是否需要代收货款只能为0或者1")
    private Integer codNeed;

    @ApiModelProperty(value = "代收货款金额")
    @Digits(integer = 10, fraction = 2, message = "代收货款金额超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal codMoney;

    @ApiModelProperty(value = "代收货款手续费")
    @Digits(integer = 10, fraction = 2, message = "代收货款手续费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal codFee;

    @ApiModelProperty(value = "包材规格名称")
    @Size(max = 30, message = "包材规格名称不能超过30个字符")
    private String boxStandardName;

    @ApiModelProperty(value = "包材规格id")
    private Long boxStandardId;

    @ApiModelProperty(value = "包材规格code")
    @Size(max = 30, message = "包材规格code名称不能超过30个字符")
    private String boxStandardCode;

    @ApiModelProperty(value = "包材单价")
    @Digits(integer = 10, fraction = 2, message = "包材单价超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal boxPrice;

    @ApiModelProperty(value = "包材数量")
    private Integer boxNumber;

    @ApiModelProperty(value = "代收货款币别名称")
    @Size(max = 32, message = "代收货款币别名称不能超过30个字符")
    private String codCurrencyTypeName;

    @ApiModelProperty(value = "代收货款币别code")
    @Size(max = 32, message = "代收货款币别编码不能超过30个字符")
    private String codCurrencyTypeCode;

    @ApiModelProperty(value = "物品类型名称")
    @Size(max = 30, message = "物品类型名称不能超过30个字符")
    @NotBlank(message = "物品类型名称不能为空", groups = {JmsUpdateGroup.class})
    private String goodsTypeName;

    @ApiModelProperty(value = "物品类型id")
    private Integer goodsTypeId;

    @ApiModelProperty(value = "物品类型code")
    @Size(max = 30, message = "物品类型编码不能超过30个字符")
    @NotBlank(message = "物品类型编码不能为空", groups = {JmsUpdateGroup.class})
    private String goodsTypeCode;

    @ApiModelProperty(value = "物品名称")
    @Size(max = 200, message = "物品名称不能超过200个字符")
    private String goodsName;

    @ApiModelProperty(value = "件数,＞1表示子母件，如果业务上不支持子母件，则前端限制不显示，并默认为1")
    @Min(value = 1, message = "包裹数量不能小于1")
    @Max(value = 127, message = "包裹数量不能超过127")
    private Integer packageNumber;

    @ApiModelProperty(value = "包裹总长,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹总长超出了允许范围(只允许在5位整数和2位小数范围内)")
    @NotNull(message = "包裹总长不能为空", groups = {JmsUpdateGroup.class})
    private BigDecimal packageLength;

    @ApiModelProperty(value = "包裹总宽,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹总宽超出了允许范围(只允许在5位整数和2位小数范围内)")
    @NotNull(message = "包裹总宽不能为空", groups = {JmsUpdateGroup.class})
    private BigDecimal packageWide;

    @ApiModelProperty(value = "包裹总高,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹总高超出了允许范围(只允许在5位整数和2位小数范围内)")
    @NotNull(message = "包裹总高不能为空", groups = {JmsUpdateGroup.class})
    private BigDecimal packageHigh;

    @ApiModelProperty(value = "包裹体积重,单位立方厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹体积重超出了允许范围(只允许在5位整数和2位小数范围内)")
    @NotNull(message = "包裹体积重不能为空", groups = {JmsUpdateGroup.class})
    private BigDecimal packateVolume;

    @ApiModelProperty(value = "包裹计费重量,单位千克")
    @Digits(integer = 5, fraction = 2, message = "包裹计费重量,超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal packageChargeWeight;

    @ApiModelProperty(value = "包裹总重量,单位千克")
    @Digits(integer = 5, fraction = 2, message = "包裹总重量超出了允许范围(只允许在5位整数和2位小数范围内)")
    @DecimalMin(value = "0.01", message = "包裹总重量必须大于或等于0.01")
    private BigDecimal packageTotalWeight;

    @ApiModelProperty(value = "结算方式名称")
    @Size(max = 30, message = "结算方式名称不能超过30个字符")
    @NotBlank(message = "结算方式名称不能为空", groups = {JmsUpdateGroup.class})
    private String paymentModeName;

    @ApiModelProperty(value = "结算方式id")
    private Integer paymentModeId;

    @ApiModelProperty(value = "结算方式code")
    @Size(max = 30, message = "结算方式编码不能超过30个字符")
    @NotBlank(message = "结算方式编码不能为空", groups = {JmsUpdateGroup.class})
    private String paymentModeCode;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    @Size(max = 30, message = "客户名称不能超过30个字符")
    private String customerName;

    @ApiModelProperty(value = "商品信息")
    private List<OmsOrderMemberDTO> items;

    /**
     * 运单号
     */

    @NotBlank(message = "运单号不能为空")
    private String waybillId;

    /**
     * 结算货币类型
     */
    @ApiModelProperty(value = "结算货币类型code")
    @Size(max = 30, message = "结算货币类型编码不能超过30个字符")
    private String spmMoneyType;

    /**
     * 客户下单时间
     */
    @ApiModelProperty(value = "客户下单时间")
    private LocalDateTime customerOrderTime;

    /**
     * 订单录入时间
     */

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime inputTime;

    /**
     * 取消订单原因
     */

    private String cancelReason;

    /**
     * 订单取消时间
     */

    private LocalDateTime cancelTime;

    /**
     * 取件失败时间
     */

    private LocalDateTime pickFailTime;

    /**
     * 取件失败原因
     */

    private String pickFailReason;

    /**
     * 打印次数
     */

    private Integer printsNumber;

    /**
     * 实际取件网点名称
     */

    private String realPickNetworkName;

    /**
     * 实际取件网点code
     */

    private String realPickNetworkCode;

    /**
     * 调度网点时间
     */

    private LocalDateTime dispatchNetworkTime;

    /**
     * 取件网点名称
     */

    private String pickNetworkName;

    /**
     * 取件网点code
     */

    private String pickNetworkCode;

    /**
     * 取件网点id
     */

    private Long pickNetworkId;

    /**
     * 调度业务员时间
     */

    private LocalDateTime dispatchStaffTime;

    /**
     * 取件业务员名称
     */

    private String pickStaffName;

    /**
     * 取件业务员code
     */

    private String pickStaffCode;

    /**
     * 取件时间
     */

    private LocalDateTime pickTime;

    /**
     * 上一次调度网点名称
     */

    private String lastDispatchNetworkName;

    /**
     * 上一次调度网点code
     */

    private String lastDispatchNetworkCode;

    /**
     * 上一次调度网点id
     */

    private Long lastDispatchNetworkId;

    /**
     * 上一次调度网点时间
     */

    private LocalDateTime lastDispatchNetworkTime;

    /**
     * 派件网点id
     */
    private Integer dispatchNetworkId;

    /**
     * 派件网点编码
     */
    private String dispatchNetworkCode;

    /**
     * 派件网点名称
     */
    private String dispatchNetworkName;

    /**
     * 集包地编码
     */
    private String lastCenterCode;

    /**
     * 集包地名称
     */
    private String lastCenterName;


    /**
     * 调度网点原因
     */

    private String dispatchNetworkReason;

    /**
     * 上一次调度业务员名称
     */

    private String lastDispatchStaffName;

    /**
     * 上一次调度业务员code
     */

    private String lastDispatchStaffCode;

    /**
     * 上一次调度业务员时间
     */

    private LocalDateTime lastDispatchStaffTime;

    /**
     * 委托人姓名
     */

    private String assignerName;

    /**
     * 委托人手机号
     */

    private String assignerMobilePhone;

    /**
     * 物品价值
     */

    private BigDecimal goodsValue;

    private String senderEmail;

    private String receiverEmail;

    /**
     * 是否删除:1未删除,2已删除
     */

    private Integer isDelete;


    /**
     * 更新人
     */

    private Integer updateBy;

    /**
     * 更新时间
     */

    private LocalDateTime updateTime;

    /**
     * 更新人
     */

    private String updateByName;

    /**
     * 包材费
     */

    private BigDecimal packageCost;

    /**
     * 物品图片链接，运单表有的字段
     */
    @Size(max = 500, message = "物品图片链接不能超过500个字符")
    private String goodsUrl;

    /**
     * 身份证号码，运单表有的字段
     */
    @Size(max = 18, message = "身份证号码不能超过18个字符")
    private String idNo;

    /**
     * 三段码
     **/
    private String terminalDispatchCode;

    /**
     * 二段码命中原因
     */
    private String secondCodeHitDesc;

    /**
     * 签回单 0否   1是
     */
    private Integer signReceipt;

    /**
     * 操作来源
     */
    private String operateSource;

    private OmsOrderOriginalAddress omsOrderOriginalAddress;
}
