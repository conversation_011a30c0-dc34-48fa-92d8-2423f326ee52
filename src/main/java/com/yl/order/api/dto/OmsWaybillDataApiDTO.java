package com.yl.order.api.dto;






import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OmsWaybill请求对象", description = "运单表请求实体")
public class OmsWaybillDataApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(value = "运单号")
    private String waybillNo;

    @ApiModelProperty(value = "是否纸质运单,1是，0否")
    @Min(value = 0, message = "是否纸质运单只能为0或者1")
    @Max(value = 1, message = "是否纸质运单只能为0或者1")
    @NotNull(message = "是否纸质运单不能为空")
    private Integer isPaper;

    @ApiModelProperty(value = "寄件时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "寄件时间不能为空")
    private LocalDateTime deliveryTime;

    @ApiModelProperty(value = "揽件业务员id")
    private Integer collectStaffId;

    @ApiModelProperty(value = "揽件业务员code")
    @NotBlank(message = "取件员编码不能为空")
    @Size(max = 30, message = "取件员编码不能超过30个字符")
    private String collectStaffCode;

    @ApiModelProperty(value = "揽件业务员名称")
    @NotBlank(message = "取件员名称不能为空")
    @Size(max = 30, message = "取件员名称不能超过30个字符")
    private String collectStaffName;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "客户编号code")
    @Size(max = 30, message = "客户编号不能超过30个字符")
    private String customerCode;

    @ApiModelProperty(value = "客户编号名称")
    @Size(max = 30, message = "客户名称不能超过30个字符")
    private String customerName;

    @ApiModelProperty(value = "产品类型id")
    private Integer expressTypeId;

    @ApiModelProperty(value = "产品类型code")
    @Size(max = 30, message = "产品类型编码不能超过30个字符")
    @NotBlank(message = "产品类型编码不能为空")
    private String expressTypeCode;

    @ApiModelProperty(value = "产品类型名称")
    @Size(max = 30, message = "产品类型名称不能超过30个字符")
    @NotBlank(message = "产品类型名称不能为空")
    private String expressTypeName;

    @ApiModelProperty(value = "派件方式code")
    @NotBlank(message = "派件方式编码不能为空")
    @Size(max = 30, message = "派件方式编码不能超过30个字符")
    private String dispatchCode;

    @ApiModelProperty(value = "派件方式名称")
    @NotBlank(message = "派件方式名称不能为空")
    @Size(max = 30, message = "派件方式名称不能超过30个字符")
    private String dispatchName;

    @ApiModelProperty(value = "寄件人姓名")
    @Size(max = 60, message = "寄件人姓名不能超过60个字符")
    @NotBlank(message = "寄件人姓名不能为空")
    private String senderName;

    @ApiModelProperty(value = "寄件人手机号")
    @Size(max = 15, message = "寄件人手机号不能超过15个字符")
    private String senderMobilePhone;

    @ApiModelProperty(value = "寄件人座机")
    @Size(max = 15, message = "寄件人座机不能超过15个字符")
    private String senderTelphone;

    @ApiModelProperty(value = "寄件邮编")
    @Size(max = 30, message = "寄件邮编不能超过30个字符")
    private String senderPostalCode;

    @ApiModelProperty(value = "寄件国家Id")
    private Integer senderCountryId;

    @ApiModelProperty(value = "寄件国家名称")
    @Size(max = 30, message = "寄件国家名称不能超过30个字符")
    private String senderCountryName;

    @ApiModelProperty(value = "寄件省份id")
    @NotNull(message = "寄件省份id不能为空")
    private Integer senderProvinceId;

    @ApiModelProperty(value = "寄件省份名称")
    @Size(max = 30, message = "寄件省份名称不能超过30个字符")
    @NotBlank(message = "寄件省份名称不能为空")
    private String senderProvinceName;

    @ApiModelProperty(value = "寄件城市id")
    @NotNull(message = "寄件城市id不能为空")
    private Integer senderCityId;

    @ApiModelProperty(value = "寄件城市名称")
    @Size(max = 30, message = "寄件城市名称不能超过30个字符")
    @NotBlank(message = "寄件城市名称不能为空")
    private String senderCityName;

    @ApiModelProperty(value = "寄件区县id")
    @NotNull(message = "寄件区县id不能为空")
    private Integer senderAreaId;

    @ApiModelProperty(value = "寄件区县名称")
    @Size(max = 30, message = "寄件区县名称不能超过30个字符")
    @NotBlank(message = "寄件区县名称不能为空")
    private String senderAreaName;

    @ApiModelProperty(value = "寄件乡镇")
    @Size(max = 200, message = "寄件乡镇不能超过200个字符")
    private String senderTownship;

    @ApiModelProperty(value = "寄件街道")
    @Size(max = 200, message = "不能超过200个字符")
    private String senderStreet;

    @ApiModelProperty(value = "寄件详细地址")
    @Size(max = 200, message = "寄件详细地址不能超过200个字符")
    @NotBlank(message = "寄件详细地址不能为空")
    private String senderDetailedAddress;

    @ApiModelProperty(value = "始发地id")
    @NotNull(message = "始发地id不能为空")
    private Integer originId;

    @ApiModelProperty(value = "始发地code")
    @Size(max = 30, message = "始发地code不能超过30个字符")
    @NotBlank(message = "始发地code不能为空")
    private String originCode;

    @ApiModelProperty(value = "始发地名称")
    @Size(max = 30, message = "始发地名称不能超过30个字符")
    @NotBlank(message = "始发地名称不能为空")
    private String originName;

    @ApiModelProperty(value = "是否实名制,1是，0否")
    @Min(value = 0, message = "是否实名制只能为0或者1")
    @Max(value = 1, message = "是否实名制只能为0或者1")
    @NotNull(message = "是否实名制不能为空")
    private Integer isRealName;

    @ApiModelProperty(value = "实名姓名")
    @Size(max = 80, message = "实名姓名不能超过80个字符")
    private String realName;

    @ApiModelProperty(value = "证件类型")
    private Integer idNoType;

    @ApiModelProperty(value = "证件号码")
    @Size(max = 80, message = "证件号码不能超过80个字符")
    private String idNo;

    @ApiModelProperty(value = "性别")
    private Integer sex;

    @ApiModelProperty(value = "收件人姓名")
    @Size(max = 60, message = "收件人姓名不能超过60个字符")
    @NotBlank(message = "收件人姓名不能为空")
    private String receiverName;

    @ApiModelProperty(value = "收件人手机号")
    @Size(max = 15, message = "收件人手机号不能超过15个字符")
    private String receiverMobilePhone;

    @ApiModelProperty(value = "收件人座机")
    @Size(max = 15, message = "收件人座机不能超过15个字符")
    private String receiverTelphone;

    @ApiModelProperty(value = "收件邮编")
    @Size(max = 30, message = "收件邮编不能超过30个字符")
    private String receiverPostalCode;

    @ApiModelProperty(value = "收件国家id")
    private Integer receiverCountryId;

    @ApiModelProperty(value = "收件国家名称")
    @Size(max = 30, message = "收件国家名称不能超过30个字符")
    private String receiverCountryName;

    @ApiModelProperty(value = "收件省份id")
    @NotNull(message = "收件省份id不能为空")
    private Integer receiverProvinceId;

    @ApiModelProperty(value = "收件省份名称")
    @Size(max = 30, message = "收件省份名称不能超过30个字符")
    @NotBlank(message = "收件省份名称不能为空")
    private String receiverProvinceName;

    @ApiModelProperty(value = "收件城市id")
    @NotNull(message = "收件城市id不能为空")
    private Integer receiverCityId;

    @ApiModelProperty(value = "收件城市名称")
    @Size(max = 30, message = "收件城市名称不能超过30个字符")
    @NotBlank(message = "收件城市名称不能为空")
    private String receiverCityName;

    @ApiModelProperty(value = "收件区县id")
    @NotNull(message = "收件区县id不能为空")
    private Integer receiverAreaId;

    @ApiModelProperty(value = "收件区县名称")
    @Size(max = 30, message = "收件区县名称不能超过30个字符")
    @NotBlank(message = "收件区县名称不能为空")
    private String receiverAreaName;

    @ApiModelProperty(value = "收件乡镇")
    @Size(max = 200, message = "收件乡镇不能超过200个字符")
    private String receiverTownship;

    @ApiModelProperty(value = "收件街道")
    @Size(max = 200, message = "收件街道不能超过200个字符")
    private String receiverStreet;

    @ApiModelProperty(value = "收件详细地址")
    @Size(max = 200, message = "收件详细地址不能超过200个字符")
    @NotBlank(message = "收件详细地址不能为空")
    private String receiverDetailedAddress;

    @ApiModelProperty(value = "目的地id")
    @NotNull(message = "目的地id不能为空")
    private Integer destinationId;

    @ApiModelProperty(value = "目的地code")
    @Size(max = 30, message = "目的地code不能超过30个字符")
    @NotBlank(message = "目的地code不能为空")
    private String destinationCode;

    @ApiModelProperty(value = "目的地名称")
    @Size(max = 30, message = "目的地名称不能超过30个字符")
    @NotBlank(message = "目的地名称不能为空")
    private String destinationName;

    @ApiModelProperty(value = "物品类型id")
    private Integer goodsTypeId;

    @ApiModelProperty(value = "物品类型code")
    @Size(max = 30, message = "物品类型编码不能超过30个字符")
    @NotBlank(message = "物品类型编码不能为空")
    private String goodsTypeCode;

    @ApiModelProperty(value = "物品类型名称")
    @Size(max = 30, message = "物品类型名称不能超过30个字符")
    @NotBlank(message = "物品类型名称不能为空")
    private String goodsTypeName;

    @ApiModelProperty(value = "物品名称")
    @Size(max = 200, message = "物品名称不能超过200个字符")
    private String goodsName;

    @ApiModelProperty(value = "件数,＞1表示子母件，如果业务上不支持子母件，则前端限制不显示，并默认为1")
    @Min(value = 1, message = "包裹数量不能小于1")
    @Max(value = 99, message = "包裹数量不能超过99")
    private Integer packageNumber;

    @ApiModelProperty(value = "包裹总长,单位厘米")
    @Digits(integer = 3, fraction = 2, message = "包裹总长超出了允许范围(只允许在3位整数和2位小数范围内)")
    private BigDecimal packageLength;

    @ApiModelProperty(value = "包裹总宽,单位厘米")
    @Digits(integer = 3, fraction = 2, message = "包裹总宽超出了允许范围(只允许在3位整数和2位小数范围内)")
    private BigDecimal packageWide;

    @ApiModelProperty(value = "包裹总高,单位厘米")
    @Digits(integer = 3, fraction = 2, message = "包裹总高超出了允许范围(只允许在3位整数和2位小数范围内)")
    private BigDecimal packageHigh;

    @ApiModelProperty(value = "包裹计费重量,单位千克")
    @Digits(integer = 5, fraction = 2, message = "包裹计费重量,超出了允许范围(只允许在5位整数和2位小数范围内)")
    @NotNull(message = "包裹计费重量不能为空")
    private BigDecimal packageChargeWeight;

    @ApiModelProperty(value = "包裹总重量,单位千克")
    @Digits(integer = 5, fraction = 2, message = "包裹总重量超出了允许范围(只允许在5位整数和2位小数范围内)")
    @DecimalMin(value = "0.01", message = "包裹总重量必须大于或等于0.01")
    private BigDecimal packageTotalWeight;

    @ApiModelProperty(value = "包裹总体积,单位立方厘米")
    private BigDecimal packageTotalVolume;

    @ApiModelProperty(value = "包裹体积重,单位立方厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹体积重超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal packageVolume;

    @ApiModelProperty(value = "包材规格id")
    private Long boxStandardId;

    @ApiModelProperty(value = "包材规格code")
    @Size(max = 30, message = "包材规格code不能超过30个字符")
    private String boxStandardCode;

    @ApiModelProperty(value = "包材规格名称")
    @Size(max = 30, message = "包材规格名称不能超过30个字符")
    private String boxStandardName;

    @ApiModelProperty(value = "包材数量")
    @Max(value = 999, message = "包材数量最大不超过999")
    private Integer boxNumber;

    @ApiModelProperty(value = "箱子价格")
    @Digits(integer = 10, fraction = 2, message = "箱子价格超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal boxPrice;

    @ApiModelProperty(value = "结算方式id")
    @NotNull(message = "结算方式id不能为空")
    private Integer settlementId;

    @ApiModelProperty(value = "结算方式编码")
    @Size(max = 30, message = "结算方式编码不能超过30个字符")
    @NotBlank(message = "结算方式编码不能为空")
    private String settlementCode;

    @ApiModelProperty(value = "结算方式名称")
    @Size(max = 30, message = "结算方式名称不能超过30个字符")
    @NotBlank(message = "结算方式名称不能为空")
    private String settlementName;

    @ApiModelProperty(value = "发票编号")
    @Size(max = 50, message = "发票编号不能超过50个字符")
    private String invoiceNo;

    @ApiModelProperty(value = "是否需要代收货款,1是，0否")
    @Min(value = 0, message = "是否需要代收货款只能为0或者1")
    @Max(value = 1, message = "是否需要代收货款只能为0或者1")
    @NotNull(message = "是否需要代收货款不能为空")
    private Integer codNeed;

    @ApiModelProperty(value = "代收货款金额")
    @Digits(integer = 4, fraction = 2, message = "代收货款金额超出了允许范围(只允许在4位整数和2位小数范围内)")
    private BigDecimal codMoney;

    @ApiModelProperty(value = "代收货款手续费")
    @Digits(integer = 10, fraction = 2, message = "代收货款手续费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal codFee;

    @ApiModelProperty(value = "COD收款标识,1是，0否")
    @Min(value = 0, message = "COD收款标识只能为0或者1")
    @Max(value = 1, message = "COD收款标识只能为0或者1")
    private Integer isCodReceive;

    @ApiModelProperty(value = "货币币别编码")
    @Size(max = 30, message = "货币币别编码不能超过30个字符")
    private String currencyCode;

    @ApiModelProperty(value = "货币币别名称")
    @Size(max = 30, message = "货币币别名称不能超过30个字符")
    private String currencyName;

    @ApiModelProperty(value = "是否需要保价,1是，0否")
    @Min(value = 0, message = "是否需要保价只能为0或者1")
    @Max(value = 1, message = "是否需要保价只能为0或者1")
    @NotNull(message = "是否需要保价不能为空")
    private Integer insured;

    @ApiModelProperty(value = "保价金额")
    @Digits(integer = 5, fraction = 2, message = "保价金额超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal insuredAmount;

    @ApiModelProperty(value = "保价费")
    @Digits(integer = 10, fraction = 2, message = "保价费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal insuredFee;

    @ApiModelProperty(value = "是否需要需要签回单,1是，0否")
    @Min(value = 0, message = "是否需要需要签回单只能为0或者1")
    @Max(value = 1, message = "是否需要需要签回单只能为0或者1")
    @NotNull(message = "是否需要需要签回单不能为空")
    private Integer isNeedReceipt;

    @ApiModelProperty(value = "回单编号")
    @Size(max = 60, message = "回单编号不能超过60个字符")
    private String receiptNo;

    @ApiModelProperty(value = "优惠券编号")
    @Size(max = 50, message = "优惠券编号不能超过50个字符")
    private String couponCode;

    @ApiModelProperty(value = "优惠金额")
    @Digits(integer = 10, fraction = 2, message = "保价费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal couponAmount;

    @ApiModelProperty(value = "包材费")
    @Digits(integer = 10, fraction = 2, message = "包材费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal packageCost;

    @ApiModelProperty(value = "运费")
    @Digits(integer = 10, fraction = 2, message = "运费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal freight;

    @ApiModelProperty(value = "税金")
    @Digits(integer = 10, fraction = 2, message = "税金超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal tax;

    @ApiModelProperty(value = "手工运费")
    @Digits(integer = 5, fraction = 2, message = "手工运费超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal handicraftFee;

    @ApiModelProperty(value = "其他费")
    @Digits(integer = 5, fraction = 2, message = "其他费超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal otherFee;

    @ApiModelProperty(value = "税后总运费")
    @Digits(integer = 10, fraction = 2, message = "税后总运费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal afterTaxFreight;

    @ApiModelProperty(value = "总运费")
    @Digits(integer = 10, fraction = 2, message = "总运费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal totalFreight;

    @ApiModelProperty(value = "备注")
    @Size(max = 100, message = "备注不能超过100个字符")
    private String remarks;

    @ApiModelProperty(value = "运单来源code")
    @Size(max = 30, message = "运单来源code不能超过30个字符")
    private String waybillSourceCode;

    @ApiModelProperty(value = "运单来源名称")
    @Size(max = 30, message = "运单来源名称不能超过30个字符")
    private String waybillSourceName;

    @ApiModelProperty(value = "异常原因")
    private String exceptionReason;

    @ApiModelProperty(value = "问题件标识：1是，0否'")
    private Integer isAbnormal;

    @ApiModelProperty(value = "录入人id")
    private Integer inputStaffId;

    private String inputStaffCode;

    private String inputStaffName;

    @ApiModelProperty(value = "录入网点id")
    private Integer inputNetworkId;

    private String inputNetworkCode;

    private String inputNetworkName;

    @Digits(integer = 12, fraction = 2, message = "到付运费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal receivePayFee;

    @ApiModelProperty(value = "结算重量,单位千克")
    @Digits(integer = 10, fraction = 2, message = "结算重量超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal settlementWeight;

    @NotNull(message = "最后更新人网点Id不能为空")
    private Integer lastUpdateNetworkId;

    @NotBlank(message = "最后更新人网点编号不能为空")
    private String lastUpdateNetworkCode;

    @NotBlank(message = "最后更新人网点名称不能为空")
    private String lastUpdateNetworkName;

    @NotBlank(message = "最后更新人编号不能为空")
    private String lastUpdateStaffCode;

    @NotBlank(message = "最后更新人名称不能为空")
    private String lastUpdateStaffName;

    /**
     * 最后更新时间
     */

    private LocalDateTime lastUpdateTime;

}
