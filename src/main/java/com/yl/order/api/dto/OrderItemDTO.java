package com.yl.order.api.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description: TODO
 * @Author: mac
 * @Date: 2021/6/29 4:08 下午
 */
@Data
public class OrderItemDTO  implements Serializable {

    /**
     * 订单编号,客户/业务员取单号的话,一定有订单编号
     */
    private String orderId;

    /**
     * 件数
     */
    @NotNull(message = "件数不能为空")
    private Integer item;
}
