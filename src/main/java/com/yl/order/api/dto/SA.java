package com.yl.order.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiong<PERSON>bin
 * @create: 2020-07-24 15:29
 */
@Data
public class SA implements Serializable {
    /**
     * 员工编号
     */
    private String code;
    /**
     * 员工姓名
     */
    private String name;
    /**
     * 员工所属网点编号
     */
    private String siteCode;
    /**
     * 员工所属网点姓名编号
     */
    private String siteName;
    /**
     * 机构级别：22总部,334代理区,335中心,336一级网点，337二级网点
     */
    private Integer institutionalLevelId;
}
