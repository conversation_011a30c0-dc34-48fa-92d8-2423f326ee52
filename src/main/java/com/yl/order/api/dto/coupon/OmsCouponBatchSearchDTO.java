package com.yl.order.api.dto.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * YL_OMS_COUPON_BATCH
 * <AUTHOR>
@ApiModel(value="com.yl.order.api.dto.coupon.OmsCouponBatchSearchDTO")
@Data
public class OmsCouponBatchSearchDTO implements Serializable {

    /**
     * 优惠券批次编码/优惠券名称
     */
    @ApiModelProperty(value="优惠券批次编码/优惠券名称")
    private String keywords;

    private static final long serialVersionUID = 1L;
}