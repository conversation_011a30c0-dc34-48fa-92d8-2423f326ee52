package com.yl.order.api.dto.coupon;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yl.common.base.model.dto.JwtUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * YL_OMS_COUPON_TYPE
 * <AUTHOR>
@ApiModel(value="com.yl.looseorder.api.dto.coupon.OmsCouponTypeStatusDTO优惠券类型")
@Data
public class OmsCouponBatchStatusDTO implements Serializable {


    /**
     * 优惠券类型ID
     */
    @ApiModelProperty(value="优惠券批次ID")
    @NotNull(message = "优惠券批次ID不为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;


    /**
     * 状态
     */
    @ApiModelProperty(value="批次状态,",example = "1、待审核 2、启用中 3、已下架 4、审核未通过 5、无库存 6、已过期")
    @NotNull(message = "状态不为空")
    private Short batchStatus;

    /**
     * 审核不通过原因
     */
    @ApiModelProperty(value="审核不通过原因")
    private String auditReason;

    @ApiModelProperty(value="用户")
    private JwtUserDTO jwtUser;

    private static final long serialVersionUID = 1L;
}