package com.yl.order.api.dto.dispatch;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019
 *
 * <AUTHOR>
 * @date ：Created in 2021-01-26 14:46
 * @description：订单调度监控查询DTO
 * @modified By：
 * @version: $version$
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DispatchMonitorQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单号")
    private Long orderId;

    @ApiModelProperty(value = "运单号")
    private String waybillNo;

    @ApiModelProperty(value = "订单录入开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startInputTime;

    @ApiModelProperty(value = "订单录入结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endInputTime;

    @ApiModelProperty(value = "代理区/加盟商/网点")
    private String searchNetworkCode;

    @ApiModelProperty(value = "是否代理区")
    private Boolean isProxyArea = false;

    @ApiModelProperty(value = "订单来源")
    private List<String> orderSourceCodes;

    @ApiModelProperty(value = "调度类型")
    private Integer dispatchType;

    @ApiModelProperty(value = "自动/手动")
    private Integer operateType;

    @ApiModelProperty(value = "是否超时")
    private Integer overTimeSign;

    @ApiModelProperty(value = "是否错派")
    private Integer wrongDispatchSign;

    @ApiModelProperty(value = "当前页码数")
    private Long current;

    @ApiModelProperty(value = "每页显示记录数")
    private Long size;

    @ApiModelProperty(value = "代理区编码（用户权限）")
    private String proxyAreaCode;

    @ApiModelProperty(value = "网点编码（用户权限）")
    private String netWorkCode;

    @ApiModelProperty(value = "当前角色级别ID")
    private Integer institutionalLevelId;
}