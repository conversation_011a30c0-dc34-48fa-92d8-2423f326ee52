package com.yl.order.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title BasePO
 * @Package com.yl.order.lmdm.base.entity
 * @Description
 * @date 2021/6/8 9:38 上午
 */
@Data
public class BasePO {

    /**
     * id
     */
    @TableField(value = "id")
    @TableId(type = IdType.INPUT)
    private Long id;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人编码
     */
    @TableField(value = "update_user")
    private String updateUserCode;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建人编码
     */
    @TableField(value = "create_user")
    private String createUserCode;

    /**
     * 是否删除 1 否 ，0 是
     */
    @TableField(value = "is_delete")
    private Integer isDelete;

}