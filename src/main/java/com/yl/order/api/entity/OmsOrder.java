package com.yl.order.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_OMS_OMS_ORDER")
public class OmsOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 客户订单编号
     */

    private String customerOrderId;

    /**
     * 订单来源名称
     */

    private String orderSourceName;

    /**
     * 订单来源code
     */

    private String orderSourceCode;

    /**
     * 订单类型code
     */

    private Integer orderTypeCode;

    /**
     * 运单号
     */

    private String waybillId;

    /**
     * 寄件人姓名
     */

    private String senderName;

    /**
     * 寄件人公司
     */

    private String senderCompany;

    /**
     * 寄件人手机号
     */

    private String senderMobilePhone;

    /**
     * 寄件人座机
     */

    private String senderTelphone;

    /**
     * 寄件国家名称
     */

    private String senderCountryName;

    /**
     * 寄件国家Id
     */

    private Integer senderCountryId;

    /**
     * 寄件省份名称
     */

    private String senderProvinceName;

    /**
     * 寄件省份id
     */

    private Integer senderProvinceId;

    /**
     * 寄件城市名称
     */

    private String senderCityName;

    /**
     * 寄件城市id
     */

    private Integer senderCityId;

    /**
     * 寄件区域名称
     */

    private String senderAreaName;

    /**
     * 寄件区域Id
     */

    private Integer senderAreaId;

    /**
     * 寄件乡镇
     */

    private String senderTownship;

    /**
     * 寄件乡镇
     */

    private Integer senderTownshipId;

    /**
     * 寄件街道
     */

    private String senderStreet;

    /**
     * 寄件详细地址
     */

    private String senderDetailedAddress;

    /**
     * 寄件邮编
     */

    private String senderPostalCode;

    /**
     * 收件人姓名
     */

    private String receiverName;

    /**
     * 收件人公司
     */

    private String receiverCompany;

    /**
     * 收件人手机号
     */

    private String receiverMobilePhone;

    /**
     * 收件人座机
     */

    private String receiverTelphone;

    /**
     * 收件国家名称
     */

    private String receiverCountryName;

    /**
     * 收件国家id
     */

    private Integer receiverCountryId;

    /**
     * 收件省份名称
     */

    private String receiverProvinceName;

    /**
     * 收件省份id
     */

    private Integer receiverProvinceId;

    /**
     * 收件城市名称
     */

    private String receiverCityName;

    /**
     * 收件城市id
     */

    private Integer receiverCityId;

    /**
     * 收件区域名称
     */

    private String receiverAreaName;

    /**
     * 收件区域id
     */

    private Integer receiverAreaId;

    /**
     * 收件乡镇
     */

    private String receiverTownship;

    /**
     * 收件乡镇
     */

    private Integer receiverTownshipId;

    /**
     * 收件街道
     */

    private String receiverStreet;

    /**
     * 收件详细地址
     */

    private String receiverDetailedAddress;

    /**
     * 收件邮编
     */

    private String receiverPostalCode;

    /**
     * 收件分拣码
     */

    private String receiverSortingCode;

    /**
     * 备注
     */

    private String remarks;

    /**
     * 快件类型code
     */

    private String expressTypeCode;

    /**
     * 快件类型名称
     */

    private String expressTypeName;

    /**
     * 需要保价1是，0否
     */

    private Integer insured;

    /**
     * 保价金额
     */

    private BigDecimal declaredValue;

    /**
     * 保价费
     */

    private BigDecimal insuredValue;

    /**
     * 需要代收货款,1是，0否
     */

    private Integer codNeed;

    /**
     * 代收货款金额
     */

    private BigDecimal codMoney;

    /**
     * 代收货款手续费
     */

    private BigDecimal codFee;

    /**
     * 代收货款币别名称
     */

    private String codCurrencyTypeName;

    /**
     * 代收货款币别code
     */

    private String codCurrencyTypeCode;

    /**
     * 物品类型名称
     */

    private String goodsTypeName;

    /**
     * 物品类型id
     */

    private Integer goodsTypeId;

    /**
     * 物品类型code
     */

    private String goodsTypeCode;

    /**
     * 物品名称
     */

    private String goodsName;

    /**
     * 件数,＞1表示子母件，如果业务上不支持子母件，则前端限制不显示，并默认为1
     */

    private Integer packageNumber;

    /**
     * 包裹总长,单位厘米
     */

    private BigDecimal packageLength;

    /**
     * 包裹总宽,单位厘米
     */

    private BigDecimal packageWide;

    /**
     * 包裹总高,单位厘米
     */

    private BigDecimal packageHigh;

    /**
     * 包裹体积重,单位立方厘米
     */

    private BigDecimal packateVolume;

    /**
     * 包装类型
     */
    private String packageType;

    /**
     * 包裹计费重量,单位千克
     */

    private BigDecimal packageChargeWeight;

    /**
     * 下单时的包裹计费重量,单位千克
     */

    private BigDecimal initPackageChargeWeight;

    /**
     * 包裹总重量,单位千克
     */

    private BigDecimal packageTotalWeight;

    /**
     * 包材规格
     */

    private Long boxStandardId;

    /**
     * 包材规格编码
     */

    private String boxStandardCode;

    /**
     * 包材规格名称
     */

    private String boxStandardName;

    /**
     * 包材单价
     */

    private BigDecimal boxPrice;

    /**
     * 包材数量
     */

    private Integer boxNumber;

    /**
     * 标准运费
     */

    private BigDecimal standardValue;

    /**
     * 总费用
     */

    private BigDecimal totalFreight;

    /**
     * 结算方式名称
     */

    private String paymentModeName;

    /**
     * 结算方式code
     */

    private String paymentModeCode;

    /**
     * 结算方式id
     */
    private Integer paymentModeId;


    /**
     * 支付方式名称
     */

    private String paidModeName;

    /**
     * 支付方式code
     */

    private String paidModeCode;

    /**
     * 客户id
     */

    private Integer customerId;

    /**
     * 客户编号code
     */

    private String customerCode;

    /**
     * 客户编号名称
     */

    private String customerName;

    /**
     * 结算货币类型
     */

    private String spmMoneyType;

    /**
     * 寄件服务方式名称
     */

    private String sendName;

    private String sendCode;

    /**
     * 派件服务方式名称
     */

    private String dispatchName;

    /**
     * 派件服务方式code
     */

    private String dispatchCode;

    /**
     * 客户下单时间
     */

    private LocalDateTime customerOrderTime;

    /**
     * 订单录入时间
     */

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime inputTime;

    /**
     * 始发地id
     */

    private Integer originId;

    /**
     * 始发地code
     */

    private String originCode;

    /**
     * 始发地名字
     */

    private String originName;

    /**
     * 目的地id
     */

    private Integer destinationId;

    /**
     * 目的地code
     */

    private String destinationCode;

    /**
     * 目的地名字
     */

    private String destinationName;

    /**
     * 订单状态code
     */

    private Integer orderStatusCode;

    /**
     * 取消订单原因code
     */
    private Integer cancelReasonCode;

    /**
     * 取消订单原因
     */

    private String cancelReason;

    /**
     * 订单取消时间
     */

    private LocalDateTime cancelTime;

    /**
     * 取件失败时间
     */

    private LocalDateTime pickFailTime;

    /**
     * 取件失败原因
     */

    private String pickFailReason;

    /**
     * 打印次数
     */

    private Integer printsNumber = 0;

    /**
     * 最佳取件开始时间
     */

    private LocalDateTime bestPickTimeStart;

    /**
     * 最佳取件结束时间
     */

    private LocalDateTime bestPickTimeEnd;

    /**
     * 实际取件网点名称
     */

    private String realPickNetworkName;

    /**
     * 实际取件网点code
     */

    private String realPickNetworkCode;

    /**
     * 调度网点时间
     */

    private LocalDateTime dispatchNetworkTime;

    /**
     * 取件网点名称
     */

    private String pickNetworkName;

    /**
     * 取件网点code
     */

    private String pickNetworkCode;

    /**
     * 取件网点id
     */
    private Long pickNetworkId;


    /**
     * 调度业务员时间
     */

    private LocalDateTime dispatchStaffTime;

    /**
     * 取件业务员名称
     */

    private String pickStaffName;

    /**
     * 取件业务员code
     */

    private String pickStaffCode;

    /**
     * 取件时间
     */

    private LocalDateTime pickTime;

    /**
     * 调度网点原因
     */

    private String dispatchNetworkReason;

    /**
     * 上一次调度业务员时间
     */

    private LocalDateTime lastDispatchStaffTime;

    /**
     * 物品价值
     */

    private BigDecimal goodsValue;

    private String senderEmail;

    private String receiverEmail;

    /**
     * 是否删除:1未删除,2已删除
     */

    private Integer isDelete;


    /**
     * 录入人ID
     */

    private Integer createBy;

    /**
     * 录入人编码
     */

    private String createByCode;

    /**
     * 录入人名称
     */

    private String createByName;

    /**
     * 更新人
     */

    private Integer updateBy;

    /**
     * 更新人
     */

    private String updateByName;

    /**
     * 更新时间
     */

    private LocalDateTime updateTime = LocalDateTime.now();

    /**
     * 三段码
     **/

    private String terminalDispatchCode;

    /**
     * 打回调度订单次数
     */

    private Integer backDispatchNetworkNumber;

    /**
     * 打回调度订单原因
     */

    private String backDispatchNetworkReason;

    /**
     * 打回调度订单时间
     */

    private LocalDateTime backDispatchNetworkTime;

    /**
     * 结算重量
     */

    private BigDecimal settlementWeight;

    private Integer memberId;

    /**
     * 注意：与上一次调派网点(lastDispatchNetworkId)要区分开
     */

    private Integer dispatchNetworkId;

    /**
     * 是否需要调度 1需要 2不需要
     */
    private Integer needDispatch;

    /**
     * 代理区code
     */
    private String proxyAreaCode;

    /**
     * 代理区name
     */
    private String proxyAreaName;

    /**
     * 调度代理区时间
     */
    private LocalDateTime dispatchProxyAreaTime;

    /**
     * 取消订单说明
     */
    private String cancelExplain;

    /**
     * 寄/收件人信息数据是否明文
     */
    private Boolean isPlaintext;

    /**
     * 实名姓名
     */
    private String realName;

    /**
     * 身份证号码
     */
    private String idNo;

    /**
     * 是否实名
     */
    private Integer isRealName;

    /**
     * 证件类型 DocumentTypeEnum
     */
    private Integer idNoType;

    /**
     * 1男,0女
     */
    private Integer sex;

    /**
     * 订单异常信息
     */
    private String exceptionMsg;

    /**
     * 客户运单号
     */

    private String customerWaybillNo;


    /**
     * 包裹体积 单位立方厘米
     */
    private BigDecimal packageVolume;

    /**
     * 是否是商务件 1是 2不是
     */
    private Integer isBusiness;


    /**
     * 云打印状态CODE
     */
    private String printStatusCode;
    /**
     * 二段码命中原因
     */
    private String secondCodeHitDesc;
    /**
     * 派件网点编码
     */
    private String dispatchNetworkCode;

    /**
     * 派件网点名称
     */
    private String dispatchNetworkName;

    /**
     * 是否允许被极兔取消#1,不允许;0,允许
     */
    private Integer isCanCancel;

    /**
     * 集包地编码
     */
    private String lastCenterCode;

    /**
     * 集包地名称
     */
    private String lastCenterName;

    /**
     * 签回单 0否   1是
     */
    private Integer signReceipt;

    /**
     * 回单费用SIGN_SMS_NOTIFY
     */
    private BigDecimal receiptFreight;

    /**
     * 回单类型（1：纸质回单 2：电子回单）
     */
    private Integer receiptType;

    /**
     * 是否转寄
     */
    private Integer isTransfer;

    /**
     * 寄件人手机号码是否明文 true:明文  false:脱敏
     */
    @TableField(exist = false)
    private Boolean isSenderClearText;

    /**
     * 收件人手机号码是否明文 true:明文  false:脱敏
     */
    @TableField(exist = false)
    private Boolean isReceiverClearText;

    /**
     * 隐私标记; 1:是, 0:否
     */
    private Integer isPrivacy;

    /**
     * 子运单号
     */
    @TableField(exist = false)
    private String subWaybillIds;

    /**
     * 签回单单号
     */
    private String receiptWaybillNo;

    /**
     * 总体积
     */
    private BigDecimal totalPackageVolume;

    @ApiModelProperty(value = "取件失败原因code")
    private String pickFailReasonCode;

    @ApiModelProperty(value = "取件失败网点id")
    private Long pickFailNetworkId;
    @ApiModelProperty(value = "取件失败网点code")
    private String pickFailNetworkCode;
    @ApiModelProperty(value = "取件失败网点name")
    private String pickFailNetworkName;
    @ApiModelProperty(value = "取件失败业务员id")
    private Integer pickFailStaffId;
    @ApiModelProperty(value = "取件失败业务员code")
    private String pickFailStaffCode;
    @ApiModelProperty(value = "取件失败业务员name")
    private String pickFailStaffName;
    /**
     * 预估时效
     */
    private String estimatedTime;

    /**
     * 是否易碎
     */
    @TableField(exist = false)
    private Integer isFragile;

    // 代收点代收网点编码
    private String agentCollectPointCode;

    private String agentCollectPointName;

    // 业务类型
    private String businessType;

}

