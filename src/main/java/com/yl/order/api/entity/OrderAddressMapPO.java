package com.yl.order.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title OrderAddressMapPO
 * @Package com.yl.order.lmdm.po
 * @Description 订单地址映射管理PO 平台多个地址映射对应我们一个地址
 * @date 2021/6/8 9:48 上午
 */
@Data
@TableName("YL_OMS_ADDRESS_MAP")
public class OrderAddressMapPO extends BasePO {

    /**
     * 客户来源code
     */
    @TableField("customer_source_code")
    private String customerSourceCode;

    /**
     * 客户来源name
     */
    @TableField("customer_source_name")
    private String customerSourceName;

    /**
     * 电商平台编码
     */
    @TableField("ec_code")
    private String ecCode;

    /**
     * 电商平台省名称
     */
    @TableField("ec_province_name")
    private String ecProvinceName;

    /**
     * 电商平台省编码
     */
    @TableField("ec_province_code")
    private String ecProvinceCode;

    /**
     * 电商平台市名称
     */
    @TableField("ec_city_name")
    private String ecCityName;

    /**
     * 电商平台市编码
     */
    @TableField("ec_city_code")
    private String ecCityCode;

    /**
     * 电商平台区名称
     */
    @TableField("ec_area_name")
    private String ecAreaName;

    /**
     * 电商平台区编码
     */
    @TableField("ec_area_code")
    private String ecAreaCode;

    /**
     * 电商平台乡镇名称
     */
    @TableField("ec_township_name")
    private String ecTownshipName;

    /**
     * 电商平台乡镇编码
     */
    @TableField("ec_township_code")
    private String ecTownshipCode;

    /**
     * 极兔省名称
     */
    @TableField("jt_province_name")
    private String jtProvinceName;

    /**
     * 极兔省编码
     */
    @TableField("jt_province_code")
    private String jtProvinceCode;

    /**
     * 极兔省id
     */
    @TableField("jt_province_id")
    private String jtProvinceId;

    /**
     * 极兔市名称
     */
    @TableField("jt_city_name")
    private String jtCityName;

    /**
     * 极兔市编码
     */
    @TableField("jt_city_code")
    private String jtCityCode;

    /**
     * 极兔市id
     */
    @TableField("jt_city_id")
    private String jtCityId;

    /**
     * 极兔区名称
     */
    @TableField("jt_area_name")
    private String jtAreaName;

    /**
     * 极兔区编码
     */
    @TableField("jt_area_code")
    private String jtAreaCode;

    /**
     * 极兔区id
     */
    @TableField("jt_area_id")
    private String jtAreaId;

    /**
     * 极兔乡镇名称
     */
    @TableField("jt_township_name")
    private String jtTownshipName;

    /**
     * 极兔乡镇编码
     */
    @TableField("jt_township_code")
    private String jtTownshipCode;

    /**
     * 极兔乡镇ID
     */
    @TableField("jt_township_id")
    private String jtTownshipId;

    /**
     * 大头笔
     */
    @TableField("big_word")
    private String bigWord;


    /**
     * 当前行政区单位
     */
    @TableField("current_address_level")
    private String currentAddressLevel;

    /**
     * 开启状态，0 false ，1 true
     */
    @TableField("is_enable")
    private Integer isEnable;

    /**
     * 客户输入格式化后的地址信息 去除空格全小写
     */
    @TableField("format_address")
    private String formatAddress;

    @ApiModelProperty(name = "ediPostCode", value = "平台邮编")
    private String ecPostCode;

    @ApiModelProperty(name = "jtPostCode", value = "极兔邮编")
    private String jtPostCode;

}