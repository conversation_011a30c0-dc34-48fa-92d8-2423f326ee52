package com.yl.order.api.enums;


import java.util.ArrayList;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2020
 * author: songxg <br>
 * description: MaterialEnum <br>
 * date: 2020-12-03 15:15 <br>
 */
public enum MaterialEnum {
    GENERAL_ELECTRONIC_FACE("PMT1M100013", "通用电子面单", 60, 69, true, MaterialTypeEnum.INTERNAL_ELECTRONIC_FACE.getCode()),
    ROOKIE_ELECTRONIC_FACE("PMT1M200014", "菜鸟电子面单", 10, 19, false, MaterialTypeEnum.THIRD_PARTY_ELECRTRONIC_FACE.getCode()),
    PDD_ELECTRONIC_FACE("PMT1M300015", "拼多多电子面单", 20, 39, false, MaterialTypeEnum.THIRD_PARTY_ELECRTRONIC_FACE.getCode()),
    JD_ELECTRONIC_FACE("PMT1M400016", "京东电子面单", 50, 59, false, MaterialTypeEnum.THIRD_PARTY_ELECRTRONIC_FACE.getCode()),
    ELECTRONIC_RECEIPT("PMT1M500019", "电子回执", 3, 3, false, MaterialTypeEnum.INTERNAL_ELECTRONIC_FACE.getCode()),
    VIPSHOP_ELECTRONIC_FACE("PMT1M600020", "唯品会电子面单", 40, 49, false, MaterialTypeEnum.THIRD_PARTY_ELECRTRONIC_FACE.getCode()),
    INTERNAL_PART_ELECTRONIC_FACE("PMT1M700021", "内部件电子单", 1, 1, true, MaterialTypeEnum.INTERNAL_ELECTRONIC_FACE.getCode()),
    ELECTRONIC_CARD("PMT1M800022", "电子包牌", 0, 0, false, MaterialTypeEnum.INTERNAL_ELECTRONIC_FACE.getCode()),
    ARRIVAL_PAYMENT_ITEM_ELECTRONIC_FACE("PMT1M300029", "到付件电子面单", 2, 2, true, MaterialTypeEnum.INTERNAL_ELECTRONIC_FACE.getCode()),
    RETURN_ITEM_ELECTRONIC_FACE("PMT1M300030", "退回件电子面单", 0, 0, true, MaterialTypeEnum.INTERNAL_ELECTRONIC_FACE.getCode());

    private String code;
    private String name;
    private Integer startBus;
    private Integer endBus;
    private Boolean generation;
    private Integer type;

    public Boolean getGeneration() {
        return this.generation;
    }

    public void setGeneration(Boolean generation) {
        this.generation = generation;
    }

    private MaterialEnum(String code, String name, Integer startBus, Integer endBus, Boolean generation, Integer type) {
        this.code = code;
        this.name = name;
        this.startBus = startBus;
        this.endBus = endBus;
        this.generation = generation;
        this.type = type;
    }

    public Integer getType() {
        return this.type;
    }

    public String getCode() {
        return this.code;
    }

    public Integer getStartBus() {
        return this.startBus;
    }

    public void setStartBus(Integer startBus) {
        this.startBus = startBus;
    }

    public Integer getEndBus() {
        return this.endBus;
    }

    public void setEndBus(Integer endBus) {
        this.endBus = endBus;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<String> getCodes() {
        List<String> list = new ArrayList();
        MaterialEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            MaterialEnum material = var1[var3];
            list.add(material.getCode());
        }

        return list;
    }

    public static String getNameByCode(String code) {
        MaterialEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            MaterialEnum material = var1[var3];
            if (material.getCode().equals(code)) {
                return material.getName();
            }
        }

        return "";
    }

    public static MaterialEnum getByCode(String code) {
        MaterialEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            MaterialEnum transactType = var1[var3];
            if (transactType.getCode().equals(code)) {
                return transactType;
            }
        }

        return null;
    }
}
