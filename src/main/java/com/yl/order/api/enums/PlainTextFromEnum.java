package com.yl.order.api.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;


/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * <AUTHOR>
 * @description : 查询订单明文来源枚举类
 * @date ：Created in 2020-11-09
 */
@Getter
public enum PlainTextFromEnum {
    JOB("job", "定时器"),
    CSS("css", "客服"),
    PDA("pda", "pda"),
    CABINET("cabinet", "终端");

    private String from;
    private String name;

    PlainTextFromEnum(String from, String name) {
        this.from = from;
        this.name = name;
    }

    public static boolean allowSource(String from) {
        return StringUtils.isNotBlank(from) && Objects.nonNull(ofEnum(from));
    }

    private static PlainTextFromEnum ofEnum(String from) {
        PlainTextFromEnum[] values = PlainTextFromEnum.values();
        for (PlainTextFromEnum e : values) {
            if (e.from.equals(from)) {
                return e;
            }
        }
        return null;
    }

}
