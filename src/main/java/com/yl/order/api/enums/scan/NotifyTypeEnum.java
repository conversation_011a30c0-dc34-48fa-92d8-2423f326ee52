package com.yl.order.api.enums.scan;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: 操作码
 * @Project:
 * @CreateDate: Created in 2019-07-16 18:00
 * @Author: <a href="<EMAIL>">chunlin.zhang</a>
 */
public enum NotifyTypeEnum {
    NETWORK(1,"调度网点"),
    SALEMAN(2,"调度业务员"),
    PROXY_AREA(3,"调度代理区"),
    ;
    private Integer code;

    private String name;

    NotifyTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }}