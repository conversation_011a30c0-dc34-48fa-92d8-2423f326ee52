package com.yl.order.api.enums.scan;

import java.util.Arrays;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019-07-25 10:51
 */
public enum PickTaskStatusEnum {

    CANCELLED_TASK(1, "已取消"),
    DISPATCHED_ORDER(2, "已调度"),
    RECEIVED_ORDER(3, "已取件"),
    ;
    private Integer code;

    private String name;

    PickTaskStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PickTaskStatusEnum of(Integer orderStatusCode) {
        return Arrays.stream(PickTaskStatusEnum.values()).filter(r -> r.code.equals(orderStatusCode)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}