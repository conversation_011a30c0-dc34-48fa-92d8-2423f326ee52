package com.yl.order.api.exception;

import com.yl.common.base.enums.RespCodeEnum;
import com.yl.common.base.enums.ResultCodeEnum;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-12-06 10:54 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */
public class OmsBusinessException extends OmsServiceException {
    public OmsBusinessException(ResultCodeEnum resultCode) {
        super(resultCode);
    }

    public OmsBusinessException(RespCodeEnum resultCode) {
        super(resultCode);
    }

    public OmsBusinessException(Integer code, String msg) {
        super(code, msg);
    }

    public OmsBusinessException(String msg) {
        super(msg);
    }

    public OmsBusinessException(Throwable throwable) {
        super(throwable);
    }

    public OmsBusinessException(String msg, Throwable throwable) {
        super(msg, throwable);
    }
}
