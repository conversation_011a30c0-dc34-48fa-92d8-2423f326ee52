package com.yl.order.api.feign;

import com.yl.common.base.model.vo.Result;
import com.yl.order.api.dto.OmsCollectionOrderUpdateApiDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-08-06 14:11 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */

public interface OmsWaybillClient {

    /**
     * 确认揽收校验
     * @param omsCollectionOrderUpdateApiDTO
     * @return
     */
    @PostMapping("/checkCollectionOrder")
    Result<OmsCollectionOrderUpdateApiDTO> checkCollectionOrder(@RequestBody OmsCollectionOrderUpdateApiDTO omsCollectionOrderUpdateApiDTO);

    /**
     *  查询第三方寄收件
     * @param thirdDeliveryExpressDTO
     * @return
     */
//    @PostMapping(value = "/getThirdDeliveryExpressByWaybillId")
//    Result<ThirdDeliveryExpressVO> getThirdDeliveryExpressByWaybillNo(@RequestBody ThirdDeliveryExpressDTO thirdDeliveryExpressDTO);

}
