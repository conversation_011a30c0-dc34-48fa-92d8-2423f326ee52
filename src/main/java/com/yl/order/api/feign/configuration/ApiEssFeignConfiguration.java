package com.yl.order.api.feign.configuration;

import com.yl.common.base.config.RequestHeaderInterceptorConfiguration;
import feign.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2020
 * author: songxg <br>
 * description: ApiEssFeignConfiguration <br>
 * date: 2020-12-03 15:11 <br>
 */
@Configuration
public class ApiEssFeignConfiguration extends RequestHeaderInterceptorConfiguration {
    public ApiEssFeignConfiguration() {
    }

    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.BASIC;
    }
}
