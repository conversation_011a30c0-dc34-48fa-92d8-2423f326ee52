package com.yl.order.api.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.order.api.dto.coupon.OmsCouponTypeSaveDTO;
import com.yl.order.api.dto.coupon.OmsCouponTypeStatusDTO;
import com.yl.order.api.feign.LooseCouponTypeClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LooseCouponTypeFallback implements LooseCouponTypeClient {


    @Override
    public Result couponTypeAdd(OmsCouponTypeSaveDTO dto) {
        log.error("【添加优惠券类型,请求参数为:{}】", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result couponTypeUpdateStatus(OmsCouponTypeStatusDTO dto) {
        log.error("【优惠券类型更新状态,请求参数为:{}】", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }
}
