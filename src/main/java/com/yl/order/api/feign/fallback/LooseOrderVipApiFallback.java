package com.yl.order.api.feign.fallback;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.order.api.dto.vip.OmsOrderVipMergerDto;
import com.yl.order.api.dto.vip.OmsVipOrderBatchCheckDto;
import com.yl.order.api.feign.LooseOrderVipApiClient;
import com.yl.order.api.vo.OmsOrderApiVO;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2020
 * author: songxg <br>
 * description: LooseOrderVipApiFallback <br>
 * date: 2020-07-09 19:15 <br>
 */
@Component
public class LooseOrderVipApiFallback implements LooseOrderVipApiClient {
    @Override
    public Result<OmsOrderApiVO> mergerOrder(OmsOrderVipMergerDto omsOrderVipBufferDto) {
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Collection<String>> checkRepeatCustomerOrderId(OmsVipOrderBatchCheckDto omsVipOrderBatchCheckDto) {
        return Result.error(ResultCodeEnum.FAIL);
    }
}
