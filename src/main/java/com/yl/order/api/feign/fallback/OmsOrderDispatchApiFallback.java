package com.yl.order.api.feign.fallback;


import com.yl.common.base.model.vo.Result;
import com.yl.order.api.dto.OmsOrderDispatchOperateApiDTO;
import com.yl.order.api.dto.OmsOrderDispatchProxyAreaApiDTO;
import com.yl.order.api.dto.OmsOrderDispatchQueryApiDTO;
import com.yl.order.api.feign.gateway.OmsOrderDispatchGateWayFeign;
import com.yl.order.api.vo.OmsOrderDispatchListExcelApiVO;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.yl.common.base.enums.ResultCodeEnum.FAIL;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-12
 * @Author: shijin.miao
 */
@Component
public class OmsOrderDispatchApiFallback implements OmsOrderDispatchGateWayFeign {


    @Override
    public Result getPages(OmsOrderDispatchQueryApiDTO dto) {
        return Result.error(FAIL);
    }

    @Override
    public Result dispatchOrder(OmsOrderDispatchOperateApiDTO dto) {
        return Result.error(FAIL);
    }

    @Override
    public Result dispatchProxyArea(OmsOrderDispatchProxyAreaApiDTO dto) {
        return null;
    }

    @Override
    public Result backDispatchOrder(OmsOrderDispatchOperateApiDTO dto) {
        return Result.error(FAIL);
    }

    @Override
    public Result dispatchSales(OmsOrderDispatchOperateApiDTO dto) {
        return Result.error(FAIL);
    }

    @Override
    public Result pickupFail(OmsOrderDispatchOperateApiDTO dto) {
        return Result.error(FAIL);
    }

    @Override
    public Result<List<OmsOrderDispatchListExcelApiVO>> export(OmsOrderDispatchQueryApiDTO dto) {
        return Result.error(FAIL);
    }
}
