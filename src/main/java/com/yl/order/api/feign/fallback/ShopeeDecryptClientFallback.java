package com.yl.order.api.feign.fallback;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.order.api.dto.shopee.OrderDecryptReqDTO;
import com.yl.order.api.dto.shopee.OrderDecryptResponseDTO;
import com.yl.order.api.feign.ShopeeDecryptClient;
import feign.Request;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ShopeeDecryptClientFallback implements FallbackFactory<ShopeeDecryptClient> {

    @Override
    public ShopeeDecryptClient create(Throwable throwable) {
        return new ShopeeDecryptClient() {
            @Override
            public Result<OrderDecryptResponseDTO> getOrderDecryptInfo(OrderDecryptReqDTO orderDecryptReqDTO) {
                log.error("ShopeeDecryptClientFallback-getOrderDecryptInfo:", throwable);
                return Result.error(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
            }
        };
    }
}
