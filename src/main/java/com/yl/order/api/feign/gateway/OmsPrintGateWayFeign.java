package com.yl.order.api.feign.gateway;

import com.yl.order.api.feign.OmsPrintClient;
import com.yl.order.api.feign.fallback.OmsPrintApiFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-13
 * @Author: shijin.miao
 */
@FeignClient(name = "ylorderapi", path = "/orderapi/print", fallback = OmsPrintApiFallback.class)
public interface OmsPrintGateWayFeign extends OmsPrintClient {
}
