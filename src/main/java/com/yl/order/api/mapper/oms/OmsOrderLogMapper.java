/*
package com.yl.order.api.mapper.oms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yl.order.api.entity.OmsOrderLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OmsOrderLogMapper extends BaseMapper<OmsOrderLog> {

    */
/**
     * 通过订单ID查询所有的订单日志
     * @param orderId
     * @return
     *//*

    List<OmsOrderLog> queryAll(@Param("orderId") Long orderId);
}
*/
