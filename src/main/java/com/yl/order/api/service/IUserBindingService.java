package com.yl.order.api.service;

import com.yl.order.api.dto.UserBindingDTO;
import com.yl.order.api.entity.UserBinding;

public interface IUserBindingService {
    /**
     * 保存绑定数据
     * @param userBindingDTO
     */
    void saveUserBing(UserBindingDTO userBindingDTO);

    /**
     * 根据用户标识获取网点信息
     * @param userMark
     * @return
     */
    UserBinding queryByUserMark(String userMark,Integer isDelete);

    /**
     * 解绑网点
     * @param userMark
     */
    void unBindByUserMark(String userMark);
}
