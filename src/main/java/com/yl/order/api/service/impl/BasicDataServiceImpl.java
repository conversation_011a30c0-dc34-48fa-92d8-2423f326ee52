package com.yl.order.api.service.impl;

import com.yl.common.base.model.vo.Result;
import com.yl.order.api.constants.OmsapiRedisKeyEnum;
import com.yl.order.api.feign.BasicDataFeignClient;
import com.yl.order.api.service.IBasicDataService;
import com.yl.order.api.vo.dispatch.BasicDataVO;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.yl.common.base.util.ValidateUtil.isSuccess;


/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description: 基础数据服务实现类
 * @Author: 陈绪军
 * @Date: 2021-08-07 11:21
 */
@Service
@Slf4j
public class BasicDataServiceImpl implements IBasicDataService {

    @Autowired
    private BasicDataFeignClient basicDataFeignClient;

    @Value(value = "${refresh.basicdata.time:300}")
    private Integer freshBasicDataTime;

    @Override
    public BasicDataVO getProductTypeByCode(String code) {
        /*String key = OmsapiRedisKeyEnum.OMSAPI_LMDM_PRODUCT_TYPE_INFO.keyBuilder(code);
        Object o = RedisUtil.get(key);
        if (Objects.isNull(o)) {
            Result<List<BasicDataVO>> result = basicDataFeignClient.getProductTypeByCodes(Arrays.asList(code));
            log.info("获取物料平台产品类型响应结果:{}", result);
            if (!isSuccess(result)) {
                log.error("【请求物料平台产品类型接口失败,返回结果信息为:{}】", result);
                return null;
            }
            if (CollectionUtils.isEmpty(result.getData())) {
                log.warn("获取物料平台产品类型响应为空");
                return null;
            }
            BasicDataVO serviceMethod = result.getData().get(0);
            RedisUtil.set(key, serviceMethod);
            RedisUtil.expire(key, freshBasicDataTime);
            return serviceMethod;
        }*/

        return null;
    }

    @Override
    public BasicDataVO getServiceMethodByCode(String code) {
        /*String key = OmsapiRedisKeyEnum.OMSAPI_LMDM_SERVICE_INFO.keyBuilder(code);
        Object o = RedisUtil.get(key);
        if (Objects.isNull(o)) {
            Result<List<BasicDataVO>> result = basicDataFeignClient.getServiceMethodByCodes(Arrays.asList(code));
            log.info("获取物料平台服务方式响应结果:{}", result);
            if (!isSuccess(result)) {
                log.error("【获取物料平台服务方式异常,返回结果信息为:{}】", result);
                return null;
            }
            if (CollectionUtils.isEmpty(result.getData())) {
                log.warn("获取物料平台服务方式响应结果为空");
                return null;
            }
            BasicDataVO serviceMethod = result.getData().get(0);
            RedisUtil.set(key, serviceMethod);
            RedisUtil.expire(key, freshBasicDataTime);
            return serviceMethod;
        }*/
        return null;
        //return (BasicDataVO) o;
    }

    @Override
    public BasicDataVO getPaymentMannerByCode(String code) {
        /*String key = OmsapiRedisKeyEnum.OMSAPI_LMDM_PAYMENT_MANNER_INFO.keyBuilder(code);
        Object o = RedisUtil.get(key);
        if (Objects.isNull(o)) {
            Result<List<BasicDataVO>> result = basicDataFeignClient.getPaymentMannerByCodes(Arrays.asList(code));
            log.info("获取物料平台结算类型响应结果:{}", result);
            if (!isSuccess(result)) {
                log.error("【请求物料平台结算类型接口失败,返回结果信息为:{}】", result);
                return null;
            }
            if (CollectionUtils.isEmpty(result.getData())) {
                log.warn("获取物料平台结算类型响应结果为空");
                return null;
            }
            BasicDataVO serviceMethod = result.getData().get(0);
            RedisUtil.set(key, serviceMethod);
            RedisUtil.expire(key, freshBasicDataTime);
            return serviceMethod;*/
        return null;
        }

    @Override
    public BasicDataVO getGoodsTypeByCode(String code) {
        /*String key = OmsapiRedisKeyEnum.OMSAPI_LMDM_ARTICLE_TYPE_INFO.keyBuilder(code);
        Object o = RedisUtil.get(key);
        if (Objects.isNull(o)) {
            Result<BasicDataVO> result = basicDataFeignClient.getArticleTypeByCode(code);
            log.info("获取物料平台物品类型响应结果:{}", result);
            if (!isSuccess(result)) {
                log.error("【请求物料平台物品类型接口失败,返回结果信息为:{}】", result);
                return null;
            }
            BasicDataVO articleType = result.getData();
            RedisUtil.set(key, articleType);
            RedisUtil.expire(key, freshBasicDataTime);
            return articleType;
        }

        return (BasicDataVO) o;*/
        return null;
    }

    @Override
    public List<BasicDataVO> getWayBillStatusByCodes(List<String> codes) {
        String key = OmsapiRedisKeyEnum.OMSAPI_LMDM_ARTICLE_TYPE_INFO.keyBuilder(codes.toString());
        Object o = RedisUtil.get(key);
        if (Objects.isNull(o)) {
            Result<List<BasicDataVO>> result = basicDataFeignClient.getWayBillStatusByCodes(codes);
            log.info("批量获取基础数据订单状态入参:{}，响应:{}", codes , result );
            if (!isSuccess(result)) {
                log.error("【批量获取基础数据订单状态接口失败,返回结果信息为:{}】", result);
                return null;
            }
            List<BasicDataVO> basicDataVOS = result.getData();
            RedisUtil.set(key, basicDataVOS);
            RedisUtil.expire(key, freshBasicDataTime);
            return basicDataVOS;
        }

        return (List<BasicDataVO>) o;
    }
}
