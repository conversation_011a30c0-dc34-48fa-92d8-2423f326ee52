package com.yl.order.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yl.order.api.entity.OmsOrderBackDetail;
import com.yl.order.api.mapper.oms.OmsOrderBackDetailMapper;
import com.yl.order.api.service.OmsOrderBackDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OmsOrderBackDetailServiceImpl implements OmsOrderBackDetailService {

    @Resource
    private OmsOrderBackDetailMapper omsOrderBackDetailMapper;

    @Override
    public Map<Long, OmsOrderBackDetail> getOmsOrderBackDetail(List<Long> orderIds) {
        LambdaQueryWrapper<OmsOrderBackDetail> OmsOrderBackDetailWrapper = new LambdaQueryWrapper<>();
        OmsOrderBackDetailWrapper.in(OmsOrderBackDetail::getOrderId, orderIds);
        List<OmsOrderBackDetail> omsOrderBackDetails = omsOrderBackDetailMapper.selectList(OmsOrderBackDetailWrapper);
        if (CollectionUtil.isNotEmpty(omsOrderBackDetails)) {
            return omsOrderBackDetails.stream()
                    .collect(Collectors.toMap(OmsOrderBackDetail::getOrderId, // 键为orderId
                            order -> order, // 值为OmsOrderBackDetail对象
                            (existingOrder, newOrder) -> {
                                // 如果有重复的orderId，选择BackTime时间最大的对象
                                if (existingOrder.getBackTime().compareTo(newOrder.getBackTime()) >= 0) {
                                    return existingOrder;
                                } else {
                                    return newOrder;
                                }
                            }));
        }
        return null;
    }
}
