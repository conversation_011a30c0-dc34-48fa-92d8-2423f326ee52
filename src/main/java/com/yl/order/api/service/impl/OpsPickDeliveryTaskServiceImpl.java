package com.yl.order.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yl.common.base.model.vo.Result;
import com.yl.order.api.constants.LmdmConstants;
import com.yl.order.api.dto.AwaitTaskQueryDTO;
import com.yl.order.api.dto.OmsWaybillNosApiDTO;
import com.yl.order.api.enums.GroupByEnums;
import com.yl.order.api.enums.OrderByEnums;
import com.yl.order.api.enums.OrderStatusEnum;
import com.yl.order.api.enums.ResultCodeEnum;
import com.yl.order.api.exception.BusinessException;
import com.yl.order.api.feign.OmsWaybillFeignClient;
import com.yl.order.api.mapper.oms.PickTaskMapper;
import com.yl.order.api.service.IOpsPickDeliveryTaskService;
import com.yl.order.api.util.AmapUtils;
import com.yl.order.api.util.DateUtils;
import com.yl.order.api.util.DesensitizationUtils;
import com.yl.order.api.vo.ExpressCollectionListVO;
import com.yl.order.api.vo.NetworkDashboardVo;
import com.yl.order.api.vo.OmsWaybillSubVO;
import com.yl.order.api.vo.OpsPickTaskPageVO;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Class Description
 *
 * <AUTHOR> href="<EMAIL>">Swain Wong</ a>
 * @version 1.0.0
 * @date 2020/4/11 9:59
 */
@Slf4j
@Service
public class OpsPickDeliveryTaskServiceImpl implements IOpsPickDeliveryTaskService {

    @Autowired
    private AmapUtils amapUtils;
    @Autowired
    private PickTaskMapper pickTaskMapper;
    @Autowired
    private OmsWaybillFeignClient omsWaybillFeignClient;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public List<ExpressCollectionListVO> pageCompletePickTask(AwaitTaskQueryDTO dto) {
        long start = System.currentTimeMillis();
        StopWatch stopWatch = new StopWatch("已取件任务秒表:" + dto.getStaffCode());
        stopWatch.start("查询数据库");
        Page<OpsPickTaskPageVO> page = new Page<>(Objects.isNull(dto.getPageNum()) ? 1 : dto.getPageNum(),
                Objects.isNull(dto.getPageSize()) ? 100 : dto.getPageSize(), false);
        List<ExpressCollectionListVO> ret = pickTaskMapper.callProceduceAlready(page, dto);
        log.info("OpsPickDeliveryTaskServiceImpl-pageCompletePickTask:{}", JSON.toJSONString(ret));
        stopWatch.stop();
        stopWatch.start("内存分组查询:" + (CollectionUtil.isNotEmpty(ret) ? ret.size() : 0));
        ret = CollectionUtil.isEmpty(ret) ?
                Lists.newArrayList()
                :
                ret.stream()
                        .map(e -> {
                            e.setSendAddress(com.yl.common.base.util.StringUtils.null2EmptyWithTrim(e.getSenderProvinceName())
                                    .concat(com.yl.common.base.util.StringUtils.null2EmptyWithTrim(e.getSenderCityName()))
                                    .concat(com.yl.common.base.util.StringUtils.null2EmptyWithTrim(e.getSenderAreaName()))
                                    .concat(com.yl.common.base.util.StringUtils.null2EmptyWithTrim(e.getSenderTownship()))
                                    .concat(com.yl.common.base.util.StringUtils.null2EmptyWithTrim(e.getSenderDetailedAddress())));
                            return e;
                        })
                        .filter(e -> filter(dto, e.getWaybillId(), null, e.getSendAddress(), e.getSendMobile(), e.getSenderName()))
                        .collect(Collectors.toList());
        stopWatch.stop();
//        log.info("========================> [取件任务] 已取件 查询条件:{}, 查询结果:{}", JSON.toJSONString(dto), JSON.toJSONString(ret));
        long end = System.currentTimeMillis();
        if ((end - start) > 4000) log.info(stopWatch.prettyPrint());

        if (CollectionUtils.isEmpty(ret)) {
            return ret;
        }
        log.info("OpsPickDeliveryTaskServiceImpl-ret:{}", JSON.toJSONString(ret));
        List<String> waybillNos = ret.stream().map(ExpressCollectionListVO::getWaybillId).collect(Collectors.toList());
        OmsWaybillNosApiDTO omsWaybillNosApiDTO = new OmsWaybillNosApiDTO();
        omsWaybillNosApiDTO.setWaybillNos(waybillNos);
        Result<List<OmsWaybillSubVO>> result = omsWaybillFeignClient.getWaybillSubs(omsWaybillNosApiDTO);
        log.info("OpsPickDeliveryTaskServiceImpl-getWaybillSubs:{}", JSON.toJSONString(result));
        if (result.isFail()) {
            throw new BusinessException(ResultCodeEnum.SERVICE_TRANSFER_ERROR);
        }
        List<OmsWaybillSubVO> subVos = result.getData();
        if (CollectionUtils.isEmpty(subVos)) {
            return ret;
        }
        Map<String, List<OmsWaybillSubVO>> collect = subVos.stream().collect(Collectors.groupingBy(OmsWaybillSubVO::getWaybillNo));
        ret.forEach(x -> {
            x.setWaybillSubVOs(collect.get(x.getWaybillId()));
            x.setPickupByContact(DesensitizationUtils.desensitizationPhone(x.getPickupByContact()));
            x.setSendMobile(DesensitizationUtils.desensitizationPhone(x.getSendMobile()));
            x.setScanByContact(DesensitizationUtils.desensitizationPhone(x.getScanByContact()));
            x.setScanNetworkContact(DesensitizationUtils.desensitizationPhone(x.getScanNetworkContact()));
        });
        return ret;
    }

    @Override
    public Integer pageCompletePickTaskCount(AwaitTaskQueryDTO dto) {
        return pickTaskMapper.callProceduceAlreadyCount(dto);
    }

    private boolean filter(AwaitTaskQueryDTO dto, String waybillId, Long orderId, String detailAddress, String mobile, String name) {
        log.info("=================> [查询过滤条件] 过滤条件:{}, 传入数据: waybillId:{}, orderId:{}, address:{}, mobile:{}, name:{}",
                JSON.toJSONString(dto), waybillId, orderId, detailAddress, mobile, name);
        //构建过滤器
        return StringUtils.equalsIgnoreCase(dto.getWaybillId(), waybillId)      //运单号全匹配
                ||
                (null != orderId && StringUtils.equalsIgnoreCase(dto.getWaybillId(), orderId.toString()))      //订单号全匹配
                ||
                StringUtils.contains(detailAddress, dto.getAddress())        //地址模糊匹配
                ||
                StringUtils.contains(mobile, dto.getPhone())                    //手机号模糊匹配
                ||
                StringUtils.contains(name, dto.getCustomerName());              //名字模糊匹配
    }


    @Override
    public List<OpsPickTaskPageVO> pageAwaitPickTask(AwaitTaskQueryDTO dto) {
        long start = System.currentTimeMillis();
        StopWatch stopWatch = new StopWatch("待取件任务秒表:" + dto.getStaffCode());
        stopWatch.start("查询数据库");
        dto.setOrderStatus(Lists.newArrayList(OrderStatusEnum.dispatched_saleman.getCode(), OrderStatusEnum.pick_fail.getCode()));
        if (null == dto.getStartTime() || null == dto.getEndTime()) {
            LocalDateTime localDateTime = LocalDateTime.now();
            LocalDateTime before = localDateTime.with(LocalTime.MIN);
            LocalDateTime now = localDateTime.with(LocalTime.MAX);
            dto.setStartTime(before);
            dto.setEndTime(now);
        }
        Page<OpsPickTaskPageVO> page = new Page<>(Objects.isNull(dto.getPageNum()) ? 1 : dto.getPageNum(),
                Objects.isNull(dto.getPageSize()) ? 100 : dto.getPageSize(), false);
        List<OpsPickTaskPageVO> ret = pickTaskMapper.callProceduceWait(page, dto);
        stopWatch.stop();
        log.info("=====================> [取件任务] 待取件 查询条件:{}, 查询结果:{}", JSON.toJSONString(dto), JSON.toJSONString(ret));
        stopWatch.start("内存分组查询:" + (CollectionUtil.isNotEmpty(ret) ? ret.size() : 0));
        if (CollectionUtil.isNotEmpty(ret)) {
            List<OpsPickTaskPageVO> tasks = ret.stream().filter(e -> filter(dto, e.getWaybillId(), e.getOrderId(), e.getSenderDetailedAddress(), e.getSenderMobile(), e.getSenderName()))   //过滤
                    .map(opsPickTaskPageVO -> {
                        opsPickTaskPageVO.setId("");//兼容ios
                        opsPickTaskPageVO.setIsAbnormal(StringUtils.isNotBlank(opsPickTaskPageVO.getPickFailReason()) ? 1 : 0);
                        opsPickTaskPageVO.setSenderMobile(DesensitizationUtils.desensitizationPhone(opsPickTaskPageVO.getSenderMobile()));
                        opsPickTaskPageVO.setSenderPhone(DesensitizationUtils.desensitizationPhone(opsPickTaskPageVO.getSenderPhone()));
                        return opsPickTaskPageVO;
                    })
                    .collect(Collectors.toList());  //收集
            log.info("=====================> [取件任务] 待取件 过滤结果:{}", JSON.toJSONString(tasks));
            ret = orderOrGroupPickTask(dto, tasks);
        }
        stopWatch.stop();
        long end = System.currentTimeMillis();
        if ((end - start) > 3000) log.info("[取件任务] 待取件耗时：{}", stopWatch.prettyPrint());
        return ret;
    }

    @Override
    public Integer pageAwaitPickTaskCount(AwaitTaskQueryDTO dto) {
        dto.setOrderStatus(Lists.newArrayList(OrderStatusEnum.dispatched_saleman.getCode(), OrderStatusEnum.pick_fail.getCode()));
        if (null == dto.getStartTime() || null == dto.getEndTime()) {
            LocalDateTime localDateTime = LocalDateTime.now();
            LocalDateTime before = localDateTime.with(LocalTime.MIN);
            LocalDateTime now = localDateTime.with(LocalTime.MAX);
            dto.setStartTime(before);
            dto.setEndTime(now);
        }
        return pickTaskMapper.callProceduceWaitCount(dto);
    }

    /***
     * Method Description: 对待取件/已取消任务进行排序
     *
     * @param dto
     * @param tasks
     * @return void
     * <AUTHOR> href="<EMAIL>">Swain Wong</a>
     * @date 2020/4/8
     */
    public List<OpsPickTaskPageVO> orderOrGroupPickTask(AwaitTaskQueryDTO dto, List<OpsPickTaskPageVO> tasks) {
        Comparator<OpsPickTaskPageVO> comparator = Comparator.comparing(OpsPickTaskPageVO::getCreateTime).reversed();   //默认调度时间降序
        Integer orderBy = dto.getOrderFlag();
        Integer groupBy = dto.getGroupFlag();
        if (OrderByEnums.contains(orderBy)) {
            comparator = OrderByEnums.isOrderByTime(orderBy) ?
                    Comparator.comparing(OpsPickTaskPageVO::getCreateTime)
                    :
                    Comparator.comparing(OpsPickTaskPageVO::getDistance);
            //根据距离排序, 业务员的经纬度不能为空
            if (StrUtil.isNotEmpty(dto.getStaffLngLat())) {
                tasks.stream().forEach(e -> {
                    String lngLat = e.getLngLat();
                    Double distance = amapUtils.getDistance(dto.getStaffLngLat(), lngLat);
                    e.setDistance(distance);
                });
            }

        } else if (GroupByEnums.contains(groupBy)) {
            //默认都是中文升序
            comparator = GroupByEnums.isGroupBySenderName(groupBy) ?
                    Comparator.comparing(OpsPickTaskPageVO::getSenderName, Comparator.comparing(OpsPickDeliveryTaskServiceImpl::toPinyin))
                    :
                    Comparator.comparing(OpsPickTaskPageVO::getSenderDetailedAddress, Comparator.comparing(OpsPickDeliveryTaskServiceImpl::toPinyin));
        }
        if (!OrderByEnums.isAsc(orderBy)) comparator = comparator.reversed();
        //最后来统一排序即可
        List<OpsPickTaskPageVO> ret = tasks.stream().sorted(comparator).collect(Collectors.toList());
        return ret;
    }


    /***
     * Method Description: 将字符串转换成拼音串
     *
     * @param str
     * @return java.lang.String
     * <AUTHOR> href="<EMAIL>">Swain Wong</a>
     * @date 2020/4/10
     */
    public static String toPinyin(String str) {
        StringBuffer sb = new StringBuffer();
        String[] arr;
        if (StrUtil.isNotEmpty(str)) {
            for (int i = 0; i < str.length(); i++) {
                arr = PinyinHelper.toHanyuPinyinStringArray(str.charAt(i));
                if (ArrayUtil.isNotEmpty(arr)) {
                    Arrays.stream(arr).forEach(e -> sb.append(e));
                }
            }
        }
        return sb.toString();
    }


    @Override
    public Map<String, Map<String, List<OpsPickTaskPageVO>>> classificationSummary(AwaitTaskQueryDTO dto) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("app查询待揽/已取件分类汇总 uuid:{}", requestId);
        if (dto == null) throw new BusinessException("不支持查询全部,请传入条件查询!");
        if (StringUtils.isBlank(dto.getStaffCode())) throw new BusinessException("请根据调派业务员编码查询!");
        if (StringUtils.isBlank(dto.getNetworkCode())) throw new BusinessException("网点编码必填!");
        //默认查询待取件/取件失败
        if (dto.getQueryType() == null || (dto.getQueryType() != 1 && dto.getQueryType() != 2)) dto.setQueryType(1);
        //设置查询时间
        setQueryTime(dto);
        log.info("app查询待揽/已取件分类汇总 uuid:{} 参数:{}", requestId, JSON.toJSONString(dto));
        List<OpsPickTaskPageVO> result = pickTaskMapper.classificationSummary(dto);
        log.info("app查询待揽/已取件分类汇总 uuid:{} 获取到结果总数:{} .开始汇总", requestId, result == null ? 0 : result.size());
        //开始汇总
        Map<String, Map<String, List<OpsPickTaskPageVO>>> map = groupSortingSummary(result);
        log.info("app查询待揽/已取件分类汇总 uuid:{} 汇总结果:{}", requestId, JSON.toJSONString(map));
        return map;
    }

    /**
     * 分类汇总
     * 1.按照渠道类型分组
     * 2.每个渠道类型中数据的按照 客户编码+寄件人姓名+寄件邮编+寄件详细地址 继续分组
     *
     * @param result
     * @return
     */
    private Map<String, Map<String, List<OpsPickTaskPageVO>>> groupSortingSummary(List<OpsPickTaskPageVO> result) {
        Map<String, Map<String, List<OpsPickTaskPageVO>>> map = null;
        if (result != null && !result.isEmpty()) {
            StringBuffer groupingKey = new StringBuffer();
            map = result.stream().map(vo -> {
                vo.setSenderMobile(DesensitizationUtils.desensitizationPhone(vo.getSenderMobile()));
                vo.setSenderPhone(DesensitizationUtils.desensitizationPhone(vo.getSenderPhone()));
                String orderSourceName = String.valueOf(redisUtil.hGet(LmdmConstants.ORDER_MQ_LMDM_SYSC_ORDER_SOURCE_CODE_PREFEX, vo.getOrderSourceCode()));
                vo.setOrderSourceName(StringUtils.isNotBlank(orderSourceName) ? orderSourceName : vo.getOrderSourceCode());
                //分组key
                groupingKey.setLength(0);
                groupingKey.append(vo.getCustomerCode()).append(",")
                        .append(vo.getSenderName()).append(",")
                        .append(vo.getSenderPostalCode()).append(",")
                        .append(vo.getSenderDetailedAddress());
                vo.setGroupingKey(groupingKey.toString());
                return vo;
            }).collect(Collectors.groupingBy(OpsPickTaskPageVO::getOrderSourceName, Collectors.groupingBy(OpsPickTaskPageVO::getGroupingKey)));
        }
        return map;
    }

    /**
     * 设置查询时间
     *
     * @param dto
     */
    private void setQueryTime(AwaitTaskQueryDTO dto) {
        //日期超限抛出异常
        if (DateUtils.between(dto.getStartTime(), dto.getEndTime()) > 6) {
            throw new BusinessException(ResultCodeEnum.DATE_INTERVAL_EXCEEDING_07);
        }
        if (null == dto.getStartTime() || null == dto.getEndTime()) {
            //默认查询七天
            LocalDateTime localDateTime = LocalDateTime.now();
            LocalDateTime before = localDateTime.with(LocalTime.MIN).plusDays(-6);
            LocalDateTime now = localDateTime.with(LocalTime.MAX);
            dto.setStartTime(before);
            dto.setEndTime(now);
        }
    }

    @Override
    public IPage<NetworkDashboardVo> orderGroupTotal(NetworkDashboardVo networkDashboardVo, Integer current, Integer size) {
        IPage<NetworkDashboardVo> page = new Page<>(current, size);
        Integer groupType = networkDashboardVo.getGroupType();
        if (groupType == 1) {
            page = pickTaskMapper.orderGroupTotalByMobile(page, networkDashboardVo);
        } else {
            page = pickTaskMapper.orderGroupTotalByAddress(page, networkDashboardVo);
        }
        for (NetworkDashboardVo record : page.getRecords()) {
            record.setSenderMobilePhone(DesensitizationUtils.desensitizationPhone(record.getSenderMobilePhone()));
            record.setSenderTelphone(DesensitizationUtils.desensitizationPhone(record.getSenderTelphone()));
        }
        return page;
    }

    @Override
    public IPage<NetworkDashboardVo> queryOrderDetail(NetworkDashboardVo networkDashboardVo, Integer current, Integer size) {
        IPage<NetworkDashboardVo> page = new Page<>(current, size);
        for (NetworkDashboardVo record : page.getRecords()) {
            record.setSenderMobilePhone(DesensitizationUtils.desensitizationPhone(record.getSenderMobilePhone()));
            record.setSenderTelphone(DesensitizationUtils.desensitizationPhone(record.getSenderTelphone()));
        }
        return pickTaskMapper.queryOrderDetail(page, networkDashboardVo);
    }


}
