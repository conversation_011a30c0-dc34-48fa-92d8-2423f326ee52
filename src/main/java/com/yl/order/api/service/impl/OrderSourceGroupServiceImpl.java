/*
package com.yl.order.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.yl.id.IdGenerator;
import com.yl.order.api.config.OrikaBeanMapper;
import com.yl.order.api.constant.LmdmConstants;
import com.yl.order.api.constant.OrderDbCons;
import com.yl.order.api.dto.OrderSourceGroupDTO;
import com.yl.order.api.entity.OmsOrder;
import com.yl.order.api.entity.OrderSource;
import com.yl.order.api.entity.OrderSourceGroup;
import com.yl.order.api.enums.OrderSourceGroupEnum;
import com.yl.order.api.mapper.oms.OrderSourceGroupMapper;
import com.yl.order.api.service.OrderSourceGroupService;
import com.yl.order.api.util.CollectionUtils;
import com.yl.order.api.util.JsonUtils;
import com.yl.order.api.vo.OmsOrderApiVO;
import com.yl.order.api.vo.OrderSourceGroupVO;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

*/
/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2020
 * author: songxg <br>
 * description: OrderSourceGroupServiceImpl <br>
 * date: 2021-01-11 16:08 <br>
 *//*

@Slf4j
@Service
public class OrderSourceGroupServiceImpl extends ServiceImpl<OrderSourceGroupMapper, OrderSourceGroup> implements OrderSourceGroupService {

    @Autowired
    private OrderSourceGroupMapper orderSourceGroupMapper;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IdGenerator<Long> idGenerator;

    @Override
    public Page<OrderSourceGroupVO> listByPage(OrderSourceGroupDTO dto) {
//        initGroup();

        long total = orderSourceGroupMapper.getTotal(dto);
        List<OrderSourceGroupVO> groupVOs = new ArrayList<>();
        if(total > 0) {
            List<OrderSourceGroup> list = orderSourceGroupMapper.selectByPage(dto);
            if(CollectionUtils.isNotEmpty(list)){
                groupVOs = orikaBeanMapper.mapAsList(list, OrderSourceGroupVO.class);
            }
        }
        Page<OrderSourceGroupVO> page = new Page<>();
        page.setSize(dto.getSize());
        page.setCurrent(dto.getCurrent());
        page.setTotal(total);
        page.setRecords(groupVOs);
        return page;
    }

    */
/*private void initGroup(){
        List<OrderSourceGroup> list = list();
        log.info("分类数据：{}",JsonUtils.toJson(list));
        if(CollectionUtils.isEmpty(list)) {
            Map<Object, Object> map = RedisUtil.hGetAll(LmdmConstants.ORDER_MQ_LMDM_SYSC_ORDER_SOURCE_CODE_PREFEX);
            List<OrderSource> sources = map.entrySet().stream().map(e -> new OrderSource(e.getKey().toString(), e.getValue().toString())).collect(Collectors.toList());
            List<OrderSourceGroup> insertList = new ArrayList<>();
            sources.forEach(o -> {
                OrderSourceGroup sourceGroup = new OrderSourceGroup();
                sourceGroup.setId(idGenerator.generate());
                sourceGroup.setOrderSourceCode(o.getSourceCode());
                sourceGroup.setOrderSourceName(o.getSourceName());
                sourceGroup.setOrderSourceGroupId(OrderSourceGroupEnum.OTHER.getId());
                sourceGroup.setOrderSourceGroupName(OrderSourceGroupEnum.OTHER.getName());
                sourceGroup.setOrderSourceGroupIdAndCode(Joiner.on("_").join(OrderSourceGroupEnum.OTHER.getId(),o.getSourceCode()));
                sourceGroup.setUpdateTime(LocalDateTime.now());
                insertList.add(sourceGroup);
            });
            saveBatch(insertList);
        }
    }*//*

}
*/
