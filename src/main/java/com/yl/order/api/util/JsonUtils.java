package com.yl.order.api.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * json 工具类
 *
 * <AUTHOR>
 *
 */
public class JsonUtils {

	
	/**
	 * bean转json
	 * @param obj
	 * @return
	 */
	public static String toJson(Object obj){
		return JSON.toJSONString(obj);
	}
	
	/**
	 * json转bean
	 * @param json
	 * @param clazz
	 * @return
	 */
	public static <T>T toBean(String json,Class<T> clazz){
		return JSON.parseObject(json, clazz);
	}
	
	
	/**
	 * json转list
	 * @param json
	 * @param clazz
	 * @return
	 */
	public static <T> List<T> toList(String json,Class<T> clazz){
		return JSON.parseArray(json, clazz);
	}
	
	/**
     * json转JSONObject
     * @param json
     * @return
     */
    public static JSONObject toJSONObject(String json){
        return JSON.parseObject(json);
    }
    

}
