package com.yl.order.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * author: chenYeJun
 * Date: 2020/1/8
 * 订单调度导出
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OmsOrderDispatchListExcelApiVO extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "订单号", index = 0)
    private String id;
    @ExcelProperty(value = "运单号", index = 1)
    private String waybillId;

    @ExcelProperty(value = "订单来源", index = 2)
    private String orderSourceName;

    @ExcelProperty(value = "客户信息", index = 3)
    private String customerName;


    private LocalDateTime inputTime;
    @ExcelProperty(value = "订单录入时间", index = 4)
    private Date inputTimeExcelAdapt;

    private Integer orderStatusCode;
    @ExcelProperty(value = "订单状态", index = 5)
    private String orderStatusName;


    private LocalDateTime dispatchProxyAreaTime;
    @ExcelProperty(value = "调度代理区时间", index = 6)
    private Date dispatchProxyAreaTimeExcelAdapt;

    @ExcelProperty(value = "代理区", index = 7)
    private String proxyAreaName;


    private LocalDateTime dispatchNetworkTime;
    @ExcelProperty(value = "调度网点时间", index = 8)
    private Date dispatchNetworkTimeExcelAdapt;

    @ExcelProperty(value = "取件网点", index = 9)
    private String pickNetworkName;

    @ExcelProperty(value = "实际取件网点", index = 10)
    private String realPickNetworkName;

    private LocalDateTime dispatchStaffTime;
    @ExcelProperty(value = "调度业务员时间", index = 11)
    private Date dispatchStaffTimeExcelAdapt;

    @ExcelProperty(value = "取件业务员", index = 12)
    private String pickStaffName;

    private LocalDateTime pickTime;
    @ExcelProperty(value = "取件时间", index = 13)
    private Date pickTimeExcelAdapt;

    @ExcelProperty(value = "寄件省份", index = 14)
    private String senderProvinceName;

    @ExcelProperty(value = "寄件城市", index = 15)
    private String senderCityName;

    @ExcelProperty(value = "寄件地区", index = 16)
    private String senderAreaName;

    @ExcelProperty(value = "寄件详细地址", index = 17)
    private String senderDetailedAddress;

    private LocalDateTime cancelTime;
    @ExcelProperty(value = "订单取消时间", index = 18)
    private Date cancelTimeExcelAdapt;

    @ExcelProperty(value = "取消订单原因", index = 19)
    private String cancelReason;

    private LocalDateTime backDispatchNetworkTime;
    @ExcelProperty(value = "打回调度订单时间",index = 20)
    private Date backDispatchNetworkTimeExcelAdapt;

    @ExcelProperty(value = "打回调度订单原因", index = 21)
    private String backDispatchNetworkReason;

    @ExcelProperty(value = "打回调度订单次数", index = 22)
    private Integer backDispatchNetworkNumber;

    /**
     * 是否是商务件 1是 2不是
     */

    private Integer isBusiness;

    @ExcelProperty(value = "是否商务件", index = 23)
    private String isBusinessName;

    @ExcelProperty(value = "调度时长", index = 24)
    private String dispatchDuration;

    public Date getInputTimeExcelAdapt() {
        if (this.getInputTime() != null){
            return Date.from(this.getInputTime().atZone(ZoneId.systemDefault()).toInstant());
        }else {
            return inputTimeExcelAdapt;
        }
    }

    public Date getDispatchProxyAreaTimeExcelAdapt() {
        if (this.getDispatchProxyAreaTime() != null){
            return Date.from(this.getDispatchProxyAreaTime().atZone(ZoneId.systemDefault()).toInstant());
        }else {
            return dispatchProxyAreaTimeExcelAdapt;
        }
    }

    public Date getDispatchNetworkTimeExcelAdapt() {
        if (this.getDispatchNetworkTime() != null){
            return Date.from(this.getDispatchNetworkTime().atZone(ZoneId.systemDefault()).toInstant());
        }else {
            return dispatchNetworkTimeExcelAdapt;
        }
    }

    public Date getDispatchStaffTimeExcelAdapt() {
        if (this.getDispatchStaffTime() != null){
            return Date.from(this.getDispatchStaffTime().atZone(ZoneId.systemDefault()).toInstant());
        }else {
            return dispatchStaffTimeExcelAdapt;
        }
    }

    public Date getPickTimeExcelAdapt() {
        if (this.getPickTime() != null){
            return Date.from(this.getPickTime().atZone(ZoneId.systemDefault()).toInstant());
        }else {
            return pickTimeExcelAdapt;
        }
    }

    public Date getCancelTimeExcelAdapt() {
        if (this.getCancelTime() != null){
            return Date.from(this.getCancelTime().atZone(ZoneId.systemDefault()).toInstant());
        }else {
            return cancelTimeExcelAdapt;
        }
    }

    public Date getBackDispatchNetworkTimeExcelAdapt() {
        if (this.getBackDispatchNetworkTime() != null){
            return Date.from(this.getBackDispatchNetworkTime().atZone(ZoneId.systemDefault()).toInstant());
        }else {
            return backDispatchNetworkTimeExcelAdapt;
        }
    }
}
