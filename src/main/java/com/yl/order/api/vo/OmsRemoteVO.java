package com.yl.order.api.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-13 22:22
 * @Author: <a href="<EMAIL>">chunlin.zhang</a>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OmsRemoteVO implements Serializable {

    private String waybillNo;

    private String receiverSortingCode;

    private Integer pickNetworkId;

    private String pickNetworkCode;

    private String pickNetworkName;

    private Integer dispatchNetworkId;

    private String dispatchNetworkCode;

    private String dispatchNetworkName;

    private Integer pickFinanceNetworkId;

    private String pickFinanceNetworkCode;

    private String pickFinanceNetworkName;

    private Integer dispatchFinanceNetworkId;

    private String dispatchFinanceNetworkCode;

    private String dispatchFinanceNetworkName;

    private String localCurrencyCode;

    private String localCurrencyName;

    public OmsRemoteVO(Integer pickNetworkId, String pickNetworkCode, String pickNetworkName, Integer pickFinanceNetworkId, String pickFinanceNetworkCode, String pickFinanceNetworkName,
                       String localCurrencyCode, String localCurrencyName) {
        this.pickNetworkId = pickNetworkId;
        this.pickNetworkCode = pickNetworkCode;
        this.pickNetworkName = pickNetworkName;
        this.pickFinanceNetworkId = pickFinanceNetworkId;
        this.pickFinanceNetworkCode = pickFinanceNetworkCode;
        this.pickFinanceNetworkName = pickFinanceNetworkName;
        this.localCurrencyCode = localCurrencyCode;
        this.localCurrencyName = localCurrencyName;
    }

    public OmsRemoteVO(String receiverSortingCode, Integer dispatchNetworkId, String dispatchNetworkCode, String dispatchNetworkName, Integer dispatchFinanceNetworkId, String dispatchFinanceNetworkCode, String dispatchFinanceNetworkName) {
        this.receiverSortingCode = receiverSortingCode;
        this.dispatchNetworkId = dispatchNetworkId;
        this.dispatchNetworkCode = dispatchNetworkCode;
        this.dispatchNetworkName = dispatchNetworkName;
        this.dispatchFinanceNetworkId = dispatchFinanceNetworkId;
        this.dispatchFinanceNetworkCode = dispatchFinanceNetworkCode;
        this.dispatchFinanceNetworkName = dispatchFinanceNetworkName;
    }
}