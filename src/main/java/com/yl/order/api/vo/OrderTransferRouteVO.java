package com.yl.order.api.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrderTransferRouteVO implements Serializable {
    private static final long serialVersionUID = 7501365473344194933L;

    private String siteCode;
    private String waybillId;
    private String startTransferCode;
    private String endTransferCode;
    private String firstNetworkCode;
    private String secondNetworkCode;
    private String routeCode;

    private String startTime;
    private String endTime;
}