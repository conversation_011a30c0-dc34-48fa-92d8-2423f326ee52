package com.yl.order.api.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019/6/5 19:15
 * @Author: <a href="<EMAIL>">chunlin.zhang</a>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description= "网点DTO")

public class ProxyNetworkPcaIdVO implements Serializable {

    @ApiModelProperty(name = "id", value = "网点id")
    private Integer id;

    @ApiModelProperty(name = "name", value = "网点名称")
    private String name;

    @ApiModelProperty(name = "code", value = "网点编号")
    private String code;
}