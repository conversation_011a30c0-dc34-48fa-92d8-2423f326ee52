package com.yl.order.api.vo.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021-11-18
 *
 * @Description: 优惠券数量
 * @Author: la<PERSON><PERSON><PERSON>
 * @Date: 2021-11-18 19:07
 */
@Data
public class OmsCouponDetailCountAppVO implements Serializable {

    @ApiModelProperty(value="待使用")
    private Integer waitUse;

    @ApiModelProperty(value="已使用")
    private Integer used;

    @ApiModelProperty(value="已失效")
    private Integer invalid;


}
