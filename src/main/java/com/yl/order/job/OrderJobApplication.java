/*
 * 云路供应链科技有限公司 版权所有 © Copyright 2019
 */
package com.yl.order.job;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 定时任务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-05-20
 */
@EnableDiscoveryClient
@ComponentScan("com.yl.*")
@MapperScan("com.yl.order.job.mapper")
@EnableFeignClients(basePackages = {"com.yl.*"})
@SpringBootApplication(exclude = {MongoDataAutoConfiguration.class, MongoAutoConfiguration.class, RabbitAutoConfiguration.class, DataSourceAutoConfiguration.class, RedisAutoConfiguration.class})
@EnableApolloConfig(value = {"common.bootstrap", "common.mybatis-plus", "application", "dbConnection.yml", "xxl-job.yml", "rabbit.yml", "redis.yml"})
public class OrderJobApplication {

    public static void main(String[] args) {
        SpringApplication.run(OrderJobApplication.class, args);
    }

}
