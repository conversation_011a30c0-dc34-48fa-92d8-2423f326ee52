package com.yl.order.job.dto;

import lombok.Data;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-08-01 16:07 <br>
 * @Author: <a href="<EMAIL>">xuqing</a>
 */
@Data

public class OmsOrderCancelApiDTO {
    /**
     * 请求来源 1.jms 2.edi 3.巴枪app 4.PCCS 5.小程序
     */
    protected Integer from;

    private List<Long> orderIds;

    private String customerCode;

    private String customerOrderId;

    private Integer cancelReasonCode;

    private String cancelExplain;

    private String reason;

    private String operatorNumber;

    private String operatorName;

    private Integer updateBy;

    private String updateByName;

}
