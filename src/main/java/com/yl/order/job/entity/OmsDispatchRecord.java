package com.yl.order.job.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * <AUTHOR>
 * @description :
 * @date ：Created in 2020-06-10
 */
@Data
@TableName("YL_OMS_DISPATCH_RECORD")
public class OmsDispatchRecord {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 订单编号
     */

    private Long orderId;

    /**
     * 运单号
     */

    private String waybillId;

    /**
     * 取件网点code
     */

    private String pickNetworkCode;

    /**
     * 取件网点名称
     */

    private String pickNetworkName;

    /**
     * 取件业务员code
     */

    private String pickStaffCode;

    /**
     * 取件业务员名称
     */

    private String pickStaffName;

    /**
     * 调度时间
     */

    private LocalDateTime dispatchTime;

    /**
     * 操作人工号
     */

    private String operatorNumber;

    /**
     * 操作人名称
     */

    private String operatorName;

    /**
     * 操作码code
     */

    private Integer operatorDicCode;

    /**
     * 通知类型code
     */

    private Integer notiftTypeCode;

    /**
     * 备注
     */

    private String remark;

    /**
     * 是否删除:1未删除,2已删除
     */

    private Integer isDelete;


    /**
     * 代理区code
     */
    private String proxyAreaCode;

    /**
     * 代理区name
     */
    private String proxyAreaName;

    /**
     * 调度代理区时间
     */
    private LocalDateTime dispatchProxyAreaTime;

    /**
     * 订单来源
     */
    private String fromCode;

    /**
     * 客户订单编号
     */
    private String customerOrderId;

    /**
     * 微信id
     */
    private String memberId;

    /**
     * 包裹总重量
     */
    private String packageTotalWeight;

    /**
     * 寄件区域
     */
    private String senderAreaName;

    /**
     * 寄件人详细地址
     */
    private String senderDetailedAddress;
}

