package com.yl.ordermq.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2020
 * author: songxg <br>
 * description: OmsSubOrder <br>
 * date: 2021-07-14 14:42 <br>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_OMS_OMS_SUBORDER")
public class OmsSubOrder implements Serializable {

    //子订单号
    private Long id;

    //关联的主订单号
    private Long orderId;

    //子运单号
    private String subWaybillId;

    //主运单号
    private String waybillId;

    //打印次数
    private Integer printsNumber;

    //打印时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime printTime;
}
