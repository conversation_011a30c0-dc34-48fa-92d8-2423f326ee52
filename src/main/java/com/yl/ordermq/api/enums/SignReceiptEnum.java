package com.yl.ordermq.api.enums;

/**
 * 功能描述:
 * 回单Emun
 * @since: 1.0.0
 * @Author:luhong
 * @Date: 2020-06-23 16:59
 */
public enum SignReceiptEnum {

    IS_SIGN_RECEIPT(1,"是"),
    NOT_SIGN_RECEIPT(0,"否"),;

    private Integer code;

    private String desc;

    SignReceiptEnum(Integer code , String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
