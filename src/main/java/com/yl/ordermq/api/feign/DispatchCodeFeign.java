package com.yl.ordermq.api.feign;

import com.yl.order.api.dto.DeliverNetworkInfoQueryDTO;
import com.yl.order.api.dto.DisPatchCodeDto;
import com.yl.order.api.dto.FetchNetworkDTO;
import com.yl.order.api.vo.DeliverNetworkInfoVO;
import com.yl.order.api.vo.DispatchCodeResult;
import com.yl.ordermq.api.dto.DeliverNetworkQueryDTO;
import com.yl.ordermq.api.feign.fallback.DispatchCodeFallBack;
import com.yl.ordermq.api.vo.DeliverNetworkVO;
import com.yl.ordermq.api.vo.dispatchcode.CrossCodeVo;
import com.yl.ordermq.api.vo.dispatchcode.DispatchCodeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URI;
import java.util.List;
import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-11-06 09:24 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */
@Component
@FeignClient(
        name = "ylassapi",
        path = "/assapi/secondCode",
        fallbackFactory = DispatchCodeFallBack.class
)
public interface DispatchCodeFeign {

    /**
     * 根据收件地址批量获取三段码信息
     *
     * @param disPatchCodeDtos
     * @return
     */
    @PostMapping(value = "/fetchCodes")
    DispatchCodeResult<DispatchCodeVO> fetchCodes(@RequestBody DisPatchCodeDto disPatchCodeDtos);

    /**
     * 根据收件地址批量获取三段码信息
     *
     * @param disPatchCodeDtos
     * @return
     */
    @PostMapping(value = "/getDispatchCode")
    DispatchCodeResult<CrossCodeVo> getDispatchCode(@RequestBody DisPatchCodeDto disPatchCodeDtos);

    /**
     * 根据 收件/派件 地址获取 收件/派件 网点ID
     *
     * @param fetchNetworkDtos
     * @return
     */
    @PostMapping(value = "/fetchNetworkIds")
    DispatchCodeResult<Map<String, Integer>> fetchNetworkIds(@RequestBody List<FetchNetworkDTO> fetchNetworkDtos);

    /**
     * 根据省市区ID及二段码查询网点
     *
     * @param deliverNetworkInfoQueryDTO
     * @return
     */
    @PostMapping(value = "/deliverNetworkInfoByPddCode")
    DispatchCodeResult<DeliverNetworkInfoVO> deliverNetworkInfoByPddCode(@RequestBody DeliverNetworkInfoQueryDTO deliverNetworkInfoQueryDTO);

    /**
     * 根据订单id及一段码二段码查询派件网点
     *
     * @param baseUri
     * @param deliverNetworkQueryDTO
     * @return
     */
    @PostMapping(value = "/fetchNetworkByCodeBatch")
    DispatchCodeResult<Map<String, DeliverNetworkVO>> fetchNetworkByCodeBatch(URI baseUri, @RequestBody List<DeliverNetworkQueryDTO> deliverNetworkQueryDTO);
}
