package com.yl.ordermq.api.feign;


import com.yl.common.base.model.vo.Result;
import com.yl.ordermq.api.dto.SysStoreSelfPickupCheckDTO;
import com.yl.ordermq.api.dto.SysStoreSelfPickupFlagDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(
        name = "yllmdmapi",
        path = "/lmdmapi"
)
public interface LmdmApiFeign {


    @PostMapping("/sysStoreSelfPickup/checkWhetherSelfPickup")
    Result<SysStoreSelfPickupFlagDTO> checkWhetherSelfPickup(@RequestBody SysStoreSelfPickupCheckDTO dto);

}
