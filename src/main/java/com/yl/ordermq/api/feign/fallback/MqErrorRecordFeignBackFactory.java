package com.yl.ordermq.api.feign.fallback;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.ordermq.api.entity.MqErrorRecord;
import com.yl.ordermq.api.feign.MqErrorRecordFeign;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class MqErrorRecordFeignBackFactory implements FallbackFactory<MqErrorRecordFeign> {
    @Override
    public MqErrorRecordFeign create(Throwable throwable) {
        return new MqErrorRecordFeign() {
            @Override
            public Result<Boolean> save(List<MqErrorRecord> mqErrorRecordList) {
                log.error("MqErrorRecordFeignBackFactory-save:",throwable);
                return Result.error(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
            }

            @Override
            public Result<Boolean> updateStatus(MqErrorRecord mqErrorRecord) {
                log.error("MqErrorRecordFeignBackFactory-updateStatus:",throwable);
                return Result.error(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
            }
        };
    }
}
