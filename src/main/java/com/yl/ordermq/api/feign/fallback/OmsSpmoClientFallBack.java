package com.yl.ordermq.api.feign.fallback;

import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.order.api.dto.fee.SpmCommonComCostDTO;
import com.yl.order.api.vo.fee.SpmCommonComCostVO;
import com.yl.ordermq.api.feign.OmsSpmoClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OmsSpmoClientFallBack implements FallbackFactory<OmsSpmoClient> {

    @Override
    public OmsSpmoClient create(Throwable throwable) {
        return new OmsSpmoClient() {
            @Override
            public Result<SpmCommonComCostVO> getComCost(SpmCommonComCostDTO spmCommonComCostDTO) {
                log.error("OmsSpmoClientFallBack-getComCost-Error:", throwable);
                throw new BusinessException(throwable);
            }
        };
    }
}
