//package com.yl.ordermq.api.holder;
//
//import com.yl.ordermq.api.constans.OrderMqConstants;
//import com.yl.ordermq.api.service.IRedisLockService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import java.util.List;
//
///**
// * 云路供应链科技有限公司 版权所有 @Copyright 2020
// * author: songxg <br>
// * description: RedisClean <br>
// * date: 2021-04-02 9:26 <br>
// */
//@Component
//@Slf4j
//public class RedisClean {
//    @Autowired
//    private IRedisLockService redisLockService;
//    @PostConstruct
//    private void init(){
//        System.out.println(redisLockService.setAndExpireIfAbsent(OrderMqConstants.ORDER_MQ_LOCK_BQ_KEY_PREFEX + "UT0000346575815",60*60*2));
//        System.out.println(redisLockService.delete(OrderMqConstants.ORDER_MQ_LOCK_BQ_KEY_PREFEX + "UT0000346575815"));
//        System.out.println(redisLockService.getKeys(OrderMqConstants.ORDER_MQ_LOCK_BQ_KEY_PREFEX + "*"));
//        List<String> bqKeys = redisLockService.getKeys(OrderMqConstants.ORDER_MQ_LOCK_BQ_KEY_PREFEX + "*");
//        log.info("bqKeys size:{}",bqKeys.size());
//        bqKeys.stream().forEach(bqKey ->{
//            System.out.println(redisLockService.delete(bqKey));
//        });
//        List<String> jmsKeys = redisLockService.getKeys(OrderMqConstants.ORDER_MQ_LOCK_BQ_KEY_PREFEX + "*");
//        log.info("jmsKeys size:{}",jmsKeys.size());
//        jmsKeys.stream().forEach(jmsKey ->{
//            System.out.println(redisLockService.delete(OrderMqConstants.ORDER_MQ_LOCK_BQ_KEY_PREFEX + "UT0000346575815"));
//        });
//    }
//}
