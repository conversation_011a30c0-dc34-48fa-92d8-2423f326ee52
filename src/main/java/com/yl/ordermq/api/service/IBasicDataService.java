package com.yl.ordermq.api.service;

import com.yl.ordermq.api.vo.basicdata.BasicDataVO;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description: 基础数据
 * @Author: 陈绪军
 * @Date: 2021-08-07 11:11
 */
public interface IBasicDataService {

    /**
     * 通过编号获取产品类型
     *
     * @param code
     * @return
     */
    BasicDataVO getProductTypeByCode(String code);

    /**
     * 通过编号获取服务类型
     *
     * @param code
     * @return
     */
    BasicDataVO getServiceMethodByCode(String code);

    /**
     * 用个编号获取结算方式
     *
     * @param code
     * @return
     */
    BasicDataVO getPaymentMannerByCode(String code);

    /**
     * 获取物品类型
     *
     * @param code
     * @return
     */
    BasicDataVO getGoodsTypeByCode(String code);

    /**
     * 获取支付方式
     *
     * @param paidModeCode
     * @return
     */
    BasicDataVO getPayTypeByCode(String paidModeCode);
}
