package com.yl.ordermq.api.service;

import com.yl.order.api.dto.DeliverNetworkInfoQueryDTO;
import com.yl.order.api.dto.DisPatchCodeDto;
import com.yl.order.api.vo.DeliverNetworkInfoVO;
import com.yl.ordermq.api.dto.DeliverNetworkQueryDTO;
import com.yl.ordermq.api.dto.OrderDTO;
import com.yl.ordermq.api.vo.DeliverNetworkVO;
import com.yl.ordermq.api.vo.SegmentCodeRequestVo;
import com.yl.ordermq.api.vo.SegmentCodeResponseVo;
import com.yl.ordermq.api.vo.SysNetworkVO;
import com.yl.ordermq.api.vo.dispatchcode.CrossCodeVo;
import com.yl.ordermq.api.vo.dispatchcode.DispatchCodeVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-11-06 10:07 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */
public interface IDispatchCodeService {

    SegmentCodeResponseVo querySendSegmentCode(OrderDTO orderDTO, String requestId);

    SegmentCodeResponseVo queryDispatch(SegmentCodeRequestVo dispatchCodeDto, String requestId);

    /**
     * <AUTHOR>
     * @Description //TODO 批量获取三段码
     * @Date  2019-12-30
     * @Param disPatchCodeDtos
     * @return java.util.Map<java.lang.String,com.yl.oms.api.vo.DispatchCodeVo>
     */
    CrossCodeVo batchFetch(@RequestBody DisPatchCodeDto disPatchCodeDto, String uuid);

    /**
     * 根据省市区ID及二段码查询网点
     * @param deliverNetworkInfoQueryDTO
     * @return
     */
    DeliverNetworkInfoVO deliverNetworkInfoByPddCode(@RequestBody DeliverNetworkInfoQueryDTO deliverNetworkInfoQueryDTO, String uuid);

    /**
     * 根据一段码，二段码查询派件网点信息
     * @param deliverNetworkQueryDTO
     * @param uuid
     * @return
     */
    Map<String, DeliverNetworkVO> fetchNetworkByCodeBatch(@RequestBody List<DeliverNetworkQueryDTO> deliverNetworkQueryDTO, String uuid);

    // 调度订单取件网点
    void dispatchOrderPickNetwork(OrderDTO orderDTO, String uuid);
}
