package com.yl.ordermq.api.stream.receiver.isv;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * <AUTHOR>
 * @date ：Created in 2020-05-20 10:51
 * @description：ISV(EDI)订单消息监听
 * @modified By：
 * @version: $version$
 */
@Component
public interface IsvOrderInputInterface {

    /** ISV(EDI) */
    String ISV_ORDER_INPUT = "ordermq-ediorder-save-input";

    /** 单个修改EDI订单 */
    String ISV_ORDER_UPDATE_INPUT = "ordermq-ediorder-update-input";

    @Input(ISV_ORDER_INPUT)
    SubscribableChannel saveOrderFromEdi();

    @Input(ISV_ORDER_UPDATE_INPUT)
    SubscribableChannel updateOrderFromEdi();
}
