package com.yl.ordermq.api.trace;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 非 web 请求使用
 * LogTranceIdAspect
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2021/4/30 12:46 下午
 */
@Aspect
@Component
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
public class LogTraceIdAspect {

    @Pointcut("@annotation(TraceIdLog)")
    public void logPointCut() {
    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MDC.put(TraceUtils.UNIQUE_ID, TraceUtils.generateTraceId());
        try {
            return point.proceed();
        } catch (Exception e) {
            log.error("system error:", e);
            throw e;
        } finally {
            MDC.remove(TraceUtils.UNIQUE_ID);
        }
    }

}