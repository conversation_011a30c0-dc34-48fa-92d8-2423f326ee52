package com.yl.ordermq.api.util;

import com.yl.ordermq.api.constans.RedisKeyConstants;
import com.yl.redis.util.RedisUtil;

import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * <AUTHOR>
 * @description :
 * @date ：Created in 2020年5月25日14:02:04
 */
public class SettlementDestinationUtils {
	
	//key为 省市区id拼接
	public static void putArea(Map<String, String> maps) {
        RedisUtil.hPutAll(RedisKeyConstants.SETTLEMENT_AREA, maps);
    }

    public static String getArea(String name) {
        return (String) RedisUtil.hGet(RedisKeyConstants.SETTLEMENT_AREA, name);
    }

  //key为 省市id拼接
    public static void putCity(Map<String, String> maps) {
        RedisUtil.hPutAll(RedisKeyConstants.SETTLEMENT_CITY, maps);
    }

    public static String getCity(String name) {
        return (String) RedisUtil.hGet(RedisKeyConstants.SETTLEMENT_CITY, name);
    }
	
		
}
