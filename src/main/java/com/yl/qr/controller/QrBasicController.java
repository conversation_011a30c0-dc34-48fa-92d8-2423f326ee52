package com.yl.qr.controller;

import com.alibaba.fastjson.JSON;
import com.yl.common.base.annotation.ExcludeInterceptor;
import com.yl.common.base.model.vo.Result;
import com.yl.official.feign.SysEstimateTimeFeignClient;
import com.yl.official.service.IAppBaseDataService;
import com.yl.official.vo.AppBaseDataVO;
import com.yl.official.vo.BasicDataVO;
import com.yl.official.vo.DictionaryVO;
import com.yl.official.vo.lmdm.SysAreaOnlineConfigQueryDTO;
import com.yl.official.vo.lmdm.SysEstimateTimeVO;
import com.yl.qr.dto.AgingCostDTO;
import com.yl.qr.entity.SysArea;
import com.yl.qr.feign.SysAreaOnlineConfigFeignClient;
import com.yl.qr.service.AgingCostService;
import com.yl.qr.utils.QrOrderNoEnCodeUtil;
import com.yl.qr.vo.AgingCostVO;
import com.yl.qr.vo.SysAreaOnlineConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Api(value = "QR基础数据", tags = {"QR基础数据"})
@RestController
@Slf4j
@RequestMapping("/qr/qrBasic")
public class QrBasicController extends QrBaseController {
    @Autowired
    private SysAreaOnlineConfigFeignClient sysAreaOnlineConfigFeignClient;
    @Autowired
    private IAppBaseDataService appBaseDataService;

    @Autowired
    private SysEstimateTimeFeignClient sysEstimateTimeFeignClient;
    @Autowired
    private AgingCostService agingCostService;

    @Value("${StoreOrderUrlPrefix:https://demoqr.jtcargo.co.id/orderModule/order?network=}")
    private String storeOrderUrlPrefix;

    /**
     * 模糊查询省市区
     */
    @PostMapping("/findOnLineConfigSendAreas")
    @ExcludeInterceptor
    public Result<List<SysArea>> findOnLineConfigSendAreas(@RequestBody SysAreaOnlineConfigQueryDTO dto) {
        return sysAreaOnlineConfigFeignClient.findOnLineConfigSendAreas(dto);
    }

    /**
     * 四级地址
     */
    @PostMapping("/findOnLineConfigSendList")
    @ExcludeInterceptor
    public Result<List<SysAreaOnlineConfigVo>> findOnLineConfigSendList(@RequestBody SysAreaOnlineConfigQueryDTO dto) {
        return sysAreaOnlineConfigFeignClient.findOnLineConfigSendList(dto);
    }

    @GetMapping(value = "/list")
    @ApiOperation(value = "基础数据列表", notes = "基础数据列表")
    @ExcludeInterceptor
    public Result<AppBaseDataVO> getAppList() {
        return success(appBaseDataService.getAppList());
    }

    @GetMapping(value = "/getGoodsList")
    @ApiOperation(value = "查询物品类型列表", notes = "查询物品类型列表")
    @ExcludeInterceptor
    public Result<List<BasicDataVO>> getGoodsList() {
        return success(appBaseDataService.getGoodsList());
    }

    @GetMapping(value = "/getPaymentModeList")
    @ApiOperation(value = "查询结算方式列表", notes = "查询结算方式列表")
    @ExcludeInterceptor
    public Result<List<BasicDataVO>> getPaymentModeList() {
        return success(appBaseDataService.getPaymentModeList());
    }

    @GetMapping(value = "/getPayTypeAll")
    @ApiOperation(value = "查询支付方式列表", notes = "查询支付方式列表")
    @ExcludeInterceptor
    public Result<List<BasicDataVO>> getPayTypeAll() {
        return success(appBaseDataService.getPayTypeAll());
    }


    @GetMapping(value = "/getDictionary")
    @ApiOperation(value = "查询数据字典", notes = "查询数据字典")
    @ExcludeInterceptor
    public Result<List<DictionaryVO>> getDictionary(@RequestParam("categoryCode") String categoryCode) {
        return success(appBaseDataService.getDictionaryByCategoryCode(categoryCode));
    }

    @GetMapping(value = "/getProductTypeAll")
    @ApiOperation(value = "查询产品类型", notes = "查询产品类型")
    @ExcludeInterceptor
    public Result<List<BasicDataVO>> getProductTypeAll() {
        return success(appBaseDataService.getProductTypeAll());
    }

    @ApiOperation(value = "根据寄件省市到件省市查询时效", notes = "根据寄件省市到件省市查询时效")
    @ExcludeInterceptor
    @GetMapping("/sysEstimateTime/getAging")
    public Result<List<SysEstimateTimeVO>> getAging(@RequestParam("sendProvinceId") Integer sendProvinceId,
                                                    @RequestParam("sendCityId") Integer sendCityId,
                                                    @RequestParam("dispatchProvinceId") Integer dispatchProvinceId,
                                                    @RequestParam("dispatchCityId") Integer dispatchCityId) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        log.info("requestId==>{},调基础数据，寄件省==>{}，寄件市==>{}，目的地省==>{}，目的地市==>{}", requestId, sendProvinceId, sendCityId, dispatchProvinceId, dispatchCityId);
        List<SysEstimateTimeVO> sysEstimateTime = sysEstimateTimeFeignClient.getAging(sendProvinceId, sendCityId, dispatchProvinceId, dispatchCityId).getData();
        log.info("requestId==>{},查询时效==>{}", requestId, JSON.toJSONString(sysEstimateTime));
        return Result.success(sysEstimateTime);
    }

    /**
     * 运费时效查询
     *
     * @param agingCostDTO
     * @return
     */
    @PostMapping("/agingCost")
    @ExcludeInterceptor
    public Result<List<AgingCostVO>> agingCost(@Valid @RequestBody AgingCostDTO agingCostDTO) {
        log.info("运费时效查询请求入参:{}", JSON.toJSONString(agingCostDTO));
        try {
            agingCostDTO.setVolume(agingCostDTO.getVolume().multiply(new BigDecimal(1000000)));
            List<AgingCostVO> agingCosts = agingCostService.get(agingCostDTO);
            return Result.success(agingCosts);
        } catch (Exception e) {
            log.error("运费时效查询出现异常", e);
            return Result.error(500, e.getMessage());
        }
    }

    @GetMapping("/generateStoreOrderUrl")
    @ExcludeInterceptor
    public Result<String> generateStoreOrderUrl(@RequestParam("code") String code) {
        try {
            String url = storeOrderUrlPrefix + QrOrderNoEnCodeUtil.enCode(code);
            return Result.success(url);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
