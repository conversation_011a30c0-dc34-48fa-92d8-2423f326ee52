package com.yl.qr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yl.common.base.model.entity.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 系统区域表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-06-03
 */
@Data
@Accessors(chain = true)
@TableName(value = "sys_area")
public class SysArea extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer parentId;//父级ID-所属区域

    private Integer type;//区域类型

    private Integer countryId;//所属国家

    private String code;//区域编码

    private String internationalCode;//国际编码

    private String areaNo;//区号

    private String threeCode;//三字码

    private String cnName;//中文名称

    private String enName;//英文名称

    private String nativeName;//母语名称

    private String zipcode;//邮政编码

    private String abbreviation;//简称

    private String longitude;//经度

    private String latitude;//纬度

    private Integer isEnable;//是否启用

    private Integer isDelete;//是否删除

    private String version;//版本号

    private Integer sort;//排序

    private String dtbName;//大头笔名称

    private Integer noDistrictFlag;//无区县标识 1:无区县,2:有区县

//    private Integer regionalId;//大区ID 关联大区表主键id

//    private String adCode;//高德编码

}
