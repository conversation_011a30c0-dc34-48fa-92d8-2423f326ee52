package com.yl.qr.feign;

import com.yl.common.base.model.vo.Result;
import com.yl.qr.vo.OmsWaybillNoCheckDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/28 13:55
 */
@Component
@FeignClient(name = "ylwaybillouterapi",
        path = "/waybillouterapi")
public interface WaybillOuterFeign {
    /**
     * 校验运单号规则
     * @param dto
     * @return
     */
    @PostMapping("/common/checkWaybillNo")
    Result<Boolean> checkWaybillNo(@RequestBody OmsWaybillNoCheckDTO dto);

}
