package com.yl.qr.feign.fallback;


import com.yl.common.base.model.vo.Result;
import com.yl.qr.feign.WaybillOuterFeign;
import com.yl.qr.vo.OmsWaybillNoCheckDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/27 16:38
 */
@Component
@Slf4j
public class WaybillOuterFeignFallback implements WaybillOuterFeign {
    @Override
    public Result<Boolean> checkWaybillNo(OmsWaybillNoCheckDTO dto) {
        log.error("========================WaybillOuterFeign服务调用[checkWaybillNo]失败========================");
        return null;
    }
}
