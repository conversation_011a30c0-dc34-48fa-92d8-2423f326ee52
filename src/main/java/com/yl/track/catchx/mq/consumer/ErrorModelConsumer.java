package com.yl.track.catchx.mq.consumer;

import com.yl.track.catchx.service.ErrorService;
import com.yl.track.framework.catchx.model.ErrorModel;
import com.yl.track.framework.mq.RocketMQBaseConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import static com.yl.track.framework.mq.ConstantTopic.DEFAULT_CONSUMER_GROUP;
import static com.yl.track.framework.mq.ConstantTopic.ERROR_MODEL_TOPIC;

@Slf4j @Component
@RocketMQMessageListener(topic = ERROR_MODEL_TOPIC, consumerGroup = ERROR_MODEL_TOPIC + DEFAULT_CONSUMER_GROUP)
public class ErrorModelConsumer extends RocketMQBaseConsumer implements RocketMQListener<ErrorModel> {

    private final ErrorService errorService;

    public ErrorModelConsumer(ErrorService errorService) {
        this.errorService = errorService;
    }

    @Override
    public void onMessage(ErrorModel message) {
        errorService.process(message);
    }


}
