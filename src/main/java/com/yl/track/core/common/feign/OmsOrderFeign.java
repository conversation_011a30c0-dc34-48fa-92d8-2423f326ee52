package com.yl.track.core.common.feign;

import com.yl.common.base.model.vo.Result;
import com.yl.track.core.common.vo.OmsOrderApiVO;
import com.yl.track.core.common.vo.OmsOrderOriginalAddressVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/3 9:39
 */
@Component
@FeignClient(name = "ylorderapi", path = "/orderapi"
)
public interface OmsOrderFeign {
    @GetMapping({"/omsOrder/getOrder/info/{waybill_id}"})
    Result<OmsOrderApiVO> getOrderInfoByWaybillId(@PathVariable("waybill_id") String waybillId);

    @GetMapping("/http/order/getOrderSendCodeByWaybillId")
    Result<String> getOrderSendCodeByWaybillId(@RequestParam(name = "waybillId") String waybillId);

    @GetMapping("/originalAddress/getOriginalAddress")
    Result<OmsOrderOriginalAddressVo> getOriginalAddress(@RequestParam(name = "waybillId") String waybillId);
}
