package com.yl.track.core.common.feign;

import com.yl.common.base.model.vo.Result;
import com.yl.track.core.common.vo.SysNetworkVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(name = "yltmsqueryapi", path = "/tmsqueryapi/sysNetwork")
public interface TmsQueryFeign {
    @GetMapping("/getSysNetworkByCode")
    Result<SysNetworkVO> getSysNetworkByCode(@RequestParam String code);

}
