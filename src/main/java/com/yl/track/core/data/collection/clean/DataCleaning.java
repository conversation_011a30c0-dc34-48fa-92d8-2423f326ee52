package com.yl.track.core.data.collection.clean;

import com.alibaba.fastjson.JSONObject;
import com.yl.track.core.common.enums.TrackNodeEnum;
import com.yl.track.core.common.service.BillApiAccountService;
import com.yl.track.core.data.preprocess.chain.handler.ext.SignRiskControlExtHandler;
import com.yl.track.framework.constant.Constant;
import com.yl.track.framework.util.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class DataCleaning {

    @Autowired(required = false)
    private DataCleaningExt dataCleaningExt;

    @Autowired
    private SignRiskControlExtHandler signRiskControlExtHandler;

    @Autowired
    private BillApiAccountService billApiAccountService;

    public Boolean cleaning(JSONObject jsonObject, TrackNodeEnum trackNodeEnum) {
        String uuid = ThreadLocalUtil.getStr(Constant.UUID);
        String billCode = jsonObject.getString("billCode");
        String apiAccount = billApiAccountService.getApiAccount(billCode);

        if (StringUtils.isNotEmpty(apiAccount)) {

            // 判断是否是弃货签收
            if (TrackNodeEnum.SIGN.getCode() == trackNodeEnum.getCode() && "102".equals(jsonObject.getString("scanTypeCode"))) {

                // 如果是弃货签收，查看风控开关是否开启
                boolean abandonedGoodsSignedSwitch = signRiskControlExtHandler.abandonedGoodsSigned(billCode, apiAccount);
                if (abandonedGoodsSignedSwitch) {
                    log.error(Constant.LOG_PREFIX + "DataCleaning - 等于弃货签收,不处理当前轨迹，运单号：{}", uuid, billCode);
                    return false;
                }
            }

            jsonObject.put("apiAccount", apiAccount);
            return true;
        }

        if (Objects.nonNull(dataCleaningExt)) {
            return dataCleaningExt.cleaningExt(jsonObject, trackNodeEnum, uuid);
        }
        log.error(Constant.LOG_PREFIX + "DataCleaning - apiAccount缓存不存在，运单号：{}", uuid, billCode);
        return false;
    }

    public String getApiAccount(String billCode) {
        return billApiAccountService.getApiAccount(billCode);
    }


}
