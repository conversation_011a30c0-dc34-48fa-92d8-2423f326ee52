package com.yl.track.core.data.preprocess.chain.handler;

import com.alibaba.fastjson.JSONObject;
import com.yl.track.core.common.entity.PlatformTrackMsg;
import com.yl.track.core.common.entity.PreTrackMsg;
import com.yl.track.core.common.enums.TrackNodeEnum;
import com.yl.track.core.data.preprocess.chain.BaseHandler;
import com.yl.track.core.data.preprocess.chain.HandlerConfig;
import com.yl.track.core.data.preprocess.chain.handler.ext.ProblemPieceExtHandler;
import com.yl.track.setting.dao.DataMappingDao;
import com.yl.track.setting.entity.DataMapping;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@Order(HandlerConfig.ProblemPiece_ORDER)
public class ProblemPieceHandler extends BaseHandler {

    @Autowired
    private DataMappingDao dataMappingDao;

    @Autowired
    private ProblemPieceExtHandler problemPieceExtHandler;

    @Override
    protected boolean handleProcessor(PlatformTrackMsg msg, PreTrackMsg preTrackMsg) {
        if (TrackNodeEnum.ISSUE.getCode() != msg.getTrackCode()) {
            return true;
        }
        Boolean result = problemPieceExtHandler.extHandler(msg, preTrackMsg);
        if (null != result) {
            return result; // 如果扩展点返回非null值，直接使用该结果
        }
        // 如果扩展点返回null，继续执行下面的逻辑
        List<DataMapping> list = dataMappingDao.getList(msg.getApiAccount(), 1);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(msg.getPayload());
        String proleTypeCode = jsonObject.getString("proleTypeCode");
        List<DataMapping> proleTypeList = list.stream().filter(dataMapping -> dataMapping.getInternalDataCode().contains(proleTypeCode)).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(proleTypeList);
    }
}
