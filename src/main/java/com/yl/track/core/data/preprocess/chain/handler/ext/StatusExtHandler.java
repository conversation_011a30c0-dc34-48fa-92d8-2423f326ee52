package com.yl.track.core.data.preprocess.chain.handler.ext;

import com.google.common.collect.Lists;
import com.yl.track.core.common.entity.PlatformTrackMsg;
import com.yl.track.core.common.entity.PreTrackMsg;

import java.util.List;

public interface StatusExtHandler {


    /**
     * 扩展处理方法
     *
     * @param msg         平台轨迹消息
     * @param preTrackMsg 预处理轨迹消息
     * @return Boolean - true/false 直接返回结果，null 表示继续执行原有逻辑
     */
    default Boolean extHandlerBefore(PlatformTrackMsg msg, PreTrackMsg preTrackMsg) {
        return null;
    }


    default boolean extHandlerAfter(PlatformTrackMsg msg, PreTrackMsg preTrackMsg) {
        return true;
    }

    default List<String> waybillQueryColumns(){
        return Lists.newArrayList("packageChargeWeight", "freight", "totalFreight", "insuredFee", "orderId", "customerOrderId", "waybillNo", "receiptNo");
    }

}
