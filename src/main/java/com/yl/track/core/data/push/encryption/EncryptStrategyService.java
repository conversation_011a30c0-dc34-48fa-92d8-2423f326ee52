package com.yl.track.core.data.push.encryption;

import com.google.common.collect.Maps;
import com.yl.track.core.common.annotation.EncryptStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.AnnotationUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Import(StrategyContext.class)
@Configuration
public class EncryptStrategyService {

    private final Map<String, EncryptionStrategy> encryptionStrategyMap = Maps.newHashMap();

    @Autowired
    private List<EncryptionStrategy> encryptionStrategyList;

    @PostConstruct
    public void init() {
        for (EncryptionStrategy encryptionStrategy : encryptionStrategyList) {
            EncryptStrategy annotation = AnnotationUtils.getAnnotation(encryptionStrategy.getClass(), EncryptStrategy.class);
            if (Objects.isNull(annotation)) {
                continue;
            }
            encryptionStrategyMap.put(annotation.value(), encryptionStrategy);
        }
    }


    public Map<String, Object> executeStrategy(String mode, String body, Map<String, String> headers) {
        EncryptionStrategy encryptionStrategy = encryptionStrategyMap.get(mode);
        if (Objects.isNull(encryptionStrategy)) {
            return Maps.newHashMap();
        }
        return encryptionStrategy.encryption(body, headers);
    }


}
