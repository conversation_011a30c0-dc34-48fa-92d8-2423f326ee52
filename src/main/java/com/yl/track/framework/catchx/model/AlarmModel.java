package com.yl.track.framework.catchx.model;

import lombok.Data;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class AlarmModel {

    public static final String VARIABLE_NAME_ERROR = "ERROR";

    private Long alarmId;

    private String scenario;

    private String businessNo;

    private String eventTime;

    /** 默认模板ID */
    private List<String> channels = Collections.singletonList("1");

    private Map<String, Object> variables = new HashMap<>();

    public void putVariable(String name, Object value) {
        this.variables.put(name, value);
    }

    public Object getVariable(String name) {
        return variables.get(name);
    }

    public void setError(String error) {
        putVariable(VARIABLE_NAME_ERROR, error);
    }

    public String getError() {
        return (String) getVariable(VARIABLE_NAME_ERROR);
    }

}
