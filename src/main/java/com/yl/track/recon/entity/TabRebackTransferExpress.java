package com.yl.track.recon.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description tab_reback_transfer_express
 * @date 2024-06-15
 */
@Data
public class TabRebackTransferExpress implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 订单号
     */
    private String waybillNo;

    /**
     * 申请类型
     */
    private Integer applyTypeCode;

    /**
     * 转寄运单号
     */
    private String transferWaybillNo;

    /**
     * 审核时间
     */
    private LocalDateTime examineTime;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 撤销时间
     */
    private LocalDateTime revokeTime;

    /**
     * 驳回时间
     */
    private LocalDateTime rejectTime;

    /**
     * 状态 1待审核 2已审核 3 取消申请 4.驳回
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 面单是否已打印 1是 0否
     */
    private Integer printFlag;

    /**
     * 面单打印次数
     */
    private Integer printCount;

    /**
     * 打印时间
     */
    private LocalDateTime printTime;

    /**
     * 订单来源
     */
    private String orderSourceCode;

    /**
     * 客户订单编号
     */
    private String customerOrderId;

    /**
     * 1未审核 2审核通过 3审核异常
     */
    private Integer trackReconStatus;

    /**
     * 0 未删除
     */
    private Integer isDelete;

    /**
     * 修改时间字段作废
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间字段作废
     */
    private LocalDateTime createTime;

}