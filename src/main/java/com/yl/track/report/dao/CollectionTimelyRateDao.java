package com.yl.track.report.dao;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yl.common.base.exception.BusinessException;
import com.yl.track.report.entity.CollectionTimelyRate;
import com.yl.track.report.framework.constant.Constant;
import com.yl.track.report.framework.dao.BaseDao;
import com.yl.track.report.framework.util.DateUtils;
import com.yl.track.report.framework.util.ThreadLocalUtil;
import com.yl.track.report.vo.CollectionTimelyRateVo;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@Repository
public class CollectionTimelyRateDao extends BaseDao<CollectionTimelyRate> {

    public List<CollectionTimelyRate> listByWeekMonthDate(CollectionTimelyRateVo vo) {
        if (1 == vo.getDateType()) {
            return null;
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        TermsAggregationBuilder aggregation = AggregationBuilders.terms("group_by_collection_timely_rate")
                .field("orderSourceCode")
                .subAggregation(AggregationBuilders.terms("group_by_sender_province").field("senderProvinceName")
                        .subAggregation(AggregationBuilders.terms("group_by_sender_city").field("senderCityName")
                                .subAggregation(AggregationBuilders.terms("group_by_sender_area").field("senderAreaName")
                                        .subAggregation(AggregationBuilders.terms("group_by_proxy_area").field("proxyAreaCode")
                                                .subAggregation(AggregationBuilders.sum("total_order_sum").field("orderSum"))
                                                .subAggregation(AggregationBuilders.sum("total_pickup_sum").field("pickupSum"))
                                                .subAggregation(AggregationBuilders.sum("total_cancel_sum").field("cancelSum"))
                                                .subAggregation(AggregationBuilders.sum("total_pickup_fail_sum").field("pickupFailSum"))
                                                .subAggregation(AggregationBuilders.sum("total_current_day_pickup_sum").field("currentDayPickupSum"))
                                                .subAggregation(AggregationBuilders.sum("total_next_day_pickup_sum").field("nextDayPickupSum"))
                                        )
                                )
                        )
                );
        searchSourceBuilder.aggregation(aggregation);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("date");
        if (2 == vo.getDateType()) {
            rangeQueryBuilder.gte(vo.getStartWeekDate());
            rangeQueryBuilder.lte(vo.getEndWeekDate());
        } else if (3 == vo.getDateType()) {
            rangeQueryBuilder.gte(vo.getStartMonthDate());
            rangeQueryBuilder.lte(vo.getEndMonthDate());
        } else {
            throw new BusinessException("日期类型错误");
        }
        boolQueryBuilder.filter(rangeQueryBuilder);
        searchSourceBuilder.query(boolQueryBuilder);
        Aggregations aggregations = searchReport(searchSourceBuilder);

        List<CollectionTimelyRate> results = Lists.newArrayList();

        Terms orderSourceCodeTerms = aggregations.get("group_by_collection_timely_rate");
        for (Terms.Bucket orderSourceBucket : orderSourceCodeTerms.getBuckets()) {
            String orderSourceCode = orderSourceBucket.getKeyAsString();

            Terms senderProvinceTerms = orderSourceBucket.getAggregations().get("group_by_sender_province");
            for (Terms.Bucket senderProvinceBucket : senderProvinceTerms.getBuckets()) {
                String senderProvinceName = senderProvinceBucket.getKeyAsString();

                Terms senderCityTerms = senderProvinceBucket.getAggregations().get("group_by_sender_city");
                for (Terms.Bucket senderCityBucket : senderCityTerms.getBuckets()) {
                    String senderCityName = senderCityBucket.getKeyAsString();

                    Terms senderAreaTerms = senderCityBucket.getAggregations().get("group_by_sender_area");
                    for (Terms.Bucket senderAreaBucket : senderAreaTerms.getBuckets()) {
                        String senderAreaName = senderAreaBucket.getKeyAsString();

                        Terms proxyAreaTerms = senderAreaBucket.getAggregations().get("group_by_proxy_area");
                        for (Terms.Bucket proxyAreaBucket : proxyAreaTerms.getBuckets()) {
                            String proxyAreaCode = proxyAreaBucket.getKeyAsString();

                            Sum orderSumAgg = proxyAreaBucket.getAggregations().get("total_order_sum");
                            Sum pickupSumAgg = proxyAreaBucket.getAggregations().get("total_pickup_sum");
                            Sum cancelSumAgg = proxyAreaBucket.getAggregations().get("total_cancel_sum");
                            Sum pickupFailSumAgg = proxyAreaBucket.getAggregations().get("total_pickup_fail_sum");
                            Sum currentDayPickupSumAgg = proxyAreaBucket.getAggregations().get("total_current_day_pickup_sum");
                            Sum nextDayPickupSumAgg = proxyAreaBucket.getAggregations().get("total_next_day_pickup_sum");

                            CollectionTimelyRate result = new CollectionTimelyRate();
                            result.setOrderSourceCode(orderSourceCode);
                            result.setSenderProvinceName(senderProvinceName);
                            result.setSenderCityName(senderCityName);
                            result.setSenderAreaName(senderAreaName);
                            result.setProxyAreaCode(proxyAreaCode);
                            result.setOrderSum((int) orderSumAgg.getValue());
                            result.setPickupSum((int) pickupSumAgg.getValue());
                            result.setCancelSum((int) cancelSumAgg.getValue());
                            result.setPickupFailSum((int) pickupFailSumAgg.getValue());
                            result.setCurrentDayPickupSum((int) currentDayPickupSumAgg.getValue());
                            result.setNextDayPickupSum((int) nextDayPickupSumAgg.getValue());
                            results.add(result);
                        }
                    }
                }
            }
        }
        return results;
    }


    public List<CollectionTimelyRate> listByDayDate(CollectionTimelyRateVo vo) {
        if (1 != vo.getDateType()) {
            return null;
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("date", vo.getDayDate()));
        searchSourceBuilder.query(boolQueryBuilder);
        return list(searchSourceBuilder);
    }


    public void saveOrUpdateCollectionTimelyRate(CollectionTimelyRate object, boolean updateCurrentDay, boolean updateNexDay) {
        try {
            String newIndexName;

            String id = object.getId();

            CollectionTimelyRate object_ = getById(id);

            if (object_ != null) {
                object.setCreateTime(object_.getCreateTime());
                object.setUpdateTime(DateUtils.esDate());
                // true 不赋值旧数据。直接更新新数据
                if (!updateCurrentDay) {
                    object.setCurrentDayPickupSum(object_.getCurrentDayPickupSum());
                }
                if (!updateNexDay) {
                    object.setNextDayPickupSum(object_.getNextDayPickupSum());
                }

                newIndexName = object_.getIndexName();
            } else {
                String date = DateUtils.esDate();
                object.setCreateTime(date);
                object.setUpdateTime(date);
                newIndexName = indexName;
            }
            object.setIndexName(null);
            // 创建索引请求
            IndexRequest request = new IndexRequest(newIndexName)
                    .id(id)
                    .type("_doc")
                    .source(JSON.toJSONString(object), XContentType.JSON); // 设置文档内容
            // 执行索引请求
            IndexResponse index = client.index(request, RequestOptions.DEFAULT);
            DocWriteResponse.Result result = index.getResult();
            object.setIndexName(newIndexName);
            log.info(Constant.LOG_PREFIX + "保存OR修改后揽收及时率报表数据状态：{}", ThreadLocalUtil.getStr(Constant.UUID), result.getLowercase());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.info("保存OR修改后ES 揽收及时率报表数据异常：", e);
            throw new RuntimeException(e);
        }
    }


}
