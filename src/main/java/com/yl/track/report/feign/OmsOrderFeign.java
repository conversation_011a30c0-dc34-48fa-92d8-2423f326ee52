package com.yl.track.report.feign;

import com.alibaba.fastjson.JSONObject;
import com.yl.common.base.model.vo.Result;
import com.yl.track.report.vo.OrderReportVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/3 9:39
 */
@Component
@FeignClient(name = "ylordera<PERSON>", path = "/orderapi"
)
public interface OmsOrderFeign {

    @GetMapping({"/omsOrder/getOrder/info/{waybill_id}"})
    Result<JSONObject> getOrderInfoByWaybillId(@PathVariable("waybill_id") String waybillId);

    @PostMapping("/orderReport/sumByOrderSource")
    Result<List<OrderReportVo>> sumByOrderSource(@RequestBody OrderReportVo reportVo);

}
