package com.yl.track.report.framework.annotation;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Field {

    String type();

    boolean index() default true;

    String format() default "";

    String analyzer() default "";

    int nullValue() default -9999;

}
