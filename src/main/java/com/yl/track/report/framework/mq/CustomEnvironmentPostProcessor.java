package com.yl.track.report.framework.mq;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.MutablePropertySources;

import java.util.HashMap;
import java.util.Map;

import static com.yl.track.report.mq.ConstantTopic.DEFAULT_PRODUCER_GROUP;


public class CustomEnvironmentPostProcessor implements EnvironmentPostProcessor {

    @Override  
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // 在这里添加或修改属性源  
        MutablePropertySources propertySources = environment.getPropertySources();

        // 创建一个新的属性Map
        Map<String, Object> customProperties = new HashMap<>();
        customProperties.put("rocketmq.producer.group", DEFAULT_PRODUCER_GROUP);

        // 创建一个新的PropertySource，并指定一个唯一的名字
        MapPropertySource customPropertySource = new MapPropertySource("CUSTOM_PROPERTIES", customProperties);
        propertySources.addLast(customPropertySource);
    }
}