package com.yl.track.report.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-09-03 11:03 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)

@ApiModel(value = "OrderReport响应对象", description = "报表统计对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderReportVo {

    private String date;

    private String startDate;

    private String endDate;

    private String orderSourceCode;

    private String orderSourceName;

    private String senderProvinceName;

    private String senderCityName;

    private String senderAreaName;

    private String proxyAreaCode;

    private Integer orderSum;

    private Integer cancelSum;

    private Integer pickupFailSum;


}
