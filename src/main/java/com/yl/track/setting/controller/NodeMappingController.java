package com.yl.track.setting.controller;

import com.alibaba.fastjson.JSONObject;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.track.framework.entity.Page;
import com.yl.track.setting.entity.NodeMapping;
import com.yl.track.setting.service.NodeMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RequestMapping("/track-core/nodeMapper")
@RestController
public class NodeMappingController {

    @Autowired
    private NodeMappingService nodeMappingService;


    @PostMapping("/page")
    public Result<Page<NodeMapping>> page(@RequestBody NodeMapping nodeMapping) {
        log.info("nodeMapper->page 传参：{}", JSONObject.toJSONString(nodeMapping));
        return Result.success(nodeMappingService.page(nodeMapping));
    }

    @GetMapping("/defaultTemplate")
    public Result<NodeMapping> defaultTemplate(@RequestParam("jtTrackCode") Integer jtTrackCode){
        log.info("nodeMapper->defaultTemplate 传参：{}", jtTrackCode);
        return Result.success(nodeMappingService.defaultTemplate(jtTrackCode));
    }

    @PostMapping("/saveOrUpdate")
    public Result<Boolean> save(@RequestBody NodeMapping nodeMapping) {
        log.info("nodeMapper->saveOrUpdate 传参：{}", JSONObject.toJSONString(nodeMapping));
        return Result.success(nodeMappingService.save(nodeMapping));
    }

    @GetMapping("/delete")
    public Result<Boolean> delete(@RequestParam("id") Long id) {
        log.info("nodeMapper->delete 传参：{}", id);
        return Result.success(nodeMappingService.delete(id));
    }

    @GetMapping("/detail")
    public Result<NodeMapping> detail(@RequestParam("id") Long id) {
        log.info("nodeMapper->detail 传参：{}", id);
        return Result.success(nodeMappingService.detail(id));
    }

}
