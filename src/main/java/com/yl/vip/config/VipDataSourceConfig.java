package com.yl.vip.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.plugins.PerformanceInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 功能描述:
 * oracle数据源
 *
 * @since: 1.0.0
 * @Author:luhong
 * @Date: 2020/10/21 17:58
 */
@Configuration
@MapperScan(basePackages = "com.yl.vip.mapper", sqlSessionFactoryRef = "vipSqlSessionFactory")
public class VipDataSourceConfig {

    /**
     * 分页插件
     */
    @Primary
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

	@Primary
    @Bean(name = "vipDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.oracle.vip", ignoreInvalidFields = true)
    public DataSource oracleDataSource() {
        return new DruidDataSource();
    }


    //会话工厂
	@Primary
    @Bean(name = "vipSqlSessionFactory")
    public SqlSessionFactory mybatisSqlSessionFactory(@Qualifier("vipDataSource") DataSource vipDataSource) throws Exception {
        MybatisSqlSessionFactoryBean sf = new MybatisSqlSessionFactoryBean();
        sf.setDataSource(vipDataSource);
        sf.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/*Mapper.xml"));
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setJdbcTypeForNull(JdbcType.NULL);
        //mybatisplus分页插件
        sf.setPlugins(new Interceptor[]{
                paginationInterceptor()
        });
        sf.setConfiguration(configuration);
        return sf.getObject();
    }


    //事务管理器
	@Primary
    @Bean(name = "vipTransactionManager")
    public DataSourceTransactionManager mybatisTransactionManager(@Qualifier("vipDataSource") DataSource vipDataSource) throws Exception {
        return new DataSourceTransactionManager(vipDataSource);
    }

}
