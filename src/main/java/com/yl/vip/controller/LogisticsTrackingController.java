package com.yl.vip.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.vip.dto.ops.PodTrackingInnerQueryDto;
import com.yl.vip.dto.ops.PodTrackingOuterQueryDto;
import com.yl.vip.dto.ops.PodTrackingQueryDto;
import com.yl.vip.enums.OperationTypeEnum;
import com.yl.vip.enums.TrackingTypeEnum;
import com.yl.vip.feign.OpsPodTrackingClient;
import com.yl.vip.feign.PodTrackingClient;
import com.yl.vip.vo.ops.PodTrackingListVO;
import com.yl.vip.vo.ops.PodTrackingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-08-13
 */
@Api(value = "快件跟踪", tags = {"快件跟踪"})
@RestController
@RequestMapping("/logisticsTracking")
@Slf4j
public class LogisticsTrackingController extends AppBaseController {

    @Autowired
    private PodTrackingClient podTracingClient;

    @Autowired
    private OpsPodTrackingClient opsPodTrackingClient;


    @PostMapping(value = "/getDetailByWaybillNos")
    public Result logisticsTrackingByWaybillNo(@RequestBody PodTrackingQueryDto dto) {
        if (dto.getKeywordList() == null || dto.getKeywordList().size() == 0) {
            throw new BusinessException(ResultCodeEnum.PARAMS_NOT_COMPLETE);
        }
        dto.setTypeEnum(TrackingTypeEnum.CUSTOMER);
        List<PodTrackingListVO> vos = opsPodTrackingClient.podTrackingList(dto).result();
        for (PodTrackingListVO vo : vos) {
            List<PodTrackingVO> podTrackingVOS = vo.getDetails();
            if (CollectionUtils.isNotEmpty(podTrackingVOS)) {
                List<PodTrackingVO> podTrackingVOList = podTrackingVOS.stream().filter(podTrackingVO ->
                        (OperationTypeEnum.ABNORMAL_PIECE_SCAN.equals(podTrackingVO.getOperationTypeEnum())
                                && !"16".equals(podTrackingVO.getAbnormalPieceCode())) ||
                                OperationTypeEnum.REMAIN_STORAGE_SCAN.equals(podTrackingVO.getOperationTypeEnum())).collect(Collectors.toList());
                podTrackingVOS.removeAll(podTrackingVOList);
            }
        }
        return success(vos);
    }



    /**
     * 快件跟踪V2k
     *
     * @param waybillNo
     * @return
     */
    @ApiOperation(value = "快件跟踪", notes = "快件跟踪")
    @PostMapping(value = "/v2/getDetailByWaybillNo")
    public Result<Object> getDetailByWaybillNoV2(@RequestBody List<String> waybillNo) {
        if (waybillNo == null ||waybillNo.size() == 0) {
            throw new BusinessException(ResultCodeEnum.PARAMS_NOT_COMPLETE);
        }
        String language = request.getHeader("language");
        PodTrackingOuterQueryDto podTrackingOuterQueryDto = new PodTrackingOuterQueryDto();
        podTrackingOuterQueryDto.setKeywordList(waybillNo);
        podTrackingOuterQueryDto.setLangType(language);
        log.info("调用操作物流轨迹传参：{}",podTrackingOuterQueryDto);
//        List<com.yl.vip.vo.PodTrackingListVO> listResult = podTracingClient.outerLeywordList(podTrackingOuterQueryDto).result();
//        listResult.forEach(a->{
//            if(a.getDetails() == null){
//                a.setDetails(new ArrayList<>());
//            }
//        });

        List<com.yl.vip.vo.PodTrackingListVO> listResult = podTracingClient.outerLeywordList(podTrackingOuterQueryDto).result();
        listResult.forEach(a -> {
            if (CollectionUtils.isEmpty(a.getDetails())) {
                a.setDetails(new ArrayList<>());
            } else {
                trackingFilter(a);
                List<String> picUrlList = new ArrayList<>();
                for (com.yl.vip.vo.PodTrackingVO podTrackingVO : a.getDetails()) {
                    List<String> picUrl = podTrackingVO.getPicUrl();
                    if (CollectionUtils.isNotEmpty(picUrl)) {
                        picUrlList.addAll(picUrl);
                    }
                }
                a.setPicUrlList(picUrlList);
            }
        });
        return success(listResult);
    }

    private void trackingFilter(com.yl.vip.vo.PodTrackingListVO podTrackingListVO) {
        List<com.yl.vip.vo.PodTrackingVO> list = Lists.newArrayList();
        for (com.yl.vip.vo.PodTrackingVO detail : podTrackingListVO.getDetails()) {
            if (detail.getScanTypeName().contains("弃货签收")) {
                continue;
            }
            list.add(detail);
        }
        podTrackingListVO.setDetails(list);
    }

    @ApiOperation(value = "快件跟踪", notes = "快件跟踪")
    @PostMapping(value = "/waybillList")
    public Result<List<String>> waybillList(@RequestParam("waybillNo") String waybillNo){
        PodTrackingInnerQueryDto dto=new PodTrackingInnerQueryDto();
        List<String> keywordList = new LinkedList<>();
        keywordList.add(waybillNo);
        dto.setKeywordList(keywordList);
        log.info("podTracingClient.waybillList req:{}", JSON.toJSON(dto));
        Result<List<String>> listResult = podTracingClient.waybillList(dto);
        log.info("podTracingClient.waybillList resp:{}", JSON.toJSON(listResult));
        return listResult;
    }


}

