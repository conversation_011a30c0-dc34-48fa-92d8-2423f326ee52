package com.yl.vip.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.enums.EnableEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.JsonUtils;
import com.yl.common.base.util.YlPreconditions;
import com.yl.vip.dto.*;
import com.yl.vip.enums.exception.ServiceErrCodeEnum;
import com.yl.vip.service.IAppAreaService;
import com.yl.vip.service.IOrderService;
import com.yl.vip.utils.JasperPrinterUtils;
import com.yl.vip.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.JasperPrint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.UUID;


@Api(value = "订单", tags = {"订单"})
@RestController
@RequestMapping("/order")
@Slf4j
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderController extends AppBaseController {

    private final IOrderService orderService;
    private final IAppAreaService appAreaService;

    /**
     * 查询大客户订单列表
     *
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询大客户订单列表", notes = "查询大客户订单列表")
    public Result<Page<OmsOrderApiVO>> getPages(@RequestBody OmsOrderApiQueryDto omsOrderApiQueryDto) {
        String language = request.getHeader("language");
        return success(orderService.getPages(omsOrderApiQueryDto, omsOrderApiQueryDto.getCurrent().intValue(), omsOrderApiQueryDto.getSize().intValue(), language));
    }


    /**
     * 根据订单状态查询分组总数
     *
     * @return
     */
    @PostMapping("/totalGroup")
    @ApiOperation(value = "根据订单状态查询分组总数", notes = "根据订单状态查询分组总数")
    public Result<List<OmsOrderStatusGroupVO>> getTotalGroupByOrderStatus(@RequestBody OmsOrderApiQueryDto omsOrderApiQueryDto) {
        String language = request.getHeader("language");
        return success(orderService.getTotalGroupByOrderStatus(omsOrderApiQueryDto, language));
    }


    /**
     * 查询订单详情
     *
     * @return
     */
    @GetMapping("/detailOrder")
    @ApiOperation(value = "查询订单详情", notes = "查询订单详情")
    public Result<OmsOrderApiVO> getDetailById(@RequestParam Long id) {
        return success(orderService.getDetailById(id));
    }

    /**
     * 更新订单
     *
     * @return
     */
    @PostMapping("update")
    @ApiOperation(value = "更新订单", notes = "更新订单")
    public Result<OmsOrderUpdateApiVO> updateOrder(@RequestBody OmsOrderUpdateApiDTO omsOrderUpdateApiDTO) {
        return success(orderService.updateOrder(omsOrderUpdateApiDTO));
    }

    /**
     * 取消订单
     *
     * @return com.yl.common.base.model.vo.Result<com.yl.oms.api.vo.OmsOrderCancelApiVO>
     * @Param omsOrderCancelApiDTO
     **/
    @PostMapping("/cancel")
    @ApiOperation(value = "取消订单", notes = "取消订单")
    public Result<OmsOrderCancelApiVO> cancelOrder(@RequestBody OmsOrderCancelApiDTO omsOrderCancelApiDTO) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        long startTime = System.currentTimeMillis();
        log.info("取消订单-requestId==>{},入参==>{}", requestId, JSON.toJSONString(omsOrderCancelApiDTO));
        List<Long> orderIds = omsOrderCancelApiDTO.getOrderIds();

        OmsOrderApiVO vo = orderService.getDetailById(orderIds.get(0));
        log.info("取消订单-requestId==>{},查询订单详情==>{}", requestId, JSON.toJSONString(vo));
        //三方下单不做数据越权校验
        if (ObjectUtil.isNotNull(vo) && "D02".equals(vo.getOrderSourceCode())) {
            authVerify(vo.getCreateBy());
//            if(vo.getNeedDispatch() == 1){
//                throw new BusinessException("调度单不允许取消!");
//            }
        }

        log.info("取消订单-requestId==>{},数据越权校验耗时==>{}ms", requestId, System.currentTimeMillis() - startTime);
        OmsOrderCancelApiVO apiVOvo = orderService.cancelOrder(omsOrderCancelApiDTO);
        log.info("取消订单-requestId==>{},总耗时==>{}ms", requestId, System.currentTimeMillis() - startTime);
        return success(apiVOvo);
    }

    /**
     * 删除订单
     *
     * @param
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除订单", notes = "删除订单")
    public Result<OmsOrderDeleteApiVO> delete(@RequestParam Long orderId) {
        return success(orderService.delete(orderId));
    }

    /**
     * 批量删除订单
     *
     * @param
     */
    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除订单", notes = "批量删除订单")
    public Result batchDelete(@RequestBody List<Long> list) {
        return success(orderService.batchDelete(list));
    }


    /**
     * 功能描述:
     * 异步导出订单
     *
     * @param orderExcelDTO
     * @return:void
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-01-11 15:39
     */
    @PostMapping("/asyExportOrder")
    @ApiOperation(value = "异步导出Excel", notes = "异步导出Excel")
    public Result asyExportOrder(@RequestBody OrderExcelDTO orderExcelDTO) {
        String language = request.getHeader("language");
        return orderService.asyExportOrder(orderExcelDTO, getUser(), language);
    }


    /**
     * 新增
     *
     * @param dto
     * @return
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增")
    public Result<OmsOrderApiVO> save(@RequestBody @Validated OrderDTO dto) {
        String requestId = UUID.randomUUID().toString().replace("-", "");//本次请求id
        log.info("新增订单-requestId==>{},收到消息原始报文==>{}", requestId, JSON.toJSONString(dto));
        checkAuthority();
        checkSaveOrder(dto);
        Result<OmsOrderApiVO> result = orderService.save(dto, requestId);
        log.info("新增订单-requestId==>{},响应报文==>{}", requestId, JSON.toJSONString(result));
        return result;
    }

    /**
     * 批量新增
     *
     * @param dto
     * @return
     */
    @PostMapping("/batchAdd")
    @ApiOperation(value = "批量新增", notes = "批量新增")
    public Result<List<OrderDTO>> batchSave(@RequestBody @Validated OrderListDTO dto) {
        String language = request.getHeader("language");
        String requestId = UUID.randomUUID().toString().replace("-", "");//本次请求id
        log.info("批量新增订单-requestId==>{},收到消息原始报文==>{}", requestId, JSON.toJSONString(dto));
        checkAuthority();
        YlPreconditions.checkArgument(dto.getOrders().size() <= 1000, new BusinessException(ServiceErrCodeEnum.ORDERLIST_SIZE_ERROR));
        List<OrderDTO> list = orderService.batchSave(dto, requestId,language);
        log.info("批量新增订单-requestId==>{},响应报文==>{}", requestId, JSON.toJSONString(list));
        return success(list);
    }

    /**
     * 校验操作权限
     */
    private void checkAuthority() {
        if (getUser().getStatus() == EnableEnum.ENABLE.getCode()) {
            throw new BusinessException(ServiceErrCodeEnum.NO_OPERATE_AUTHORITY);
        }
    }

    /**
     * 校验要保存的订单数据
     *
     * @param dto
     */
    private void checkSaveOrder(OrderDTO dto) {
        log.info("校验要保存的订单数据入参:{}", JsonUtils.toJson(dto));
        if (dto.getSenderAreaId().equals(dto.getReceiverAreaId())) {
            if (dto.getSenderDetailedAddress().equals(dto.getReceiverDetailedAddress())) {
                throw new BusinessException(ServiceErrCodeEnum.DETAILEDADDRESS_EQUALS);
            }
        }
        //TODO 暂时屏蔽COD功能
        dto.setCodMoney(null);
        dto.setCodFee(null);
//        dto.setInsured(0);
        //TODO 暂时取消子母件业务，件数统一传1
//        dto.setPackageNumber(1);
        if (dto.getInsured() == 1) {
//            if (CategoryEnum.CUSTOMER_CC_CASH.getCode().equals(dto.getPaymentModeCode())) {
//                //用户选择保价，并且支付方式为到付现结，给出提示 到付件不支持保价
//                throw new BusinessException(ServiceErrCodeEnum.CC_CASH_INSURED_NOT_SUPPORT);
//            }
            YlPreconditions.checkArgument(Objects.nonNull(dto.getDeclaredValue()), new BusinessException(ServiceErrCodeEnum.DECLAREDVALUE_ISNULL));
            YlPreconditions.checkArgument(Objects.nonNull(dto.getInsuredValue()), new BusinessException(ServiceErrCodeEnum.INSUREDVALUE_ISNULL));
        } else {
            dto.setDeclaredValue(null);
            dto.setInsuredValue(null);
        }
        if (!appAreaService.check(dto.getSenderProvinceId(), dto.getSenderCityId(), dto.getSenderAreaId(), dto.getSenderTownshipId())) {
            throw new BusinessException(ServiceErrCodeEnum.SENDER_ADDRESS_ERROR);
        }
        if (!appAreaService.check(dto.getReceiverProvinceId(), dto.getReceiverCityId(), dto.getReceiverAreaId(), dto.getReceiverTownshipId())) {
            throw new BusinessException(ServiceErrCodeEnum.RECEIVER_ADDRESS_ERROR);
        }
    }


    @PostMapping(value = "/newPrintOrders")
    @ApiOperation(value = "订单打印", notes = "订单打印")
    public void newPrintOrders(@RequestBody @Validated PrintDTO dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        log.info("订单打印-requestId==>{}, 入参==>{}", requestId, JSON.toJSONString(dto));
        //return success(orderService.printOrders(dto, requestId));
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("订单打印");
        List<JasperPrint> jasperPrints = orderService.printOrders(dto, requestId);
        try {
            String defaultName = "一联面单.pdf";
            JasperPrinterUtils.exportPDFToWebList(jasperPrints, defaultName, response);
            stopWatch.stop();
            log.info("订单打印-requestId==>{}, 完成,耗时==>{}ms", requestId, stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            log.warn("订单打印-requestId==>{}, 打印失败，异常信息==>{}", requestId, e);
            throw new BusinessException(ServiceErrCodeEnum.PRINT_WAYBILL_ERROR);
        }

    }


    //    @PostMapping(value = "/merger")
    @ApiOperation(value = "合并订单", notes = "合并订单")
    public Result<OmsOrderApiVO> mergerOrder(@RequestBody @NotEmpty(message = "id不能为空") List<Long> ids) {
        return orderService.mergerOrder(ids);
    }

    /**
     * 客户订单号验重
     *
     * @param dto
     * @return
     */
    @PostMapping("/checkRepeatCustomerOrderId")
    @ApiOperation(value = "客户订单号验重", notes = "客户订单号验重")
    public Result<Boolean> checkRepeatCustomerOrderId(@RequestBody OmsOrderApiQueryDto dto) {
        List<OmsOrderApiVO> list = orderService.checkRepeatCustomerOrderId(dto);
        return Result.success(CollectionUtil.isEmpty(list));
    }

    @GetMapping(value = "/getReceiptFeeByCode")
    @ApiOperation(value = "查询当前用户回单费用", notes = "查询当前用户回单费用")
    public Result<Double> getReceiptFeeByCode(@RequestParam("customerCode") @NotNull(message = "客户编码不能为空") String customerCode) {
        log.info("查询当前用户回单费用入参==>{}", customerCode);
        Result<Double> result = orderService.getReceiptFeeByCode(customerCode);
        log.info("查询当前用户回单费用返回值==>{}", JSON.toJSONString(result));
        return result;
    }

}