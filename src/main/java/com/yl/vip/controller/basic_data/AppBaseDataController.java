package com.yl.vip.controller.basic_data;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import com.yl.vip.controller.AppBaseController;
import com.yl.vip.dto.SpecialTimeLimitProductType;
import com.yl.vip.feign.SpecialTimeLimitProductTypeFeignClient;
import com.yl.vip.feign.SysBubbleRatioFeignClient;
import com.yl.vip.feign.SysEstimateTimeFeignClient;
import com.yl.vip.feign.SysStoreSelfPickupClient;
import com.yl.vip.service.IAppBaseDataService;
import com.yl.vip.vo.AppBaseDataVO;
import com.yl.vip.vo.AppProductTypeVO;
import com.yl.vip.vo.SysBubbleRatioConfigVO;
import com.yl.vip.vo.SysEstimateTimeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019/6/19 17:05
 */
@Api(value = "app基础数据", tags = {"app基础数据"})
@RestController
@RequestMapping("/baseData")
@Slf4j
public class AppBaseDataController extends AppBaseController {

    @Autowired
    private IAppBaseDataService appBaseDataService;

    @Autowired
    private SysEstimateTimeFeignClient sysEstimateTimeFeignClient;

    @Autowired
    private SysBubbleRatioFeignClient sysBubbleRatioFeignClient;

    @Autowired
    private SysStoreSelfPickupClient sysStoreSelfPickupClient;

    @GetMapping(value = "/list")
    @ApiOperation(value = "基础数据列表", notes = "基础数据列表")
    public Result<AppBaseDataVO> getAppList() {
        return success(appBaseDataService.getAppList());
    }

    @ApiOperation(value = "根据寄件省市到件省市查询时效", notes = "根据寄件省市到件省市查询时效")
    @GetMapping("/sysEstimateTime/getAging")
    public Result<List<SysEstimateTimeVO>> getAging(@RequestParam("sendProvinceId") Integer sendProvinceId,
                                                    @RequestParam("sendCityId") Integer sendCityId,
                                                    @RequestParam("dispatchProvinceId") Integer dispatchProvinceId,
                                                    @RequestParam("dispatchCityId") Integer dispatchCityId) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        log.info("requestId==>{},调基础数据，寄件省==>{}，寄件市==>{}，目的地省==>{}，目的地市==>{}", requestId, sendProvinceId, sendCityId, dispatchProvinceId, dispatchCityId);
        List<SysEstimateTimeVO> sysEstimateTime = sysEstimateTimeFeignClient.getAging(sendProvinceId, sendCityId, dispatchProvinceId, dispatchCityId).getData();
        log.info("requestId==>{},查询时效==>{}", requestId, JSON.toJSONString(sysEstimateTime));
        return Result.success(sysEstimateTime);
    }


    @ApiOperation(value = "根据code、客户编码查询重泡比", notes = "根据code、客户编码查询重泡比")
    @GetMapping("/getCoefficientByQuery")
    public Result<SysBubbleRatioConfigVO> getCoefficientByQuery(@RequestParam String code) {
        return sysBubbleRatioFeignClient.getCoefficientByQuery(code, getUser().getCustomerCode());
    }

    @Autowired
    private SpecialTimeLimitProductTypeFeignClient specialTimeLimitProductTypeFeignClient;

    @GetMapping("/getProductTypeList")
    @ApiOperation(value = "查询预估时效以及产品类型")
    public Result<List<SpecialTimeLimitProductType>> getList(@RequestParam(value = "sendCityId") Integer sendCityId,
                                                             @RequestParam(value = "dispatchCityId") Integer dispatchCityId,
                                                             @RequestParam(value = "networkCode", required = false) String networkCode) {
        Result<List<SpecialTimeLimitProductType>> productTypeRes = specialTimeLimitProductTypeFeignClient.getList(sendCityId, dispatchCityId, networkCode);
        if (productTypeRes != null && productTypeRes.isSucc() && CollectionUtils.isNotEmpty(productTypeRes.getData())) {
            List<SpecialTimeLimitProductType> productTypes = productTypeRes.getData();
            List<SpecialTimeLimitProductType> productTypeList = productTypes.stream().filter(x -> x.getProductTypeExternalFlag() == 1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(productTypeList)) {
                AppBaseDataVO appList = appBaseDataService.getAppList();
                List<AppProductTypeVO> appProductTypeVOList = appList.getAppProductTypeVOList();
                productTypeList.forEach(x -> {
                    Optional<AppProductTypeVO> any = appProductTypeVOList.stream().filter(y -> y.getName().equals(x.getProductTypeName()) || y.getCode().equals(x.getProductTypeCode())).findAny();
                    if (any.isPresent()) {
                        AppProductTypeVO appProductTypeVO = any.get();
                        x.setCode(appProductTypeVO.getCode());
                        x.setName(appProductTypeVO.getName());
                        x.setLightThrowingCoefficient(appProductTypeVO.getLightThrowingCoefficient());
                    }
                });
            }
            return Result.success(productTypeList);
        }
        return Result.success(null);
    }

    @PostMapping("/checkStoreSelfPickup")
    @ApiOperation(value = "检查门店自提", notes = "检查门店自提")
    public Result<JSONObject> checkStoreSelfPickup(@RequestBody JSONObject jsonObject) {
        return sysStoreSelfPickupClient.checkStoreSelfPickup(jsonObject);
    }


}
