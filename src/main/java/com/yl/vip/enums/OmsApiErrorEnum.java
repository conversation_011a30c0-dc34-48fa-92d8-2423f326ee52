package com.yl.vip.enums;

import com.yl.common.base.enums.RespCodeEnum;

public enum OmsApiErrorEnum implements RespCodeEnum {
    OMS_API_SERVER_ERROR(121003000, "OMS_API_SERVER_ERROR", "系统繁忙，请稍候重试"),
    OMS_WAYBILL_MAX_ERROR(121003001, "oms_waybill_max_error", "运单号超过500条"),
    OMS_API_PRINT_WAYBILL_NUMBER_ERROR(121003002, "oms_api_print_waybill_number_error", "打印运单数量只能大于0小于300"),
    OMS_API_PRINT_WAYBILL_EMPTY_ERROR(121003003, "oms_api_print_waybill_empty_error", "纸质运单不支持打印"),
    OMS_API_PRINT_WAYBILL_DATA_EMPTY_ERROR(121003004, "oms_api_print_waybill_data_empty_error", "打印运单数据不能为空"),
    OMS_NO_AUTH_ERROR(121004001, "oms_no_auth_error", "没有权限查询该网点"),
    OMS_API_PRINT_ORDER_NUMBER_ERROR(121003005, "oms_api_print_order_number_error", "打印订单数量只能大于0小于300"),
    OMS_API_PRINT_ORDER_EMPTY_ERROR(121003006, "oms_api_print_order_empty_error", "订单状态不支持打印"),
    OMS_API_PRINT_ORDER_DATA_EMPTY_ERROR(121003007, "oms_api_print_order_data_empty_error", "打印订单数据不能为空"),
    OMS_API_PRINT_ORDER_GET_WAYBILLNO_ERROR(121003008, "oms_api_print_order_get_waybillno_error", "打印订单获取运单号异常"),
    OMS_API_PRINT_ORDER_GET_WAYBILLNO_NUM_ERROR(121003009, "oms_api_print_order_get_waybillno_num_error", "打印订单获取不到足够的运单号"),
    OMS_WAYBILL_PRINT_NUMBER_ERROR(121003010, "oms_waybill_print_number_error", "同一运单最多可仅可补打印三次"),
    OMS_ID_MAX_ERROR(121013001, "oms_id_max_error", "订单号超过500条"),
    OMS_SELECT_NULL_ERROR(121013002, "oms_select_null_error", "请至少勾选一条记录"),
    OMS_SELECT_OVERIDE_ERROR(121013003, "oms_select_overide_error", "勾选记录超过20条"),
    OMS_CANCLEREASON_EMPTY_ERROR(121013004, "oms_canclereason_empty_error", "取消原因不能为空"),
    OMS_GET_FAIL_ERROR(121013005, "oms_get_fail_error", "取件失败原因不能为空"),
    OMS_DISPATCH_NETWORK_CODE_ERROR(121013006, "oms_dispatch_network_code_error", "取件网点编码不能为空"),
    OMS_DISPATCH_NETWORK_NAME_ERROR(121013007, "oms_dispatch_network_name_error", "取件网点名称不能为空"),
    OMS_ID_WAYBILL_SAME_ERROR(121013008, "oms_id_waybill_same_error", "订单和运单不能同时输入"),
    OMS_STATUS_ERROR(121013009, "oms_status_error", "当前状态不允许此操作"),
    OMS_DISPATCH_SALEMAN_DIFF_ERROR(121013010, "oms_dispatch_saleman_diff_error", "所选数据网点不一致，请重新确认数据。"),
    OMS_DISPATCH_SALEMAN_CODE_ERROR(121013011, "oms_dispatch_saleman_code_error", "调派业务员编码不能为空"),
    OMS_DISPATCH_SALEMAN_NAME_ERROR(121013012, "oms_dispatch_saleman_name_error", "调派业务员名称不能为空"),
    OMS_DISPATCHREASON_LONG_ERROR(121013013, "oms_dispatchreason_long_error", "调派原因内容过长"),
    OMS_FAILREASON_LONG_ERROR(121013014, "oms_failreason_long_error", "失败原因内容过长"),
    OMS_CANNOT_DISPATCH_NETWORK_ERROR(121013015, "oms_cannot_dispatch_network_error", "存在不可调派网点的数据，请重新确认数据"),
    OMS_CANNOT_DISPATCH_SALEMAN_ERROR(121013016, "oms_cannot_dispatch_saleman_error", "存在不可调派业务员的数据，请重新确认数据"),
    OMS_CANNOT_PICKUP_FAIL_ERROR(121013017, "oms_cannot_pickup_fail_error", "存在不可做取件失败操作的数据，请重新确认数据"),
    OMS_DISPATCH_NETWORK_ID_ERROR(121013018, "oms_dispatch_network_id_error", "取件网点ID不能为空"),
    OMS_BACK_DISPATCH_NETWORK_ID_LENGTH_ERROR(121013019, "oms_back_dispatch_network_id_length_error", "打回调度订单不能超过200条！"),
    OMS_BACK_DISPATCH_NETWORK_STATUS_ERROR(121013020, "oms_back_dispatch_network_status_error", "存在不能打回订单的数据，请重新确认数据！"),
    OMS_OTHER_REASON_CAN_NOT_BE_NULL(121013021, "oms_other_reason_can_not_be_null", "取消订单选择其他时, 取消原因不能为空"),
    OMS_REASON_CODE_CAN_NOT_BE_NULL(121013022, "oms_reason_code_can_not_be_null", "取消订单原因code不能为空"),
    OMS_CANCEL_REASON_CODE_WRONG(121013023, "oms_cancel_reason_code_wrong", "错误的取消订单原因code"),
    OMS_ORDER_DISPATCH_PROXY_AREA_STATUS_ERROR(121013024, "oms_order_dispatch_proxy_area_status_error", "只有未调度或己调度代理区的订单才能调度代理区"),
    OMS_ORDER_DISPATCH_NETWORK_STATUS_ERROR(121013025, "oms_order_dispatch_network_status_error", "只有己调度代理区或己调度网点的订单才能调度网点"),
    OMS_ORDER_DISPATCH_PROXY_AREA_ID_ERROR(121013026, "oms_order_dispatch_proxy_area_id_error", "调度代理区代理区id不能为空"),
    OMS_ORDER_DISPATCH_PROXY_AREA_NAME_ERROR(121013027, "oms_order_dispatch_proxy_area_name_error", "调度代理区代理区name不能为空"),
    OMS_ORDER_DISPATCH_PROXY_AREA_CODE_ERROR(121013028, "oms_order_dispatch_proxy_area_code_error", "调度代理区代理区code不能为空"),
    OMS_DATE_START_ERROR(121023001, "oms_date_range_error", "开始时间大于结束时间"),
    OMS_DATE_RANGE_ERROR(121023002, "oms_date_range_error", "开始时间与结束时间之差大于31天"),
    OMS_START_AND_END_TIME_NOT_NULL(121023003, "oms_start_and_end_time_not_null", "开始或结束时间不能为空"),
    OMS_BATCH_SIGNING_SIZE_CHECK(121023004, "oms_batch_signing_size_check", "批量签收查询运单号集合大小不能超过200条"),
    OMS_PDD_QUERY_DETAIL_LIMIT(121023005, "OMS_PDD_QUERY_DETAIL_LIMIT", "拼多多当日已无调用次数"),
    OMS_VIP_ORDER_EXPORT_DATA_LIMIT_ERROR(121023006, "oms_vip_order_export_data_limit_error", "导出的数据总量超过10000条"),
    OMS_PDD_SCAN_ORDER_NOT_FOUND(121023007, "oms_pdd_scan_order_not_found", "订单数据加载有误，无法进行揽收，请注意"),
    OMS_API_CUSTOMER_ORDER_ID_EXIST(121033001, "oms_api_customer_order_id_exist", "客户订单号已存在,无法下单"),
    OMS_ORDER_SENDER_PHONE_ERROR(121033002, "oms_order_sender_phone_error", "发件人手机号码、座机必填一项"),
    OMS_ORDER_RECEIVER_PHONE_ERROR(121033003, "oms_order_receiver_phone", "收件人手机号码、座机必填一项"),
    OMS_ORDER_STATUS_EDIT_ERROR(121033004, "oms_order_edit_status_error", "当前订单状态不可编辑"),
    OMS_ORDER_ALI_NOT_FOUND_ORDER_ID(121033005, "oms_api_ali_not_found_order_id", "未找到对应的订单,无法更新."),
    OMS_API_GENERATE_WAYBILL_ID_FAIL(121036001, "oms_api_generate_waybill_id_fail", "生成运单号失败"),
    OMS_GENERATOR_ORDER_ID_ERROR(121036002, "oms_generator_order_id_error", "生成orderid错误"),
    OMS_NAN_JING_ERROR(121036003, "oms_nan_jing_error", "收发城市只能选择江苏省-南京"),
    OMS_VIP_MERGER_HAS_ALREADY_PRINT_ORDER(121036004, "oms_vip_merger_has_already_print_order", "只有待打印的订单支持合单"),
    OMS_VIP_MERGER_ORDER_INFO_DIFFERENT(121036005, "oms_vip_merger_order_info_different", "不同账号上传的订单不允许合并"),
    OMS_VIP_MERGER_ORDER_GOODS_NAME_TOO_LONG(121036006, "oms_vip_merger_order_goods_name_too_long", "物品名称长度超限，已保留前200字，您可以在订单列表中编辑"),
    OMS_VIP_MERGER_ORDER_EXCEED_LIMIT_WEIGHT(121036007, "oms_vip_merger_order_exceed_limit_weight", "单票寄件重量不能大于30kg，无法合单，请重新选择"),
    OMS_ORDER_EXCEED_LIMIT_WEIGHT(121036008, "oms_order_exceed_limit_weight", "单票寄件重量不能大于30kg"),
    OMS_WAYBILL_NO_REPEAT(121036009, "oms_waybill_no_repeat", "运单号重复"),
    REPEAT_SAVE_ORDER(121036010, "repeat_save_order", "重复下单"),
    OMS_PAPER_NEED_WAYBILL_ERROR(121043001, "oms_paper_need_waybill_error", "纸质面单必须填写运单编号"),
    OMS_EXISTS_WAYBILL_ERROR(121043002, "oms_exists_waybill_error", "运单编号已经存在"),
    OMS_EMPTY_STAFF_CODE_ERROR(121043003, "oms_empty_staff_code_error", "取件员编码不能为空"),
    OMS_EMPTY_STAFF_NAME_ERROR(121043004, "oms_empty_staff_name_error", "取件员名称不能为空"),
    OMS_EMPTY_PRODUCT_ID_ERROR(121043005, "oms_empty_product_id_error", "产品类型id不能为空"),
    OMS_EMPTY_PRODUCT_CODE_ERROR(121043006, "oms_empty_product_code_error", "产品类型编码不能为空"),
    OMS_EMPTY_PRODUCT_NAME_ERROR(121043007, "oms_empty_product_name_error", "产品类型名称不能为空"),
    OMS_EMPTY_DISPATCH_CODE_ERROR(121043008, "oms_empty_dispatch_code_error", "派件方式编码不能为空"),
    OMS_EMPTY_DISPATCH_NAME_ERROR(121043009, "oms_empty_dispatch_name_error", "派件方式名称不能为空"),
    OMS_EMPTY_PICKUP_TIME_ERROR(121043010, "oms_empty_pickup_time_error", "寄件时间不能为空"),
    OMS_EMPTY_PICKUP_NAME_ERROR(121043011, "oms_empty_pickup_name_error", "寄件姓名不能为空"),
    OMS_EMPTY_PROVINCE_ID_ERROR(121043012, "oms_empty_province_id_error", "寄方省份id不能为空"),
    OMS_EMPTY_PROVINCE_NAME_ERROR(121043013, "oms_empty_province_name_error", "寄方省份名称不能为空"),
    OMS_EMPTY_CITY_ID_ERROR(121043014, "oms_empty_city_id_error", "寄方城市id不能为空"),
    OMS_EMPTY_CITY_NAME_ERROR(121043015, "oms_empty_city_name_error", "寄方城市名称不能为空"),
    OMS_EMPTY_AREA_ID_ERROR(121043016, "oms_empty_area_id_error", "寄方区县id不能为空"),
    OMS_EMPTY_AREA_NAME_ERROR(121043017, "oms_empty_area_name_error", "寄方区县名称不能为空"),
    OMS_OVERLENGTH_SENDER_STREET_ERROR(121043018, "oms_overlength_sender_street_error", "寄方村庄/街道/路号/楼宇长度超过限制200个字符"),
    OMS_EMPTY_SENDER_DETAIL_STREET_ERROR(121043019, "oms_empty_sender_detail_street_error", "寄方详细地址不能为空"),
    OMS_OVERLENGTH_SENDER_DETAIL_STREET_ERROR(121043020, "oms_overlength_sender_detail_street_error", "寄方详细地址长度超过限制200个字符"),
    OMS_EMPTY_ORIGIN_ID_ERROR(121043021, "oms_empty_origin_id_error", "始发地id不能为空"),
    OMS_EMPTY_ORIGIN_CODE_ERROR(121043022, "oms_empty_origin_code_error", "始发地编码不能为空"),
    OMS_EMPTY_ORIGIN_NAME_ERROR(121043023, "oms_empty_origin_name_error", "始发地名称不能为空"),
    OMS_EMPTY_IDNO_ERROR(121043024, "oms_empty_idno_error", "身份证号码必须填写"),
    OMS_OVERLENGTH_IDNO_ERROR(121043025, "oms_overlength_idno_error", "身份证号码长度只能为15或者18个字符"),
    OMS_EMPTY_RECEIVER_NAME_ERROR(121043026, "oms_empty_receiver_name_error", "收方姓名不能为空"),
    OMS_EMPTY_RECEIVER_PROVINCE_ID_ERROR(121043027, "oms_empty_receiver_province_id_error", "收方省份id不能为空"),
    OMS_EMPTY_RECEIVER_PROVINCE_NAME_ERROR(121043028, "oms_empty_receiver_province_name_error", "收方省份名称不能为空"),
    OMS_EMPTY_RECEIVER_CITY_ID_ERROR(121043029, "oms_empty_receiver_city_id_error", "收方城市id不能为空"),
    OMS_EMPTY_RECEIVER_CITY_NAME_ERROR(121043030, "oms_empty_receiver_city_name_error", "收方城市名称不能为空"),
    OMS_EMPTY_RECEIVER_AREA_ID_ERROR(121043031, "oms_empty_receiver_area_id_error", "收方区县id不能为空"),
    OMS_EMPTY_RECEIVER_AREA_NAME_ERROR(121043032, "oms_empty_receiver_area_name_error", "收方区县名称不能为空"),
    OMS_OVERLENGTH_RECEIVER_STREET_ERROR(121043033, "oms_overlength_sender_street_error", "收方村庄/街道/路号/楼宇长度超过限制200个字符"),
    OMS_EMPTY_RECEIVER_DETAIL_STREET_ERROR(121043034, "oms_empty_sender_detail_street_error", "收方详细地址不能为空"),
    OMS_OVERLENGTH_RECEIVER_DETAIL_STREET_ERROR(121043035, "oms_overlength_sender_detail_street_error", "收方详细地址长度超过限制200个字符"),
    OMS_EMPTY_DESTINATION_ID_ERROR(121043036, "oms_empty_destination_id_error", "目的地id不能为空"),
    OMS_EMPTY_DESTINATION_CODE_ERROR(121043037, "oms_empty_destination_code_error", "目的地编码不能为空"),
    OMS_EMPTY_DESTINATION_NAME_ERROR(121043038, "oms_empty_destination_name_error", "目的地名称不能为空"),
    OMS_EMPTY_GOODSTYPE_ID_ERROR(121043039, "oms_empty_goodstype_id_error", "物品类型id不能为空"),
    OMS_EMPTY_GOODSTYPE_CODE_ERROR(121043040, "oms_empty_goodstype_code_error", "物品类型编码不能为空"),
    OMS_EMPTY_GOODSTYPE_NAME_ERROR(121043041, "oms_empty_goodstype_name_error", "物品类型名称不能为空"),
    OMS_OVERLENGTH_GOODS_LENGTH_ERROR(121043042, "oms_overlength_goods_length_error", "物品名称长度超过限制50个字符"),
    OMS_PACKAGE_NUMBER_RANGE_ERROR(121043043, "oms_package_number_range_error", "包裹件数只能为1-99"),
    OMS_EMPTY_SETTELEMENT_ID_ERROR(121043044, "oms_empty_settelement_id_error", "结算方式id不能为空"),
    OMS_EMPTY_SETTELEMENT_NAME_ERROR(121043045, "oms_empty_settelement_name_error", "结算方式名称不能为空"),
    OMS_OVERLENGTH_INVOICE_LENGTH_ERROR(121043046, "oms_overlength_invoice_length_error", "发票编号长度超过限制50个字符"),
    OMS_EMPTY_COD_MONEY_ERROR(121043047, "oms_empty_cod_money_error", "代收货款金额不能为空"),
    OMS_EMPTY_INSURANCE_AMOUNT_ERROR(121043048, "oms_empty_insurance_amount_error", "保价金额不能为空"),
    OMS_EMPTY_RECEIPT_NO_ERROR(121043049, "oms_empty_receipt_no_error", "回单编号不能为空"),
    OMS_OVERLENGTH_REMARKS_ERROR(121043050, "oms_overlength_remarks_error", "备注长度超过限制200个字符"),
    OMS_WAYBILL_DISPATCH_CODE_ERROR(121043051, "oms_waybill_dispatch_code_error", "派件方式编码错误"),
    OMS_EMPTY_SENDER_MOBILE_ERROR(121043052, "oms_empty_sender_mobile_error", "寄件人手机号码不能为空"),
    OMS_EMPTY_RECEIVER_MOBILE_ERROR(121043053, "oms_empty_receiver_mobile_error", "收件人手机号码不能为空"),
    OMS_PACKAGE_NUMBER_RANGE_BAC_ERROR(121043054, "oms_package_number_range_bac_error", "包裹件数只能为1-999"),
    OMS_EMPTY_WEIGHT_ERROR(121043055, "oms_empty_weight_error", "重量不能为空"),
    OMS_WAYBILL_SOURCE_CODE_ERROR(121043056, "oms_waybill_source_code_error", "运单来源编码错误"),
    OMS_OVERLENGTH_PRODUCT_NAME_ERROR(121043057, "oms_overlength_product_name_error", "产品类型名称长度超过限制"),
    OMS_OVERLENGTH_DISPATCH_NAME_ERROR(121043058, "oms_overlength_dispatch_name_error", "派件方式名称长度超过限制"),
    OMS_OVERLENGTH_CUSTOMER_NAME_ERROR(121043059, "oms_overlength_customer_name_error", "客户名称长度超过限制"),
    OMS_OVERLENGTH_SENDER_PROVIDER_NAME_ERROR(121043060, "oms_overlength_sender_province_name_error", "寄件人省份名称长度超过限制"),
    OMS_OVERLENGTH_SENDER_CITY_NAME_ERROR(121043061, "oms_overlength_sender_city_name_error", "寄件人城市名称长度超过限制"),
    OMS_OVERLENGTH_SENDER_AREA_NAME_ERROR(121043062, "oms_overlength_sender_area_name_error", "寄件人区/县名称长度超过限制"),
    OMS_OVERLENGTH_RECEIVER_PROVIDER_NAME_ERROR(121043063, "oms_overlength_receiver_province_name_error", "收件人省份名称长度超过限制"),
    OMS_OVERLENGTH_RECEIVER_CITY_NAME_ERROR(121043064, "oms_overlength_receiver_city_name_error", "收件人城市名称长度超过限制"),
    OMS_OVERLENGTH_RECEIVER_AREA_NAME_ERROR(121043065, "oms_overlength_receiver_area_name_error", "收件人区/县名称长度超过限制"),
    OMS_OVERLENGTH_ORIGIN_NAME_ERROR(121043066, "oms_overlength_origin_name_error", "起始地名称长度超过限制"),
    OMS_OVERLENGTH_PAID_MODE_NAME_ERROR(121043067, "oms_overlength_paid_mode_name_error", "支付方式名称长度超过限制"),
    OMS_OVERLENGTH_INPUT_STAFF_NAME_ERROR(121043068, "oms_overlength_input_staff_name_error", "录入人名称长度超过限制"),
    OMS_OVERLENGTH_INPUT_NETWORK_NAME_ERROR(121043069, "oms_overlength_input_network_name_error", "录入网点名称长度超过限制"),
    OMS_OVERLENGTH_PACKAGE_LENGTH_ERROR(121043070, "oms_overlength_package_length_error", "包裹总长超出了允许范围(只允许在3位整数和2位小数范围内)"),
    OMS_OVERLENGTH_PACKAGE_WIDE_ERROR(121043071, "oms_overlength_package_wide_error", "包裹总宽超出了允许范围(只允许在3位整数和2位小数范围内)"),
    OMS_OVERLENGTH_PACKAGE_HIGH_ERROR(121043072, "oms_overlength_package_high_error", "包裹总高超出了允许范围(只允许在3位整数和2位小数范围内)"),
    OMS_OVERLENGTH_CHARGEWEIGHT_ERROR(121043073, "oms_overlength_chargeweight_error", "包裹计费重量超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_OVERLENGTH_TOTALVOLUME_ERROR(121043074, "oms_overlength_totalvolume_error", "包裹总体积超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_OVERLENGTH_PACKAGEVOLUME_ERROR(121043075, "oms_overlength_packagevolume_error", "包裹体积重超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_OVERLENGTH_INSURED_MONEY_ERROR(121043076, "oms_overlength_insured_amount_error", "保价金额超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_INSURED_FEE_ERROR(121043077, "oms_overlength_insured_fee_error", "保价费超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_COUPON_AMOUNT_ERROR(*********, "oms_overlength_coupon_amount_error", "优惠金额超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_PACKAGE_COST_ERROR(*********, "oms_overlength_package_cost_error", "包材费超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_FREIGHT_ERROR(*********, "oms_overlength_freight_error", "运费超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_TAX_ERROR(*********, "oms_overlength_tax_error", "税金超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_AFTERTAX_ERROR(*********, "oms_overlength_aftertax_fee_error", "税后总费用超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_TOTALFEE_ERROR(*********, "oms_overlength_total_fee_error", "总费用超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_PICKNETWORK_NAME_ERROR(*********, "oms_overlength_picknetwork_name_error", "取件网点名称长度超过限制"),
    OMS_OVERLENGTH_PICKNETWORK_CODE_ERROR(*********, "oms_overlength_picknetwork_code_error", "取件网点编码长度超过限制"),
    OMS_OVERLENGTH_ORIGIN_CODE_ERROR(*********, "oms_overlength_origin_code_error", "起始地编码长度超过限制"),
    OMS_OVERLENGTH_DESTINATION_CODE_ERROR(*********, "oms_overlength_destination_code_error", "目的地编码长度超过限制"),
    OMS_OVERLENGTH_PAID_MODE_CODE_ERROR(*********, "oms_overlength_paid_mode_code_error", "支付方式编码长度超过限制"),
    OMS_OVERLENGTH_INPUT_STAFF_CODE_ERROR(*********, "oms_overlength_input_staff_code_error", "录入人编码长度超过限制"),
    OMS_OVERLENGTH_INPUT_NETWORK_CODE_ERROR(121043090, "oms_overlength_input_network_code_error", "录入网点编码长度超过限制"),
    OMS_EMPTY_SEND_NETWORK_CODE_ERROR(121043091, "oms_empty_send_network_code_error", "寄件网点编码不能为空"),
    OMS_ORDER_SENDER_PHONE_FORMAT_ERROR(121043092, "oms_order_sender_phone_error", "发件人手机号码格式有误"),
    OMS_ORDER_SENDER_POSTAL_CODE_ERROR(121043093, "oms_order_sender_postal_code_error", "发件人邮编格式有误"),
    OMS_ORDER_BOX_NUMBER_MAX_ERROR(121043094, "oms_order_box_number_max_error", "包材数量超过了最大值999"),
    OMS_ORDER_COUPON_CODE_ERROR(121043095, "oms_order_coupon_code_error", "优惠券只能为字母加数字且长度不能超过19位"),
    OMS_OVERLENGTH_OTHER_FEE_ERROR(121043096, "oms_overlength_other_fee_error", "其他费超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_OVERLENGTH_HANDICRAFT_FEE_ERROR(121043097, "oms_overlength_handicraft_fee_error", "手工运费超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_OVERLENGTH_COD_MONEY_ERROR(121043098, "oms_overlength_cod_money_error", "代收货款金额超出了允许范围(只允许在4位整数和2位小数范围内)"),
    OMS_OVERLENGTH_INSURED_AMOUNT_ERROR(121043099, "oms_overlength_insured_amount_error", "保价金额超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_EMPTY_SETTELEMENT_CODE_ERROR(121043100, "oms_empty_settelement_code_error", "结算方式编码不能为空"),
    OMS_WAYBILL_EDIT_ERROR(121043101, "oms_waybill_edit_error", "运单不允许编辑"),
    OMS_WAYBILL_STATUS_CODE_ERROR(121043102, "oms_waybill_status_code_error", "非法的运单状态"),
    OMS_WAYBILL_NOT_SIGNED_DELETE_ERROR(121043103, "oms_waybill_not_signed_delete_error", "未签收运单不允许删除"),
    OMS_WAYBILL_EXPORT_DATA_LIMIT_ERROR(121043104, "oms_waybill_export_data_limit_error", "导出的数据总量超过10000条"),
    OMS_WAYBILL_CANCEL_VERIFY_LENGTH_ERROR(121043105, "oms_waybill_cancel_verify_length_error", "反审核运单不能超过200条！"),
    OMS_WAYBILL_CANCEL_VERIFY_NETWORK_ERROR(121043106, "oms_waybill_cancel_verify_network_error", "存在不同寄件网点的运单，不允许反审核！"),
    OMS_WAYBILL_CANCEL_VERIFY_ERROR(121043107, "oms_waybill_cancel_verify_error", "存在运单生成账单，不允许反审核！"),
    OMS_WAYBILL_BATCH_UPDATE_LENGTH_ERROR(121043108, "oms_waybill_batch_update_length_error", "批量编辑运单不能超过200条！"),
    OMS_WAYBILL_BATCH_UPDATE_STATUS_ERROR(121043109, "oms_waybill_batch_update_status_error", "存在已审核或已签收运单，不允许修改！"),
    OMS_WAYBILL_BATCH_UPDATE_SETTLEMENT_ERROR(121043110, "oms_waybill_batch_update_settlement_error", "散客的结算方式不能改为寄付月结！"),
    OMS_OVERLENGTH_SETTLEMENT_WEIGHT_ERROR(121043200, "oms_overlength_settlement_weight_error", "结算重量超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_GET_WAYBILL_NO_ERROR(121043111, "oms_get_waybill_no_error", "获取运单号客户id不为空时，客户订单编号和运单id不能同时为空"),
    OMS_SEND_WAYBILL_CHECK_SETTLEMENT_STATUS_ERROR(121053001, "oms_send_waybill_check_settlement_status_error", "己审核的运单不能重复审核"),
    OMS_SEND_WAYBILL_CHECK_ABANDON_ERROR(121053002, "oms_send_waybill_check_abandon_error", "己废弃的运单不能审核"),
    OMS_SEND_WAYBILL_CHECK_SETTLEMENT_CODE_ERROR(121053003, "oms_send_waybill_check_settlement_code_error", "只能审核结算方式为寄付月结的运单"),
    OMS_CANNOT_CANCLE_ERROR(121093001, "oms_cannot_cancle_error", "存在不可取消的数据，请重新确认数据"),
    OMS_CANCLEREASON_LONG_ERROR(121093002, "oms_canclereason_long_error", "取消原因内容过长"),
    OMS_NULL_ORDER_ERROR(121103001, "oms_null_order_error", "订单不存在"),
    OMS_RECEIVER_MOBILE_NEED_FAIL(121103002, "oms_receiver_mobile_need_fail", "签收短信通知勾选后，请填写寄件人手机号"),
    OMS_COLLECT_TO_WAYBILL_ERROR(121103003, "oms_collect_to_waybill_error", "揽收生成运单失败"),
    OMS_COLLECT_TO_WAYBILL_REPEAT_ERROR(121103004, "oms_collect_to_waybill_repeat_error", "取件任务揽收运单重复"),
    OMS_COLLECT_TO_WAYBILL_CANCEL_ERROR(121103005, "oms_collect_to_waybill_cancel_error", "取件任务揽收已取消的订单"),
    OMS_GENERATE_WAYBILL_NO_FAIL(121106001, "oms_generate_waybill_no_fail", "获取运单号失败"),
    OMS_STAFF_CODE_NOT_NULL(121113001, "oms_staff_code_not_null", "员工编码不能为空"),
    OMS_STAFF_CODE_EXCEEDING_LIMITED_LENGTH(121113002, "oms_staff_code_exceeding_limited_length", "员工编号超过30位"),
    OMS_SCAN_TYPE_NOT_NULL(121113003, "oms_scan_type_not_null", "扫描类型不能为空"),
    OMS_SEND_STATUS_ERROR(121123001, "oms_send_status_error", "该运单己发件"),
    OMS_ID_CHECK_NOPASS_ERROR(121130001, "oms_id_check_nopass_error", "实名认证未通过,无法提交,请重新认证!"),
    OMS_ID_CHECK_CALL_ERROR(121130002, "oms_id_check_call_error", "实名认证调取失败,请重新认证!"),
    OMS_SEARCH_TYPE_ERROR(121140001, "oms_search_type_error", "查询类型错误"),
    OMS_EMPTY_CUSTOMER_CODE_ERROR(121140002, "oms_empty_customer_code_error", "会员编号不能为空"),
    OMS_EMPTY_PHONE_NO_ERROR(121140003, "oms_empty_phone_no_error", "手机号码不能为空"),
    OMS_EMPTY_CUSTOMER_CODE_PHONE_NO_ERROR(121140004, "oms_empty_phone_no_error", "会员编号和手机号码不能为空"),
    OMS_DATE_RANGE_NIGHTY_ERROR(121140005, "oms_date_range_nighty_error", "开始时间与结束时间之差大于90天"),
    OMS_MINI_MINE_DELETE_STATUS_ERROR(121153001, "oms_mini_mine_delete_status_error", "只能删除己取消或己签收的数据"),
    OMS_DELETE_STATUS_ERROR(121163001, "oms_delete_status_error", "只能删除己取消的数据"),
    OMS_ID_CARD_IDENTIFY_ERROR(121173001, "oms_id_card_identify_error", "识别错误,请上传正确的身份证图片"),
    OMS_IMAGE_FORMAT_ERROR(121173002, "oms_image_format_error", "图片格式错误"),
    OMS_ID_CARD_INTERFACE_ERROR(121173003, "oms_id_card_interface_error", "调用ocr身份证文字识别接口失败"),
    CLOUDPRINT_TRANSFINITE_ERROR(121183001, "cloudprint_transfinite_error", "打印次数已超限，请联系网点核实"),
    CLOUDPRINT_PRINT_ERROR(121183002, "cloudprint_print_error", "打印失败，您可以在“我寄的”列表页再次打印，或者联系网点处理");

    private int code;
    private String key;
    private String msg;

    private OmsApiErrorEnum(int code, String key, String msg) {
        this.code = code;
        this.key = key;
        this.msg = msg;
    }

    public void setArgs(Object[] var1) {
    }

    public int getCode() {
        return this.code;
    }

    public String getKey() {
        return this.key;
    }

    public String getMsg() {
        return this.msg;
    }
}
