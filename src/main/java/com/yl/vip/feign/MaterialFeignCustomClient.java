package com.yl.vip.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.model.vo.Result;
import com.yl.vip.feign.config.FeignConfiguration;
import com.yl.vip.feign.fallback.MaterialFeignCustomFallback;
import com.yl.vip.vo.MaterialVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;



/**
 * <p>
 * 物料管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-12
 */
@Component
@FeignClient(
        name = "ylmsdmpackingmaterialapi",
        path =  "/msdmpackingmaterialapi/material",
        configuration = {FeignConfiguration.class},
        fallback = MaterialFeignCustomFallback.class)
public interface MaterialFeignCustomClient {

    /**
     * 包材接口，给其他平台调用
     * @param current
     * @param size
     * @param networkId
     * @return
     */
    @ApiOperation(value = "包材接口", notes = "包材接口")
    @GetMapping("/getMaterialSalesPrice")
    Result<Page<MaterialVO>> getMaterialSalesPrice(@RequestParam("current") long current, @RequestParam("size") long size, @RequestParam("networkId") Integer networkId);
}
