package com.yl.vip.feign.fallback;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.vip.dto.EssWarehouseStockSubordinateQueryApiDTO;
import com.yl.vip.dto.StockNumQueryDTO;
import com.yl.vip.feign.EssWarehouseStockSubordinateFeignClient;
import com.yl.vip.vo.EssWarehouseStockSubordinateApiVO;
import com.yl.vip.vo.StockNumQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-10-14 15:25
 */
@Component
@Slf4j
public class EssWarehouseStockSubordinateFeignFallback implements EssWarehouseStockSubordinateFeignClient {
    @Override
    public Result<Page<EssWarehouseStockSubordinateApiVO>> page(EssWarehouseStockSubordinateQueryApiDTO var1, long var2, long var4) {
        log.error("========================EssWarehouseStockSubordinateFeignClient服务调用[page]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<StockNumQueryVO> getStockNumQuery(StockNumQueryDTO dto) {
        log.error("========================EssWarehouseStockSubordinateFeignClient服务调用[getStockNumQuery]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }
}
