package com.yl.vip.feign.fallback;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.vip.dto.*;
import com.yl.vip.feign.OmsOrderGateWayFeign;
import com.yl.vip.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-10-14 15:34
 */
@Component
@Slf4j
public class OmsOrderGateWayFeignFallback implements OmsOrderGateWayFeign {
    @Override
    public Result<OmsOrderApiVO> saveVipOrder(@Valid OmsOrderApiVipDTO param) {
        log.error("========================OmsOrderGateWayFeign服务调用[saveVipOrder]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsBatchSaveVipOrderInfo> batchSaveVipOrder(OmsOrderApiVipListDTO param) {
        log.error("========================OmsOrderGateWayFeign服务调用[batchSaveVipOrder]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsOrderUpdateApiVO> updateOrder(OmsOrderUpdateApiDTO omsOrderUpdateApiDTO) {
        log.error("========================OmsOrderGateWayFeign服务调用[updateOrder]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsOrderCancelApiVO> cancelOrder(OmsOrderCancelApiDTO omsOrderCancelApiDTO) {
        log.error("========================OmsOrderGateWayFeign服务调用[cancelOrder]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsOrderDeleteApiVO> delete(Long id) {
        log.error("========================OmsOrderGateWayFeign服务调用[delete]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsOrderDeleteApiVO> deleteOrders(List<Long> ids) {
        log.error("========================OmsOrderGateWayFeign服务调用[deleteOrders]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }

    /**
     * 根据客户订单号查询
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<OmsOrderApiVO>> orderOfCustomerAndOrderId(OmsOrderApiQueryDto dto) {
        log.error("========================OmsOrderGateWayFeign服务调用[orderOfCustomerAndOrderId]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }
}
