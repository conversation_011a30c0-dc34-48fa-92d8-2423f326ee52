package com.yl.vip.feign.fallback;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.vip.feign.OpsRebackExpressListFeign;
import com.yl.vip.vo.RebackExpressListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-10-14 15:45
 */
@Component
@Slf4j
public class OpsRebackExpressListFeignFallback implements OpsRebackExpressListFeign {
    @Override
    public Result<RebackExpressListVO> getDetailByWaybillNo(String waybillNo) {
        log.error("========================OpsRebackExpressListFeign服务调用[getDetailByWaybillNo]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }
}
