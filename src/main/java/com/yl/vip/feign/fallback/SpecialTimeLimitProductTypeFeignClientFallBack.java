package com.yl.vip.feign.fallback;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.vip.dto.SpecialTimeLimitProductType;
import com.yl.vip.feign.SpecialTimeLimitProductTypeFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SpecialTimeLimitProductTypeFeignClientFallBack implements SpecialTimeLimitProductTypeFeignClient {

    @Override
    public Result<List<SpecialTimeLimitProductType>> getList(Integer sendCityId, Integer dispatchCityId, String networkCode) {
        log.error("========================SmsFeignClient服务调用[base]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }
}
