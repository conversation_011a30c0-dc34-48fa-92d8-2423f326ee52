//package com.yl.vip.mapper.vip;
//
//import com.yl.vip.entity.VipUser;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import org.apache.ibatis.annotations.Param;
//
//import java.util.List;
//
///**
// * <p>
// * vip用户 Mapper 接口
// * </p>
// *
// * <AUTHOR> * @since 2019-10-14
// */
//public interface VipUserMapper extends BaseMapper<VipUser> {
//
//    /**
//     * 更新状态
//     *
//     * @param status
//     * @param ids
//     * @return
//     */
//    int updateStatus(@Param("status") Integer status, @Param("ids") List<Integer> ids);
//
//    /**
//     * 根据客户编码修改客户名称
//     *
//     * @param customerName
//     * @param customerCode
//     * @return
//     */
//    int updateByCode(@Param("quoteId") Integer quoteId, @Param("customerName") String customerName, @Param("customerCode") String customerCode,@Param("staffCode") String staffCode,@Param("staffName") String staffName);
//
//}
