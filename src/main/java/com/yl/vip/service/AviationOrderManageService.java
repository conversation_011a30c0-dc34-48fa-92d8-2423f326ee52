package com.yl.vip.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.common.base.model.vo.Result;
import com.yl.vip.dto.OmsAviationWaybillVipPageDTO;
import com.yl.vip.vo.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface AviationOrderManageService {

    Result<Page<OmsAviationWaybillPageVO>> page(OmsAviationWaybillVipPageDTO queryDTO, UserSessionVo user);

    Result<String> saveAviationOrder(AviationOrderVo aviationOrderVo, UserSessionVo user);

    Result<List<AviationOrderExcelVo>> batchSaveAviationOrder(List<AviationOrderExcelVo> excelVoList, UserSessionVo user);

    AviationOrderExcelImportVO excelImport(MultipartFile file, UserSessionVo user) throws IOException;

    Result<List<BasicDataVO>> getAviationBasicData(Integer code);

    void downloadTemplate(HttpServletRequest request, HttpServletResponse response) throws Exception;
}
