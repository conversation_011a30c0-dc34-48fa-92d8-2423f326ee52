package com.yl.vip.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.vip.dto.AddressDTO;
import com.yl.vip.entity.YlVipAddress;
import com.yl.vip.vo.AddressExcelImportVO;
import com.yl.vip.vo.AddressVo;
import com.yl.vip.vo.UserSessionVo;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 11:13
 */
public interface YlVipAddressService extends IService<YlVipAddress>{

    /**
     * 地址列表
     *
     * @param page
     * @param keyword
     * @param type
     * @param user
     * @return
     */
    IPage<AddressVo> getPages(Page<YlVipAddress> page, String keyword, Integer type, UserSessionVo user);

    /**
     * 新增
     *
     * @param dto
     * @param user
     * @return
     */
    Boolean save(AddressDTO dto, UserSessionVo user);

    /**
     * 修改
     *
     * @param dto
     * @param user
     * @return
     */
    Boolean update(AddressDTO dto, UserSessionVo user, String requestId, StopWatch stopWatch);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean deleteById(Long id);
    /**
     * 设置默认地址
     *
     * @return
     */
    Boolean setDefaultAddress(Long id, Integer type, UserSessionVo user);
    /**
     * 该系列账号是否有默认地址
     *
     * @return
     */
    Boolean hasDefaultAddress(Integer type, UserSessionVo user);

    List<YlVipAddress> findAllAddress(Integer type, String keyword, UserSessionVo user);

    AddressVo getDefaultAddress(Integer type, UserSessionVo user);


    /**
     * 批量导入地址
     * @param file
     * @return
     */
    AddressExcelImportVO importData(MultipartFile file, UserSessionVo userSessionVo);

    /**
     * 根据ID获取地址信息
     * @param id
     * @return
     */
    YlVipAddress selectById(Long id);

    int queryIsExistByCondition(AddressDTO addressDTO);

    /**
     * 根据userId 批量删除
     * @param userIds
     * @return
     */
    boolean delByUserIds(List<Integer> userIds);

}
