package com.yl.vip.service.impl;

import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.SysAreaTypeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.StringUtils;
import com.yl.vip.constant.VipCacheConstant;
import com.yl.vip.dto.SysAreaOnlineConfigQueryDTO;
import com.yl.vip.enums.SysAreaOnlineConfigTypeEnum;
import com.yl.vip.feign.SysAreaFeignClient;
import com.yl.vip.feign.SysAreaOnlineConfigFeignClient;
import com.yl.vip.service.IAppAreaService;
import com.yl.vip.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019-08-23
 */
@Service
@Slf4j
public class AppAreaServiceImpl implements IAppAreaService {

    @Autowired
    private SysAreaFeignClient sysAreaFeignClient;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private SysAreaOnlineConfigFeignClient sysAreaOnlineConfigFeignClient;

    @Override
    public List<SysArea> selectByCondition(SysAreaOnlineConfigQueryDTO dto) {
        dto.setPlateformType(SysAreaOnlineConfigTypeEnum.TYPE_VIP.getType());
        return sysAreaOnlineConfigFeignClient.findOnLineConfigSendAreas(dto).result();
    }

    /**
     * 模糊查询开通上线的区域详情
     *
     * @param dto
     * @return
     */
    @Override
    public List<SysAreaOnlineConfigVo> selectByFuzzyCondition(SysAreaOnlineConfigQueryDTO dto) {
        dto.setPlateformType(SysAreaOnlineConfigTypeEnum.TYPE_VIP.getType());
        return sysAreaOnlineConfigFeignClient.findOnLineConfigSendList(dto).result();
    }

    @Override
    public Boolean check(Integer provinceId, Integer cityId, Integer areaId, Integer townId) {
        if (Objects.isNull(provinceId)
                || Objects.isNull(cityId)
                || Objects.isNull(areaId)) {
            return false;
        }

        Integer param = Optional.ofNullable(townId).orElse(areaId);
        Result<List<SysAreaDetailVO>> result = sysAreaFeignClient.getParentAreaList(param);
        if (result.isFail() || CollectionUtils.isEmpty(result.getData())) {
            return false;
        }
        List<SysAreaDetailVO> areaList = result.getData();

        List<SysAreaDetailVO> areas = areaList.stream().filter(sysArea -> SysAreaTypeEnum.PROVINCE.getId().equals(sysArea.getType())
                && provinceId.equals(sysArea.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areas)) {
            return false;
        }
        areas = areaList.stream().filter(sysArea -> provinceId.equals(sysArea.getParentId()) && SysAreaTypeEnum.CITY.getId().equals(sysArea.getType())
                && cityId.equals(sysArea.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areas)) {
            return false;
        }
        areas = areaList.stream().filter(sysArea -> cityId.equals(sysArea.getParentId()) && SysAreaTypeEnum.AREA.getId().equals(sysArea.getType())
                && areaId.equals(sysArea.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areas)) {
            return false;
        }
        if (Objects.nonNull(townId)) {
            areas = areaList.stream().filter(sysArea -> areaId.equals(sysArea.getParentId()) && SysAreaTypeEnum.TOWN.getId().equals(sysArea.getType())
                    && townId.equals(sysArea.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(areas)) {
                return false;
            }
        }
        return true;
    }

}
