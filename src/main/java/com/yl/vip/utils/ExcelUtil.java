package com.yl.vip.utils;

import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.vip.enums.exception.ExcelExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public class ExcelUtil {
    public static final String SIMSUN = "simsun";
    public static final String TIME = "Time";


    public static <T> XSSFWorkbook exportExcel(List<String> sort, List<String> columnNames, List<T> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException(ExcelExceptionEnum.DATA_LIST_IS_NULL);
        }
        if (CollectionUtils.isEmpty(sort) || CollectionUtils.isEmpty(columnNames)) {
            throw new BusinessException(ExcelExceptionEnum.SORT_LIST_IS_NULL);
        }
        XSSFWorkbook workbook = new XSSFWorkbook();
        //创建标题
        Sheet sheet = createTitle(workbook, columnNames);
        //按顺序写数据
        writeRowsToExcel(sheet, dataList, sort);
        return workbook;
    }

    private static <T> void writeRowsToExcel(Sheet sheet, List<T> dataList, List<String> sort) {
        //创建数据行并写入值
        for (int j = 0; j < dataList.size(); j++) {
            T t = dataList.get(j);
            try {
                Map<String, String> objectMap = ObjectUtil.objectToMap(t);
                int lastRowNum = sheet.getLastRowNum();
                Row dataRow = sheet.createRow(lastRowNum + 1);
                for (int k = 0; k < sort.size(); k++) {
                    String value = objectMap.get(sort.get(k));
                    if (sort.get(k).contains(TIME)) {
                        value = value.replace("T", " ");
                        if (value.length() == 16) {
                            value = value + ":00";
                        }
                    }
                    dataRow.createCell(k).setCellValue(value);
                }
            } catch (Exception e) {
                log.error("writeRowsToExcel error {}", e);
            }
        }
    }

    private static Sheet createTitle(XSSFWorkbook workbook, List<String> columnNames) {
        Sheet sheet = workbook.createSheet();
        Font titleFont = workbook.createFont();
        titleFont.setFontName(SIMSUN);
        titleFont.setBold(true);
        titleFont.setColor(IndexedColors.BLACK.index);
        XSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        titleStyle.setFillForegroundColor(IndexedColors.YELLOW.index);
        titleStyle.setFont(titleFont);
        Row titleRow = sheet.createRow(0);
        for (int i = 0; i < columnNames.size(); i++) {
            Cell cell = titleRow.createCell(i);
            cell.setCellValue(columnNames.get(i));
            cell.setCellStyle(titleStyle);
        }
        return sheet;
    }


    public static void writeExcel(XSSFWorkbook xssfWorkbook, HttpServletResponse response, String fileName) {
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        try {
            response.flushBuffer();
            xssfWorkbook.write(response.getOutputStream());
        } catch (IOException e) {
            log.error("write excel error {}", e);
        }

    }


    public static void buildExcelFile(String filename, Workbook workbook) throws Exception {
        FileOutputStream fos = new FileOutputStream(filename);
        workbook.write(fos);
        fos.flush();
        fos.close();
    }

    //浏览器下载excel
    public static void buildExcelDocument(String filename, Workbook workbook, HttpServletResponse response) throws Exception {
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "utf-8"));
        response.setHeader("Access-Control-Expose-Headers", "content-Disposition");
        OutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    /**
     * 读取 Excel(多个 sheet)
     *
     * @param excel    文件
     * @param rowModel 实体类映射，继承 BaseRowModel 类
     * @return Excel 数据 list
     */
    public static List<Object> readExcel(MultipartFile excel, BaseRowModel rowModel) {
        ModelExcelListener excelListener = new ModelExcelListener();
        ExcelReader reader = getReader(excel, excelListener);
        if (reader == null) {
            return null;
        }
        for (com.alibaba.excel.metadata.Sheet sheet : reader.getSheets()) {
            if (rowModel != null) {
                sheet.setClazz(rowModel.getClass());
            }
            reader.read(sheet);
        }
        return excelListener.getDatas();
    }

    /**
     * 读取某个 sheet 的 Excel
     *
     * @param excel    文件
     * @param rowModel 实体类映射，继承 BaseRowModel 类
     * @param sheetNo  sheet 的序号 从1开始
     * @return Excel 数据 list
     */
    public static List<Object> readExcel(MultipartFile excel, BaseRowModel rowModel, int sheetNo) {
        try {
            return readExcel(excel, rowModel, sheetNo, 1);
        } catch (Exception e) {
            log.warn("文件读取异常：{}",e);
            throw new BusinessException(ResultCodeEnum.FILE_READ_ERROR);
        }
    }

    /**
     * 读取某个 sheet 的 Excel
     *
     * @param excel       文件
     * @param rowModel    实体类映射，继承 BaseRowModel 类
     * @param sheetNo     sheet 的序号 从1开始
     * @param headLineNum 表头行数，默认为1
     * @return Excel 数据 list
     */
    public static List<Object> readExcel(MultipartFile excel, BaseRowModel rowModel, int sheetNo,
                                         int headLineNum) {
        ModelExcelListener excelListener = new ModelExcelListener();
        ExcelReader reader = getReader(excel, excelListener);
        if (reader == null) {
            return Collections.emptyList();
        }
        reader.read(new com.alibaba.excel.metadata.Sheet(sheetNo, headLineNum, rowModel.getClass()));
        List<Object> readData = excelListener.getDatas();
        return readData;
    }

    /**
     * 读取某个 sheet 的 Excel
     *
     * @param excel       文件
     * @param sheetNo     sheet 的序号 从1开始
     * @param headLineNum 表头行数，默认为1
     * @return Excel 数据 list
     */
    public static List<Object> readExcel(MultipartFile excel, int sheetNo,
                                         int headLineNum) {
        ModelExcelListener excelListener = new ModelExcelListener();
        ExcelReader reader = getReader(excel, excelListener);
        if (reader == null) {
            return null;
        }
        reader.read(new com.alibaba.excel.metadata.Sheet(sheetNo, headLineNum));
        List<Object> readData = excelListener.getDatas();
        return readData;
    }

    /**
     * 读取某个 sheet 的 Excel  默认读取 sheetNo=1, headLineNum=1
     *
     * @param excel 文件
     * @return Excel 数据 list
     */
    public static List<Object> readExcel(MultipartFile excel) {
        return readExcel(excel, 1, 1);
    }

    /**
     * 读取某个 sheet 的 Excel 表头
     *
     * @param excel 文件
     * @return Excel 表头数据 list
     */
    public static List<Object> readExcelHeader(MultipartFile excel, int sheetNo) {
        ModelExcelListener excelListener = new ModelExcelListener();
        ExcelReader reader = getReader(excel, excelListener);
        if (reader == null) {
            return null;
        }
        reader.read(new com.alibaba.excel.metadata.Sheet(sheetNo, 0));
        List<Object> datas = excelListener.getDatas();
        return datas;
    }

    /**
     * 导出 Excel到浏览器：一个 sheet，带表头
     *
     * @param response  HttpServletResponse
     * @param list      数据 list，每个元素为一个 BaseRowModel
     * @param fileName  导出的文件名
     * @param sheetName 导出文件的 sheet 名
     * @param object    映射实体类，Excel 模型
     */
    public static void writeExcel(HttpServletResponse response, List<? extends BaseRowModel> list,
                                  String fileName, String sheetName, BaseRowModel object) {
        ExcelWriter writer = new ExcelWriter(getOutputStream(fileName, response), ExcelTypeEnum.XLSX);
        com.alibaba.excel.metadata.Sheet sheet = new com.alibaba.excel.metadata.Sheet(1, 0, object.getClass());
        sheet.setSheetName(sheetName);
        writer.write(list, sheet);
        writer.finish();
    }

    /**
     * 导出 Excel到指定目录：一个 sheet，带表头
     *
     * @param list      数据 list，每个元素为一个 BaseRowModel
     * @param fileName  导出的文件名,完整目录和后缀
     * @param sheetName 导出文件的 sheet 名
     * @param object    映射实体类，Excel 模型
     */
    public static void writeExcel2Directory(List<? extends BaseRowModel> list,
                                            String fileName, String sheetName, BaseRowModel object) {
        ExcelWriter writer = new ExcelWriter(getOutputStream(fileName), ExcelTypeEnum.XLSX);
        com.alibaba.excel.metadata.Sheet sheet = new com.alibaba.excel.metadata.Sheet(1, 0, object.getClass());
        sheet.setSheetName(sheetName);
        writer.write(list, sheet);
        writer.finish();
    }

    /**
     * 导出 Excel到浏览器：多个 sheet，带表头
     *
     * @param response  HttpServletResponse
     * @param list      数据 list，每个元素为一个 BaseRowModel
     * @param fileName  导出的文件名
     * @param sheetName 导出文件的 sheet 名
     * @param object    映射实体类，Excel 模型
     */
    public static ExcelWriterFactroy writeExcelWithSheets(HttpServletResponse response, List<? extends BaseRowModel> list,
                                                          String fileName, String sheetName, BaseRowModel object) {
        ExcelWriterFactroy writer = new ExcelWriterFactroy(getOutputStream(fileName, response), ExcelTypeEnum.XLSX, 1);
        com.alibaba.excel.metadata.Sheet sheet = new com.alibaba.excel.metadata.Sheet(1, 0, object.getClass());
        sheet.setSheetName(sheetName);
        writer.write(list, sheet);
        return writer;
    }

    /**
     * 导出文件时为Writer生成OutputStream（浏览器导出）
     *
     * @param fileName 文件名（不含后缀）
     * @param response
     * @return
     */
    private static OutputStream getOutputStream(String fileName, HttpServletResponse response) {
        try {
            fileName = new String(fileName.getBytes("GB2312"), "ISO_8859_1") + ".xlsx";
            response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
            return response.getOutputStream();
        } catch (IOException e) {
            throw new BusinessException(ResultCodeEnum.FILE_CREATE_ERROR.getMsg());
        }
    }

    /**
     * 导出文件到指定目录
     *
     * @param fileName 文件路径含文件名和目录
     * @return
     */
    private static OutputStream getOutputStream(String fileName) {
        try {
            return new FileOutputStream(fileName);
        } catch (IOException e) {
            log.error("系统异常，获取输出流失败", e);
            throw new BusinessException(ResultCodeEnum.SYSTEM_INNER_ERROR);
        }
    }

    /**
     * 返回 ExcelReader
     *
     * @param excel         需要解析的 Excel 文件
     * @param excelListener new ModelExcelListener()
     */
    private static ExcelReader getReader(MultipartFile excel,
                                         ModelExcelListener excelListener) {
        String filename = excel.getOriginalFilename();
        if (filename == null || (!filename.toLowerCase().endsWith(".xls") && !filename.toLowerCase().endsWith(".xlsx"))) {
            throw new BusinessException(ResultCodeEnum.FILE_TYPE_ERROR);
        }
        InputStream inputStream;
        try {
            inputStream = new BufferedInputStream(excel.getInputStream());
            return new ExcelReader(inputStream, null, excelListener, false);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static class ModelExcelListener extends AnalysisEventListener {

        //自定义用于暂时存储data。
        //可以通过实例获取该值
        private List<Object> datas = new ArrayList<>();

        /**
         * 通过 AnalysisContext 对象还可以获取当前 sheet，当前行等数据
         */
        @Override
        public void invoke(Object object, AnalysisContext context) {
            //数据存储到list，供批量处理，或后续自己业务逻辑处理。
            datas.add(object);
            //根据业务自行 do something
        }

        /**
         * 根据业务自行实现该方法
         */
        private void doSomething() {

        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            //datas.clear();
            //解析结束销毁不用的资源
        }

        public List<Object> getDatas() {
            return datas;
        }

        public void setDatas(List<Object> datas) {
            this.datas = datas;
        }
    }
}
