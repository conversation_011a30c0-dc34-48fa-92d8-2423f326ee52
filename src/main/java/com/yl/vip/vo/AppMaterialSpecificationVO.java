package com.yl.vip.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
public class AppMaterialSpecificationVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty(
            name = "id",
            value = "id"
    )
    private Long id;
    @ApiModelProperty(
            name = "materialCode",
            value = "物料编码"
    )
    private String materialCode;
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long materialId;
    @ApiModelProperty(
            name = "materialName",
            value = "物料名称"
    )
    private String materialName;
    @ApiModelProperty(
            name = "smallMaterialName",
            value = "物料小类名称"
    )
    private String smallMaterialName;
    @ApiModelProperty(
            name = "smallMaterialCode",
            value = "物料小类编码"
    )
    private String smallMaterialCode;
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long smallMaterialId;
    @ApiModelProperty(
            name = "specification",
            value = "规格"
    )
    private String specification;
    @ApiModelProperty(
            name = "salePrice",
            value = "销售价格"
    )
    private BigDecimal salePrice;
    @ApiModelProperty(
            name = "isEnable",
            value = "是否启用"
    )
    private Integer isEnable;
    @ApiModelProperty(
            name = "isDelete",
            value = "是否删除"
    )
    private Integer isDelete;
    @ApiModelProperty(
            name = "version",
            value = "版本号"
    )
    private String version;
    @ApiModelProperty(
            name = "sort",
            value = "排序"
    )
    private Integer sort;
}