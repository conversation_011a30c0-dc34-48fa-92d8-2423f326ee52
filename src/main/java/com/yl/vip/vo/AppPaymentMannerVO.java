package com.yl.vip.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class AppPaymentMannerVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(
            name = "name",
            value = "名称"
    )
    private String name;
    @ApiModelProperty(
            name = "code",
            value = "付款方式编码"
    )
    private String code;
    @ApiModelProperty(
            name = "id",
            value = "id"
    )
    private Integer id;

}
