package com.yl.vip.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AviationOrderExcelVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "客户订单号", index = 0)
    private String customerOrderId;

    @ExcelProperty(value = "寄件人", index = 1)
    private String senderName;

    @ExcelProperty(value = "寄件公司", index = 2)
    private String senderCompany;

    @ExcelProperty(value = "寄件人电话", index = 3)
    private String senderMobilePhone;

    @ExcelProperty(value = "寄件人邮编", index = 4)
    private String senderPostalCode;

    @ExcelProperty(value = "寄件省份", index = 5)
    private String senderProvinceName;

    @ExcelIgnore
    private Integer senderProvinceId;

    @ExcelProperty(value = "寄件城市", index = 6)
    private String senderCityName;

    @ExcelIgnore
    private Integer senderCityId;

    @ExcelProperty(value = "寄件区域", index = 7)
    private String senderAreaName;

    @ExcelIgnore
    private Integer senderAreaId;

    @ExcelProperty(value = "寄件乡镇", index = 8)
    private String senderTownship;

    @ExcelIgnore
    private Integer senderTownshipId;

    @ExcelProperty(value = "寄件地址", index = 9)
    private String senderDetailedAddress;

    @ExcelProperty(value = "收件人", index = 10)
    private String receiverName;

    @ExcelProperty(value = "收件公司", index = 11)
    private String receiverCompany;

    @ExcelProperty(value = "收件人电话", index = 12)
    private String receiverMobilePhone;

    @ExcelProperty(value = "收件人邮编", index = 13)
    private String receiverPostalCode;

    @ExcelProperty(value = "收件省份", index = 14)
    private String receiverProvinceName;

    @ExcelIgnore
    private Integer receiverProvinceId;

    @ExcelProperty(value = "收件城市", index = 15)
    private String receiverCityName;

    @ExcelIgnore
    private Integer receiverCityId;

    @ExcelProperty(value = "收件区域", index = 16)
    private String receiverAreaName;

    @ExcelIgnore
    private Integer receiverAreaId;

    @ExcelProperty(value = "收件乡镇", index = 17)
    private String receiverTownship;

    @ExcelIgnore
    private Integer receiverTownshipId;

    @ExcelProperty(value = "收件地址", index = 18)
    private String receiverDetailedAddress;

    @ExcelProperty(value = "物品类型", index = 19)
    private String goodsTypeName;

    @ExcelIgnore
    private String goodsTypeCode;

    @ExcelIgnore
    private String goodsTypeId;

    @ExcelProperty(value = "物品名称", index = 20)
    private String goodsName;

    @ExcelProperty(value = "件数", index = 21)
    private String packageNumber;

    @ExcelProperty(value = "物品重量", index = 22)
    private String packageTotalWeight;

    @ExcelIgnore
    private BigDecimal packageChargeWeight;

    @ExcelProperty(value = "总体积", index = 23)
    private String packageTotalVolume;

    @ExcelProperty(value = "保价金额", index = 24)
    private String declaredValue;

    @ExcelIgnore
    private Integer insured;

    @ExcelProperty(value = "支付方式", index = 25)
    private String paymentModeName;

    @ExcelIgnore
    private String paymentModeCode;

    @ExcelIgnore
    private String paymentModeId;

    @ExcelProperty(value = "签回单 0否  1是", index = 26)
    private String signReceiptName;

    @ExcelIgnore
    private Integer signReceipt;

    @ExcelProperty(value = "包装类型", index = 27)
    private String packagingTypeName;

    @ExcelIgnore
    private String packagingTypeCode;

    @ExcelIgnore
    private String packagingTypeId;

    @ExcelProperty(value = "备注", index = 28)
    private String remarks;

    @ExcelIgnore
    private Integer formIndex;

    @ExcelIgnore
    private String errorMsg;

    /**
     * 导入是否失效  失效:true
     **/
    @ExcelIgnore
    private Boolean isInvalid;

    /**
     * 客户订单号是否失效  失效:true
     **/
    @ExcelIgnore
    private Boolean isInvalidCustomerOrderId;

    // 用于判断异常行数据
    @ExcelIgnore
    private String rowNum;


}
