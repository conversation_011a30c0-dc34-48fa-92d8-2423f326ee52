package com.yl.vip.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "下载中心返回响应", description = "下载中心返回响应")
public class DownExcelVO {

	@ApiModelProperty(value = "记录编号")
	private String infoId;
	@ApiModelProperty(value = "下载Excel的来源")
	private String downSite;
	@ApiModelProperty(value = "导出时间")
	private String downTime;
	@ApiModelProperty(value = "查询条件")
	private String queryJson;
	@ApiModelProperty(value = "是否完成")
	private String finishOrNot;
	@ApiModelProperty(value = "模块名称")
	private String moduleType;
	@ApiModelProperty(value = "导出类型")
	private String exceltype;
	@ApiModelProperty(value = "导出Excel地址")
	private String downUrl;
	@ApiModelProperty(value = "锁定标识")
	private String lockSign;
	@ApiModelProperty(value = "锁定时间")
	private String lockTime;
	@ApiModelProperty(value = "服务器IP")
	private String serviceIP;
	@ApiModelProperty(value = "是否下载")
	private String finishDownLoad;
	
	public String getInfoId() {
		return infoId;
	}
	public void setInfoId(String infoId) {
		this.infoId = infoId;
	}
	public String getDownSite() {
		return downSite;
	}
	public void setDownSite(String downSite) {
		this.downSite = downSite;
	}
	public String getDownTime() {
		return downTime;
	}
	public void setDownTime(String downTime) {
		this.downTime = downTime;
	}
	public String getQueryJson() {
		return queryJson;
	}
	public void setQueryJson(String queryJson) {
		this.queryJson = queryJson;
	}
	public String getFinishOrNot() {
		return finishOrNot;
	}
	public void setFinishOrNot(String finishOrNot) {
		this.finishOrNot = finishOrNot;
	}
	public String getModuleType() {
		return moduleType;
	}
	public void setModuleType(String moduleType) {
		this.moduleType = moduleType;
	}
	public String getExceltype() {
		return exceltype;
	}
	public void setExceltype(String exceltype) {
		this.exceltype = exceltype;
	}
	public String getDownUrl() {
		return downUrl;
	}
	public void setDownUrl(String downUrl) {
		this.downUrl = downUrl;
	}
	public String getLockSign() {
		return lockSign;
	}
	public void setLockSign(String lockSign) {
		this.lockSign = lockSign;
	}
	public String getLockTime() {
		return lockTime;
	}
	public void setLockTime(String lockTime) {
		this.lockTime = lockTime;
	}
	public String getServiceIP() {
		return serviceIP;
	}
	public void setServiceIP(String serviceIP) {
		this.serviceIP = serviceIP;
	}
	public String getFinishDownLoad() {
		return finishDownLoad;
	}
	public void setFinishDownLoad(String finishDownLoad) {
		this.finishDownLoad = finishDownLoad;
	}
	
}
