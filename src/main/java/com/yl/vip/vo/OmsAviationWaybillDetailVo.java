package com.yl.vip.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName OmsAviationWaybillDetailVo
 * @Description
 * <AUTHOR>
 * @Date 2024/11/12
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OmsAviationWaybillDetailVo响应对象", description = "航空运单详情")
public class OmsAviationWaybillDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 运单号
     */

    private String waybillNo;
    /**
     * 内部订单编号
     */

    private Long orderId;

    /**
     * 运单来源code
     */

    private String waybillSourceCode;

    /**
     * 运单来源名称
     */

    private String waybillSourceName;

    private Integer customerId;

    /**
     * 客户编号code
     */

    private String customerCode;

    /**
     * 客户编号名称
     */

    private String customerName;

    /**
     * 客户网点code
     */
    private String customerNetworkCode;

    /**
     * 客户编号网点名称
     */
    private String customerNetworkName;

    /**
     * 客户所属：1-总部共享,2-代理区共享,3-自有客户
     */
    private Integer customerType;

    /**
     * 客户订单编号
     */

    private String customerOrderId;

    /**
     * 供应商
     */
    private String supplierName;

    /**
     * 寄件人姓名
     */

    private String senderName;

    /**
     * 寄件人手机号
     */

    private String senderMobilePhone;

    /**
     * 寄件人座机
     */

    private String senderTelphone;

    /**
     * 寄件国家Id
     */

    private Integer senderCountryId;

    /**
     * 寄件国家名称
     */

    private String senderCountryName;

    /**
     * 寄件省份id
     */

    private Integer senderProvinceId;

    /**
     * 寄件省份名称
     */

    private String senderProvinceName;

    /**
     * 寄件城市id
     */

    private Integer senderCityId;

    /**
     * 寄件城市名称
     */

    private String senderCityName;

    /**
     * 寄件区域Id
     */

    private Integer senderAreaId;

    /**
     * 寄件区域名称
     */

    private String senderAreaName;

    /**
     * 寄件乡镇
     */

    private String senderTownship;

    /**
     * 寄件街道
     */

    private String senderStreet;

    /**
     * 寄件详细地址
     */

    private String senderDetailedAddress;

    /**
     * 寄件邮编
     */

    private String senderPostalCode;
    /**
     * 寄件网点id
     */
    private Integer pickNetworkId;
    /**
     * 寄件网点code
     */

    private String pickNetworkCode;

    /**
     * 寄件网点名称
     */

    private String pickNetworkName;

    /**
     * 寄件乡镇
     */

    private Long senderTownshipId;

    /**
     * 寄件所属一级网点编码
     */
    private String parentNetworkCode;

    /**
     * 寄件所属一级网点名称
     */
    private String parentNetworkName;
    /**
     * 寄件公司
     */
    private String senderCompany;
    /**
     * 寄件财务中心id
     */
    private Integer pickFinanceId;
    /**
     * 寄件财务中心code
     */

    private String pickFinanceCode;

    /**
     * 寄件财务中心名称
     */

    private String pickFinanceName;

    /**
     * 收件人姓名
     */

    private String receiverName;

    /**
     * 收件人公司
     */

    private String receiverCompany;

    /**
     * 收件人手机号
     */

    private String receiverMobilePhone;

    /**
     * 收件人座机
     */

    private String receiverTelphone;

    /**
     * 收件国家id
     */

    private Integer receiverCountryId;

    /**
     * 收件国家名称
     */

    private String receiverCountryName;

    /**
     * 收件省份id
     */

    private Integer receiverProvinceId;

    /**
     * 收件省份名称
     */

    private String receiverProvinceName;

    /**
     * 收件城市id
     */

    private Integer receiverCityId;

    /**
     * 收件城市名称
     */

    private String receiverCityName;

    /**
     * 收件区域id
     */

    private Integer receiverAreaId;

    /**
     * 收件区域名称
     */

    private String receiverAreaName;

    /**
     * 收件乡镇ID
     */

    private Integer receiverTownshipId;
    /**
     * 收件乡镇
     */

    private String receiverTownship;

    /**
     * 收件街道
     */

    private String receiverStreet;

    /**
     * 收件详细地址
     */

    private String receiverDetailedAddress;

    /**
     * 收件邮编
     */

    private String receiverPostalCode;

    /**
     * 包裹总长,单位厘米
     */

    private BigDecimal packageLength;

    /**
     * 包裹总宽,单位厘米
     */

    private BigDecimal packageWide;

    /**
     * 包裹总高,单位厘米
     */

    private BigDecimal packageHigh;
    /**
     * 件数
     */

    private Integer packageNumber;

    /**
     * 包裹总重量,单位千克
     */

    private BigDecimal packageTotalWeight;

    /**
     * 包裹总体积,单位立方厘米
     */

    private BigDecimal packageTotalVolume;

    /**
     * 物品名称
     */

    private String goodsName;

    /**
     * 物品类型id
     */

    private Integer goodsTypeId;

    /**
     * 物品类型code
     */

    private String goodsTypeCode;

    /**
     * 物品类型名称
     */

    private String goodsTypeName;

    /**
     * 物品价值
     */

    private BigDecimal insuredAmount;

    /**
     * 损坏保护
     */

    private BigDecimal insuredFee;

    /**
     * 是否签回单,1是，0否
     */

    private Integer isNeedReceipt;

    /**
     * 城市运费
     */
    private BigDecimal freight;

    /**
     * 转运费
     */
    private BigDecimal transferFee;

    /**
     * 运输费
     */
    private BigDecimal freightFee;

    /**
     * 商品附加费
     */
    private BigDecimal productSurchargeFee;

    /**
     * 重量附加费
     */
    private BigDecimal weightAdditionFee;

    /**
     * 包装费
     */

    private BigDecimal packageCost;

    /**
     * 其他附加费
     */

    private BigDecimal otherFee;

    /**
     * 退货费
     */

    private BigDecimal returnFee;

    /**
     * 总运费
     */

    private BigDecimal totalFreight;

    /**
     * 到付方式id
     */

    private Integer settlementId;

    /**
     * 到付方式编码
     */

    private String settlementCode;

    /**
     * 到付方式名称
     */

    private String settlementName;

    /**
     * 备注
     */

    private String remarks;

    /**
     * 派件业务员code
     */

    private String dispatchStaffCode;

    /**
     * 派件业务员名称
     */

    private String dispatchStaffName;

    /**
     * 派件网点id
     */
    private Integer dispatchNetworkId;

    /**
     * 派件网点code
     */

    private String dispatchNetworkCode;

    /**
     * 派件网点名称
     */

    private String dispatchNetworkName;

    /**
     * 派件财务中心code
     */

    private String dispatchFinanceCode;

    /**
     * 派件财务中心名称
     */

    private String dispatchFinanceName;

    /**
     * 签收标识,1是，0否
     */

    private Integer isSign;

    /**
     * 签收网点id
     */

    private Integer signNetworkId;

    /**
     * 签收网点code
     */

    private String signNetworkCode;

    /**
     * 签收网点名称
     */

    private String signNetworkName;
    /**
     * 签收财务网点编码
     */
    private String signFinanceCode;
    /**
     * 签收财务网点名称
     */
    private String signFinanceName;

    /**
     * 是否作废件,1是，0否
     */

    private Integer isVoid;


    /**
     * 录入时间
     */

    private LocalDateTime inputTime;

    /**
     * 创建时间
     */

    private LocalDateTime createTime;
    /**
     * 寄件时间
     */

    private LocalDateTime deliveryTime;

    /**
     * 预估到达时间
     */

    private LocalDateTime estimateArriveTime;

    /**
     * 最后更新时间
     */

    private LocalDateTime lastUpdateTime;

    /**
     * 作废时间
     */
    private LocalDateTime voidTime;


    /**
     * 签收时间
     */

    private LocalDateTime signTime;


    /**
     * 派件时间
     */

    private LocalDateTime dispatchTime;

    /**
     * 录入人id
     */

    private Integer inputStaffBy;

    /**
     * 录入人code
     */

    private String inputStaffCode;

    /**
     * 录入人名称
     */

    private String inputStaffName;

    /**
     * 录入网点code
     */

    private String inputNetworkCode;

    /**
     * 录入网点名称
     */

    private String inputNetworkName;

    /**
     * 货币币别编码
     */

    private String currencyCode;

    /**
     * 货币币别名称
     */

    private String currencyName;

    /**
     * 是否删除,1未删除，2已删除
     */

    private Integer isDelete;

    /**
     * 寄/收件人信息数据是否明文
     */
    private Boolean isPlaintext;

    /**
     * 作废人的员工编号
     */
    private String voiderStaffNo;

    /**
     * 作废人的名称
     */
    private String voiderName;

    /**
     * 业务员code
     */
    private String salespersonCode;

    /**
     * 业务员名称
     */
    private String salespersonName;

    /**
     * 始发区域ID
     */
    private Integer startCountyId;
    /**
     * 始发区域编码
     */
    private String startCountyCode;
    /**
     * 始发区域名称
     */
    private String startCountyName;
    /**
     * 目的区域ID
     */
    private Integer terminalCountyId;
    /**
     * 目的区域编码
     */
    private String terminalCountyCode;
    /**
     * 目的区域名称
     */
    private String terminalCountyName;

    /**
     * 包装类型code
     */
    private String boxStandardCode;
    /**
     * 包装类型名称
     */
    private String boxStandardName;
    /**
     * 运输状态
     */
    private String transportationStatus;
}
