package com.yl.vip.vo;





import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since Created in 2019/10/10 16:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OmsWaybill状态统计响应对象", description = "运单表状态统计响应实体")
public class OmsVipWaybillStatusCountApiVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "运单状态code")
    private Integer waybillStatusCode;

    @ApiModelProperty(value = "运单状态")
    private String waybillStatusName;

    @ApiModelProperty(value = "运单状态统计值")
    private Long count;

}
