package com.yl.vip.vo;

import lombok.Data;

/**
 * @ClassName OrderPrintResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-07-15
 * @Version 1.0
 **/
@Data
public class OrderPrintResultVo {

    private String lastCenterName;
    private String receiverSortingCode;
    private String deliveryTime;
    private String senderCompany;
    private String senderProvinceName;
    private boolean insured;
    private long orderId;
    private String expressTypeName;
    private String senderTelphone;
    private String receiverMobilePhone;
    private int packageIndex;
    private String receiverCompany;
    private String receiverDetailedAddress;
    private int isPrivacy;
    private String packageNumber;
    private String packageChargeWeight;
    private String senderName;
    private String receiverSortingQRCode;
    private String middleCode;
    private String codMoney;
    private String receiverPostalCode;
    private int isBusiness;
    private String receiverTelphone;
    private int signReceipt;
    private String subWaybillIds;
    private String goodsName;
    private String receiverProvinceName;
    private String senderPostalCode;
    private String senderMobilePhone;
    private String senderCityName;
    private String receiverName;
    private String currencySymbol;
    private String lastCenterCode;
    private String index;
    private String senderDetailedAddress;
    private String senderAreaName;
    private String settlementName;
    private String expressTrackUrl;
    private String receiverCityName;
    private int codNeed;
    private String receiverAreaName;
    private String currencyCode;
    private String printTime;
    private String remarks;
    private String terminalDispatchCode;
    // 新三段码
    private String newTerminalDispatchCode;
    private String waybillNo;
    private Integer needDispatch;
    private String printsNumber;
    private String pickNetworkCode;
    private String paidModeName;
    private String receiptWaybillNo;
    private String packageVolume;
    private String totalFreight;
    private boolean isSub;
    private String pickNetworkName;
    private String dispatchName;
    private String settlementCode;
    private String companyName;
    private String companyTax;
    private String insuredFee;
    private String bigCustomer;

}
