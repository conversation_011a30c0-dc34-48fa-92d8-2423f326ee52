
======================================================================
OpenOffice 4.1.6 ReadMe
======================================================================


有关此自述文件的最新更新，请参阅  http://www.openoffice.org/welcome/readme.html

该文件包含有关此程序的重要信息。请在开始操作之前仔细阅读这些信息。

负责此产品开发的 Apache OpenOffice.org 社区诚挚邀请您加入社区并成为社区成员。作为新用户，您可以查看 OpenOffice 社区站点上的有用信息，网址为 http://openoffice.apache.org

也可以阅读下面来了解如何参与 Apache OpenOffice 项目。

OpenOffice 对任何用户确实是免费的吗？
----------------------------------------------------------------------

OpenOffice 对所有用户都是免费的。您可以获得此 OpenOffice 的副本，将其安装到您所需要的任何计算机上，并将其用于您需要的任何用途（包括商业、政府、公共管理和教育用途）。要了解详情，请参见 OpenOffice 随附的许可文本或 http://www.openoffice.org/license.html

为什么 OpenOffice 对任何用户都是免费的？
----------------------------------------------------------------------

现今，您可以免费使用 OpenOffice 的副本，这是由于个人贡献者和社区发起人已经设计、开发、测试、翻译、评注、支持、营销以及通过许多其他方式帮助 OpenOffice 成为今天 - 世界上最主要的开源办公软件。

如果您欣赏他们的努力， 并且愿意帮助Apache OpenOffice可以继续发展，您可以考虑为这个项目做出贡献，具体请参考http://openoffice.apache.org/get-involved.html，还可以参考http://www.apache.org/foundation/contributing.html来了解如何捐助这个项目。任何人都可以贡献其中。

----------------------------------------------------------------------
安装注意事项
----------------------------------------------------------------------

OpenOffice 要求一个较新版本的JAVA运行环境以使用它的全部功能； JAVA可以从http://java.com 下载。

系统要求
----------------------------------------------------------------------

* 微软 Windows XP, Vista, Windows 7 或者 Windows 8
* Pentium III 或更高系列处理器
* 256 MB RAM（建议使用 512 MB RAM）
* 高达 1.5 GB 的硬盘可用空间
* 1024x768 分辨率（建议使用更高分辨率），至少 256 色

请注意，安装过程中需要拥有管理员权限。

通过使用下列含有该安装程序的命令行开关项，可以强制或禁止将 OpenOffice 注册为 Microsoft Office 格式默认的应用程序：

* /msoreg=1 强制将 OpenOffice 注册为 Microsoft Office 格式默认的应用程序。
* /msoreg=0 禁止将 OpenOffice 注册为 Microsoft Office 格式默认的应用程序。

如果您使用 setup /a 执行管理安装，则需要确保系统上安装了 msvcr100.dll 文件。OpenOffice 需要此文件才能在管理安装后启动。您可以从 http://www.microsoft.com/en-us/download/details.aspx?id=5555 获取该文件。

请注意，安装过程中需要拥有管理员权限。

请确定您系统上的临时目录中有足够可用的内存空间，并且已获得读、写及运行的访问权限。开始安装前请关闭其他所有应用程序。

----------------------------------------------------------------------
程序启动时出现的问题
----------------------------------------------------------------------

显卡驱动程序经常会引发 OpenOffice 启动问题（例如应用程序挂起）和屏幕显示问题。如果出现这些问题，请更新您的显卡驱动程序或尝试使用操作系统附带的图形驱动程序。如果显示三维对象时出现问题，则经常可以通过禁用“工具”-“选项”- OpenOffice -“视图”-“三维视图”下的“使用 OpenGL”选项得到解决。

----------------------------------------------------------------------
Windows 下的 ALPS/Synaptics 笔记本触摸板
----------------------------------------------------------------------

由于 Windows 驱动的问题, 您将不能通过使用 ALPS/Synaptics 触摸板的滚动条控制 OpenOffice 的上下滚动。

如果想要使用触摸板的滚动条，需要把下面几行内容添加到配置文件 "C:\Program Files\Synaptics\SynTP\SynTPEnh.ini" 中，然后重新启动您的计算机：

[OpenOffice]

FC = "SALFRAME"

SF = 0x10000000

SF |= 0x00004000

在 Windows 的不同版本中配置文件的位置可能会不同。

----------------------------------------------------------------------
快捷键
----------------------------------------------------------------------

OpenOffice 中只能够使用操作系统尚未预设的快捷键（组合键）。如果 OpenOffice 帮助中描述的一些组合键在使用 OpenOffice 时根本无法起作用，您就需要检查一下这些快捷键是否已经被操作系统占用。要解除此类冲突，您可以更改操作系统指定的快捷键。另外，您也可以更改 OpenOffice 中绝大部分的快捷键。有关此主题的详细信息，请参阅 OpenOffice 帮助或操作系统的帮助文档。

----------------------------------------------------------------------
OpenOffice 中以当前文档格式发送电子邮件时出现的问题
----------------------------------------------------------------------

当通过“文件”-“发送”-“文档作为电子邮件”或“文档作为 PDF 附件”发送文档时可能会发生问题（程序崩溃或中止）。这是由于 Windows 系统文件 "Mapi" (Messaging Application Programming Interface) 导致了一些文件版本上的问题。不幸的是，我们无法确定哪些版本有问题。有关详细信息，请访问 http://www.microsoft.com 以在知识库中搜索 "mapi dll"。

----------------------------------------------------------------------
辅助功能的重要提示
----------------------------------------------------------------------

有关 OpenOffice 中辅助功能的详细信息，请参见 http://www.openoffice.org/access/

----------------------------------------------------------------------
用户支持
----------------------------------------------------------------------

主支持页 http://support.openoffice.org/ 提供了各种可能的方法来帮助您使用 OpenOffice。您的问题可能已得到回答 - 可以在 http://forum.openoffice.org 查看社区论坛，或在 http://openoffice.apache.org/mailing-lists.html 搜索 "<EMAIL>" 邮件列表的存档。或者，您也可以将问题发送至 <EMAIL>。以下网页介绍了如何订阅此列表（获取电子邮件响应）：http://openoffice.apache.org/mailing-lists.html。

您还可以查阅常见问题解答部分，网址为 http://wiki.openoffice.org/wiki/Documentation/FAQ。

----------------------------------------------------------------------
报告错误和问题
----------------------------------------------------------------------

OpenOffice 网站中的 BugZilla, 是我们用于报告、跟踪和解决错误与问题的机制。我们鼓励所有用户利用这种权利，并欢迎您报告在您的特定平台上出现的问题。热心的问题报告是用户群体对正在开发和改进的办公套件可做的最重要贡献之一。

----------------------------------------------------------------------
涉及
----------------------------------------------------------------------

您在该重要的开源项目发展中的积极参与将会对 OpenOffice 社区非常有帮助。

作为一名用户，您已经是本套件开发过程中重要的一环，我们鼓励您成为更加活跃的社区长期贡献者。请加入并查看用户页面 http://openoffice.apache.org/get-involved.html

开始方式
----------------------------------------------------------------------

订阅一个或多个邮件列表是开始做出贡献的最佳途径，经过一段时间的邮件阅读，并且逐步借助归档邮件使自己熟悉更多的从 OpenOffice 源代码在2000年10月发布以来的讨论话题。当您觉得适当的时候，您只需要发送一封邮件做自我介绍并加入讨论。

订阅邮件
----------------------------------------------------------------------

在http://openoffice.apache.org/mailing-lists.html中有一些您可以订阅的关于 OpenOffice 的邮件列表。

* 新闻: <EMAIL> *推荐所有用户订阅* (低流量)
* 主用户论坛: <EMAIL> *更容易看到各种讨论* (高流量)
* 通用项目开发和讨论邮件列表：<EMAIL> (高流量)

Joining the Project
----------------------------------------------------------------------

您可以为这个重要的开源项目做出主要的贡献，即使您的软件设计和编码经验有限。是的，您可以！

在 http://openoffice.apache.org/get-involved.html 中你将会发现一些概览，范围从本地化、质量保障、用户支持到一些真正核心编码的项目工程，你可以从它们开始。如果你不是一名程序员，你可以进行其他帮助，如完善文档或从事市场推广。OpenOffice 的市场推广可以是零散的，也可以借鉴传统的推广开源软件的商业手段，并且我们需要使产品可以不受语言和文化的限制，所以你要做的可以仅仅是把这个办公套件的信息传播出去或者告诉你的朋友。

你可以通过加入市场推广的邮件列表 <EMAIL> 来提供帮助，在那里你可以建立所在国家和本地社区内新闻、媒体、政府机构、顾问、学校、Linux用户群和开发者的通讯联系点。

我们希望您能够愉快地使用新的 OpenOffice 4.1.6 并在线加入我们。

Apache OpenOffice 社区