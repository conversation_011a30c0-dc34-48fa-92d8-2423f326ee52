<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="107" height="100" viewBox="0 0 107 100"
	 overflow="visible" enable-background="new 0 0 107 100" xml:space="preserve">
<g>
	<g>
		<defs>
			<circle id="XMLID_1_" cx="53.334" cy="48.251" r="44.362"/>
		</defs>
		<use xlink:href="#XMLID_1_"  fill="#65040B"/>
		<clipPath id="XMLID_9_">
			<use xlink:href="#XMLID_1_" />
		</clipPath>
		<defs>
			<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="2.808" y="-23.796" width="100.296" height="85.939">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="2.808" y="-23.796" width="100.296" height="85.939" id="XMLID_10_">
			<g clip-path="url(#XMLID_9_)" filter="url(#Adobe_OpacityMaskFilter)">
				
					<linearGradient id="XMLID_11_" gradientUnits="userSpaceOnUse" x1="183.1445" y1="-382.751" x2="237.308" y2="-339.1559" gradientTransform="matrix(0.8962 0.4437 -0.4437 0.8962 -290.3355 261.1143)">
					<stop  offset="0" style="stop-color:#FFFFFF"/>
					<stop  offset="1" style="stop-color:#000000"/>
				</linearGradient>
				<path fill="url(#XMLID_11_)" d="M101.263,48.966C88.151,75.448,57.38,86.94,32.534,74.638
					C7.687,62.337-1.825,30.897,11.286,4.415c13.111-26.48,43.883-37.975,68.73-25.672C104.862-8.954,114.374,22.485,101.263,48.966
					z"/>
			</g>
		</mask>
		
			<ellipse transform="matrix(0.3991 0.9169 -0.9169 0.3991 49.404 -37.0335)" opacity="0.45" clip-path="url(#XMLID_9_)" mask="url(#XMLID_10_)" fill="#FFFFFF" cx="52.955" cy="19.174" rx="41.105" ry="51.666"/>
	</g>
	<g>
		<defs>
			<circle id="XMLID_5_" cx="53.334" cy="48.251" r="44.362"/>
		</defs>
		<clipPath id="XMLID_12_">
			<use xlink:href="#XMLID_5_" />
		</clipPath>
		<defs>
			<filter id="Adobe_OpacityMaskFilter_1_" filterUnits="userSpaceOnUse" x="-12.511" y="-17.618" width="87.346" height="99.065">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="-12.511" y="-17.618" width="87.346" height="99.065" id="XMLID_13_">
			<g clip-path="url(#XMLID_12_)" filter="url(#Adobe_OpacityMaskFilter_1_)">
				
					<linearGradient id="XMLID_2_" gradientUnits="userSpaceOnUse" x1="-93.4854" y1="133.3926" x2="-39.3224" y2="176.9873" gradientTransform="matrix(0.978 -0.2087 0.2087 0.978 75.1313 -127.7948)">
					<stop  offset="0" style="stop-color:#FFFFFF"/>
					<stop  offset="1" style="stop-color:#000000"/>
				</linearGradient>
				<path fill="url(#XMLID_2_)" d="M87.528,25.272c6.166,28.898-10.816,57.016-37.932,62.801S-4.499,75.12-10.663,46.222
					c-6.166-28.898,10.816-57.016,37.932-62.801S81.362-3.626,87.528,25.272z"/>
			</g>
		</mask>
		
			<ellipse transform="matrix(0.8822 0.4709 -0.4709 0.8822 18.7013 -10.9141)" opacity="0.3" clip-path="url(#XMLID_12_)" mask="url(#XMLID_13_)" fill="#FFFFFF" cx="31.161" cy="31.915" rx="41.105" ry="51.667"/>
	</g>
	
		<ellipse transform="matrix(0.9343 -0.3565 0.3565 0.9343 -2.8363 13.9398)" opacity="0.7" fill="#FFFFFF" cx="36.397" cy="14.664" rx="8.527" ry="4.939"/>
	
		<ellipse transform="matrix(0.6848 -0.7287 0.7287 0.6848 -8.5144 25.1061)" opacity="0.93" fill="#FFFFFF" cx="24.768" cy="22.396" rx="3.119" ry="2.389"/>
</g>
</svg>
