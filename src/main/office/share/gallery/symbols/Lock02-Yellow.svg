<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="120" height="100" viewBox="0 0 120 100"
	 overflow="visible" enable-background="new 0 0 120 100" xml:space="preserve">
<g>
	<g>
		<g>
			
				<linearGradient id="XMLID_7_" gradientUnits="userSpaceOnUse" x1="-1037.5718" y1="29.1748" x2="-986.1113" y2="29.1748" gradientTransform="matrix(-1 0 0 1 -929.6729 0)">
				<stop  offset="0" style="stop-color:#B8B8B8"/>
				<stop  offset="0.0199" style="stop-color:#BDBDBD"/>
				<stop  offset="0.1417" style="stop-color:#DADADA"/>
				<stop  offset="0.2631" style="stop-color:#EFEFEF"/>
				<stop  offset="0.3831" style="stop-color:#FBFBFB"/>
				<stop  offset="0.5" style="stop-color:#FFFFFF"/>
				<stop  offset="0.612" style="stop-color:#FAFAFA"/>
				<stop  offset="0.7361" style="stop-color:#EDEDED"/>
				<stop  offset="0.8662" style="stop-color:#D7D7D7"/>
				<stop  offset="0.9996" style="stop-color:#B8B8B8"/>
				<stop  offset="1" style="stop-color:#B8B8B8"/>
			</linearGradient>
			<path fill="url(#XMLID_7_)" d="M56.438,29.657l0.07,23.394h10.903l-0.021-22.744c0-7.677,6.63-13.923,14.779-13.923
				c8.133,0,14.762,6.236,14.776,13.9v12.267h10.953V29.615C107.873,16.206,96.331,5.298,82.169,5.298
				C67.981,5.298,56.438,16.226,56.438,29.657z"/>
			<path fill="#7F7F7F" d="M82.169,4.549c-7.058,0-13.697,2.601-18.695,7.324c-5.021,4.745-7.786,11.062-7.786,17.784
				c0,0.552,0.064,21.747,0.067,22.649l0.005,1.495h1.495h9.405h1.502L68.16,52.3c-0.009-7.24-0.02-21.992-0.02-21.992
				c0-7.264,6.293-13.173,14.029-13.173c7.72,0,14.012,5.899,14.025,13.151v11.516v1.5h1.5h9.453h1.5v-1.5V29.615v-0.001v-0.002
				c-0.013-6.714-2.783-13.019-7.802-17.754C95.85,7.145,89.217,4.549,82.169,4.549L82.169,4.549z M57.188,29.657
				c0-13.039,11.184-23.608,24.981-23.608c13.78,0,24.954,10.547,24.979,23.566v12.187l0,0l0,0h-9.453c0-7.247,0-11.519,0-11.519
				c-0.016-8.077-6.979-14.648-15.525-14.648c-8.563,0-15.529,6.584-15.529,14.674c0,0,0.011,14.753,0.02,21.993l0,0l0,0h-9.405
				C57.255,52.302,57.188,30.214,57.188,29.657L57.188,29.657L57.188,29.657z"/>
		</g>
		
			<linearGradient id="XMLID_8_" gradientUnits="userSpaceOnUse" x1="-1043.8428" y1="31.5137" x2="-976.4653" y2="31.5137" gradientTransform="matrix(-1 0 0 1 -929.6729 0)">
			<stop  offset="0" style="stop-color:#7F7F7F"/>
			<stop  offset="0.0013" style="stop-color:#808080"/>
			<stop  offset="0.1043" style="stop-color:#A4A4A4"/>
			<stop  offset="0.2075" style="stop-color:#C1C0C0"/>
			<stop  offset="0.3099" style="stop-color:#D6D5D4"/>
			<stop  offset="0.411" style="stop-color:#E2E1E0"/>
			<stop  offset="0.5095" style="stop-color:#E6E5E4"/>
			<stop  offset="0.5992" style="stop-color:#E1E0E0"/>
			<stop  offset="0.6987" style="stop-color:#D4D3D2"/>
			<stop  offset="0.803" style="stop-color:#BEBDBD"/>
			<stop  offset="0.9098" style="stop-color:#9F9F9E"/>
			<stop  offset="1" style="stop-color:#7F7F7F"/>
		</linearGradient>
		<path fill="url(#XMLID_8_)" d="M82.169,10.727c11.147,0,20.233,8.571,20.253,19.108c0,0,0,5.756,0,11.966h-4.727
			c0-6.831,0-11.518,0-11.518c-0.016-8.077-6.98-14.649-15.526-14.649c-8.561,0-15.529,6.584-15.529,14.674
			c0,0,0.014,15.167,0.024,21.993h-4.699c-0.023-6.194-0.051-22.431-0.051-22.431C61.915,19.313,71.001,10.727,82.169,10.727z"/>
	</g>
	<g>
		<linearGradient id="XMLID_9_" gradientUnits="userSpaceOnUse" x1="12.1006" y1="68.6357" x2="75.8589" y2="68.6357">
			<stop  offset="0" style="stop-color:#B8B8B8"/>
			<stop  offset="0.0199" style="stop-color:#BDBDBD"/>
			<stop  offset="0.1417" style="stop-color:#DADADA"/>
			<stop  offset="0.2631" style="stop-color:#EFEFEF"/>
			<stop  offset="0.3831" style="stop-color:#FBFBFB"/>
			<stop  offset="0.5" style="stop-color:#FFFFFF"/>
			<stop  offset="0.612" style="stop-color:#FAFAFA"/>
			<stop  offset="0.7361" style="stop-color:#EDEDED"/>
			<stop  offset="0.8662" style="stop-color:#D7D7D7"/>
			<stop  offset="0.9996" style="stop-color:#B8B8B8"/>
			<stop  offset="1" style="stop-color:#B8B8B8"/>
		</linearGradient>
		<path fill="url(#XMLID_9_)" d="M17.049,42.57c-2.729,0-4.949,2.22-4.949,4.948l0.065,42.236c0,2.728,2.22,4.947,4.949,4.947
			h53.796c1.322,0,2.564-0.515,3.499-1.449c0.934-0.935,1.449-2.178,1.449-3.499l-0.064-42.236c0-1.32-0.515-2.563-1.45-3.498
			c-0.935-0.935-2.178-1.449-3.5-1.449H17.049z"/>
		<path fill="#7F7F7F" d="M70.844,41.82H17.049c-1.523,0-2.954,0.593-4.03,1.669c-1.076,1.077-1.669,2.508-1.669,4.03l0.065,42.237
			c0,1.52,0.593,2.95,1.669,4.026c1.077,1.076,2.508,1.669,4.03,1.669H70.91c3.142,0,5.698-2.556,5.698-5.697l-0.064-42.237
			c0,0,0,0.001,0,0.002C76.544,44.378,73.987,41.82,70.844,41.82L70.844,41.82z M12.85,47.52c0-2.319,1.879-4.199,4.199-4.199
			h53.795c2.319,0,4.2,1.88,4.2,4.199l0.064,42.235l0,0l0,0c0,2.317-1.879,4.197-4.198,4.197H17.114
			c-2.319,0-4.199-1.88-4.199-4.197L12.85,47.52L12.85,47.52L12.85,47.52z"/>
	</g>
	<g>
		<linearGradient id="XMLID_10_" gradientUnits="userSpaceOnUse" x1="12.1006" y1="68.6357" x2="75.8589" y2="68.6357">
			<stop  offset="0.0048" style="stop-color:#EEA43A"/>
			<stop  offset="0.0421" style="stop-color:#F1AD45"/>
			<stop  offset="0.1389" style="stop-color:#F7BF5D"/>
			<stop  offset="0.2421" style="stop-color:#FCCC6E"/>
			<stop  offset="0.3548" style="stop-color:#FED478"/>
			<stop  offset="0.4952" style="stop-color:#FFD67B"/>
			<stop  offset="0.6407" style="stop-color:#FED378"/>
			<stop  offset="0.7574" style="stop-color:#F9C96E"/>
			<stop  offset="0.8643" style="stop-color:#F1B85D"/>
			<stop  offset="0.9646" style="stop-color:#E7A045"/>
			<stop  offset="1" style="stop-color:#E2963B"/>
		</linearGradient>
		<path fill="url(#XMLID_10_)" d="M17.049,42.57c-2.729,0-4.949,2.22-4.949,4.948l0.065,42.236c0,2.728,2.22,4.947,4.949,4.947
			h53.796c1.322,0,2.564-0.515,3.499-1.449c0.934-0.935,1.449-2.178,1.449-3.499l-0.064-42.236c0-1.32-0.515-2.563-1.45-3.498
			c-0.935-0.935-2.178-1.449-3.5-1.449H17.049z"/>
		<g>
			<path fill="#774E26" d="M70.844,41.82H17.049c-1.523,0-2.954,0.593-4.03,1.669c-1.076,1.077-1.669,2.508-1.669,4.03l0.065,42.237
				c0,1.52,0.593,2.95,1.669,4.026c1.077,1.076,2.508,1.669,4.03,1.669H70.91c3.142,0,5.698-2.556,5.698-5.697l-0.064-42.237
				c0,0,0,0.001,0,0.002C76.544,44.378,73.987,41.82,70.844,41.82L70.844,41.82z M12.85,47.52c0-2.319,1.879-4.199,4.199-4.199
				h53.795c2.319,0,4.2,1.88,4.2,4.199l0.064,42.235l0,0l0,0c0,2.317-1.879,4.197-4.198,4.197H17.114
				c-2.319,0-4.199-1.88-4.199-4.197L12.85,47.52L12.85,47.52L12.85,47.52z"/>
		</g>
	</g>
	<defs>
		<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="11.35" y="41.82" width="65.258" height="53.632">
			<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
		</filter>
	</defs>
	<mask maskUnits="userSpaceOnUse" x="11.35" y="41.82" width="65.258" height="53.632" id="XMLID_11_">
		<g filter="url(#Adobe_OpacityMaskFilter)">
			
				<linearGradient id="XMLID_12_" gradientUnits="userSpaceOnUse" x1="35.2891" y1="70.6064" x2="43.9335" y2="-0.8768" gradientTransform="matrix(0.9984 -0.0569 0.0569 0.9984 1.4887 4.5997)">
				<stop  offset="0" style="stop-color:#FFFFFF"/>
				<stop  offset="1" style="stop-color:#000000"/>
			</linearGradient>
			<path fill="url(#XMLID_12_)" d="M99.333,33.484c0.998,17.484-23.392,33.095-54.476,34.867
				C13.771,70.123-12.234,57.386-13.231,39.9C-14.228,22.416,10.163,6.806,41.247,5.033C72.331,3.262,98.336,15.999,99.333,33.484z"
				/>
		</g>
	</mask>
	<g opacity="0.15" mask="url(#XMLID_11_)">
		<path fill="#FFFFFF" d="M17.049,42.57c-2.729,0-4.949,2.22-4.949,4.948l0.065,42.236c0,2.728,2.22,4.947,4.949,4.947h53.796
			c1.322,0,2.564-0.515,3.499-1.449c0.934-0.935,1.449-2.178,1.449-3.499l-0.064-42.236c0-1.32-0.515-2.563-1.45-3.498
			c-0.935-0.935-2.178-1.449-3.5-1.449H17.049z"/>
		<g>
			<path fill="#774E26" d="M70.844,41.82H17.049c-1.523,0-2.954,0.593-4.03,1.669c-1.076,1.077-1.669,2.508-1.669,4.03l0.065,42.237
				c0,1.52,0.593,2.95,1.669,4.026c1.077,1.076,2.508,1.669,4.03,1.669H70.91c3.142,0,5.698-2.556,5.698-5.697l-0.064-42.237
				c0,0,0,0.001,0,0.002C76.544,44.378,73.987,41.82,70.844,41.82L70.844,41.82z M12.85,47.52c0-2.319,1.879-4.199,4.199-4.199
				h53.795c2.319,0,4.2,1.88,4.2,4.199l0.064,42.235l0,0l0,0c0,2.317-1.879,4.197-4.198,4.197H17.114
				c-2.319,0-4.199-1.88-4.199-4.197L12.85,47.52L12.85,47.52L12.85,47.52z"/>
		</g>
	</g>
	<g opacity="0.4">
		
			<image width="135" height="77" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIcAAABNCAYAAACMlHfYAAAACXBIWXMAAAsSAAALEgHS3X78AAAA
BGdBTUEAAK/INwWK6QAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAIsSURB
VHja7NptT9swFMXxuKSjg20MkEBC+/7fbUg8ia7Q0ta77o6lOwtNvItn/3/SUYXgReOe3DgpwwAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAQC9CrW8sxhi6+RBCiJTj44UI7r21XJLoXmNtRQmVFSNlViQ0
WpBciH2RWEtBQkXFyGWYu4yWowYLkouxs2wtby6HktRQkFBRMVIJPlk+W04tJ5aFStJSQXwxUhle
LSvLL8uLZaPfTV6QsaJLSSrGF8uZ5cJybvmqsoz6m/99D5I/7L0mRirDs+XBcm95siwt6z/LEyfd
g4wVLNhM72OhYlxbbvR6oQmSp0cr8tRYqRQ/dYyDSrNz02XotRx5coyaEKkcV5YfyqWmydxtTlu4
rOxVjqWOea5S5EvMWj8HN226nRx5v5GmxDfLd5dTV45W5HKMmg5LHfeJ1uFIx9vn5CgecvnNZh6n
G51F0S1WS+XYFRvQ+M5aHNZpqn3HZOVIB6yC+DGbFuvRcqsFe9SZ1Molpby0bDQ17nSsL+52dvKH
YmMli5Svt0+aEm/aqC3c1GixHDsd97O7W3nVeuyn3G/UsueI7rYu6GxKi3Vc7DVaK4ffe6z1nGOl
ddhOXYwqFtw9BBuHv5+O+onR+uPzfGubc5gcXT8EKzZoW3cmzRqdGP+aIGWG7idHMUHe+1a2B9EV
hS/ePniL25Va/68DAAAAAAAAAAAAAAAAAAAAAAAAAAAAAID2/RZgADFRxuUhUa5aAAAAAElFTkSu
QmCC" transform="matrix(1 0 0 1 -23.7407 7.0654)">
		</image>
	</g>
	<g opacity="0.05">
		
			<image width="135" height="77" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIcAAABNCAYAAACMlHfYAAAACXBIWXMAAAsSAAALEgHS3X78AAAA
BGdBTUEAAK/INwWK6QAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAJlSURB
VHja7JrbSsNQEEWbtqm2KoqCoj/Tb+/vKBartfYeJ7KPDEHFtwwna8GGXl6SdGWfSdJ+D+AX+hwC
QA5ADkAOQA5ADkAOQA5ADkAOQA4A5ADkAOQA5ADkAOQA5ADkAOQA5ABADkAOQA5ADkAOQA5ADkAO
yI1htA2aTqdFV3+M2WxWRdqeIpgUhduuLklSKV+vo0hSBGqK/g/pZS5JkuD4Q1pvkijLSl/bUroM
9HmuLeLF2Ft2LvskSGebQ62RxDixTJQzvS8zbpDKibGxvCsrvf8SpM32iNAchbZjbLmyXCsXltNG
g+Q2YxwkwptlbnnW9weJ42eRzsnhm6MW4dJyZ3mw3FjOLaMM5UjLyU5N8az9ryTLWs1xqNu1rfZo
TQ43iCZBStcet5Z7yTJ27ZHbkrK1vGoJTa/n+l3SrNW95qjPBieIr89CMozc/FFmKEdaUg5qjVG0
kyDCzHHUAdqqYuuz50VLykD1Wma2tHg56v1dahhda6lpfd6IIEc6ALUAH5aF5VHbtdP7ibu0zW1Z
2UiMJ+33QsdhH+FeR5Tm2OusWeiznc6oC80cw4wuaavGQJpOirmyarRHN5tDc0fPDWdLV7cLrcWp
NXK6GeYvZbcSZKWlJTVH67fQQxxsdzMsXbWkDDMUo7m0pAbxd0draVp/xhLxwVvz+UrOD+GqhiQp
IR6+hTrojXsfRSYzxn9nkO+rE57K/l+UThDtvxwAf8LfBAE5ADkAOQA5ADkAOQA5ADkAOQA5AJAD
kAOQA5ADkAOQA5ADkAOQA5ADADkAOQA5ADkAOQA5ADkAOSA7PgUYAN8TxLkKhrZlAAAAAElFTkSu
QmCC" transform="matrix(1 0 0 1 -23.7407 53.0654)">
		</image>
	</g>
</g>
</svg>
