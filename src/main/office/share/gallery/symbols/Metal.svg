<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="100.001" height="100"
	 viewBox="0 0 100.001 100" overflow="visible" enable-background="new 0 0 100.001 100" xml:space="preserve">
<g>
	<g>
		<linearGradient id="XMLID_10_" gradientUnits="userSpaceOnUse" x1="46.083" y1="56.9263" x2="34.7392" y2="79.3439">
			<stop  offset="0" style="stop-color:#F69F32"/>
			<stop  offset="1" style="stop-color:#EF6422"/>
		</linearGradient>
		<path fill="url(#XMLID_10_)" d="M44.269,37.058c-0.203,0.393-20.4,39.362-27.16,48.375l-0.878,1.171
			c0,0,16.094-3.973,17.203-4.246c0.095,1.112,1.202,14.116,1.202,14.116l0.775-0.672c0.471-0.407,11.785-10.524,34.88-57.494
			l0.508-1.034l-1.113,0.3c-3.655,0.982-22.69-0.604-24.849-0.791l-0.348-0.029L44.269,37.058z"/>
		<path fill="#D85F05" d="M44.126,36.198l-0.321,0.621C43.602,37.21,23.424,76.142,16.691,85.12l-1.732,2.311l2.808-0.667
			c5.366-1.273,12.422-3.045,15.197-3.745l1.063,12.479l0.175,2.044l1.55-1.344c0.121-0.104,3.027-2.663,8.811-11.082
			c5.287-7.698,14.124-22.027,26.195-46.576l1.017-2.067L69.55,37.07c-0.504,0.135-1.704,0.297-4.56,0.297
			c-7.421,0-20.04-1.097-20.167-1.107L44.126,36.198L44.126,36.198z M17.526,85.747c6.864-9.151,27.206-48.448,27.206-48.448
			s12.704,1.112,20.258,1.112c2.185,0,3.939-0.093,4.831-0.333l0,0l0,0c-23.314,47.415-34.753,57.33-34.753,57.33l-1.167-13.703
			C33.901,81.705,24.39,84.118,17.526,85.747L17.526,85.747L17.526,85.747L17.526,85.747z"/>
	</g>
	<linearGradient id="XMLID_11_" gradientUnits="userSpaceOnUse" x1="32.916" y1="80.7202" x2="39.0515" y2="70.2768">
		<stop  offset="0" style="stop-color:#F69F32"/>
		<stop  offset="1" style="stop-color:#EF6422"/>
	</linearGradient>
	<path fill="url(#XMLID_11_)" d="M22.239,82.458c5.597-1.371,11.076-2.761,11.148-2.779l2.387-0.605c0,0,0.62,7.283,0.965,11.336
		c4.614-5.705,14.476-19.673,29.555-49.922c-2.195,0.045-5.5-0.022-10.625-0.313c-5.214-0.297-8.089-0.501-9.731-0.664
		C42.84,45.462,29.768,70.431,22.239,82.458z"/>
	<linearGradient id="XMLID_12_" gradientUnits="userSpaceOnUse" x1="42.6577" y1="28.9614" x2="69.5044" y2="88.1664">
		<stop  offset="0" style="stop-color:#F69F32"/>
		<stop  offset="1" style="stop-color:#EF6422"/>
	</linearGradient>
	<path fill="url(#XMLID_12_)" stroke="#D85F05" stroke-width="1.0444" d="M58.138,36.748c0,0,17.701,40.555,23.944,50.141
		c-6.74-2.079-15.256-4.385-15.256-4.385l-2.885,12.864c0,0-10.761-10.649-30.889-59.5C36.946,37.197,58.138,36.748,58.138,36.748z"
		/>
	<linearGradient id="XMLID_13_" gradientUnits="userSpaceOnUse" x1="69.9111" y1="78.3198" x2="54.9767" y2="58.2294">
		<stop  offset="0" style="stop-color:#F69F32"/>
		<stop  offset="1" style="stop-color:#EF6422"/>
	</linearGradient>
	<path fill="url(#XMLID_13_)" d="M62.862,90.636c0.854-3.813,2.403-10.719,2.403-10.719l2.106,0.57
		c0.064,0.018,4.98,1.35,10.271,2.893c-6.716-12.475-18.168-38.387-20.859-44.519c-4.271,0.072-14.335,0.171-20.376-0.366
		C49.723,70.313,58.742,84.869,62.862,90.636z"/>
	<g>
		<linearGradient id="XMLID_14_" gradientUnits="userSpaceOnUse" x1="47.3926" y1="74.2583" x2="53.0786" y2="-1.2758">
			<stop  offset="0" style="stop-color:#E43838"/>
			<stop  offset="0.3305" style="stop-color:#DA2929"/>
			<stop  offset="0.9619" style="stop-color:#C00303"/>
			<stop  offset="1" style="stop-color:#BE0000"/>
		</linearGradient>
		<path fill="url(#XMLID_14_)" d="M57.868,4.295c0,0-6.667,3.244-6.667,3.244c-0.639,0.312-1.761,0.312-2.4,0l-6.666-3.245
			c-1.013-0.493-2.35-0.135-2.979,0.798l-4.152,6.143c-0.397,0.589-1.369,1.149-2.079,1.2l-7.396,0.524
			c-1.123,0.08-2.101,1.058-2.18,2.182l-0.525,7.394c-0.05,0.709-0.61,1.681-1.199,2.079l-6.143,4.153
			c-0.933,0.63-1.292,1.967-0.798,2.979l3.243,6.666c0.312,0.64,0.312,1.762,0,2.4l-3.244,6.668
			c-0.493,1.012-0.135,2.349,0.798,2.979l6.142,4.15c0.59,0.397,1.15,1.368,1.2,2.079l0.525,7.395
			c0.08,1.124,1.058,2.103,2.181,2.182l7.395,0.524c0.709,0.051,1.681,0.612,2.079,1.2l4.152,6.143
			c0.63,0.932,1.966,1.291,2.979,0.799l6.667-3.244c0.639-0.311,1.761-0.311,2.401,0l6.667,3.244
			c1.012,0.492,2.349,0.134,2.979-0.799l4.15-6.143c0.398-0.588,1.37-1.149,2.079-1.2l7.396-0.525
			c1.122-0.077,2.101-1.056,2.18-2.181l0.524-7.395c0.051-0.711,0.612-1.682,1.201-2.079l6.142-4.15
			c0.932-0.632,1.29-1.968,0.798-2.979l-3.244-6.668c-0.311-0.639-0.311-1.761,0-2.4l3.244-6.666
			c0.492-1.013,0.133-2.35-0.798-2.979l-6.142-4.153c-0.589-0.398-1.15-1.369-1.201-2.079l-0.524-7.395
			c-0.08-1.123-1.058-2.102-2.181-2.181l-7.396-0.524c-0.71-0.051-1.682-0.611-2.079-1.2l-4.15-6.143
			C60.216,4.16,58.88,3.802,57.868,4.295z"/>
		<path fill="#7C1212" d="M58.86,3.55c-0.428,0-0.85,0.095-1.222,0.276L50.971,7.07c-0.235,0.115-0.598,0.183-0.972,0.183
			s-0.736-0.068-0.97-0.182l-6.667-3.245C41.989,3.645,41.567,3.55,41.14,3.55c-0.969,0-1.896,0.479-2.418,1.25l-4.153,6.144
			c-0.31,0.46-1.129,0.932-1.685,0.972L25.49,12.44c-1.396,0.1-2.566,1.271-2.664,2.666L22.3,22.5
			c-0.039,0.554-0.511,1.372-0.971,1.684l-6.142,4.151c-1.16,0.783-1.588,2.384-0.975,3.642l3.243,6.666
			c0.244,0.5,0.244,1.442,0,1.941l-3.243,6.669c-0.613,1.257-0.185,2.855,0.975,3.641l6.141,4.15
			c0.462,0.312,0.933,1.129,0.972,1.683l0.526,7.396c0.098,1.397,1.269,2.568,2.665,2.666l7.394,0.525
			c0.555,0.039,1.373,0.511,1.684,0.971l4.152,6.143c0.522,0.772,1.448,1.253,2.418,1.253c0.43,0,0.853-0.097,1.225-0.278
			l6.664-3.243c0.233-0.113,0.596-0.181,0.971-0.181c0.376,0,0.739,0.067,0.972,0.181l6.668,3.244
			c0.368,0.181,0.791,0.277,1.221,0.277c0.97,0,1.897-0.48,2.42-1.254l4.15-6.142c0.311-0.46,1.129-0.932,1.683-0.971l7.397-0.526
			c1.395-0.096,2.566-1.267,2.664-2.666l0.525-7.394c0.039-0.555,0.511-1.372,0.971-1.683l6.143-4.151
			c1.159-0.784,1.587-2.383,0.975-3.641l-3.244-6.668c-0.243-0.499-0.243-1.443,0-1.942l3.244-6.666
			c0.612-1.259,0.184-2.859-0.975-3.642l-6.142-4.151c-0.46-0.313-0.933-1.13-0.972-1.684l-0.525-7.395
			c-0.098-1.394-1.268-2.565-2.664-2.665l-7.397-0.524c-0.554-0.04-1.372-0.512-1.682-0.971l-4.152-6.144L61.278,4.8
			C60.756,4.029,59.829,3.55,58.86,3.55L58.86,3.55z M49.999,8.297c0.518,0,1.036-0.096,1.429-0.288l6.668-3.244
			c0.238-0.116,0.5-0.171,0.764-0.171c0.604,0,1.213,0.289,1.554,0.792l4.151,6.143c0.489,0.725,1.602,1.366,2.474,1.429
			l7.397,0.524c0.872,0.062,1.635,0.825,1.696,1.697l0.525,7.395c0.062,0.872,0.705,1.985,1.429,2.475l6.142,4.152
			c0.509,0.344,0.798,0.962,0.792,1.572c-0.002,0.258-0.058,0.514-0.171,0.747l-3.244,6.666c-0.191,0.393-0.287,0.911-0.287,1.429
			s0.096,1.035,0.287,1.428l3.244,6.668c0.113,0.233,0.168,0.489,0.171,0.746c0.006,0.609-0.283,1.228-0.792,1.572l-6.142,4.151
			c-0.724,0.487-1.367,1.602-1.429,2.474l-0.525,7.395c-0.061,0.872-0.824,1.638-1.696,1.697l-7.397,0.526
			c-0.872,0.062-1.985,0.705-2.474,1.428l-4.151,6.143c-0.341,0.505-0.95,0.794-1.554,0.794c-0.263,0-0.526-0.056-0.764-0.173
			l-6.668-3.243c-0.393-0.191-0.911-0.286-1.429-0.286s-1.035,0.095-1.428,0.286l-6.666,3.243c-0.239,0.117-0.502,0.173-0.766,0.173
			c-0.604,0-1.212-0.289-1.553-0.794L35.434,67.7c-0.489-0.723-1.603-1.366-2.475-1.428l-7.395-0.525
			c-0.871-0.061-1.635-0.826-1.696-1.698l-0.526-7.395c-0.061-0.872-0.704-1.986-1.429-2.474l-6.141-4.151
			c-0.509-0.345-0.798-0.963-0.792-1.572c0.002-0.257,0.058-0.513,0.171-0.746l3.243-6.668c0.191-0.393,0.287-0.91,0.287-1.428
			s-0.096-1.036-0.287-1.429l-3.243-6.666c-0.113-0.233-0.169-0.489-0.171-0.747c-0.006-0.61,0.284-1.229,0.792-1.572l6.142-4.152
			c0.724-0.49,1.367-1.603,1.428-2.475l0.526-7.395c0.061-0.872,0.825-1.635,1.696-1.697l7.395-0.524
			c0.872-0.063,1.986-0.704,2.475-1.429l4.152-6.143c0.341-0.503,0.95-0.792,1.554-0.792c0.263,0,0.526,0.055,0.765,0.171
			l6.666,3.244C48.964,8.201,49.481,8.297,49.999,8.297L49.999,8.297z"/>
	</g>
	<linearGradient id="XMLID_15_" gradientUnits="userSpaceOnUse" x1="48.9902" y1="12.3096" x2="51.3884" y2="77.14">
		<stop  offset="0" style="stop-color:#E43838"/>
		<stop  offset="0.3305" style="stop-color:#DA2929"/>
		<stop  offset="0.9619" style="stop-color:#C00303"/>
		<stop  offset="1" style="stop-color:#BE0000"/>
	</linearGradient>
	<path fill="url(#XMLID_15_)" d="M48.398,11.946c0.881,0.428,2.322,0.428,3.204,0l5.081-2.473c0.881-0.428,2.051-0.115,2.6,0.697
		l3.163,4.68c0.55,0.813,1.798,1.533,2.774,1.604l5.637,0.398c0.978,0.07,1.835,0.926,1.904,1.903l0.399,5.636
		c0.069,0.978,0.79,2.227,1.603,2.776l4.68,3.164c0.812,0.549,1.125,1.719,0.697,2.6l-2.473,5.08c-0.429,0.882-0.429,2.324,0,3.205
		l2.473,5.08c0.428,0.883,0.114,2.053-0.697,2.6l-4.68,3.164c-0.813,0.549-1.533,1.797-1.603,2.774l-0.399,5.637
		c-0.069,0.978-0.927,1.835-1.904,1.904l-5.637,0.399c-0.977,0.068-2.225,0.79-2.774,1.603l-3.163,4.681
		c-0.549,0.811-1.719,1.125-2.6,0.695l-5.081-2.472c-0.882-0.429-2.323-0.429-3.204,0l-5.081,2.472
		c-0.881,0.43-2.051,0.115-2.6-0.695l-3.164-4.681c-0.549-0.813-1.798-1.534-2.775-1.603l-5.636-0.399
		c-0.978-0.069-1.834-0.927-1.903-1.904l-0.399-5.637c-0.069-0.978-0.79-2.226-1.603-2.774l-4.68-3.164
		c-0.813-0.547-1.126-1.717-0.697-2.6l2.472-5.08c0.429-0.881,0.429-2.323,0-3.205l-2.472-5.08c-0.429-0.881-0.115-2.051,0.696-2.6
		l4.682-3.164c0.812-0.549,1.532-1.798,1.602-2.776l0.399-5.636c0.069-0.978,0.926-1.833,1.903-1.903l5.636-0.398
		c0.978-0.07,2.227-0.791,2.775-1.602l3.164-4.682c0.549-0.813,1.719-1.125,2.6-0.697L48.398,11.946z"/>
	<linearGradient id="XMLID_16_" gradientUnits="userSpaceOnUse" x1="35.8291" y1="25.0405" x2="54.7849" y2="46.0769">
		<stop  offset="0.0095" style="stop-color:#F9BC21"/>
		<stop  offset="0.1218" style="stop-color:#F9C024"/>
		<stop  offset="0.2256" style="stop-color:#FBCB2F"/>
		<stop  offset="0.3257" style="stop-color:#FDDE41"/>
		<stop  offset="0.4048" style="stop-color:#FFF254"/>
		<stop  offset="0.4912" style="stop-color:#FEED4F"/>
		<stop  offset="0.593" style="stop-color:#FDDF42"/>
		<stop  offset="0.6286" style="stop-color:#FCD83B"/>
		<stop  offset="1" style="stop-color:#F9BC21"/>
	</linearGradient>
	<circle fill="url(#XMLID_16_)" stroke="#BE0000" stroke-width="1.9753" cx="49.395" cy="40.095" r="22.852"/>
	<defs>
		<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="29.059" y="18.272" width="40.672" height="40.674">
			<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
		</filter>
	</defs>
	<mask maskUnits="userSpaceOnUse" x="29.059" y="18.272" width="40.672" height="40.674" id="XMLID_17_">
		<g filter="url(#Adobe_OpacityMaskFilter)">
			<linearGradient id="XMLID_18_" gradientUnits="userSpaceOnUse" x1="48.7642" y1="7.3564" x2="48.7642" y2="52.9323">
				<stop  offset="0" style="stop-color:#FFFFFF"/>
				<stop  offset="1" style="stop-color:#000000"/>
			</linearGradient>
			<circle fill="url(#XMLID_18_)" cx="48.764" cy="38.25" r="22.941"/>
		</g>
	</mask>
	<circle mask="url(#XMLID_17_)" fill="#FFFFFF" cx="49.395" cy="38.609" r="20.336"/>
</g>
</svg>
