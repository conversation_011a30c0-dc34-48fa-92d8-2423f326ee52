<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="100.002" height="100.001"
	 viewBox="0 0 100.002 100.001" overflow="visible" enable-background="new 0 0 100.002 100.001" xml:space="preserve">
<g>
	<g>
		
			<linearGradient id="XMLID_17_" gradientUnits="userSpaceOnUse" x1="78.2979" y1="77.8174" x2="78.2978" y2="-54.2905" gradientTransform="matrix(0.9998 -0.0217 0.0217 0.9998 -28.9657 30.1705)">
			<stop  offset="0" style="stop-color:#E6E6E6"/>
			<stop  offset="1" style="stop-color:#979797"/>
		</linearGradient>
		<path fill="url(#XMLID_17_)" d="M49.21,5.439c-25.945,0.564-37.33,9.119-37.802,9.483l-0.3,0.231l1.012,46.56
			c0.002,0.338,0.128,8.628,6.498,14.468c6.192,5.678,30.647,19.714,31.685,20.309l0.391,0.224l0.381-0.238
			c1.02-0.64,25.044-15.73,30.983-21.673c6.111-6.11,5.876-14.397,5.862-14.748l-1.011-46.532l-0.29-0.219
			C86.164,12.959,75.169,4.875,49.21,5.439z"/>
		<path fill="#727272" d="M51.75,4.663c-0.831,0-1.691,0.009-2.555,0.027c-13.64,0.297-23.255,2.866-28.92,4.969
			c-6.232,2.314-9.202,4.575-9.325,4.67l-0.6,0.463l0.016,0.758l1.004,46.18c0-0.008,0-0.016,0-0.023
			c0.002,0.363,0.132,8.969,6.741,15.028c6.253,5.733,30.777,19.811,31.818,20.406l0.781,0.448l0.762-0.479
			c1.023-0.641,25.117-15.774,31.116-21.778c6.252-6.251,6.1-14.706,6.082-15.286c0,0.002,0,0.003,0,0.004l-1.003-46.181
			l-0.016-0.726l-0.579-0.438c-0.113-0.084-2.815-2.1-8.555-4.09C73.315,6.812,64.45,4.663,51.75,4.663L51.75,4.663z M11.866,15.517
			c0,0,11.363-8.762,37.36-9.327c0.858-0.018,1.699-0.027,2.524-0.027c24.181,0,34.417,7.739,34.417,7.739l1.003,46.182
			c0,0,0.003,0.072,0.003,0.208c0,1.316-0.254,8.589-5.646,13.98C75.579,80.224,50.675,95.84,50.675,95.84
			s-25.348-14.525-31.55-20.211c-6.203-5.688-6.255-13.933-6.255-13.933L11.866,15.517L11.866,15.517L11.866,15.517z"/>
	</g>
	<g>
		
			<linearGradient id="XMLID_18_" gradientUnits="userSpaceOnUse" x1="78.2974" y1="-21.7944" x2="78.2974" y2="62.4715" gradientTransform="matrix(0.9998 -0.0217 0.0217 0.9998 -28.9657 30.1705)">
			<stop  offset="0" style="stop-color:#E6E6E6"/>
			<stop  offset="1" style="stop-color:#979797"/>
		</linearGradient>
		<path fill="url(#XMLID_18_)" d="M49.21,5.439c-25.945,0.564-37.33,9.119-37.802,9.483l-0.3,0.231l1.012,46.56
			c0.002,0.338,0.128,8.628,6.498,14.468c6.192,5.678,30.647,19.714,31.685,20.309l0.391,0.224l0.381-0.238
			c1.02-0.64,25.044-15.73,30.983-21.673c6.111-6.11,5.876-14.397,5.862-14.748l-1.011-46.532l-0.29-0.219
			C86.164,12.959,75.169,4.875,49.21,5.439z"/>
		<path fill="#727272" d="M51.75,4.663c-0.831,0-1.691,0.009-2.555,0.027c-13.64,0.297-23.255,2.866-28.92,4.969
			c-6.232,2.314-9.202,4.575-9.325,4.67l-0.6,0.463l0.016,0.758l1.004,46.18c0-0.008,0-0.016,0-0.023
			c0.002,0.363,0.132,8.969,6.741,15.028c6.253,5.733,30.777,19.811,31.818,20.406l0.781,0.448l0.762-0.479
			c1.023-0.641,25.117-15.774,31.116-21.778c6.252-6.251,6.1-14.706,6.082-15.286c0,0.002,0,0.003,0,0.004l-1.003-46.181
			l-0.016-0.726l-0.579-0.438c-0.113-0.084-2.815-2.1-8.555-4.09C73.315,6.812,64.45,4.663,51.75,4.663L51.75,4.663z M11.866,15.517
			c0,0,11.363-8.762,37.36-9.327c0.858-0.018,1.699-0.027,2.524-0.027c24.181,0,34.417,7.739,34.417,7.739l1.003,46.182
			c0,0,0.003,0.072,0.003,0.208c0,1.316-0.254,8.589-5.646,13.98C75.579,80.224,50.675,95.84,50.675,95.84
			s-25.348-14.525-31.55-20.211c-6.203-5.688-6.255-13.933-6.255-13.933L11.866,15.517L11.866,15.517L11.866,15.517z"/>
	</g>
	
		<linearGradient id="XMLID_19_" gradientUnits="userSpaceOnUse" x1="78.2979" y1="75.8408" x2="78.2978" y2="-38.6532" gradientTransform="matrix(0.9998 -0.0217 0.0217 0.9998 -28.9657 30.1705)">
		<stop  offset="0" style="stop-color:#E6E6E6"/>
		<stop  offset="1" style="stop-color:#979797"/>
	</linearGradient>
	<path fill="url(#XMLID_19_)" d="M20.248,20.274c0.172,7.877,0.897,41.265,0.897,41.267c0.003,0.08,0.22,4.912,3.573,7.986
		c3.463,3.176,16.224,11.034,25.765,16.63c9.362-6.005,21.873-14.419,25.19-17.737c3.224-3.224,3.224-8.076,3.222-8.137
		c0-0.008-0.73-33.635-0.898-41.375c-4.481-1.923-13.688-4.767-28.591-4.443C34.346,14.792,24.882,18.108,20.248,20.274z
		 M21.147,61.621c0,0-0.001-0.029-0.001-0.041C21.146,61.582,21.146,61.619,21.147,61.621z M78.899,60.391l-0.001-0.029
		C78.899,60.373,78.899,60.391,78.899,60.391z"/>
	<g>
		
			<linearGradient id="XMLID_20_" gradientUnits="userSpaceOnUse" x1="63.8755" y1="-8.0225" x2="85.9625" y2="25.6026" gradientTransform="matrix(0.9998 -0.0217 0.0217 0.9998 -28.9657 30.1705)">
			<stop  offset="0" style="stop-color:#FFF254"/>
			<stop  offset="0.2682" style="stop-color:#FFEE50"/>
			<stop  offset="0.5449" style="stop-color:#FDE144"/>
			<stop  offset="0.8244" style="stop-color:#FBCD31"/>
			<stop  offset="1" style="stop-color:#F9BC21"/>
		</linearGradient>
		<path fill="url(#XMLID_20_)" d="M49.467,17.284c-12.753,0.277-21.302,2.735-26.358,4.828c0.022,1.014,0.855,39.368,0.855,39.368
			c0.009,0.126,0.28,3.788,2.659,5.97c3.045,2.791,14.208,9.733,23.794,15.395c9.413-6.075,20.348-13.5,23.264-16.417
			c2.332-2.333,2.396-6.104,2.396-6.143c0,0-0.758-34.847-0.857-39.436C70.333,19.031,62.023,17.011,49.467,17.284z"/>
		<path fill="#E29813" d="M51.705,16.511c-0.736,0-1.487,0.008-2.254,0.024c-13.593,0.295-22.472,3.097-27.104,5.073
			c0.056,2.575,0.866,39.89,0.866,39.89c0.001,0,0.223,4.049,2.903,6.506c3.151,2.888,14.816,10.123,24.318,15.724
			c9.329-6.011,20.759-13.75,23.777-16.769c2.571-2.572,2.616-6.627,2.616-6.668c0,0-0.815-37.424-0.869-39.974
			C71.725,18.686,63.818,16.511,51.705,16.511L51.705,16.511z M23.869,22.602c5.053-2.02,13.392-4.302,25.615-4.567
			c0.751-0.016,1.499-0.024,2.221-0.024c10.86,0,18.266,1.75,22.774,3.358c0.141,6.461,0.824,37.868,0.847,38.929
			c-0.009,0.303-0.16,3.583-2.177,5.6c-2.836,2.837-13.846,10.309-22.75,16.066C41.33,76.598,30.092,69.612,27.13,66.896
			c-2.078-1.904-2.388-5.098-2.417-5.466C24.688,60.282,24.01,29.07,23.869,22.602L23.869,22.602z"/>
	</g>
	<g>
		<defs>
			<path id="XMLID_5_" d="M22.348,21.607c0.056,2.574,0.866,39.889,0.866,39.889c0.001,0,0.223,4.049,2.902,6.506
				c3.151,2.889,14.816,10.123,24.318,15.725c9.329-6.012,20.759-13.75,23.776-16.77c2.571-2.572,2.616-6.627,2.616-6.668
				c0,0-0.814-37.424-0.869-39.973c-4.489-1.731-13.112-4.074-26.507-3.783C35.858,16.829,26.979,19.631,22.348,21.607z"/>
		</defs>
		<clipPath id="XMLID_21_">
			<use xlink:href="#XMLID_5_" />
		</clipPath>
		<g clip-path="url(#XMLID_21_)">
			
				<linearGradient id="XMLID_22_" gradientUnits="userSpaceOnUse" x1="93.292" y1="2.8281" x2="93.292" y2="27.1611" gradientTransform="matrix(0.9998 -0.0217 0.0217 0.9998 -28.9657 30.1705)">
				<stop  offset="0" style="stop-color:#FDF58D"/>
				<stop  offset="0.0159" style="stop-color:#FDF088"/>
				<stop  offset="0.1082" style="stop-color:#FBDB72"/>
				<stop  offset="0.2163" style="stop-color:#FACA61"/>
				<stop  offset="0.3485" style="stop-color:#F9BE55"/>
				<stop  offset="0.5306" style="stop-color:#F8B74E"/>
				<stop  offset="1" style="stop-color:#F8B54C"/>
			</linearGradient>
			<polygon fill="url(#XMLID_22_)" stroke="#8A5C29" stroke-width="1.5" points="90.831,56.539 39.041,57.665 38.706,42.217 
				90.495,41.092 			"/>
			
				<linearGradient id="XMLID_23_" gradientUnits="userSpaceOnUse" x1="40.3325" y1="2.4536" x2="40.3325" y2="26.7881" gradientTransform="matrix(0.9998 -0.0217 0.0217 0.9998 -28.9657 30.1705)">
				<stop  offset="0" style="stop-color:#962B28"/>
				<stop  offset="1" style="stop-color:#E15656"/>
			</linearGradient>
			<polygon fill="url(#XMLID_23_)" stroke="#791A16" stroke-width="1.5" points="37.874,57.315 -13.915,58.441 -14.251,42.992 
				37.539,41.867 			"/>
			
				<linearGradient id="XMLID_24_" gradientUnits="userSpaceOnUse" x1="52.8647" y1="24.1216" x2="52.8647" y2="-2.2128" gradientTransform="matrix(0.9998 -0.0217 0.0217 0.9998 -28.9657 30.1705)">
				<stop  offset="0" style="stop-color:#FDF58D"/>
				<stop  offset="0.0159" style="stop-color:#FDF088"/>
				<stop  offset="0.1082" style="stop-color:#FBDB72"/>
				<stop  offset="0.2163" style="stop-color:#FACA61"/>
				<stop  offset="0.3485" style="stop-color:#F9BE55"/>
				<stop  offset="0.5306" style="stop-color:#F8B74E"/>
				<stop  offset="1" style="stop-color:#F8B54C"/>
			</linearGradient>
			<polygon fill="url(#XMLID_24_)" stroke="#8A5C29" stroke-width="1.5" points="50.073,41.842 -1.716,42.968 -2.052,27.52 
				49.738,26.394 			"/>
			
				<linearGradient id="XMLID_25_" gradientUnits="userSpaceOnUse" x1="105.2529" y1="24.1216" x2="105.2529" y2="-2.2145" gradientTransform="matrix(0.9998 -0.0217 0.0217 0.9998 -28.9657 30.1705)">
				<stop  offset="0" style="stop-color:#962B28"/>
				<stop  offset="1" style="stop-color:#E15656"/>
			</linearGradient>
			<polygon fill="url(#XMLID_25_)" stroke="#791A16" stroke-width="1.5" points="102.452,40.704 50.662,41.83 50.326,26.381 
				102.116,25.256 			"/>
			
				<linearGradient id="XMLID_26_" gradientUnits="userSpaceOnUse" x1="53.1265" y1="65.4541" x2="53.1265" y2="24.7871" gradientTransform="matrix(0.9998 -0.0217 0.0217 0.9998 -28.9657 30.1705)">
				<stop  offset="0" style="stop-color:#FDF58D"/>
				<stop  offset="0.0159" style="stop-color:#FDF088"/>
				<stop  offset="0.1082" style="stop-color:#FBDB72"/>
				<stop  offset="0.2163" style="stop-color:#FACA61"/>
				<stop  offset="0.3485" style="stop-color:#F9BE55"/>
				<stop  offset="0.5306" style="stop-color:#F8B74E"/>
				<stop  offset="1" style="stop-color:#F8B54C"/>
			</linearGradient>
			<polygon fill="url(#XMLID_26_)" stroke="#8A5C29" stroke-width="1.5" points="51.011,72.947 -0.779,74.073 -1.115,58.625 
				50.675,57.5 			"/>
			
				<linearGradient id="XMLID_27_" gradientUnits="userSpaceOnUse" x1="106.3789" y1="65.4531" x2="106.3789" y2="24.7836" gradientTransform="matrix(0.9998 -0.0217 0.0217 0.9998 -28.9657 30.1705)">
				<stop  offset="0" style="stop-color:#962B28"/>
				<stop  offset="1" style="stop-color:#E15656"/>
			</linearGradient>
			<polygon fill="url(#XMLID_27_)" stroke="#791A16" stroke-width="1.5" points="104.253,71.791 52.463,72.916 52.127,57.468 
				103.917,56.342 			"/>
		</g>
	</g>
	<g>
		<path fill="#E29813" d="M51.705,16.511c-0.736,0-1.487,0.008-2.254,0.024c-13.593,0.295-22.472,3.097-27.104,5.073
			c0.056,2.575,0.866,39.89,0.866,39.89c0.001,0,0.223,4.049,2.903,6.506c3.151,2.888,14.816,10.123,24.318,15.724
			c9.329-6.011,20.759-13.75,23.777-16.769c2.571-2.572,2.616-6.627,2.616-6.668c0,0-0.815-37.424-0.869-39.974
			C71.725,18.686,63.818,16.511,51.705,16.511L51.705,16.511z M23.869,22.602c5.053-2.02,13.392-4.302,25.615-4.567
			c0.751-0.016,1.499-0.024,2.221-0.024c10.86,0,18.266,1.75,22.774,3.358c0.141,6.461,0.824,37.868,0.847,38.929
			c-0.009,0.303-0.16,3.583-2.177,5.6c-2.836,2.837-13.846,10.309-22.75,16.066C41.33,76.598,30.092,69.612,27.13,66.896
			c-2.078-1.904-2.388-5.098-2.417-5.466C24.688,60.282,24.01,29.07,23.869,22.602L23.869,22.602z"/>
	</g>
	<defs>
		<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="22.348" y="16.509" width="54.479" height="67.218">
			<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
		</filter>
	</defs>
	<mask maskUnits="userSpaceOnUse" x="22.348" y="16.509" width="54.479" height="67.218" id="XMLID_28_">
		<g filter="url(#Adobe_OpacityMaskFilter)">
			
				<linearGradient id="XMLID_29_" gradientUnits="userSpaceOnUse" x1="71.4614" y1="-24.2803" x2="81.0059" y2="11.9233" gradientTransform="matrix(0.9998 -0.0217 0.0217 0.9998 -28.9657 30.1705)">
				<stop  offset="0" style="stop-color:#FFFFFF"/>
				<stop  offset="1" style="stop-color:#000000"/>
			</linearGradient>
			<path fill="url(#XMLID_29_)" d="M17.534,47.604c0,0,5.415-8.348,30.918-8.902c25.504-0.554,33.086,7.512,33.086,7.512
				l0.666-29.973L55.893,9.898l-15.795,0.343l-16.354,4.964l-6.006,3.917L17.534,47.604z"/>
		</g>
	</mask>
	<path mask="url(#XMLID_28_)" fill="#FFFFFF" d="M22.348,21.607c0.056,2.574,0.866,39.889,0.866,39.889
		c0.001,0,0.223,4.049,2.902,6.506c3.151,2.889,14.816,10.123,24.318,15.725c9.329-6.012,20.759-13.75,23.776-16.77
		c2.571-2.572,2.616-6.627,2.616-6.668c0,0-0.814-37.424-0.869-39.973c-4.489-1.731-13.112-4.074-26.507-3.783
		C35.858,16.829,26.979,19.631,22.348,21.607z"/>
	<defs>
		<filter id="Adobe_OpacityMaskFilter_1_" filterUnits="userSpaceOnUse" x="22.348" y="16.509" width="54.479" height="67.218">
			<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
		</filter>
	</defs>
	<mask maskUnits="userSpaceOnUse" x="22.348" y="16.509" width="54.479" height="67.218" id="XMLID_30_">
		<g filter="url(#Adobe_OpacityMaskFilter_1_)">
			<path fill="url(#XMLID_29_)" d="M17.534,47.604c0,0,5.415-8.348,30.918-8.902c25.504-0.554,33.086,7.512,33.086,7.512
				l0.666-29.973L55.893,9.898l-15.795,0.343l-16.354,4.964l-6.006,3.917L17.534,47.604z"/>
		</g>
	</mask>
	<path mask="url(#XMLID_30_)" fill="#FFFFFF" d="M22.348,21.607c0.056,2.574,0.866,39.889,0.866,39.889
		c0.001,0,0.223,4.049,2.902,6.506c3.151,2.889,14.816,10.123,24.318,15.725c9.329-6.012,20.759-13.75,23.776-16.77
		c2.571-2.572,2.616-6.627,2.616-6.668c0,0-0.814-37.424-0.869-39.973c-4.489-1.731-13.112-4.074-26.507-3.783
		C35.858,16.829,26.979,19.631,22.348,21.607z"/>
</g>
</svg>
