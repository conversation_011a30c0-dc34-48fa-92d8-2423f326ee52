<?xml version="1.0"?>
<oor:data xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:oor="http://openoffice.org/2001/registry"><dependency file="main"/><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Filter"><node oor:name="Filters"><node oor:name="DocBook File" oor:op="replace"><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_DocBook_File</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIComponent"/><prop oor:name="UserData"><value oor:separator=",">com.sun.star.documentconversion.XSLTFilter,,com.sun.star.comp.Writer.XMLImporter,com.sun.star.comp.Writer.XMLExporter,../share/xslt/docbook/docbooktosoffheadings.xsl,../share/xslt/docbook/sofftodocbookheadings.xsl</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.XmlFilterAdaptor</value></prop><prop oor:name="TemplateName"><value>../share/xslt/docbook/DocBookTemplate.stw</value></prop><prop oor:name="UIName"><value>DocBook</value></prop><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN 3RDPARTYFILTER</value></prop></node><node oor:name="XHTML Calc File" oor:op="replace"><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>XHTML_File</value></prop><prop oor:name="DocumentService"><value>com.sun.star.sheet.SpreadsheetDocument</value></prop><prop oor:name="UIComponent"/><prop oor:name="UserData"><value oor:separator=",">com.sun.star.documentconversion.XSLTFilter,,com.sun.star.comp.Calc.XMLOasisImporter,com.sun.star.comp.Calc.XMLOasisExporter,,../share/xslt/export/xhtml/opendoc2xhtml.xsl</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.XmlFilterAdaptor</value></prop><prop oor:name="TemplateName"/><prop oor:name="UIName"><value>XHTML</value></prop><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop></node><node oor:name="XHTML Draw File" oor:op="replace"><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>XHTML_File</value></prop><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop><prop oor:name="UIComponent"/><prop oor:name="UserData"><value oor:separator=",">com.sun.star.documentconversion.XSLTFilter,,com.sun.star.comp.Draw.XMLOasisImporter,com.sun.star.comp.Draw.XMLOasisExporter,,../share/xslt/export/xhtml/opendoc2xhtml.xsl</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.XmlFilterAdaptor</value></prop><prop oor:name="TemplateName"/><prop oor:name="UIName"><value>XHTML</value></prop><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop></node><node oor:name="XHTML Impress File" oor:op="replace"><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>XHTML_File</value></prop><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop><prop oor:name="UIComponent"/><prop oor:name="UserData"><value oor:separator=",">com.sun.star.documentconversion.XSLTFilter,,com.sun.star.comp.Impress.XMLOasisImporter,com.sun.star.comp.Impress.XMLOasisExporter,,../share/xslt/export/xhtml/opendoc2xhtml.xsl</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.XmlFilterAdaptor</value></prop><prop oor:name="TemplateName"/><prop oor:name="UIName"><value>XHTML</value></prop><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop></node><node oor:name="XHTML Writer File" oor:op="replace"><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>XHTML_File</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIComponent"/><prop oor:name="UserData"><value oor:separator=",">com.sun.star.documentconversion.XSLTFilter,,com.sun.star.comp.Writer.XMLOasisImporter,com.sun.star.comp.Writer.XMLOasisExporter,,../share/xslt/export/xhtml/opendoc2xhtml.xsl,,true</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.XmlFilterAdaptor</value></prop><prop oor:name="TemplateName"/><prop oor:name="UIName"><value>XHTML</value></prop><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop></node><node oor:name="UOF text" oor:op="replace"><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>Unified_Office_Format_text</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIComponent"/><prop oor:name="UserData"><value oor:separator=",">com.sun.star.documentconversion.XSLTFilter,,com.sun.star.comp.Writer.XMLOasisImporter,com.sun.star.comp.Writer.XMLOasisExporter,../share/xslt/import/uof/uof2odf_text.xsl,../share/xslt/export/uof/odf2uof_text.xsl</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.XmlFilterAdaptor</value></prop><prop oor:name="TemplateName"/><prop oor:name="UIName"><value>Unified Office Format text</value></prop><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN 3RDPARTYFILTER</value></prop></node><node oor:name="UOF spreadsheet" oor:op="replace"><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>Unified_Office_Format_spreadsheet</value></prop><prop oor:name="DocumentService"><value>com.sun.star.sheet.SpreadsheetDocument</value></prop><prop oor:name="UIComponent"/><prop oor:name="UserData"><value oor:separator=",">com.sun.star.documentconversion.XSLTFilter,,com.sun.star.comp.Calc.XMLOasisImporter,com.sun.star.comp.Calc.XMLOasisExporter,../share/xslt/import/uof/uof2odf_spreadsheet.xsl,../share/xslt/export/uof/odf2uof_spreadsheet.xsl</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.XmlFilterAdaptor</value></prop><prop oor:name="TemplateName"/><prop oor:name="UIName"><value>Unified Office Format spreadsheet</value></prop><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN 3RDPARTYFILTER</value></prop></node><node oor:name="UOF presentation" oor:op="replace"><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>Unified_Office_Format_presentation</value></prop><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop><prop oor:name="UIComponent"/><prop oor:name="UserData"><value oor:separator=",">com.sun.star.documentconversion.XSLTFilter,,com.sun.star.comp.Impress.XMLOasisImporter,com.sun.star.comp.Impress.XMLOasisExporter,../share/xslt/import/uof/uof2odf_presentation.xsl,../share/xslt/export/uof/odf2uof_presentation.xsl</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.XmlFilterAdaptor</value></prop><prop oor:name="TemplateName"/><prop oor:name="UIName"><value>Unified Office Format presentation</value></prop><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN 3RDPARTYFILTER</value></prop></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Types"><node oor:name="Types"><node oor:name="writer_DocBook_File" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.XMLFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>xml</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>DocBook File</value></prop><prop oor:name="UIName"><value>DocBook</value></prop><prop oor:name="ClipboardFormat"><value>doctype:-//OASIS//DTD DocBook XML V4</value></prop></node><node oor:name="XHTML_File" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.XMLFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>html xhtml</value></prop><prop oor:name="MediaType"><value>application/xhtml+xml</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>XHTML</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="Unified_Office_Format_text" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.XMLFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value oor:separator=";">uot;uof</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>Unified Office Format text</value></prop><prop oor:name="ClipboardFormat"><value>doctype:vnd.uof.text</value></prop></node><node oor:name="Unified_Office_Format_spreadsheet" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.XMLFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value oor:separator=";">uos;uof</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>Unified Office Format spreadsheet</value></prop><prop oor:name="ClipboardFormat"><value>doctype:vnd.uof.spreadsheet</value></prop></node><node oor:name="Unified_Office_Format_presentation" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.XMLFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value oor:separator=";">uop;uof</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>Unified Office Format presentation</value></prop><prop oor:name="ClipboardFormat"><value>doctype:vnd.uof.presentation</value></prop></node></node></oor:component-data></oor:data>
