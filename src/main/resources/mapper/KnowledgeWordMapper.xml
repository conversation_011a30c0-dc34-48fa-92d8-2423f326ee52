<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.knowledgebase.api.mapper.KnowledgeWordMapper">
    
    <select id="pageFrontWord" resultType="com.yl.knowledgebase.api.vo.KnowledgeWordFrontPageApiVO">
        SELECT
          kw.id id,
          kw.first_catalogue_id firstCatalogueId,
          kw.first_catalogue_code firstCatalogueCode,
          kw.first_catalogue_name firstCatalogueName,
          kw.second_catalogue_id secondCatalogueId,
          kw.second_catalogue_code secondCatalogueCode,
          kw.second_catalogue_name secondCatalogueName,
          kw.title title,
          kw.create_by createBy,
          kw.create_by_name createByName,
          kw.create_time createTime,
          kw.reading_quantity readingQuantity,
          (SELECT COUNT(1) FROM tab_knowledge_comment kc WHERE kw.id = kc.comment_word_id AND kc.is_enable = 1) commentCount,
          kw.collect_count collectCount,
          kw.give_like_count giveLikeCount,
          kw.status status,
          kw.recommend,
          kw.could_download couldDownload
        FROM tab_knowledge_word kw
        INNER JOIN tab_knowledge_catalogue caf ON kw.first_catalogue_id = caf.id AND caf.is_enable = 1
        where 1 = 1
            <if test="query.firstCatalogueId != null">
                AND kw.first_catalogue_id = #{query.firstCatalogueId}
            </if>
            <if test="query.secondCatalogueId != null">
                AND kw.second_catalogue_id = #{query.secondCatalogueId}
            </if>
            <if test="query.title != null">
                AND kw.title LIKE CONCAT('%' ,#{query.title}, '%')
            </if>
            and kw.status = 2
        order by kw.create_time desc
    </select>

    <update id="updateKnowWordCollectCount">
        UPDATE tab_knowledge_word
          SET
            collect_count =
              case
                <![CDATA[when collect_count + #{data.accumulative} < 0 then 0 ]]>
              else collect_count + #{data.accumulative}
              end
        WHERE id = #{data.knowWordId}
    </update>

    <select id="page" resultType="com.yl.knowledgebase.api.vo.KnowledgeWordPageApiVO">
        SELECT
        kw.id id,
        kw.first_catalogue_id firstCatalogueId,
        kw.first_catalogue_code firstCatalogueCode,
        kw.first_catalogue_name firstCatalogueName,
        kw.title title,
        kw.update_by updateBy,
        kw.update_by_name updateByName,
        kw.update_time updateTime,
        kw.sort sort,
        kw.recommend,
        kw.reading_quantity readingQuantity,
        (SELECT COUNT(1) FROM tab_knowledge_comment kc WHERE kw.id = kc.comment_word_id AND kc.is_enable = 1) commentCount,
        (SELECT COUNT(1) FROM tab_knowledge_download_record kc WHERE kw.id = kc.knowledge_word_id ) downloadCount,
        kw.collect_count collectCount,
        kw.give_like_count giveLikeCount,
        kw.status status,
        kw.could_download couldDownload,
        kw.publish_by_name publishByName,
        kw.publish_time publishTime
        FROM tab_knowledge_word kw
        <where>
            <if test="query.selectType != null and query.selectType == 1">
                AND kw.network_id = #{query.networkId}
            </if>
            <if test="query.firstCatalogueId != null">
                AND kw.first_catalogue_id = #{query.firstCatalogueId}
            </if>
            <if test="query.updateBy != null">
                AND kw.update_by = #{query.updateBy}
            </if>
            <if test="query.publishBy != null">
                AND kw.publish_by = #{query.publishBy}
            </if>
            <if test="query.status != null">
                AND kw.status = #{query.status}
            </if>
            <if test="query.title != null">
                AND kw.title LIKE CONCAT('%' ,#{query.title}, '%')
            </if>
        </where>
        ORDER BY update_time DESC
    </select>

    <select id="pageKnowledgeWordTitleSelect" resultType="com.yl.knowledgebase.api.vo.KnowledgeWordTitleSelectApiVO">
        select
          id id,
          title titleName,
          first_catalogue_id firstCatalogueId,
          first_catalogue_id firstCatalogueName,
          second_catalogue_id secondCatalogueId,
          second_catalogue_name secondCatalogueName,
          three_catalogue_id threeCatalogueId,
          three_catalogue_name threeCatalogueName
        from tab_knowledge_word d
        where 1 = 1
          <if test="firstCatalogueId != null and firstCatalogueId > 0">
              AND first_catalogue_id = #{firstCatalogueId}
          </if>
          <if test="title != null">
              AND d.title LIKE CONCAT('%' ,#{title}, '%')
          </if>
          <if test="selectType == 2">
              AND d.status = 2
          </if>
    </select>

    <select id="selectSecondCatalogueWord" resultType="com.yl.knowledgebase.api.vo.KnowledgeCatalogueWordVo">
        select
        id,
        title as name,
        first_catalogue_id ,
        first_catalogue_name,
        second_catalogue_id,
        second_catalogue_name,
        second_catalogue_id as parentId,
        second_catalogue_name as parentName,
        sort
        from tab_knowledge_word
        <where> 1=1
            <if test="query.firstCatalogueId != null">
                and first_catalogue_id=#{query.firstCatalogueId}
            </if>
            <if test="query.secondCatalogueId != null">
                and second_catalogue_id=#{query.secondCatalogueId}
            </if>
            <if test="query.selectType == 2">
                and status=2
            </if>
        </where>
        order by sort asc, update_time desc
    </select>


    <select id="selectThreeCatalogueWord" resultType="com.yl.knowledgebase.api.vo.KnowledgeCatalogueWordVo">
        select
        id,
        title as name,
        first_catalogue_id ,
        first_catalogue_name,
        second_catalogue_id,
        second_catalogue_name,
        three_catalogue_id,
        three_catalogue_name,
        three_catalogue_id as parentId,
        three_catalogue_name as parentName,
        sort
        from tab_knowledge_word
        <where> 1=1
            <if test="query.firstCatalogueId != null">
                and first_catalogue_id=#{query.firstCatalogueId}
            </if>
            <if test="query.secondCatalogueId != null">
                and second_catalogue_id=#{query.secondCatalogueId}
            </if>
            <if test="query.threeCatalogueId != null">
                and three_catalogue_id=#{query.threeCatalogueId}
            </if>
            <if test="query.selectType == 2">
                and status=2
            </if>
        </where>
        order by sort asc, update_time desc
    </select>

    <select id="validateWordTitle" resultType="long">
        SELECT id FROM tab_knowledge_word
        <where>
            <choose>
                <when test="query.threeCatalogueId != null and query.secondCatalogueId != null and query.firstCatalogueId != null">
                     first_catalogue_id = #{query.firstCatalogueId}  AND second_catalogue_id = #{query.secondCatalogueId}  AND three_catalogue_id = #{query.threeCatalogueId}
                </when>
                <when test="query.threeCatalogueId == null and query.secondCatalogueId != null and query.firstCatalogueId != null">
                     first_catalogue_id = #{query.firstCatalogueId}  AND second_catalogue_id = #{query.secondCatalogueId}  AND three_catalogue_id is null
                </when>
                <when test="query.threeCatalogueId == null and query.secondCatalogueId == null and query.firstCatalogueId != null">
                     first_catalogue_id = #{query.firstCatalogueId}  AND second_catalogue_id is null  AND three_catalogue_id is null
                </when>
                <otherwise>
                    1=1
                </otherwise>
            </choose>
            <if test="query.title != null and query.title != ''">
                AND title = #{query.title}
            </if>
        </where>
    </select>
    
    <select id="selectWordByParentId" resultType="com.yl.knowledgebase.api.vo.KnowledgeCatalogueWordVo">
         select
        id,
        title as name,
        first_catalogue_id ,
        first_catalogue_name,
        second_catalogue_id as parentId,
        second_catalogue_name as parentName,
        is_enable isEnable,
        sort
        from tab_knowledge_word
        <where> 1=1
            <if test="status!=null and status==2">
                and status=#{status}
            </if>
            <!--<if test="isEnable!=null and isEnable==1">-->
                <!--and is_enable=#{isEnable}-->
            <!--</if>-->
            and first_catalogue_id=#{parentId}
            and second_catalogue_id is null
        </where>
        order by sort asc, update_time desc
    </select>

    <select id="selectWordByParentIdThree" resultType="com.yl.knowledgebase.api.vo.KnowledgeCatalogueWordVo">
        select
        id,
        title as name,
        first_catalogue_id ,
        first_catalogue_name,
        second_catalogue_id as parentId,
        second_catalogue_name as parentName,
        three_catalogue_id,
        three_catalogue_name,
        is_enable isEnable,
        sort
        from tab_knowledge_word
        <where> 1=1
            <if test="status!=null and status==2">
                and status=#{status}
            </if>
            and second_catalogue_id=#{parentId}
            and three_catalogue_id is null
        </where>
        order by sort asc, update_time desc
    </select>

    <select id="showPreNextDetail" resultType="com.yl.knowledgebase.api.entity.TabKnowledgeWord">
        SELECT
          id id,
          title title,
          first_catalogue_id firstCatalogueId,
          first_catalogue_name firstCatalogueName,
          second_catalogue_id secondCatalogueId,
          second_catalogue_name secondCatalogueName,
          three_catalogue_id threeCatalogueId,
          three_catalogue_name threeCatalogueName,
          could_download couldDownload,
          description_content descriptionContent,
          accessory_url accessoryUrl,
          status status,
          reading_quantity readingQuantity,
          give_like_count giveLikeCount,
          collect_count collectCount,
          network_id networkId,
          network_name networkName,
          create_by createBy,
          create_by_name createByName,
          update_by updateBy,
          update_by_name updateByName,
          create_time createTime,
          update_time updateTime,
          is_enable isEnable,
          sort sort,
          level_id level_id
        FROM tab_knowledge_word t
        where 1 =1
        <choose>
            <when test="query.threeCatalogueId != null and query.secondCatalogueId != null and query.firstCatalogueId != null">
                AND first_catalogue_id = #{query.firstCatalogueId}  AND second_catalogue_id = #{query.secondCatalogueId}  AND three_catalogue_id = #{query.threeCatalogueId}
            </when>
            <when test="query.threeCatalogueId == null and query.secondCatalogueId != null and query.firstCatalogueId != null">
                AND first_catalogue_id = #{query.firstCatalogueId}  AND second_catalogue_id = #{query.secondCatalogueId}  AND three_catalogue_id is null
            </when>
            <when test="query.threeCatalogueId == null and query.secondCatalogueId == null and query.firstCatalogueId != null">
                AND  first_catalogue_id = #{query.firstCatalogueId}  AND second_catalogue_id is null  AND three_catalogue_id is null
            </when>
        </choose>
        <if test="query.selectType == 2">
            AND status = 2
        </if>
        <!--<if test="query.preOrNext == 1">
            <![CDATA[ AND sort <= #{query.sort}
            AND create_time > #{query.createTime} ]]>
            ORDER BY sort DESC, create_time ASC
        </if>
        <if test="query.preOrNext == 2">
            <![CDATA[ AND sort >= #{query.sort}
            AND create_time < #{query.createTime} ]]>
            ORDER BY sort ASC, create_time DESC
        </if>
        LIMIT 1-->
        ORDER BY sort ASC, create_time DESC
    </select>

    <update id="updateWordCatalogue">
        update tab_knowledge_word
        set
          first_catalogue_id = #{dto.firstCatalogueId},
          first_catalogue_code = #{dto.firstCatalogueCode},
          first_catalogue_name = #{dto.firstCatalogueName},
          second_catalogue_id = #{dto.secondCatalogueId},
          second_catalogue_code = #{dto.secondCatalogueCode},
          second_catalogue_name = #{dto.secondCatalogueName},
          three_catalogue_id = #{dto.threeCatalogueId},
          three_catalogue_code = #{dto.threeCatalogueCode},
          three_catalogue_name = #{dto.threeCatalogueName},
          sort = #{dto.sort},
          update_by = #{dto.updateBy},
          update_by_name = #{dto.updateByName},
          update_time = #{dto.updateTime}
        where id = #{dto.id}
    </update>


    <update id="updateWordCatalogueDetail">
        update tab_knowledge_word
        <trim prefix="SET" suffixOverrides=",">
            <if test="dto.id != null">id = #{dto.id},</if>
            <if test="dto.title != null">title = #{dto.title},</if>
            <if test="dto.firstCatalogueId != null">first_catalogue_id = #{dto.firstCatalogueId},</if>
            <if test="dto.firstCatalogueCode != null">first_catalogue_code = #{dto.firstCatalogueCode},</if>
            <if test="dto.firstCatalogueName != null">first_catalogue_name = #{dto.firstCatalogueName},</if>
            second_catalogue_id = #{dto.secondCatalogueId},
            second_catalogue_code = #{dto.secondCatalogueCode},
            second_catalogue_name = #{dto.secondCatalogueName},
            three_catalogue_id = #{dto.threeCatalogueId},
            three_catalogue_code = #{dto.threeCatalogueCode},
            three_catalogue_name = #{dto.threeCatalogueName},
            <if test="dto.couldDownload != null">could_download = #{dto.couldDownload},</if>
            <if test="dto.descriptionContent != '' and dto.descriptionContent !=null ">description_content = #{dto.descriptionContent},</if>
            <if test="dto.accessoryUrl != null">accessory_url = #{dto.accessoryUrl},</if>
            <if test="dto.status != null">status = #{dto.status},</if>
            <if test="dto.readingQuantity != null">reading_quantity = #{dto.readingQuantity},</if>
            <if test="dto.giveLikeCount != null">give_like_count = #{dto.giveLikeCount},</if>
            <if test="dto.collectCount != null">collect_count = #{dto.collectCount},</if>
            <if test="dto.networkId != null">network_id = #{dto.networkId},</if>
            <if test="dto.networkName != null">network_name = #{dto.networkName},</if>
            <if test="dto.createBy != null">create_by = #{dto.createBy},</if>
            <if test="dto.createByName != null">create_by_name = #{dto.createByName},</if>
            <if test="dto.updateBy != null">update_by = #{dto.updateBy},</if>
            <if test="dto.updateByName != null">update_by_name = #{dto.updateByName},</if>
            <if test="dto.createTime != null">create_time = #{dto.createTime },</if>
            <if test="dto.updateTime != null">update_time = #{dto.updateTime},</if>
            <if test="dto.publishByName != null">publish_by_name = #{dto.publishByName},</if>
            <if test="dto.publishBy != null">publish_by = #{dto.publishBy},</if>
            <if test="dto.publishTime != null">publish_time = #{dto.publishTime},</if>
            <if test="dto.isEnable != null">is_enable = #{dto.isEnable},</if>
            <if test="dto.sort != null">sort = #{dto.sort},</if>
            <if test="dto.version != null">version = #{dto.version},</if>
            <if test="dto.levelId != null">LEVEL_ID = #{dto.levelId},</if>
            <if test="dto.recommend != null">RECOMMEND = #{dto.recommend},</if>
        </trim>
        where id = #{dto.id}
    </update>

    <select id="selectMaxSort" resultType="int">
        select
          IFNULL(max(sort),0)
        from tab_knowledge_word
        where 1 = 1
          <if test="firstCatalogueId != null">
              AND first_catalogue_id = #{firstCatalogueId}
          </if>
          <if test="secondCatalogueId != null">
              AND second_catalogue_id = #{secondCatalogueId}
          </if>
        <if test="threeCatalogueId != null">
              AND three_catalogue_id = #{threeCatalogueId}
          </if>
    </select>

    <update id="updateFirstCatalogueName">
        UPDATE
            tab_knowledge_word
        SET
            first_catalogue_name =#{firstCatalogueName}
        WHERE first_catalogue_id = #{firstCatalogueId}
    </update>
    <update id="updateSecondCatalogueName">
        UPDATE
            tab_knowledge_word
        SET
            second_catalogue_name =#{secondCatalogueName}
        WHERE second_catalogue_id = #{secondCatalogueId}
    </update>

    <update id="updateThreeCatalogueName">
        UPDATE
            tab_knowledge_word
        SET
            three_catalogue_name =#{threeCatalogueName}
        WHERE three_catalogue_id = #{threeCatalogueId}
    </update>

    <select id="selectCreateBy" resultType="com.yl.knowledgebase.api.vo.KnowledgeWordCreateByApiVO">
        select
            distinct update_by updateBy,
            update_by_name updateByName
        from tab_knowledge_word
        where 1=1
        <!--<if test="query.updateBy != null">
            and update_by = #{query.updateBy}
        </if>-->
        <if test="query.updateByName != null">
            AND update_by_name LIKE CONCAT('%' ,#{query.updateByName}, '%') 
        </if>
    </select>


    <select id="selectPublishBy" resultType="com.yl.knowledgebase.api.vo.KnowledgeWordCreateByApiVO">
        select
            distinct publish_by publishBy,
            publish_by_name publishByName
        from tab_knowledge_word
        where 1=1
        <!--<if test="query.updateBy != null">
            and update_by = #{query.updateBy}
        </if>-->
        <if test="query.publishByName != null">
            AND publish_by_name LIKE CONCAT('%' ,#{query.publishByName}, '%')
        </if>
    </select>


    <select id="queryWordList" resultType="com.yl.knowledgebase.api.entity.TabKnowledgeWord" >
        SELECT
        id id,
        title title,
        first_catalogue_id firstCatalogueId,
        first_catalogue_name firstCatalogueName,
        second_catalogue_id secondCatalogueId,
        second_catalogue_name secondCatalogueName,
        create_time createTime,
        sort sort
        FROM tab_knowledge_word t
        where 1 =1
        <choose>
            <when test="query.threeCatalogueId != null and query.secondCatalogueId != null and query.firstCatalogueId != null">
                AND first_catalogue_id = #{query.firstCatalogueId}  AND second_catalogue_id = #{query.secondCatalogueId}  AND three_catalogue_id = #{query.threeCatalogueId}
            </when>
            <when test="query.threeCatalogueId == null and query.secondCatalogueId != null and query.firstCatalogueId != null">
                AND first_catalogue_id = #{query.firstCatalogueId}  AND second_catalogue_id = #{query.secondCatalogueId}  AND three_catalogue_id is null
            </when>
            <when test="query.threeCatalogueId == null and query.secondCatalogueId == null and query.firstCatalogueId != null">
                AND  first_catalogue_id = #{query.firstCatalogueId}  AND second_catalogue_id is null  AND three_catalogue_id is null
            </when>
        </choose>
        <if test="query.selectType == 2">
            AND status = 2
        </if>
        ORDER BY sort ASC, create_time DESC
    </select>

    <select id="queryAllWordNoThree" resultType="com.yl.knowledgebase.api.vo.KnowledgeTreesApiVo">
        SELECT id,
            id as catalogueCode,
            title as name,
            CASE
            WHEN second_catalogue_id is not null THEN second_catalogue_id
            WHEN first_catalogue_id is not null THEN first_catalogue_id
            END  parentId,
            CASE
            WHEN second_catalogue_id is not null THEN 3
            WHEN first_catalogue_id is not null THEN 2
            END level,
            sort,
            2 as type
        from tab_knowledge_word WHERE three_catalogue_id is null
          <if test="selectType == 2">
            and  status = 2
          </if>
        order by sort asc
    </select>

    <select id="queryThreeIds" resultType="java.lang.Long">
        select three_catalogue_id from tab_knowledge_word where three_catalogue_id is not null
        <if test="selectType == 2">
            and  status = 2
        </if>
    </select>

    <select id="getKnowledgeStatistics" resultType="com.yl.knowledgebase.api.entity.TabKnowledgeStatistics">
        SELECT
        kw.id knowledgeWordId,
        (SELECT COUNT(1) FROM tab_knowledge_download_record kc WHERE kw.id = kc.knowledge_word_id  and kc.create_time >=#{startTime}   and kc.create_time <![CDATA[ <= ]]> #{endTime} ) downloadCount,
        (SELECT COUNT(1) FROM tab_knowledge_read_record kc WHERE kw.id = kc.knowledge_word_id  and kc.create_time >=#{startTime}  and kc.create_time <![CDATA[ <= ]]> #{endTime}) readCount,
        (SELECT COUNT(1) FROM tab_knowledge_praise_record kc WHERE kw.id = kc.knowledge_word_id  and kc.create_time >=#{startTime}   and kc.create_time <![CDATA[ <= ]]> #{endTime}) praiseCount,
        (SELECT COUNT(1) FROM tab_knowledge_collect kc WHERE kw.id = kc.knowledge_word_id  and kc.create_time >=#{startTime}   and kc.create_time <![CDATA[ <= ]]> #{endTime}) collectCount,
        (SELECT COUNT(1) FROM tab_knowledge_comment kc WHERE kw.id = kc.comment_word_id  and kc.create_time >=#{startTime}   and kc.create_time <![CDATA[ <= ]]> #{endTime}) commentCount
        FROM tab_knowledge_word kw
        where 1 = 1  AND kw.status = 2
    </select>

    <select id="getOrderByReadingQuantity" resultType="java.lang.Long">
        select id   from (
                             select trar.id as `id` from tab_knowledge_word trar
                             order by  reading_quantity desc
                                 limit 10
                         ) a
        union
        select `id` from tab_knowledge_word tkw
        where RECOMMEND=1
    </select>

    <select id="getKnowledgeWordById" resultType="com.yl.knowledgebase.api.vo.KnowledgeWordReadOrderVo">
      select id,title,reading_quantity,recommend from tab_knowledge_word
          where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
       order by reading_quantity desc
    </select>

    <select id="getRecommendKnowledgeWord" resultType="java.lang.Long">
        select `id` from tab_knowledge_word tkw
        where RECOMMEND=1
    </select>
</mapper>