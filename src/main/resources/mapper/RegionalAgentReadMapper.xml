<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.knowledgebase.api.mapper.RegionalAgentReadMapper">

    <select id="pageList" resultType="com.yl.knowledgebase.api.vo.RegionalAgentReadApiVo">
        select
        id,
        catalogue_name,
        comment_word_name,
        agent_name,
        transfer_name,
        network_name,
        case when status=1 then '已阅'
        when status=2 then '未阅'
        end as status,
        create_time
        from
        tab_regional_agent_read
        <where>
           <if test="dto.agentId != null">
                and agent_id = #{dto.agentId}
            </if>
            <if test="dto.transferId != null">
                and transfer_id = #{dto.transferId}
            </if>
            <if test="dto.networkId != null">
                and network_id = #{dto.networkId}
            </if>
            <if test="dto.commentWordName !='' and dto.commentWordName !=null">
                and comment_word_name like concat('%',#{dto.commentWordName},'%')
            </if>
            <if test="dto.status !=null">
                and status =#{dto.status}
            </if>
        </where>
    </select>

    <update id="updateStatus">
        update tab_regional_agent_read set status = 1 where comment_word_id = #{knowledgeWordId} and network_id = #{networkId}
    </update>

    <delete id="deleteData">
        delete from tab_regional_agent_read where comment_word_id = #{knowledgeWordId}
    </delete>


    <update id="updateDataByNetworkId" parameterType="com.yl.knowledgebase.api.dto.RegionalAgentReadDTO">
        update tab_regional_agent_read
         set agent_id = #{dto.agentId},
             agent_name = #{dto.agentName},
             transfer_id = #{dto.transferId},
             transfer_name =#{dto.transferName},
             network_id = #{dto.networkId},
             network_name =#{dto.networkName}
        where network_id = #{dto.oldNetwork}
    </update>
</mapper>