<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.official.admin.mapper.FeedBackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yl.official.admin.vo.FeedBackVo">
        <id column="id" property="id" />
        <result column="question_id" property="questionId" />
        <result column="question_name" property="questionName" />
        <result column="option1" property="option1" />
        <result column="option2" property="option2" />
        <result column="option3" property="option3" />
        <result column="option4" property="option4" />
        <result column="option5" property="option5" />
        <result column="option6" property="option6" />
        <result column="option7" property="option7" />
        <result column="option8" property="option8" />
        <result column="option9" property="option9" />
        <result column="option10" property="option10" />
        <result column="img_url" property="imgUrl" />
        <result column="answer" property="answer" />
        <result column="question_type" property="questionType" />
        <result column="create_by" property="createBy" />
        <result column="create_phone" property="createPhone" />
        <result column="create_time" property="createTime" />
        <result column="create_id" property="createId" />
        <result column="exam_id" property="examId" />
    </resultMap>

    <sql id="FeedBackVo">
        select
        id,
        question_id,
        question_name,
        option1,
        option2,
        option3,
        option4,
        option5,
        option6,
        option7,
        option8,
        option9,
        option10,
        img_url,
        answer,
        question_type,
        create_by,
        create_phone,
        create_time,
        create_id,
        exam_id,
        record_id
       from j_advise_question
    </sql>

    <select id="selectPage"  resultMap="BaseResultMap">
        <include refid="FeedBackVo"/>
        <where>
                <if test="dto.createId != null"> and create_id like concat('%', #{dto.createId}, '%')</if>
                <if test="dto.examId != null"> and exam_id like concat('%', #{dto.examId},'%')</if>
                <if test="dto.startTimeStr != null "> and STR_TO_DATE(#{dto.startTimeStr},'%Y-%m-%d %H:%i:%s') &lt;= create_time </if>
                <if test="dto.endTimeStr != null "> and  create_time &lt;= STR_TO_DATE(#{dto.endTimeStr},'%Y-%m-%d %H:%i:%s') </if>
        </where>
        GROUP BY record_id
        ORDER BY create_time DESC
    </select>

    <select id="selectList"   parameterType="com.yl.official.admin.dto.FeedBackQueryDto" resultType="com.yl.official.admin.vo.FeedBackExcelVo">
        <include refid="FeedBackVo"/>
        <where>question_type!=4
            <if test="dto.createId != null"> and create_id like concat('%', #{dto.createId}, '%')</if>
            <if test="dto.examId != null"> and exam_id like concat('%', #{dto.examId},'%')</if>
            <if test="dto.startTimeStr != null "> and STR_TO_DATE(#{dto.startTimeStr},'%Y-%m-%d %H:%i:%s') &lt;= create_time </if>
            <if test="dto.endTimeStr != null "> and  create_time &lt;= STR_TO_DATE(#{dto.endTimeStr},'%Y-%m-%d %H:%i:%s') </if>
        </where>
        GROUP BY exam_id,create_time,create_id,question_id
        ORDER BY exam_id DESC,create_time DESC,create_id DESC
    </select>
    <select id="getInfo"   parameterType="com.yl.official.admin.dto.FeedBackQueryDto" resultType="com.yl.official.admin.vo.FeedBackVo">
        <include refid="FeedBackVo"/>
        <where>
            <if test="recordId != null"> and record_id = #{recordId}</if>
        </where>
    </select>
</mapper>
