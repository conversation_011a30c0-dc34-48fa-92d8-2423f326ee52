<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.17.0.final using JasperReports Library version 6.17.0-6d93193241dd8cc42629e188b94f9e0bc5722efd  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="jfs_print_main_waybill_new_couplet" pageWidth="283" pageHeight="283" columnWidth="283" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="*************-4f0d-8a4f-6a7de0be2029">
	<property name="ireport.zoom" value="2.5937424601000068"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="240"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<field name="receiverTelphone" class="java.lang.String"/>
	<field name="senderTelphone" class="java.lang.String"/>
	<field name="waybillNo" class="java.lang.String"/>
	<field name="remarks" class="java.lang.String"/>
	<field name="receiverName" class="java.lang.String"/>
	<field name="receiverMobilePhone" class="java.lang.String"/>
	<field name="receiverCompany" class="java.lang.String"/>
	<field name="receiverProvinceName" class="java.lang.String"/>
	<field name="receiverCityName" class="java.lang.String"/>
	<field name="receiverAreaName" class="java.lang.String"/>
	<field name="receiverDetailedAddress" class="java.lang.String"/>
	<field name="senderProvinceName" class="java.lang.String"/>
	<field name="senderCityName" class="java.lang.String"/>
	<field name="senderAreaName" class="java.lang.String"/>
	<field name="senderDetailedAddress" class="java.lang.String"/>
	<field name="senderMobilePhone" class="java.lang.String"/>
	<field name="senderCompany" class="java.lang.String"/>
	<field name="senderName" class="java.lang.String"/>
	<field name="terminalDispatchCode" class="java.lang.String"/>
	<field name="newTerminalDispatchCode" class="java.lang.String"/>
	<field name="pickNetworkName" class="java.lang.String"/>
	<field name="printTime" class="java.lang.String"/>
	<field name="settlementName" class="java.lang.String"/>
	<field name="dispatchName" class="java.lang.String"/>
	<field name="packageNumber" class="java.lang.String"/>
	<field name="printsNumber" class="java.lang.String"/>
	<field name="settlementCode" class="java.lang.String"/>
	<field name="packageChargeWeight" class="java.lang.String"/>
	<field name="packageVolume" class="java.lang.String"/>
	<field name="totalFreight" class="java.lang.String"/>
	<field name="orderSourceName" class="java.lang.String"/>
	<field name="allocationCode1" class="java.lang.String"/>
	<field name="allocationCode2" class="java.lang.String"/>
	<field name="allocationCode3" class="java.lang.String"/>
	<field name="allocationCode4" class="java.lang.String"/>
	<field name="allocationCode5" class="java.lang.String"/>
	<field name="allocationCode6" class="java.lang.String"/>
	<field name="allocationCode7" class="java.lang.String"/>
	<field name="areaCode1" class="java.lang.String"/>
	<field name="areaCode2" class="java.lang.String"/>
	<field name="areaCode3" class="java.lang.String"/>
	<field name="areaCode4" class="java.lang.String"/>
	<field name="areaCode5" class="java.lang.String"/>
	<field name="areaCode6" class="java.lang.String"/>
	<field name="areaCode7" class="java.lang.String"/>
	<field name="index" class="java.lang.String"/>
	<background>
		<band height="283" splitType="Stretch">
			<image>
				<reportElement x="170" y="8" width="103" height="30" uuid="cb19f1ca-cff6-4586-b0be-54083035b25f"/>
				<imageExpression><![CDATA["/printTemplate/main.png"]]></imageExpression>
			</image>
		</band>
	</background>
	<detail>
		<band height="283" splitType="Stretch">
			<line>
				<reportElement x="0" y="116" width="283" height="1" uuid="653e390c-72ef-4b47-99fb-1fc280cfbcc8"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="156" y="3" width="125" height="10" uuid="68abaf5f-e8f1-4f41-b07f-738863e094e6"/>
				<textElement>
					<font fontName="SansSerif" size="6" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["waktu cetak:"+($F{printTime}==null?"":$F{printTime})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="156" y="13" width="125" height="10" uuid="39ce32d6-96b3-4157-919d-1fab7415f2e4"/>
				<textElement>
					<font fontName="SansSerif" size="6" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["outlet pengirim:"+($F{pickNetworkName}==null?"":$F{pickNetworkName})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="156" y="23" width="72" height="16" uuid="e399a64d-fda7-43b4-9c9e-8909168f11a4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="12" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{dispatchName}==null?"":$F{dispatchName})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="32" width="283" height="43" uuid="cb2385a0-6fb8-4d82-8c83-096b26a02992">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="34" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{terminalDispatchCode}==null?"":$F{terminalDispatchCode}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="32" width="283" height="45" uuid="cb2385a0-6fb8-4d82-8c83-096b26a02992">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="22" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{newTerminalDispatchCode}==null?"":$F{newTerminalDispatchCode}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="159" width="283" height="1" uuid="d831c0b3-b363-4266-8000-bfb3c588bd61">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="0" y="247" width="283" height="1" uuid="a22eeafc-1452-4b96-8b01-afa56c9875b6">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="26" y="149" width="0" height="8" uuid="ae9726a9-4eef-45b5-aafe-b8001152698c"/>
				<textElement>
					<font fontName="SansSerif" size="6" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["cetakan ke-"+($F{printsNumber}==null?"":$F{printsNumber})]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="27" y="121" width="225" height="34" uuid="0140d548-d627-4ec8-88c7-76601a803f6a">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="Code128A" drawText="false" checksumRequired="false" barWidth="5" barHeight="120">
					<jr:codeExpression><![CDATA[$F{waybillNo}==null?"":$F{waybillNo}]]></jr:codeExpression>
				</jr:barbecue>
			</componentElement>
			<textField isBlankWhenNull="true">
				<reportElement x="93" y="149" width="107" height="11" uuid="4a0305f2-d52b-4d06-b8da-612da8076bbf">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="8" isBold="false" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Utama "+($F{waybillNo}==null?"":$F{waybillNo})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="19" y="150" width="78" height="8" uuid="a0007027-ac9d-4acf-95f2-ca4cdf069409"/>
				<textElement>
					<font fontName="SansSerif" size="6" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["cetakan ke-"+($F{printsNumber}==null?"":$F{printsNumber})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="5" y="234" width="110" height="12" uuid="15939318-8a9a-42e3-b612-88725a5c226d"/>
				<textElement markup="html">
					<font fontName="SansSerif" size="8" isBold="true" pdfEncoding="Cp1250" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["volume: "+($F{packageVolume}==null?"":$F{packageVolume}+"m<sup>3</sup>")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="5" y="221" width="110" height="12" uuid="8ba31b73-a629-4247-b2e6-5e9842549a65"/>
				<textElement>
					<font fontName="SansSerif" size="8" isBold="true" pdfEncoding="Cp1250" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["berat: "+($F{packageChargeWeight}==null?"":$F{packageChargeWeight}+"KG")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="116" y="221" width="111" height="12" uuid="110192f2-ffbc-49d8-8ebb-e015439faf61"/>
				<textElement>
					<font fontName="SansSerif" size="8" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{settlementCode}.isEmpty()? "" : ($F{settlementCode}.equals("CC_CASH")?("DFOD: "+($F{totalFreight}.isEmpty() ? "" : $F{totalFreight})): null)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="5" y="248" width="18" height="10" uuid="9cc5974b-eedb-425c-aa55-f35b3403dec5"/>
				<textElement>
					<font fontName="SansSerif" size="6" isBold="true" pdfEncoding="Cp1250" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[From:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="23" y="248" width="94" height="10" isPrintWhenDetailOverflows="true" uuid="5d08cfd5-58eb-453b-925d-9cc43043bfff"/>
				<textElement>
					<font fontName="SansSerif" size="6" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{senderName}==null?"":$F{senderName})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="172" y="248" width="56" height="10" uuid="5686439a-7b27-4721-a106-acbeeae2c111"/>
				<textElement>
					<font fontName="SansSerif" size="6" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{senderTelphone}==null?"":$F{senderTelphone})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="117" y="248" width="55" height="10" uuid="5bbd0c37-0cc3-4e95-a9e7-2573f7b3bf84"/>
				<textElement>
					<font fontName="SansSerif" size="6" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{senderMobilePhone}==null?"":$F{senderMobilePhone})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="5" y="258" width="270" height="16" isPrintWhenDetailOverflows="true" uuid="2c5e1768-9ad7-4ad3-b120-6c88b4ad9066">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="6" isBold="false" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["keterangan: "+($F{remarks}==null?"":$F{remarks})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="5" y="273" width="221" height="10" uuid="b0c33bb7-b57a-43e2-b288-026d38ddaf93"/>
				<textElement>
					<font fontName="SansSerif" size="7" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Nomor CS: 021-80661666]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="124" y="174" width="69" height="12" uuid="e1670e55-5270-49b7-a7f5-b13b542ac835">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="SansSerif" size="6" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{receiverMobilePhone}==null?"":$F{receiverMobilePhone})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="3" y="186" width="274" height="12" isPrintWhenDetailOverflows="true" uuid="912c1f09-0ca7-4ec0-8e8e-cf55c5555a7c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="7" isBold="true" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{receiverProvinceName}.isEmpty()?"":$F{receiverProvinceName}+" ")+$F{receiverCityName}+" "+$F{receiverAreaName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="5" y="162" width="15" height="10" uuid="09fc00bb-2a1b-4c87-8def-6dbe1a425038"/>
				<textElement>
					<font fontName="SansSerif" size="7" isBold="true" pdfEncoding="Cp1250" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[TO:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="30" y="162" width="208" height="12" isPrintWhenDetailOverflows="true" uuid="91e8e136-297d-4a6a-b2f2-97d203440652">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="7" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{receiverCompany}==null?"":$F{receiverCompany})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="193" y="174" width="60" height="12" uuid="3dddc9f1-5332-41d7-9615-82c5b7caa8e4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="SansSerif" size="6" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{receiverTelphone}==null?"":$F{receiverTelphone})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="3" y="199" width="276" height="18" isPrintWhenDetailOverflows="true" uuid="8cc0ce86-0786-4012-a2a6-5b8157bd7377">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="7" isBold="true" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{receiverDetailedAddress}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="217" width="283" height="1" uuid="9ee258d4-dfaa-480f-8b11-e34ef101f757">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="30" y="174" width="94" height="12" isPrintWhenDetailOverflows="true" uuid="52140d22-e355-44e2-b4ee-81f725acfe22">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="SansSerif" size="7" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{receiverName}==null?"":$F{receiverName})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="228" y="23" width="53" height="16" uuid="176f8153-9285-4beb-a417-9247c4aa1923">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="8" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{orderSourceName}==null?"":$F{orderSourceName})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="117" y="234" width="24" height="12" uuid="58def210-fd4b-4b14-97dd-20c2ad9707a4"/>
				<textElement markup="html">
					<font fontName="SansSerif" size="8" isBold="true" pdfEncoding="Cp1250" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["pcs: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="141" y="230" width="84" height="19" uuid="a2c7ab1f-1877-4641-813e-42bf8b77eb21">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="15" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["001/"+$F{packageNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="75" width="39" height="20" uuid="fa17e69f-22d4-4a29-bee4-a15f483f8c2b">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{allocationCode1}==null?"":$F{allocationCode1})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="96" width="39" height="20" uuid="e29ccf74-cd7f-4536-b158-cfff51f279fc">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{areaCode1}==null?"":$F{areaCode1})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="41" y="96" width="39" height="20" uuid="345eb668-706d-4c3c-9252-df8c74e29905">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{areaCode2}==null?"":$F{areaCode2})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="41" y="75" width="39" height="20" uuid="080fb146-9cb2-49ac-9a88-32a36e8bda47">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{allocationCode2}==null?"":$F{allocationCode2})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="81" y="75" width="39" height="20" uuid="52022cbe-8d46-4504-967a-df0e51763e67">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{allocationCode3}==null?"":$F{allocationCode3})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="81" y="96" width="39" height="20" uuid="50dec9ab-7963-499f-9426-a80dabb2b846">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{areaCode3}==null?"":$F{areaCode3})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="121" y="96" width="39" height="20" uuid="fba99e79-ab1a-4b58-9411-432fdc12cf6c">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{areaCode4}==null?"":$F{areaCode4})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="121" y="75" width="39" height="20" uuid="6268dd6d-ee1d-465d-8e9b-69d76e89bf12">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{allocationCode4}==null?"":$F{allocationCode4})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="161" y="75" width="39" height="20" uuid="f00c711c-1801-415e-a464-44b22c0d92ca">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{allocationCode5}==null?"":$F{allocationCode5})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="201" y="75" width="39" height="20" uuid="04c032e1-6ce1-4c07-b6ab-683eea200f7a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{allocationCode6}==null?"":$F{allocationCode6})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="242" y="75" width="39" height="20" uuid="75fa3a82-3982-43af-bbdb-66c87c39c3ec">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{allocationCode7}==null?"":$F{allocationCode7})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="242" y="96" width="39" height="20" uuid="4d4b2fd9-60cb-4a55-8f68-26342621454d">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{areaCode7}==null?"":$F{areaCode7})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="201" y="96" width="39" height="20" uuid="6780797c-d72a-46d5-97a7-938930e06719">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{areaCode6}==null?"":$F{areaCode6})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="161" y="96" width="39" height="20" uuid="1ac57c6f-4193-458a-b0c7-5fe1bebf74fa">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{areaCode5}==null?"":$F{areaCode5})]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="74" width="283" height="1" uuid="7c4ee826-07ef-462a-b5a0-c5483dd8b7b1">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="40" y="96" width="1" height="20" uuid="0d0e8fbe-4b5e-4ba3-bf2c-ed06f825a92a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="80" y="96" width="1" height="20" uuid="1c87f6f2-edb7-488b-8c8f-62dfe0912e6a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="120" y="95" width="1" height="20" uuid="68f51237-6fb6-479e-a918-bbe67b71403d">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="160" y="96" width="1" height="20" uuid="8e52a2cf-ac16-4ad8-b870-9cce16c0c2e5">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="200" y="96" width="1" height="20" uuid="c68a8440-c764-42af-9fef-4d9816749382">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="241" y="96" width="1" height="20" uuid="2b6826d8-90ee-449b-a353-af27ff179fac">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="0" y="95" width="283" height="1" uuid="cc40aa39-c81b-42d6-8e97-8df5382851da">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="40" y="75" width="1" height="20" uuid="40d970c7-320e-47a2-bfc2-5d33756e8498">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="80" y="75" width="1" height="20" uuid="97a7a905-ac73-48e7-b585-d6e35b9d3eea">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="120" y="75" width="1" height="20" uuid="325c3bc6-4831-49a1-b38a-7a6946e89e62">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="160" y="75" width="1" height="20" uuid="d59a7509-fc36-44c8-b4d1-2da1754b9652">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="200" y="75" width="1" height="20" uuid="340feb30-479b-4358-aeb5-a391f074cd78">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="241" y="75" width="1" height="20" uuid="93e92145-8dbe-41ec-a97b-870511e5a6a6">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#8C807B"/>
				</graphicElement>
			</line>
		</band>
	</detail>
</jasperReport>
