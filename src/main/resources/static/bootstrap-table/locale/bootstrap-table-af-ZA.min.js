/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.16.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t=t||self).jQuery)}(this,(function(t){"use strict";t=t&&t.hasOwnProperty("default")?t.default:t;var n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(t,n){return t(n={exports:{}},n.exports),n.exports}var e=function(t){return t&&t.Math==Math&&t},o=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof n&&n)||Function("return this")(),i=function(t){try{return!!t()}catch(t){return!0}},u=!i((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),a={}.propertyIsEnumerable,f=Object.getOwnPropertyDescriptor,c={f:f&&!a.call({1:2},1)?function(t){var n=f(this,t);return!!n&&n.enumerable}:a},l=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},s={}.toString,p=function(t){return s.call(t).slice(8,-1)},g="".split,y=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==p(t)?g.call(t,""):Object(t)}:Object,d=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},m=function(t){return y(d(t))},h=function(t){return"object"==typeof t?null!==t:"function"==typeof t},v=function(t,n){if(!h(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!h(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!h(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!h(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},b={}.hasOwnProperty,w=function(t,n){return b.call(t,n)},S=o.document,O=h(S)&&h(S.createElement),j=!u&&!i((function(){return 7!=Object.defineProperty((t="div",O?S.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),P=Object.getOwnPropertyDescriptor,T={f:u?P:function(t,n){if(t=m(t),n=v(n,!0),j)try{return P(t,n)}catch(t){}if(w(t,n))return l(!c.f.call(t,n),t[n])}},x=function(t){if(!h(t))throw TypeError(String(t)+" is not an object");return t},A=Object.defineProperty,E={f:u?A:function(t,n,r){if(x(t),n=v(n,!0),x(r),j)try{return A(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},M=u?function(t,n,r){return E.f(t,n,l(1,r))}:function(t,n,r){return t[n]=r,t},R=function(t,n){try{M(o,t,n)}catch(r){o[t]=n}return n},C=o["__core-js_shared__"]||R("__core-js_shared__",{}),_=Function.toString;"function"!=typeof C.inspectSource&&(C.inspectSource=function(t){return _.call(t)});var k,F,N,I,L=C.inspectSource,D=o.WeakMap,q="function"==typeof D&&/native code/.test(L(D)),G=r((function(t){(t.exports=function(t,n){return C[t]||(C[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.6.0",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})})),W=0,z=Math.random(),B=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++W+z).toString(36)},H=G("keys"),K={},Z=o.WeakMap;if(q){var J=new Z,Q=J.get,U=J.has,V=J.set;k=function(t,n){return V.call(J,t,n),n},F=function(t){return Q.call(J,t)||{}},N=function(t){return U.call(J,t)}}else{var Y=H[I="state"]||(H[I]=B(I));K[Y]=!0,k=function(t,n){return M(t,Y,n),n},F=function(t){return w(t,Y)?t[Y]:{}},N=function(t){return w(t,Y)}}var X,$,tt={set:k,get:F,has:N,enforce:function(t){return N(t)?F(t):k(t,{})},getterFor:function(t){return function(n){var r;if(!h(n)||(r=F(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},nt=r((function(t){var n=tt.get,r=tt.enforce,e=String(String).split("String");(t.exports=function(t,n,i,u){var a=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,c=!!u&&!!u.noTargetGet;"function"==typeof i&&("string"!=typeof n||w(i,"name")||M(i,"name",n),r(i).source=e.join("string"==typeof n?n:"")),t!==o?(a?!c&&t[n]&&(f=!0):delete t[n],f?t[n]=i:M(t,n,i)):f?t[n]=i:R(n,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||L(this)}))})),rt=o,et=function(t){return"function"==typeof t?t:void 0},ot=function(t,n){return arguments.length<2?et(rt[t])||et(o[t]):rt[t]&&rt[t][n]||o[t]&&o[t][n]},it=Math.ceil,ut=Math.floor,at=function(t){return isNaN(t=+t)?0:(t>0?ut:it)(t)},ft=Math.min,ct=function(t){return t>0?ft(at(t),9007199254740991):0},lt=Math.max,st=Math.min,pt=function(t){return function(n,r,e){var o,i=m(n),u=ct(i.length),a=function(t,n){var r=at(t);return r<0?lt(r+n,0):st(r,n)}(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},gt={includes:pt(!0),indexOf:pt(!1)}.indexOf,yt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),dt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=m(t),o=0,i=[];for(r in e)!w(K,r)&&w(e,r)&&i.push(r);for(;n.length>o;)w(e,r=n[o++])&&(~gt(i,r)||i.push(r));return i}(t,yt)}},mt={f:Object.getOwnPropertySymbols},ht=ot("Reflect","ownKeys")||function(t){var n=dt.f(x(t)),r=mt.f;return r?n.concat(r(t)):n},vt=function(t,n){for(var r=ht(n),e=E.f,o=T.f,i=0;i<r.length;i++){var u=r[i];w(t,u)||e(t,u,o(n,u))}},bt=/#|\.prototype\./,wt=function(t,n){var r=Ot[St(t)];return r==Pt||r!=jt&&("function"==typeof n?i(n):!!n)},St=wt.normalize=function(t){return String(t).replace(bt,".").toLowerCase()},Ot=wt.data={},jt=wt.NATIVE="N",Pt=wt.POLYFILL="P",Tt=wt,xt=T.f,At=Array.isArray||function(t){return"Array"==p(t)},Et=function(t){return Object(d(t))},Mt=function(t,n,r){var e=v(n);e in t?E.f(t,e,l(0,r)):t[e]=r},Rt=!!Object.getOwnPropertySymbols&&!i((function(){return!String(Symbol())})),Ct=Rt&&!Symbol.sham&&"symbol"==typeof Symbol(),_t=G("wks"),kt=o.Symbol,Ft=Ct?kt:B,Nt=function(t){return w(_t,t)||(Rt&&w(kt,t)?_t[t]=kt[t]:_t[t]=Ft("Symbol."+t)),_t[t]},It=Nt("species"),Lt=function(t,n){var r;return At(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!At(r.prototype)?h(r)&&null===(r=r[It])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Dt=ot("navigator","userAgent")||"",qt=o.process,Gt=qt&&qt.versions,Wt=Gt&&Gt.v8;Wt?$=(X=Wt.split("."))[0]+X[1]:Dt&&(!(X=Dt.match(/Edge\/(\d+)/))||X[1]>=74)&&(X=Dt.match(/Chrome\/(\d+)/))&&($=X[1]);var zt,Bt=$&&+$,Ht=Nt("species"),Kt=Nt("isConcatSpreadable"),Zt=Bt>=51||!i((function(){var t=[];return t[Kt]=!1,t.concat()[0]!==t})),Jt=(zt="concat",Bt>=51||!i((function(){var t=[];return(t.constructor={})[Ht]=function(){return{foo:1}},1!==t[zt](Boolean).foo}))),Qt=function(t){if(!h(t))return!1;var n=t[Kt];return void 0!==n?!!n:At(t)};!function(t,n){var r,e,i,u,a,f=t.target,c=t.global,l=t.stat;if(r=c?o:l?o[f]||R(f,{}):(o[f]||{}).prototype)for(e in n){if(u=n[e],i=t.noTargetGet?(a=xt(r,e))&&a.value:r[e],!Tt(c?e:f+(l?".":"#")+e,t.forced)&&void 0!==i){if(typeof u==typeof i)continue;vt(u,i)}(t.sham||i&&i.sham)&&M(u,"sham",!0),nt(r,e,u,t)}}({target:"Array",proto:!0,forced:!Zt||!Jt},{concat:function(t){var n,r,e,o,i,u=Et(this),a=Lt(u,0),f=0;for(n=-1,e=arguments.length;n<e;n++)if(i=-1===n?u:arguments[n],Qt(i)){if(f+(o=ct(i.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(r=0;r<o;r++,f++)r in i&&Mt(a,f,i[r])}else{if(f>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Mt(a,f++,i)}return a.length=f,a}}),t.fn.bootstrapTable.locales["af-ZA"]={formatLoadingMessage:function(){return"Besig om te laai, wag asseblief"},formatRecordsPerPage:function(t){return"".concat(t," rekords per bladsy")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Resultate ".concat(t," tot ").concat(n," van ").concat(r," rye (filtered from ").concat(e," total rows)"):"Resultate ".concat(t," tot ").concat(n," van ").concat(r," rye")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Soek"},formatNoMatches:function(){return"Geen rekords gevind nie"},formatPaginationSwitch:function(){return"Wys/verberg bladsy nummering"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Herlaai"},formatToggle:function(){return"Wissel"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolomme"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["af-ZA"])}));
