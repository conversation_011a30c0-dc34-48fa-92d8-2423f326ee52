-- yl_community_service.tab_exam_course definition

-- auto-generated definition
create table tab_exam_course
(
    id            bigint auto_increment comment 'id'
        primary key,
    course_name   varchar(50)                         not null comment '课程名称',
    picture_path  varchar(200)                        null comment '课程图片地址',
    chapter_count int       default 0                 null comment '课程章数',
    section_count int       default 0                 null comment '课程小节数',
    teacher       varchar(50)                         null comment '课程老师',
    start_time    timestamp                           null comment '课程开始时间',
    end_time      timestamp                           null comment '课程结束时间',
    course_type   int       default 1                 null comment '课程类别',
    status        int(1)    default 2                 null comment '课程状态(1已发布 2待发布)',
    must          int(1)    default 1                 null comment '是否必学(1必学 2不必)',
    pass_exam     int(1)    default 2                 null comment '考试是否已通过(1通过 2未通过)',
    create_by     varchar(50)                         null comment '创建人',
    create_time   timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    create_id     bigint                              null comment '创建人id',
    update_by     varchar(50)                         null comment '更新',
    update_time   timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    update_id     bigint                              null comment '更新人id',
    introduction  varchar(500)                        null comment '课程简介',
    paper_count   int       default 0                 null comment '试卷数'
)
    comment '课程表';

-- auto-generated definition
create table tab_exam_course_chapter
(
    id             bigint auto_increment comment 'id'
        primary key,
    chapter_number bigint                              not null comment '章节编号(唯一)',
    course_id      bigint                              not null comment '所属课程id',
    parent_number  bigint                              not null comment '所属章节编号',
    chapter_name   varchar(50)                         not null comment '课程章节名称',
    picture_path   varchar(200)                        null comment '课程图片地址',
    chapter_type   int(1)    default 1                 not null comment '章节类型(1目录 2小节)',
    content_type   int(1)    default 1                 null comment '章节内容类型 1.视频 2文档',
    teacher        varchar(50)                         null comment '课程老师',
    phone          varchar(50)                         null comment '联系方式',
    content_path   varchar(200)                        null comment '章节内容地址',
    create_by      varchar(50)                         null comment '创建人',
    create_time    timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    create_id      bigint                              null comment '创建人id',
    sort           int                                 null comment '顺序。章以100开始。'
)
    comment '课程章节目录表';

create index tab_exam_course_chapter_course_id_IDX
    on tab_exam_course_chapter (course_id, chapter_number);

-- auto-generated definition
create table tab_exam_course_paper
(
    id          bigint auto_increment comment 'id'
        primary key,
    paper_id    bigint                              not null comment '试卷id',
    course_id   bigint                              not null comment '课程id',
    paper_name  varchar(50)                         null comment '试卷名称',
    start_time  timestamp                           null comment '考试开始时间',
    end_time    timestamp                           null comment '考试结束时间',
    status      int(1)    default 1                 null comment '课程状态(1启用 2未启用)',
    create_by   varchar(50)                         null comment '创建人',
    create_time timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    create_id   bigint                              null comment '创建人id'
)
    comment '课程试卷表';


-- yl_community_service.tab_exam_paper definition

CREATE TABLE `tab_exam_paper` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                  `course_id` bigint(20) DEFAULT NULL COMMENT '试卷所属课程id，备用',
                                  `picture_path` varchar(200) DEFAULT NULL COMMENT '图片地址',
                                  `paper_name` varchar(50) NOT NULL COMMENT '试卷名称',
                                  `status` int(1) NOT NULL DEFAULT '1' COMMENT '试卷状态 1 启用 2禁用',
                                  `question_count` int(11) DEFAULT '0' COMMENT '试题数',
                                  `score` int(11) DEFAULT '0' COMMENT '试卷分数',
                                  `pass_score` int(11) DEFAULT '0' COMMENT '合格分数',
                                  `exam_time_length` int(11) DEFAULT '0' COMMENT '考试时长(分钟)',
                                  `exam_count` int(11) DEFAULT '0' COMMENT '考试允许考试次数',
                                  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `create_id` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                  `update_by` varchar(50) DEFAULT NULL COMMENT '更新',
                                  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  `update_id` bigint(20) DEFAULT NULL COMMENT '更新人id',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='试卷表';


-- yl_community_service.tab_exam_paper_question definition

CREATE TABLE `tab_exam_paper_question` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                           `paper_name` varchar(50) DEFAULT NULL COMMENT '试卷名称',
                                           `paper_id` bigint(20) NOT NULL COMMENT '试卷id',
                                           `question_id` bigint(20) NOT NULL COMMENT '试题id',
                                           `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                           `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `create_id` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='试卷试题表';


-- yl_community_service.tab_exam_test_question definition

CREATE TABLE `tab_exam_test_question` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `course_id` bigint(20) DEFAULT NULL COMMENT '课程id',
                                          `question_name` varchar(50) NOT NULL COMMENT '试题名称',
                                          `answer_a` varchar(100) DEFAULT NULL COMMENT '试题答案A',
                                          `answer_b` varchar(100) DEFAULT NULL COMMENT '试题答案B',
                                          `answer_c` varchar(100) DEFAULT NULL COMMENT '试题答案C',
                                          `answer_d` varchar(100) DEFAULT NULL COMMENT '试题答案D',
                                          `answer_e` varchar(100) DEFAULT NULL COMMENT '试题答案E',
                                          `answer_f` varchar(100) DEFAULT NULL COMMENT '试题答案F',
                                          `right_answers` varchar(10) NOT NULL COMMENT '试题正确答案',
                                          `question_type` int(1) NOT NULL DEFAULT '1' COMMENT '试题类型（1 选择题，2多选择题  3判断题）',
                                          `score` int(11) NOT NULL COMMENT '题分',
                                          `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                          `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `create_id` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                          `update_by` varchar(50) DEFAULT NULL COMMENT '更新',
                                          `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                          `update_id` bigint(20) DEFAULT NULL COMMENT '更新人id',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='试题表';




-- yl_community_service.tab_user_chapter_study definition

CREATE TABLE `tab_user_chapter_study` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `chapter_number` bigint(20) NOT NULL COMMENT '章节编号(唯一)',
                                          `course_id` bigint(20) NOT NULL COMMENT '所属课程id',
                                          `chapter_name` varchar(50) NOT NULL COMMENT '课程章节名称',
                                          `study_start_time` timestamp NULL DEFAULT NULL COMMENT '章节学习开始时间',
                                          `video_view_progress` int(11) NOT NULL DEFAULT '0' COMMENT '视频内容观看进度，当前播放位置  秒记',
                                          `status` int(1) DEFAULT '2' COMMENT '章节学习状态 1.已完成，2 学习中 3待学习',
                                          `study_total_time` int(11) DEFAULT '0' COMMENT '章节学习总时长(分钟)',
                                          `study_new_time` timestamp NULL DEFAULT NULL COMMENT '章节学习最新时间',
                                          `study_end_time` timestamp NULL DEFAULT NULL COMMENT '章节学习最新结束时间',
                                          `study_today_time` int(11) DEFAULT '0' COMMENT '课程学习当天时长',
                                          `user_id` bigint(20) NOT NULL COMMENT '用户id',
                                          PRIMARY KEY (`id`),
                                          KEY `tab_user_chapter_study_course_id_IDX` (`course_id`,`chapter_number`,`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='个人章节学习记录';


-- yl_community_service.tab_user_course definition

CREATE TABLE `tab_user_course` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                   `user_id` bigint(20) NOT NULL COMMENT '用户id',
                                   `course_id` bigint(20) NOT NULL COMMENT '课程id',
                                   `course_name` varchar(50) DEFAULT NULL COMMENT '课程名称',
                                   `teacher` varchar(50) DEFAULT NULL COMMENT '课程老师',
                                   `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '课程开始学习时间',
                                   `new_time` timestamp NULL DEFAULT NULL COMMENT '课程最新学习时间',
                                   `status` int(1) NOT NULL DEFAULT '2' COMMENT '课程学习状态 1.已完成，2 学习中 3待学习',
                                   `learned_section_count` int(11) DEFAULT '0' COMMENT '课程已学习小节数',
                                   `learning_chapter_name` varchar(50) DEFAULT NULL COMMENT '课程正在学习的小节名称',
                                   `learning_chapter_number` bigint(20) DEFAULT NULL COMMENT '课程正在学习的小节id',
                                   `study_time_count` int(11) DEFAULT '0' COMMENT '课程学习总时长',
                                   `pass_exam` int(1) NOT NULL DEFAULT '2' COMMENT '课程是否通过 1 通过 2未通过',
                                   PRIMARY KEY (`id`),
                                   KEY `tab_user_course_course_id_IDX` (`course_id`,`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='个人课程学习记录';


-- yl_community_service.tab_user_exam_history definition

CREATE TABLE `tab_user_exam_history` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                         `user_id` bigint(20) NOT NULL COMMENT '用户id',
                                         `course_id` bigint(20) NOT NULL COMMENT '课程id',
                                         `course_name` varchar(50) DEFAULT NULL COMMENT '课程名称',
                                         `paper_id` bigint(20) NOT NULL COMMENT '试卷id',
                                         `question_count` int(11) DEFAULT '0' COMMENT '试题数',
                                         `paper_name` varchar(50) DEFAULT NULL COMMENT '试卷名称',
                                         `score` int(11) DEFAULT '0' COMMENT '试卷分数',
                                         `pass_score` int(11) DEFAULT '0' COMMENT '合格分数',
                                         `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '考试开始时间',
                                         `end_time` timestamp NULL DEFAULT NULL COMMENT '考试结束时间  ',
                                         `exam_frequency` int(11) NOT NULL DEFAULT '1' COMMENT '当前考试次数',
                                         `exam_batch` bigint(20) NOT NULL COMMENT '考试批次',
                                         `grade` int(11) NOT NULL DEFAULT '0' COMMENT '考试分数',
                                         `exam_ip` varbinary(20) DEFAULT NULL COMMENT '考试人员ip',
                                         `exam_state` int(1) DEFAULT '3' COMMENT '考试状态  1.通过 2未通过 3 代考',
                                         `user_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户姓名',
                                         `network_id` bigint(20) NOT NULL COMMENT '网点id',
                                         `network_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '网点姓名',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='个人考试历史日志表';


-- yl_community_service.tab_user_exam_record definition

CREATE TABLE `tab_user_exam_record` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `user_id` bigint(20) NOT NULL COMMENT '用户id',
                                        `course_id` bigint(20) NOT NULL COMMENT '课程id',
                                        `course_name` varchar(50) DEFAULT NULL COMMENT '课程名称',
                                        `paper_id` bigint(20) NOT NULL COMMENT '试卷id',
                                        `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '考试开始时间',
                                        `end_time` timestamp NULL DEFAULT NULL COMMENT '考试结束时间  ',
                                        `exam_frequency` int(11) NOT NULL DEFAULT '0' COMMENT '当前考试次数',
                                        `exam_batch` bigint(20) NOT NULL COMMENT '考试批次',
                                        `grade` int(11) DEFAULT '0' COMMENT '考试分数',
                                        `exam_ip` varbinary(20) DEFAULT NULL COMMENT '考试人员ip',
                                        `exam_state` int(1) DEFAULT '3' COMMENT '考试状态 1.通过 2未通过 3 代考',
                                        `user_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户姓名',
                                        `network_id` bigint(20) NOT NULL COMMENT '网点id',
                                        `network_name` varchar(100) NOT NULL COMMENT '网点姓名',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='个人考试记录表';

-- yl_community_service.tab_user_answer_record definition

CREATE TABLE `tab_user_answer_record` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
                                          `paper_id` bigint(20) NOT NULL COMMENT '试卷id',
                                          `exam_batch` bigint(20) NOT NULL COMMENT '考试批次',
                                          `course_id` bigint(20) DEFAULT NULL COMMENT '课程id',
                                          `question_id` bigint(20) NOT NULL COMMENT '试题id',
                                          `question_name` varchar(50) NOT NULL COMMENT '试题名称',
                                          `answer_a` varchar(100) DEFAULT NULL COMMENT '试题答案A',
                                          `answer_b` varchar(100) DEFAULT NULL COMMENT '试题答案B',
                                          `answer_c` varchar(100) DEFAULT NULL COMMENT '试题答案C',
                                          `answer_d` varchar(100) DEFAULT NULL COMMENT '试题答案D',
                                          `answer_e` varchar(100) DEFAULT NULL COMMENT '试题答案E',
                                          `answer_f` varchar(100) DEFAULT NULL COMMENT '试题答案F',
                                          `right_answers` varchar(10) NOT NULL COMMENT '试题正确答案',
                                          `question_type` int(1) NOT NULL DEFAULT '1' COMMENT '试题类型（1 选择题，2多选择题  3判断题）',
                                          `score` int(11) NOT NULL COMMENT '题分',
                                          `answer_time` timestamp NULL DEFAULT NULL COMMENT '答题时间',
                                          `answer_result` int(1) DEFAULT '2' COMMENT '答题结果,是否正确（1 正确 2不正确）',
                                          `answer_score` int(11) DEFAULT '0' COMMENT '答题得分',
                                          `answer_ip` int(11) DEFAULT NULL COMMENT '答题人员IP',
                                          `answer` varchar(10) DEFAULT NULL COMMENT '做题答案',
                                          `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                          `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `create_id` bigint(20) DEFAULT NULL COMMENT '创建人id',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='个人试卷答题记录表';
