'use strict'

import Mock from 'mockjs'
import url from 'url'
// 模拟数据

const MOCK_DATA = Mock.mock({
  'list|10': [
    {
      'id|+1': 1, // 自增id
      waybillNumber: '@natural()',
      'status|1': ['已签收', '已退件', '待取件', '已取件', '已取消'],
      recipient: '@name',
      'receivingPhone|1': /^1[0-9]{10}$/,
      receivingProvince: '@province()',
      receivingCity: '@city()',
      receivingArea: '@county()',
      shippingAddress: '粤海大道科技园十二路粤美特大厦云路供应链1506室',
      boxType: '文件',
      boxName: '面单纸',
      boxWeight: '@integer(1, 10)kg',
      boxNumbers: '@integer(1, 10)件',
      boxSpecification: '1#纸箱-530*290*370mm',
      boxCount: '@natural(60, 100)',
      boxPrice: '￥@float(0, 1)',
      paymentMethod: ['寄付月结', '到付'],
      COD: '@increment(100)',
      codFee: '@increment(100)',
      receivableFreight: '@integer(60, 100)元',
      itemValue: '@increment(100)',
      insuranceFee: '@integer(60, 100)元',
      remark: '@ctitle(10)',
      collectionTime: '@datetime',
    },
  ],
})

// default response
const result = {
  code: 1,
  msg: '操作成功',
  data: null,
}

/**
 * 请求拦截
 */
// 获取列表信息
const URL_LIST = /\/demo\/list/
const METHOD_LIST = 'get'
Mock.mock(URL_LIST, METHOD_LIST, req => {
  // 获取参数
  const { current = 1, size = 20, name, addressType } = url.parse(req.url, true).query // url参数
  // 获取非分页参数
  const params = { name, addressType }
  // 获取不为空参数
  const paramsNoEmpty = Object.entries(params).reduce((acc, currentValue) => {
    acc.push({ key: currentValue[0], value: currentValue[1] })
    return acc
  }, [])
  // 根据参数查询过滤列表-非分页参数
  const list = MOCK_DATA.list.filter(item => {
    let len = paramsNoEmpty.length - 1
    let state = true
    while (len >= 0) {
      const { key, value } = paramsNoEmpty[len]
      if (value && item[key] !== value) state = false
      len--
    }
    return state
  })
  // 列表总数量
  const total = list.length
  // 列表总页数
  const pages = Math.ceil(total / size)
  // 根据分页参数
  const records = list.filter((item, index) => (current - 1) * size <= index && index < current * size)
  // 返回数据
  const data = {
    current: Number(current),
    pages,
    total,
    records,
    searchCount: true,
    size: Number(size),
  }
  console.log(data)
  return Object.assign({}, result, { data })
})

// 获取详情信息
const URL_DETAIL = /\/demo\/detail/
const METHOD_DETAIL = 'get'

Mock.mock(URL_DETAIL, METHOD_DETAIL, req => {
  // 获取参数
  const { id } = url.parse(req.url, true).query
  // 参数处理
  if (!id) throw new Error('id is not empty!')
  // 根据ID过滤数据
  const data = MOCK_DATA.list.find(item => item.id === Number(id))
  // 客户不存在
  if (!data) throw new Error('The user is not exist!')
  // 返回数据
  return Object.assign({}, result, { data })
})

// 新增
const URL_ADD = /\/demo\/add/
const METHOD_ADD = 'post'
Mock.mock(URL_ADD, METHOD_ADD, req => {
  // 获取参数
  const { newData } = JSON.parse(req.body)
  // 参数处理
  if (!newData) throw new Error('newData is not empty!')
  // 数据合并
  const { sysCustomerContractDTO = {}, sysCustomerDTO = {}} = newData
  // 插入数据
  const ids = MOCK_DATA.list.map(item => item.id)
  const MAX = Math.max(...ids)
  const id = MAX + 1 // 当前用户的ID
  const createTime = new Date()
  const updateTime = createTime
  const user = Object.assign({ id, createTime, updateTime }, sysCustomerContractDTO, sysCustomerDTO)
  MOCK_DATA.list.unshift(user)
  // 返回数据
  return Object.assign({}, result, { data: id })
})

// 编辑
const URL_EDIT = /\/demo\/update$/
const METHOD_EDIT = 'post'
Mock.mock(URL_EDIT, METHOD_EDIT, req => {
  // 获取参数
  const { newData } = JSON.parse(req.body)
  // 参数处理
  if (!newData) throw new Error('newData is not empty!')
  const { id } = newData || {}
  if (!id) throw new Error('id is not empty!')
  // 根据ID过滤数据
  let detail = MOCK_DATA.list.find(item => item.id === Number(id))
  // 客户不存在
  if (!detail) throw new Error('The user is not exist!')
  // 数据合并
  detail = Object.assign(detail, newData)
  // 返回数据
  return Object.assign({}, result, { data: null })
})

// 删除
const URL_DELETE = /\/demo\/delete/
const METHOD_DELETE = 'post'
Mock.mock(URL_DELETE, METHOD_DELETE, req => {
  // 获取参数
  const { id } = JSON.parse(req.body)
  // 参数处理
  if (!id) throw new Error('id is not empty!')
  // 根据ID过滤数据
  const index = MOCK_DATA.list.findIndex(item => item.id === Number(id))
  // 删除用户数据
  MOCK_DATA.list.splice(index, 1)
  // 返回数据
  return Object.assign({}, result, { data: null })
})

