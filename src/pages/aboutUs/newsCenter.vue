<template>
  <q-page class="flex column online-order">
    <div
      class="flex column justify-center banner"
      :style="`background-size: cover;background-position: center;background-image: url(${bannerImg})`"
    >
      <div class="title">{{ $t('关于我们') }}</div>
      <div class="slogan">{{ $t('新闻中心') }}</div>
    </div>
    <div class="content">
      <div class="flex justify-between message-wrap">
          <!-- <div class="select-input">
            <el-select v-model="value" @change="selectChange">
              <el-option
                v-for="(item, index) in yearOptions"
                :key="index"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </div> -->
          <el-tabs v-model="infoType" class="tabs-centent" @tab-click="handleClick" type="border-card">
            <template v-for="(item, index) in newsList">
              <el-tab-pane :label="item.label" :name="item.name" :key="index">
                <template v-if="paiList && paiList.length > 0">
                  <div class="newslist-del flex" v-if="paiList && paiList.length > 0">
                    <div class="detail-item flex-sb" style="cursor: pointer;" @click="checkDetails(v)" v-for="(v, k) in paiList" :key="k">
                      <div class="del-img-box">
                          <img class="del-img" :src="v.coverImg" />
                      </div>
                      <div class="desc">
                          {{v.title.length > 34 ? v.title.substring(0,34) + '...' : v.title}}
                      </div>
                      <div class="del-time">{{v.releaseTime | setTime}}</div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="no-data">
                    <img style="width:240px;height:240px" src="~assets/images/network/emptyResult1.png" alt="" srcset="">
                      <div class="no-data-text" v-if="isSearch">
                        {{ isSearch ? $t('pageNetwork.暂无查询数据') : '' }}
                      </div>
                  </div>
                </template>
              </el-tab-pane>
            </template>
          </el-tabs>
          <div class="page-center">
            <el-pagination
              :background="true"
              @current-change="handleCurrentChange"
              :current-page.sync="page"
              :page-size="size"
              layout="prev, pager, next, jumper"
              :total="total">
            </el-pagination>
          </div>
      </div>
    </div>
  </q-page>
</template>
<script>
const bannerImg = require('../../assets/img/banner/about-banner.jpg')
import { mapState } from 'vuex'
import _ from 'lodash'
import { order } from '../../api'
import { value } from 'dom7';
export default {
  meta () {
    return {
      title: this.$t("关于我们")
    }
  },
  components: {

  },
  filters: {
    setTime(val) {
      if(val) {
        val = val.split(' ')[0]
        return val
      }
    }
  },
  data () {
    return {
      paiList: [], // 公司新闻
      newsList: [
        {
          name: '0',
          label: this.$t('公司新闻')
        },
        // {
        //   name: '1',
        //   label: this.$t('服务公告')
        // },
        // {
        //   name: '2',
        //   label: this.$t('内部消息')
        // },
        // {
        //   name: '3',
        //   label: this.$t('服务网点')
        // }
      ],
      yearOptions: [],
      value: this.$t('全部'),
      bannerImg,
      page: 1,
      size: 8,
      total: 0, // 总条数
      infoType: '0',
      isSearch: false
    }
  },
  watch: {
  },
  mounted () {
    const { infoType } = this.$route.query
    this.infoType = infoType || '0'
    this.getNewsList()
    this.getYear()
  },
  methods: {
    selectChange(val) { // 年份下拉选择
      this.value = val
      this.page = 1
      this.getNewsList()
    },
    async getNewsList () { // 获取新闻列表
      const params = {
        page: this.page,
        size: 8,
        infoType: this.infoType
      }
      if (this.value && this.value !== this.$t('全部')) {
        params.beginTime = `${this.value}-01-01 00:00:00`
        params.endTime =  `${this.value}-12-31 23:59:59`
      }
      const { code, data } = await order.getNewsList(params)
      this.isSearch = true
      if (code === 1) {
        this.paiList = data.records
        this.total = data.total
      }
    },
    async getYear() { // 获取年份
      const { code, data } = await order.getYear({ infoType: this.infoType })
      if (code === 1) {
        this.yearOptions = data
        this.yearOptions.unshift(this.$t('全部'))
      }
    },
    handleClick(tab, event) { // tab切换
      this.value = this.$t('全部')
      this.page = 1
      this.getNewsList()
      this.getYear()
    },
    handleCurrentChange(val) { // 页码
      this.page = val
      this.getNewsList()
    },
    checkDetails: function (data) {
      this.$router.push({ path: '/detail', query: { 'id': data.id, 'infoType': this.infoType }})
    }
  }
}
</script>

<style lang="scss" scoped>
.online-order {
  background: #f8f8f8;
  margin-top: 64px;
  .banner {
    height: 420px;
    width: 100%;
    .title,
    .slogan {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      color: #ffffff;
      &.title {
        font-size: 60px;
        font-weight: 600;
      }
      &.slogan {
        margin-top: 24px;
        font-size: 28px;
        font-weight: 400;
      }
    }
  }
  .content {
    width: 1200px;
    margin: 0 auto;
    .message-wrap {
      position: relative;
      margin: 0 auto;
      margin-top: 40px;
      width: 100%;
      max-width: 1200px;
      padding-bottom: 30px;
      background: #F8F8F8;
      .tabs-centent {
        width: 100%;
      }
      .select-input {
        position: absolute;
        z-index: 999;
        right: 0;
        top: 0;

        ::v-deep.el-input__inner {
          border: none;
          width: 90px;
          background: #f8f8f8;
        }
      }
      .tabs-centent {
        position: relative;

      }
      ::v-deep.el-tabs__header {
        margin-bottom: 10px;
      }
      ::v-deep.el-tabs--border-card {
        position: relative;
        border: none;
        box-shadow: none;
      }
      ::v-deep.el-tabs--border-card > .el-tabs__header {
        border: none;
      }
      ::v-deep.el-tabs__item {
        border: none;
        font-weight: 800;
      }
      ::v-deep.el-tabs--border-card > .el-tabs__content {
        padding: 20px;
      }

      .newslist-del {
        width: 100%;
        min-height: 500px;
        margin: 0 auto;
        flex-wrap: wrap;
        flex-direction: row;
        .detail-item {
          width: 560px;
          height: 500px;
          display: inline-block;
          flex-direction: column;
          flex-wrap: wrap;
          background: #FFFFFF;
          .del-img-box {
            width: 560px;
            height: 374px;
            overflow: hidden;
            .del-img {
              width: 560px;
              height: 374px;
              transition: all .3s;
            }
            &:hover img {
              transform: scale(1.1);
            }
            img{
              transition: .3s;
              height: 100%;
            }
          }
          .desc {
            width: 100%;
            overflow: hidden;
            word-wrap: break-word;
            white-space: normal;
            -webkit-line-clamp: 2;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            color: #2B2B2B;
            line-height: 33px;
            margin: 10px 0;
          }
          .del-time {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #949494;
            line-height: 20px;
            margin-left: 0;
            margin-bottom: 56px;
          }
        }
        .detail-item:nth-child(odd) {
          margin-right: 40px;
        }
        .del-notice {
          width: 1200px;
          display: inline-block;
          display: flex;
          flex-direction: column;
          border-bottom: 1px solid #DDDDDD;
          margin-bottom: 40px;
          cursor: pointer;
          .tit {
            font-size: 24px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #171717;
            line-height: 33px;
            margin-top: 32px;
            margin-bottom: 12px;
          }
          .tit:hover {
            color: red;
          }
          .del-desc {
            width: 1175px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #6A6A6A;
            line-height: 20px;
            overflow: hidden;
            white-space: normal;
            word-break: break-all;
            word-wrap: break-word;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
          }
          .time-del {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #6A6A6A;
            line-height: 20px;
            margin-top: 40px;
            margin-bottom: 24px;
          }
        }
      }
      .page-center {
        width: 100%;
        margin-top: 60px;
        text-align: center;
      }
      ::v-deep.number {
        background-color: #fff;
        color: #666666;
        border: 1px solid #bbb;
      }
    }
    .no-data {
      height: 100%;
      position: relative;
      min-height: 500px;
      img {
        position: absolute;
        left: 50%;
        top: 130px;
        transform: translateX(-50%);
      }
    .no-data-text {
      position: absolute;
      left: 50%;
      top: 370px;
      transform: translateX(-50%);
      text-align: center;
    }
      // display: flex;
      // align-items: center;
      // justify-content: center;
      // background: url("~assets/images/network/emptyResult1.png") no-repeat center;
      // background-size: 280px;
      .emDada {
          padding-top: 312px;
          opacity: 1;
          font-size: 16px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: center;
          color: #666666;
          line-height: 20px;
          height: 20px;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.en-us-lang,
.id-lang {
}
</style>
