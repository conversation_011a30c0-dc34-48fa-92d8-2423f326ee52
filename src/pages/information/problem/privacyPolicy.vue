<template>
  <div>
    <q-scroll-area :thumb-style="thumbStyle" style="height: 800px" ref="scrollArea">
      <div class="terms">
        <div v-html="$t('pageProblem.PrivacyPolicy')"></div>
      </div>
    </q-scroll-area>
  </div>
</template>
<script>
export default {
  meta () {
    return {
      title: this.$t('pageProblem.隐私政策')
    }
  },
  data () {
    return {
      pageData: this.$t('pageProblem.条款与协议'),
      thumbStyle: {
        right: '2px',
        borderRadius: '3px',
        backgroundColor: '#999',
        width: '8px',
        opacity: 1
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.terms {
  padding-top: 30px;
  padding-right: 24px;
  ::v-deep h5 {
    margin-bottom: 17px;
    font-size: 20px;
    line-height: 20px;
    font-weight: 500;
    color: #2b2b2b;
  }
  ::v-deep p {
    padding-bottom: 40px;
    font-weight: 400;
    color: #666;
    line-height: 28px;
  }
}
</style>