<template>
  <div>
    <el-collapse v-model="activeName" accordion>
      <el-collapse-item :name="index" v-for="(item, index) in faqData" :key="index">
        <template slot="title">
          <div class="title-text">
            {{ item.title }}
          </div>
        </template>
        <div class="content-text">
          {{ item.content }}
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script>

export default {
  meta () {
    return {
      title: this.$t("常见问题")
    }
  },
  data () {
    return {
      faqData: [
        {
          title: this.$t('pageProblem.有哪些优势'),
          content: this.$t('pageProblem.有哪些优势a')
        },
        {
          title: this.$t('pageProblem.支付系统是如何'),
          content: this.$t('pageProblem.支付系统是如何a')
        },
        {
          title: this.$t('pageProblem.面单检查如何'),
          content: this.$t('pageProblem.面单检查如何a')
        },
        {
          title: this.$t('pageProblem.怎么和什么时候收到面单'),
          content: this.$t('pageProblem.怎么和什么时候收到面单a')
        },
        {
          title: this.$t('pageProblem.提出索赔如何'),
          content: this.$t('pageProblem.提出索赔如何a')
        },
        {
          title: this.$t('pageProblem.如果客户送有危险品的包裹怎么样'),
          content: this.$t('pageProblem.如果客户送有危险品的包裹怎么样a')
        },
        {
          title: this.$t('pageProblem.报告投诉和给建议如何'),
          content: this.$t('pageProblem.报告投诉和给建议如何a')
        },
        {
          title: this.$t('pageProblem.体积重量的包装计算如何'),
          content: this.$t('pageProblem.体积重量的包装计算如何a')
        }
      ],
      slefName: 'FAQ',
      activeName: ''
    }
  },
  computed: {
    qaData () {
      if (this.slefName === 'FAQ') {
        return this.faqData
      }
      return this.baoData
    }
  },
  created () {

  },
  mounted () {
  },

  methods: {

  }
}
</script>

<style lang="scss" scoped>
.online-order {
  background: #f8f8f8;
  margin-top: 64px;
  .banner {
    height: 420px;
    width: 100%;
    .title,
    .slogan {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      color: #ffffff;
      &.title {
        font-size: 60px;
        font-weight: 600;
      }
      &.slogan {
        margin-top: 24px;
        font-size: 28px;
        font-weight: 400;
      }
    }
  }
  .message-wrap {
    margin: 0 auto;
    margin-top: 40px;
    width: 100%;
    max-width: 1200px;
    padding-bottom: 100px;
    .content-left {
      padding: 24px;
      padding-top: 0;
      width: 280px;
      height: auto;
      max-height: 560px;
      background: #ffffff;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      &-items {
        padding-top: 24px;
        border-bottom: 1px solid #eee;
        h5 {
          margin-bottom: 16px;
          font-size: 18px;
          font-weight: 500;
          color: #2b2b2b;
        }
        &-list {
          padding-bottom: 16px;
          i {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            display: block;
            background: #d8d8d8;
          }
          span {
            cursor: pointer;
            margin-left: 10px;
            font-size: 14px;
            font-weight: 400;
            color: #666;
          }
          &.active {
            span {
              color: #00b075;
            }
            i {
              background: #00b075;
            }
          }
          &:last-child {
            padding-bottom: 24px;
          }
        }
        &:last-child {
          border-bottom: none;
        }
      }
    }
    .content-right {
      padding: 0 24px;
      width: 896px;
      height: 880px;
      background: #ffffff;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      ::v-deep .el-collapse-item__header {
        height: 62px;
        line-height: 62px;
      }
      .title-text {
        font-size: 16px;
        font-weight: 400;
        color: #2b2b2b;
      }
      .content-text {
        font-size: 14px;
        font-weight: 400;
        text-align: left;
        color: #666666;
        line-height: 24px;
      }
    }
  }
  .el-collapse {
    border: none;
  }
}
</style>

<style lang="scss" scoped>
.en-us-lang,
.id-lang {
}
</style>