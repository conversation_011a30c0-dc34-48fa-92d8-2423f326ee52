/**
 * @description: 新增/编辑弹窗
 * @author: xuesong
 */
<template>
  <div>
   <q-dialog v-model="visible" content-class="data_dialog" persistent>
    <q-card>
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{isEdit? $t("personalCenter.编辑联系人"): $t("personalCenter.新增联系人") }}</div>
        <q-space />
        <i class="iconfont icon-danchuangguanbi" v-close-popup @click="hideDiaLog"></i>
      </q-card-section>
      <q-card-section class="wrap dialog_content" style="padding:0 48px;">
        <q-form ref="myForm" @submit="onSubmit" style="position: relative;">
          <q-input
            label=""
            stack-label
            dense
            no-error-icon
            v-model.trim="form.name"
            class="column mt30"
            :placeholder="$t('personalCenter.姓名')"
            maxlength="100"
            :rules="[(val) => (val && val.length > 0) || $t('personalCenter.请输入姓名')]"
          />
          <q-input
            label=""
            stack-label
            dense
            no-error-icon
            v-model.trim="form.mobile"
            maxlength="30"
            :placeholder="$t('personalCenter.手机号')"
            :rules="[(val) => (val && val.length > 0) || $t('personalCenter.请输入手机号码'), (val) => (val && !isNaN(Number(val))) || $t('personalCenter.手机号码只能为数字'), val => (val.length >= 10 &&val.length <= 13 || $t('pageOnlineOrder.手机号10-13位限制'))]"
            class="column m210"
            @keyup="form.mobile = onlyNumber(form.mobile)"
          />
          <q-input
            label=""
            readonly
            stack-label
            dense
            no-error-icon
            v-model="form.address"
            :placeholder="$t('personalCenter.城市')"
            :rules="[(val) => (val && val.length > 0) || $t('personalCenter.请选择地址')]"
            @click="showAddress = !showAddress"
            class="column m210"
          >
            <template style="line-height: -1px">
              <div>
                <i v-if="!showAddress" style="color:#999999" class="iconfont icon-daohang_jiantouxiala"></i>
                <i v-else style="color:#999999" class="iconfont icon-daohang_jiantouxiala q-icon notranslate q-btn-dropdown__arrow rotate-180"></i>
              </div>
            </template>
          </q-input>
          <AddressSelect
            v-show="showAddress"
            :visible.sync="showAddress"
            @closeAddressDrupdown="getAddress"
            >
          </AddressSelect>
          <q-input
            label=""
            stack-label
            dense
            no-error-icon
            v-model.trim="form.detailedAddress"
            :placeholder="$t('personalCenter.详细地址')"
            maxlength="255"
            :rules="[(val) => (val && val.length > 0) || $t('personalCenter.请输入详细地址')]"
            class="column m210"
          />
          <q-input
            label=""
            stack-label
            dense
            no-error-icon
            v-model.trim="form.postalCode"
            @keyup="form.postalCode = onlyNumber(form.postalCode)"
            :placeholder="$t('personalCenter.邮政编码')"
            maxlength="10"
            :rules="[(val) => (val && val.length > 0) || $t('personalCenter.请输入邮区编码'), (val) => (val && !isNaN(Number(val))) || $t('personalCenter.邮编只能为数字')]"
            class="column m210"
          />
          <div class="flex flex-center btn-div">
              <q-btn
                unelevated
                size="18px"
                padding="9px 102px"
                :label="isEdit ? $t('personalCenter.确认修改') : $t('personalCenter.保存') "
                color="primary"
                type="submit"
                :loading="loading"
              />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</div>

</template>
<script>
import { pensonCenter } from '../../../api/index'
import AddressSelect from 'components/AddressSelect.vue'
export default {
  meta() {
    return {
      // title: this.$t('通讯录弹窗')
    }
  },
  name:'AddAddressDialog',
  components:{ AddressSelect },
  data(){
    return{
      visible:this.dialogVisible, // 新增/编辑弹窗
      isEdit: false,// 新增还是编辑弹窗
      form:{},// 编辑弹窗数据
      showAddress:false, // 是否展示修改地址组件
      loading: false,
    }
  },
  props:{
    dialogVisible:{
      type: Boolean,
      default: false
    },
    dialogOptions:{
      type: Object,
      require:true
    },
  },
  watch:{
    dialogVisible:{
      handler(val){
        console.log(val);
       this.visible = val
      },
      immediate:true
    },
    dialogOptions:{
      handler(newVal){
        if(newVal){
          const { isEdit, detailData } = newVal
          this.isEdit = isEdit
          this.form ={}
          if(isEdit){
            this.form = detailData
            this.form.address= (detailData.provinceName ? detailData.provinceName + '/' : '' )+ (detailData.cityName ? detailData.cityName + '/' : '')+ (detailData.areaName || '')
          }
        }
      },
      deep: true,
      immediate:true
    }
  },
  methods:{
    onlyNumber(item) {
      if (item) {
        const item1 = item.replace(/[^\d.]/g, '')
        const item2 = item1.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
        return item2
      }
    },
    // 保存
    onSubmit(){
      this.isEdit? this.handleEdit() : this.handleSave()
    },
    // 关闭弹窗
    hideDiaLog () {
      this.visible = false
      this.$emit('update:dialogVisible', false)
    },
    // 编辑
    async handleEdit(){
       try {
        const res = await pensonCenter.getUpdateAddress(this.form)
        if (res && res.succ) {
          this.hideDiaLog()
          this.$emit('refresh')
        }else{
          this.loading = false
          this.$q.notify({
            position: 'center',
            icon:'iconfont icon-icon-cuowutishi',
            message: res.msg
          })
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 新增
    async handleSave(){
      this.loading = true
      try {
        const res = await pensonCenter.getAddAddress({...this.form ,isDefault: 2})
        if (res && res.succ) {
          this.loading = false
          this.hideDiaLog()
          this.$emit('refresh')
        }else{
          this.loading = false
          this.$q.notify({
            position: 'center',
            icon:'iconfont icon-icon-cuowutishi',
            message: res.msg
          })
        }
      } catch (error) {
        this.loading = false
        console.log(error);
      }
    },
    // 省市区回传
    getAddress(addressVisible,addressObj){
      console.log('省市区组件回传数据',addressVisible,addressObj);
      this.showAddress = addressVisible
      this.form.address= (addressObj.provinceName ? addressObj.provinceName + '/' : '' )+ (addressObj.cityName ? addressObj.cityName + '/' : '')+ addressObj.areaName
      this.form= Object.assign( this.form , addressObj)
    }
  }
}
</script>
<style lang="scss" scoped>
.data_dialog{
  position: relative;
  .q-card{
    padding: 24px 0 36px;
    width: 560px;
    max-width: 80vw;
    min-height:600px;
    .q-pb-none {
      padding: 0 48px 23px !important;
      border-bottom: 1px solid #ddd;
    }
    .text-h6 {
      font-size: 24px;
      font-weight: 600;
      color: #2b2b2b;
    }
    .q-card__section--vert {
      padding: 0;
    }
  }
  .dialog_warp{
    width: 560px;
    min-height:598px;
    .dialog_title{
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 24px;
        font-weight: 600;
        color: #2b2b2b;
        height:78px;
        border-bottom:1px solid #dddddd;
        padding:0 48px;
    }
    .dialog_content{
      padding:0 48px;
      margin-top:30px;
      .q-form{
        ::v-deep .q-field__control{
          &::before{
            border-bottom: 1px solid #eeeeee !important;
          }
          &:hover{
            border-color: #eeeeee !important;
          }
          &:focus{
            border-bottom: 1px solid #eeeeee !important;
          }
          &::after{
            transition: none;
            height: 1px;
            color: #eeeeee !important;
          }
        }
      }
    }
    .dialog_btn{
        width: 240px;
        height: 48px;
        line-height: 48px;
        text-align: center;
        opacity: 1;
        background: #00b075;
        border-radius: 4px;
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        margin-top: 20px;
    }
  }
  .btn-div {
    margin: 26px 0 6px;
  }
  .icon-danchuangguanbi {
    color: #999;
    cursor: pointer;
  }
}
::v-deep .q-placeholder{
  color: #999;
}
::v-deep .q-field__control{
  &::before{
    border-bottom: 1px solid #eeeeee !important;
    &:hover{
      border-color: red !important;
      color: #ddd !important;
    }
  }
  &:hover{
    border-bottom: 1px solid #ddd !important;
  }
  &:focus{
    border-bottom: 1px solid #ddd !important;
  }
  &::after{
    transition: none;
    height: 1px;
    color: #ddd !important;
  }
}
::v-deep .text-negative{
  color: #ddd !important;
}
.m210{
  margin-top:20px;
}
.mt30{
  margin-top:30px;
}
.q-field--standard.q-field--readonly ::v-deep .q-field__control:before {
    border-bottom-style: solid !important;
}
</style>
