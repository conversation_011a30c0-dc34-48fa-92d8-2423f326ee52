/**
 * @description: 我的订单
 * @author: xuesong
 */
<template>
  <div class="my_orders_list">
    <q-tabs
      v-model="defaultTab"
      align="left"
      indicator-color="primary"
      >
      <q-tab name="undone" :ripple="false" :label="$t('personalCenter.未完成')" />
      <q-tab name="done" :ripple="false" :label="$t('personalCenter.已完成')" />
    </q-tabs>
    <div v-if="!isEmpty" class="content_warp">
      <div class="content_scroll">
        <div class="content_list" v-for="item in orderList" :key="item.id">
          <div class="content_item">
            <div class="item_title">
              <div class="title_left">{{$t("personalCenter.运单号")+ '：'}}<span>{{ item.waybillNo || '--' }}</span></div>
              <div class="title_right">{{item.statusCode || '--' | waybillStatus(that)}}</div>
            </div>
            <div  class="item_contnet flex justify-between">
              <div class="sender">
                <div class="sender_label">{{$t("personalCenter.寄件人")}}</div>
                <div class="sender_name">{{item.senderName || '--'}}</div>
                <div class="sender_time">{{item.statusTime || '--'}}</div>
              </div>
              <div class="sender">
                <div class="sender_label">{{$t("personalCenter.收件人")}}</div>
                <div class="sender_name">{{item.receiverName || '--'}}</div>
                <div class="sender_time" v-if="item.waybillNo">{{item.waybillNo || '--'}} &nbsp;&nbsp; {{item.subWaybillNum ? item.subWaybillNum + $t("personalCenter.个子运单") : ''}}</div>
              </div>
              <div class="item_btn items-center">
                <div class="btn" @click="toReorder(item.id)" v-if="item.statusCode === 8">{{$t("personalCenter.重新下单")}}</div>
                <div class="btn" @click="searchOrder(item.waybillNo)" v-if="item.statusCode === 5|| item.statusCode === 2 || item.  statusCode === 3 || item.statusCode === 4">{{$t("personalCenter.查件")}}</div>
                <div class="btn" style="margin-left:10px;" v-if="item.statusCode === 1" @click="delVisible(item.id)">{{$t ("personalCenter.取消")}}</div>
                <div class="btn" style="margin-left:10px;" @click="toDetail(item.id)">{{$t("personalCenter.详情")}}</div>
              </div>
            </div>
          </div>
      </div>
      </div>

      <!-- 翻页 -->
      <div class="pagination flex center" style="padding:24px 0" v-show="total">
        <Pagination @pageChanged="handlePageChanged" :pagination='pageMsg' :total="total" />
      </div>
    </div>
    <div v-else class="no-data">
      <div class="emDada">{{ $t('personalCenter.暂无订单') }}</div>
    </div>
    <!-- 取消订单弹窗 -->
    <q-dialog v-model="dialog" persistent >
      <q-card class="cancel-card"  style="width: 480px; max-width: 80vw;">
        <q-card-section class="row items-center">
          <span class="q-pt-none" style="margin: 36px auto;font-size: 18px;font-weight: 600;text-align: center;color: #2b2b2b;">{{ $t('personalCenter.是否取消该订单') }}?</span>
        </q-card-section>
        <q-card-actions class="btns-bottom" align="center" style="margin-bottom:36px">
          <q-btn  outline color=""  :label="$t('personalCenter.取消')"   size="18px" padding="9px 54px" v-close-popup />
          <q-btn style="margin-left:24px" :label="$t('personalCenter.确认')" size="18px" padding="9px 54px" color="primary" @click="cancelOrder(id)" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>

</template>
<script>
import Pagination from 'components/Pagination.vue'
import { pensonCenter } from '../../../api/index'
export default {
   meta () {
    return {
      title: this.$t("pageNetwork.我的订单")
    }
  },
  name: "myOrdersList",
  components:{ Pagination },
  data(){
    return{
      isEmpty: false, // 缺省
      defaultTab: 'undone', //tabs
      dialog: false, // 展示取消订单弹窗
      orderList:[], // 订单列表
      total:0, // 总页数
      pageMsg:{
        size:5,
        current:1,
      },
      id:0,
      that:this,
    }
  },
  watch:{
    'defaultTab':{
      handler(newVal){
        if(newVal){
          this.defaultTab = newVal
          this.pageMsg.current = 1
          this.getDataPage()
        }
      },
      immediate:true
    }
  },
  methods:{
    // 获取订单列表
    async getDataPage(){
      let params = {}
      // isDone订单是否完成 0未完成，1已完成
      this.defaultTab === 'done'? params = Object.assign(this.pageMsg,{isDone:1}):params = Object.assign(this.pageMsg,{isDone:0})
      try {
        const res = await pensonCenter.orderList(params)
        if (res && res.succ) {
          if(res.data.records.length === 0){
            this.isEmpty = true
          }else{
            this.isEmpty =false
          }
          console.log('已完成/未完成',this.orderList);
          this.orderList= res.data.records
          this.total = res.data.total

        }
      } catch (error) {
        console.log(error);
      }
    },
     // 进入取消订单弹窗
    delVisible(id){
      this.dialog = true
      this.id = id
    },
    // 取消订单
    async cancelOrder(id){
      try {
        const res = await pensonCenter.cancelOrder({orderIds:[id]})
        if (res && res.succ) {
          this.$q.notify({
            position: 'center',
            timeout: 1000,
            message: this.$t('取消成功')
          })
          this.pageMsg.current = 1
          this.getDataPage()
          console.log('取消订单成功')
        } else {
          this.$q.notify({
            position: 'center',
            icon:'iconfont icon-icon-cuowutishi',
            message: this.$t('personalCenter.取消失败')
          })
          this.getDataPage()
        }
      } catch (error) {
        this.$q.notify({
            position: 'center',
            icon:'iconfont icon-icon-cuowutishi',
            message: error
          })
      }
    },
    // 详情
    toDetail(id){
      this.$router.push({
        path:'/personalCenter/orderDetail',
        query: { id }
      })
    },
    // 重新下单
    async toReorder(id){
      const _this = this
      try {
        const res = await pensonCenter.detailOrder({id})
        if (res && res.succ) {
          if(res.data){
            _this.$router.push({
              name:'onlineOrder',
              params: {data:res.data}
            })
          }
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 查件
    searchOrder(waybillNo){
      // type: 0 为快件查询 1为网点查询
      this.$router.push({
        path: '/networkQuery',
        query: {
          type: 0,
          waybillNo: waybillNo
        }
      })
    },
     // 翻页
    handlePageChanged(params){
      console.log('handlePageChanged', params);
      this.pageMsg = params
      this.getDataPage ()
    },
  },
  filters:{
    waybillStatus(value,that){
      console.log(value, that)
      const waybillStatus =[
        {value:1, label:that.$t("personalCenter.待上门")},
        {value:2, label:that.$t('personalCenter.已揽件')},
        {value:3, label:that.$t('personalCenter.运送中')},
        {value:4, label:that.$t('personalCenter.派件中')},
        {value:5, label:that.$t('personalCenter.已签收')},
        // {value:6, label:'已退件'},
        // {value:7, label:'问题件'},
        {value:8, label:that.$t('personalCenter.已取消')},
        // {value:9, label:'退件中'},
        // {value:10, label:'已拒收'},
      ]
      const item =  waybillStatus.filter( i => value === i.value)[0]
      console.log(item);
      return item.label
    }

  }
}

</script>
<style scoped lang="scss">
.my_orders_list{
  height:97%;
  position: relative;
  .q-tabs{
    height: 72px;
    border-bottom: 1px solid #eeeeee;
    border-radius: 4px 4px 0px 0px;
    padding-left:40px;
    .q-tab{
      padding: 0;
      transition:none;
      &:last-child{
        margin-left:60px;
      }
      &:active{
          transition: none;
          transform: none;
          background-color: #fff;
        }
      ::v-deep .q-tab__content{
        min-width: 60px;
        .q-tab__label{
          font-size: 18px !important;
        }
        &:active{
          transition: none;
          transform: none;
          background-color: #fff;
        }
      }
      ::v-deep .q-tab__indicator{
          height: 3px;
      }
    }
  }
  .no-data {
    height: 480px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url("~assets/img/personalCenter/emptyorders.png") no-repeat center;
    .emDada {
        padding-top: 312px;
        opacity: 1;
        font-size: 16px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: center;
        color: #666666;
        line-height: 20px;
        height: 20px;
    }
  }
  .content_warp{
    .content_scroll{
      min-height: 480px;
      .content_list{
        .content_item{
          height: auto;
          border: 1px solid #eeeeee;
          margin: 24px 24px 0px;
          .item_title{
            height:40px;
            line-height: 40px;
            display: flex;
            justify-content: space-between;
            background: #f8f8f8;
            padding:0 24px;
            .title_right{
              font-size: 14px;
              font-weight: 500;
              text-align: right;
              color: #666666;
            }
            .title_left{
              font-size: 12px;
              font-weight: 400;
              text-align: left;
              color: #666666;
            }
          }
          .item_contnet{
            max-width: 844px;
            padding:20px 24px;
            .sender{
              flex: 1;
              .sender_label{
                font-size: 14px;
                font-weight: 400;
                text-align: left;
                color: #999999;
              }
              .sender_name{
                margin-top:4px;
                font-size: 18px;
                font-weight: 400;
                text-align: left;
                color: #2b2b2b;
                line-height: 25px;
              }
              .sender_time{
                margin-top:20px;
                font-size: 14px;
                font-weight: 400;
                text-align: left;
                color: #999999;
              }
            }
            .item_btn{
              display: flex;
              align-items: center;
              .btn{
                &:hover{
                  cursor:pointer;
                }
                min-width: 68px;
                height: 30px;
                font-size: 12px;
                border: 1px solid #dddddd;
                border-radius: 2px;
                text-align: center;
                color: #2b2b2b;
                line-height: 30px;
                padding: 0 10px;
              }
            }
          }
        }
      }
    }
  }

  .cancel_dialog{
    .dialog_content{
      margin: 0 auto;
      font-size: 18px;
      font-weight: 600;
      text-align: center;
      color: #2b2b2b;
    }
  }
}
</style>

