/**
 * @description: 订单详情
 * @author: xuesong
 */
<template>
  <q-page class="order_detail">
    <div class="headline flex items-center">
      <div class="headline_btn" @click="lastStep"><i class="iconfont icon-danchuangfanhui"></i></div>
      <div class="headline_txt">{{ $t("personalCenter.订单详情") }}<span v-if="orderDetail.waybillId">({{orderDetail.waybillId || '--'}})</span></div>
    </div>
    <div class="order_detail_content">
      <div class="dress_item">
        <div class="dress_title">{{ $t("personalCenter.寄件人信息") }}</div>
        <div class="dress_content row flex">
          <div class="msg_label col-6">{{ $t("personalCenter.姓名") }}：<span class="msg_value">{{orderDetail.senderName ||'--'}}</span></div>
          <div class="msg_label col-6 flex">{{ $t("personalCenter.手机") }}(08xxx)：<span class="msg_value">{{orderDetail.senderMobilePhone || '--'}}</span></div>
          <div class="msg_label col-6">{{ $t("personalCenter.城市") }}：<span class="msg_value">{{orderDetail.senderProvinceName || '--' +'-'+orderDetail.senderCityName || '--' +'-'+orderDetail.senderAreaName || '--'}}</span></div>
          <div class="msg_label col-6">{{ $t("personalCenter.邮政编码") }}：<span class="msg_value">{{orderDetail.senderPostalCode || '--' }}</span></div>
          <div class="msg_label col-12">{{ $t("personalCenter.详细地址") }}：<span class="msg_value">{{orderDetail.senderDetailedAddress|| '--'}}</span></div>
        </div>
      </div>
      <div class="dress_item">
        <div class="dress_title">{{ $t("personalCenter.收件人信息") }}</div>
        <div class="dress_content row flex">
            <div class=" msg_label col-6 flex">{{ $t("personalCenter.姓名") }}：<span class="msg_value">{{orderDetail.receiverName|| '--'}}</span></div>
            <div class="msg_label col-6 flex">{{ $t("personalCenter.手机") }}(08xxx)：<span class="msg_value">{{orderDetail.receiverMobilePhone|| '--'}}</span></div>
            <div class="msg_label col-6 flex">{{ $t("personalCenter.城市") }}：<span class="msg_value">{{orderDetail.receiverProvinceName || '--' +'-'+orderDetail.receiverCityName || '--'+'-'+orderDetail.receiverAreaName || '--'}}</span></div>
            <div class="msg_label col-6 flex">{{ $t("personalCenter.邮政编码") }}：<span class="msg_value">{{orderDetail.receiverPostalCode|| '--'}}</span></div>
            <div class="msg_label col-12 flex absoluteitems-center">{{ $t("personalCenter.详细地址") }}：<span class="msg_value">{{orderDetail.receiverDetailedAddress|| '--'}}</span></div>
        </div>
      </div>
      <div class="dress_item">
        <div class="dress_title">{{ $t("personalCenter.包裹信息") }}</div>
        <div class="dress_content row">
          <div class="msg_label col-6 flex">{{ $t("personalCenter.订单号") }}：<span class="msg_value">{{orderDetail.id || '--'}}</span></div>
          <div class="msg_label col-6 flex">{{ $t("personalCenter.数量") }}：<span class="msg_value">{{orderDetail.packageNumber || '--'}} </span></div>
          <div class="msg_label col-6 flex">{{ $t("personalCenter.产品类型") }}：<span class="msg_value">{{orderDetail.expressTypeName || '--'}}</span></div>
          <div class="msg_label col-6 flex">{{ $t("personalCenter.重量") }}：<span class="msg_value">{{orderDetail.packageTotalWeight || '--'}}</span></div>
          <div class="msg_label col-12 flex">{{ $t("personalCenter.备注") }}：<span class="msg_value">{{orderDetail.remarks || '--'}}</span></div>
        </div>
      </div>
    </div>
  </q-page>
</template>
<script>
import {pensonCenter} from '../../../api/index'
export default {
  meta() {
    return {
      title: this.$t('订单详情')
    }
  },
  name: "orderDetail",
  data(){
    return{
      orderDetail: {},// 详情数据
    }
  },
  created(){
    console.log('created',this.$route.query.id);
    this.getDetail();
  },
  methods:{
    // 获取订单详情
    async getDetail () {
      try {
        const res = await pensonCenter.detailOrder({id:this.$route.query.id})
        if (res && res.succ) {
          console.log('订单详情',this.orderDetail);
          this.orderDetail= res.data
        }else{

        }
      } catch (error) {
        console.log(error);
      }
    },
    lastStep(){
      this.$router.go(-1)
    }
  }
}

</script>
<style scoped lang="scss">
.order_detail{
  max-width: 890px;
  height:97%;
  position: relative;
  .headline{
    margin:24px 0 10px;
    padding:0 24px;
    display: flex;
    .headline_btn{
      margin-right:16px;
      i{
        font-size:24px;
        color:#999999;
      }
    }
    .headline_txt{
      font-size: 18px;
      font-weight: 700;
      color: #2b2b2b;
    }
  }
  .no-data {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url("~assets/images/network/emptyResult1.png") no-repeat center;
    .emDada {
        padding-top: 312px;
        opacity: 1;
        font-size: 16px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: center;
        color: #666666;
        line-height: 20px;
        height: 20px;
    }
  }
  .order_detail_content{
    height: 88%;
    overflow: auto;
    ::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }
    ::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px #9b9ba3;
      background: rgba(152, 155, 163, 0.5);
    }
    .dress_item{
      height: auto;
      border: 1px solid #eeeeee;
      margin: 18px 24px 0px;
      .dress_title{
        height:40px;
        line-height: 40px;
        display: flex;
        justify-content: space-between;
        background: #f8f8f8;
        padding:0 24px;
        .title_right{
          i{
            font-size: 20px;
            &:last-child{
              margin-left: 25px;
            }
          }
        }
        .title_left{
          display: flex;
          align-items: center;
          font-size: 14px;
          font-weight: 500;
          text-align: left;
          color: #2b2b2b;
          .default_address_btn{
            font-size: 12px;
            border:1px solid $primary;
            height: 20px;
            line-height: 19px;
            width: 60px;
            text-align: center;
            margin-left: 10px;
            color: $primary;
          }
        }

      }
      .dress_content{
        display:flex;
        justify-content: space-between;
        padding: 15px 24px;
        .msg_label{
            font-size: 14px;
            font-weight: 400;
            color: #999999;
            padding: 8px 0;
        }
        .msg_value{
            font-size: 14px;
            font-weight: 400;
            color: #2b2b2b;;
            &:not(:first-child){
               margin-top:12px;
            }
        }
      }
    }
  }
  .pagination{
    width: 100%;
    position: absolute;
    bottom: 0px;
  }
  .del_dialog_wrap{
      width: 480px;
      height:202px;
      padding: 25px 35px;
      .dialog_content{
        margin: 0 auto;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        color: #2b2b2b;
      }
      .del_dialog_cancelbtn{
        width: 144px;
        height: 48px;
        opacity: 1;
        background: #ffffff;
        border: 1px solid #999999;
        border-radius: 4px;
      }
      .del_dialog_confirmbtn{
        width: 144px;
        height: 48px;
        opacity: 1;
        background: #00b075;
        border-radius: 4px;
      }
  }
  .dialog_content{
    margin: 0 auto;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    color: #2b2b2b;
  }
}
</style>
