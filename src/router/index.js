import Vue from 'vue';
import store from '@store';
import Router from 'vue-router';
import { INTERRUPT } from '@public/http/config'
// import { Message } from 'element-ui';
import { lang } from '@public/utils/filters.js';
import NProgress from 'nprogress'; // progress js
// import 'nprogress/nprogress.css'; // progress css
// 不显示右边的 loading
NProgress.configure({ showSpinner: false })
const vm = new Vue()
Vue.use(Router);
const files = require.context('./modules/', true, /\.js$/)
// 系统路由
let routesMap = []
const isProd = process.env.NODE_ENV !== 'development'
files.keys().forEach(key => {
  // 非开发环境忽略demo相关路由
  if (isProd && key === './demo.js') return
  // 其他路由
  routesMap = routesMap.concat(files(key).default)
})
console.log('basic-data routesMap', routesMap)
const router = new Router({
  mode: 'history',
  routes: routesMap
});
// router before hook
router.beforeEach(async (to, from, next) => {
  console.log('aaaa3basic-data-app router beforeEach', to, from, next)
  //  如果路由不存在则通知gateway处理
  // if (to.matched.length === 0) {
  //   next(false)
  // }
  NProgress.start()
  const { httpInstanceList } = store.state.base
  if (httpInstanceList.length) {
    for (const cancel of httpInstanceList) {
      if (from.name) cancel(INTERRUPT + `|${from.name}` + `|${from.path}`)
    }
    store.state.base.httpInstanceList = []
  }
  const { routeAuthList } = store.state.base
  // token存在
  if (store.getters.token && routeAuthList) {
    // 获取路由权限列表
    const pos = routeAuthList.indexOf(to.name) > -1
    //  有systemName代表不是白名单的
    const systemName = to.path.split('/')[2]
    //  没权限且是本系统的
    if (to.name !== 'redirect' && !pos && systemName === process.env.VUE_APP_SYS_NAME) {
      vm.$message.warning(lang('没有访问该页面权限'))
      next({
        redirect: true,
        name: process.env.VUE_APP_SYS_NAME
      })
    } else {
      next()
    }
  } else {
    next()
  }
});
// router after hook
router.afterEach((to, from) => {
  window.scrollTo(0, 0);
  NProgress.done();
  const systemName = to.path.split('/')[2]
  if (systemName === process.env.VUE_APP_SYS_NAME) {
    console.log('aaaa1gateway tagBar send', to)
    //  通知gateway处理缓存
    window.RXGW.subject.next({
      target: ['gateway'],
      type: 'tagBar',
      data: to
    });
    if (store.getters.token) {
      const VUE_APP_SYS_NAME = process.env.VUE_APP_SYS_NAME
      try {
        // 获取参数
        const params = window.$Bury.util.handleBuryParams({ to, from, store, systemName: VUE_APP_SYS_NAME })
        // 调用页面埋点
        window.$Bury.buryPage(params)
      } catch (error) {
        console.error(`埋点系统-${VUE_APP_SYS_NAME}:error`, error)
      }
    }
  }
});

window.routerList[process.env.VUE_APP_SYS_NAME] = router
export default window.routerList[process.env.VUE_APP_SYS_NAME];

