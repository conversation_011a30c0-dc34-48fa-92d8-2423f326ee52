// 社区管理路由
const routes = [
  {
    path: '/LearningCenter',
    name: 'LearningCenter',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/LearningCenter/Index.vue'),
    meta: { title: '学习中心' }
  },
  {
    path: '/LearnCourse',
    name: 'LearnCourse',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/LearningCenter/LearnCourse.vue'),
    meta: { title: '课程详情' }
  },
  {
    path: '/LCExam',
    name: 'LCExam',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/LearningCenter/Exam.vue'),
    meta: { title: '在线考试' }
  },
  {
    path: '/ExamResult',
    name: 'ExamResult',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/LearningCenter/ExamResult.vue'),
    meta: { title: '查看成绩' }
  },
  {
    path: '/ManageExam',
    name: 'ManageExa<PERSON>',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageExam/index.vue'),
    meta: { title: '考试管理' }
  },
  {
    path: '/ManageExamAdd',
    name: 'ManageExamAdd',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageExam/BindExamUser.vue'),
    meta: { title: '考试管理新增' }
  },
  {
    path: '/ManageCourse',
    name: 'ManageCourse',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageCourse/Index.vue'),
    meta: { title: '课程管理' }
  },
  {
    path: `/ManageCourseAdd`,
    name: 'ManageCourseAdd',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageCourse/CourseAdd.vue'),
    meta: { title: '新增课程' }
  },
  {
    path: '/ManageCourseEdit/:id',
    name: 'ManageCourseEdit',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageCourse/CourseEdit.vue'),
    meta: { title: '编辑课程' }
  },
  {
    path: '/ManageCourseView',
    name: 'ManageCourseView',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageCourse/CourseView.vue'),
    meta: { title: '查看课程' }
  },

  {
    path: '/ManageScore',
    name: 'ManageScore',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageScore/Index.vue'),
    meta: { title: '成绩管理' }
  },
  {
    path: '/ManageScoreViewScore',
    name: 'ManageScoreViewScore',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageScore/ViewScore.vue'),
    meta: { title: '预览成绩' }
  },
  {
    path: '/ManageTests',
    name: 'ManageTests',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageTests/Index.vue'),
    meta: { title: '试卷管理' }
  },
  {
    path: '/ManageTestsAdd',
    name: 'ManageTestsAdd',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageTests/ManageTestsAdd.vue'),
    meta: { title: '试卷新增' }
  },
  {
    path: '/ManageTestsEdit/:id',
    name: 'ManageTestsEdit',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageTests/ManageTestsEdit.vue'),
    meta: { title: '试卷编辑' }
  },
  {
    path: '/ManageTestsView/:id',
    name: 'ManageTestsView',
    component: () => import(/* webpackChunkName: "JTClassRoom" */ '@views/JTClassRoom/ManageTests/ManageTestsView.vue'),
    meta: { title: '试卷查看' }
  },
  {
    path: '/',
    name: process.env.VUE_APP_SYS_NAME,
    component: () => import(/* webpackChunkName: "community" */ '@views/Index.vue'),
    meta: { title: '首页', keepAlive: true, affix: true }
  },
  {
    path: '/redirect/:path*',
    name: 'redirect',
    component: () => import(/* webpackChunkName: "community" */ '@layout/Redirect.vue'),
    meta: { title: '', keepAlive: false, affix: true } // 刷新空白页
  },
  {
    path: '/issue',
    name: 'issue',
    component: () => import(/* webpackChunkName: "community" */ '@views/Issue.vue'),
    meta: { title: '提交问题', keepAlive: true, affix: true }
  },
  {
    path: '/knowledgeFile',
    name: 'knowledgeFile',
    component: () => import(/* webpackChunkName: "community" */ '@views/KnowledgeStore/KnowledgeFile.vue'),
    meta: { title: '知识库文档', keepAlive: true, affix: true }
  },
  {
    path: '/knowledgeFileDetail',
    name: 'knowledgeFileDetail',
    props: true,
    component: () => import(/* webpackChunkName: "organization" */ '@views/KnowledgeStore/KnowledgeFileDetail.vue'),
    meta: { title: '知识库详情' }
  },
  {
    path: '/KnowledgeFileSearch',
    name: 'knowledgeFileSearch',
    props: true,
    component: () => import(/* webpackChunkName: "organization" */ '@views/KnowledgeStore/KnowledgeFileSearch.vue'),
    meta: { title: '知识库文档搜索' }
  },
  {
    path: '/myFavorite',
    name: 'myFavorite',
    component: () => import(/* webpackChunkName: "community" */ '@views/KnowledgeStore/MyFavorite.vue'),
    meta: { title: '我的收藏', affix: true }
  },
  {
    path: '/moduleAdmin',
    name: 'moduleAdmin',
    component: () => import(/* webpackChunkName: "community" */ '@views/KBM/ModuleAdmin.vue'),
    meta: { title: '模块管理', keepAlive: true, affix: true }
  },
  {
    path: '/documentManagement',
    name: 'documentManagement',
    component: () => import(/* webpackChunkName: "community" */ '@views/KBM/DocumentManagement.vue'),
    meta: { title: '文档管理', keepAlive: true, affix: true }
  },
  {
    path: '/documentManagementAdd',
    name: 'documentManagementAdd',
    component: () => import(/* webpackChunkName: "setting" */ '@views/KBM/DocumentManagementAdd.vue'),
    meta: { title: '新建文档' }
  },
  {
    path: '/documentManagementEdit/:id',
    name: 'documentManagementEdit',
    component: () => import(/* webpackChunkName: "setting" */ '@views/KBM/DocumentManagementEdit.vue'),
    meta: { title: '修改文档' }
  },
  {
    path: '/documentReport',
    name: 'documentReport',
    component: () => import(/* webpackChunkName: "setting" */ '@views/KBM/DocumentReport.vue'),
    meta: { title: '文档报表', keepAlive: true, affix: true }
  },

  {
    path: '/catalogDocuManagement',
    name: 'catalogDocuManagement',
    component: () => import(/* webpackChunkName: "community" */ '@views/KBM/CatalogDocuManagement.vue'),
    meta: { title: '目录文档管理', affix: true }
  },
  {
    path: '/commentAdmin',
    name: 'commentAdmin',
    component: () => import(/* webpackChunkName: "community" */ '@views/PublicManagement/CommentAdmin.vue'),
    meta: { title: '评论管理', keepAlive: true, affix: true }
  },
  {
    path: '/sensitiveWords',
    name: 'sensitiveWords',
    component: () => import(/* webpackChunkName: "community" */ '@views/PublicManagement/SensitiveWords.vue'),
    meta: { title: '敏感字管理', keepAlive: true, affix: true }
  },
  {
    path: '/agencyArea',
    name: 'agencyArea',
    component: () => import(/* webpackChunkName: "community" */ '@views/PublicManagement/AgencyArea.vue'),
    meta: { title: '阅读量管理', keepAlive: true, affix: true }
  },
  {
    path: '/questionAndAnswer',
    name: 'questionAndAnswer',
    component: () => import(/* webpackChunkName: "community" */ '@views/Managemnt/QuestionAndAnswer.vue'),
    meta: { title: '试题管理', keepAlive: true }
  },
  {
    path: '/questionManage',
    name: 'questionManage',
    component: () => import(/* webpackChunkName: "integratedServices" */ '@views/Managemnt/QuestionManage.vue'),
    meta: { title: '试题分类维护', keepAlive: true }
  }
]

// 基础路由路径
const baseUrl = `/app/${process.env.VUE_APP_SYS_NAME}`

export default routes.map(item => ({
  path: baseUrl + item.path,
  name: item.name,
  component: item.component,
  meta: item.meta
}))
