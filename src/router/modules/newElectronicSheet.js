/*
 * @Author: hongye
 * @Description: 电子面单路由 / 基础信息 新
 */
const routes = [
  // {
  //   path: '/serviceManagementNew',
  //   name: 'serviceManagementNew',
  //   component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/ServiceManagementNew.vue'),
  //   meta: { title: '平台客户审核', keepAlive: true }
  // },
  // {
  //   path: '/causeMaintenanceNew',
  //   name: 'causeMaintenanceNew',
  //   component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/CauseMaintenanceNew.vue'),
  //   meta: { title: '审核原因维护', keepAlive: true }
  // },
  // {
  //   path: '/customerBalanceInquiryNew',
  //   name: 'customerBalanceInquiryNew',
  //   component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/CustomerBalanceInquiryNew.vue'),
  //   meta: { title: '平台客户库存', keepAlive: true }
  // },
  // {
  //   path: '/networkBalanceInquiryNew',
  //   name: 'networkBalanceInquiryNew',
  //   component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/NetworkBalanceInquiryNew.vue'),
  //   meta: { title: '平台网点库存', keepAlive: true }
  // },
  {
    path: '/generalNetworkInventoryNew',
    name: 'generalNetworkInventoryNew',
    component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/GeneralNetworkInventoryNew.vue'),
    meta: { title: '通用网点库存', keepAlive: true }
  },
  {
    path: '/generalCustomerInventoryNew',
    name: 'generalCustomerInventoryNew',
    component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/GeneralCustomerInventoryNew.vue'),
    meta: { title: '通用客户库存', keepAlive: true }
  },
  {
    path: '/salesmanInventoryNew',
    name: 'salesmanInventoryNew',
    component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/stock/SalesmanInventoryNew.vue'),
    meta: { title: '收派员库存', keepAlive: true }
  },
  // 流水记录 flow-record

  // {
  //   path: '/flowRecordPlatformCustomers',
  //   name: 'flowRecordPlatformCustomers',
  //   component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/flow-record/FlowRecordPlatformCustomers.vue'),
  //   meta: { title: '平台客户流水记录', keepAlive: true }
  // }, {
  //   path: '/flowRecordPlatformOutlet',
  //   name: 'flowRecordPlatformOutlet',
  //   component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/flow-record/FlowRecordPlatformOutlet.vue'),
  //   meta: { title: '平台网点流水记录', keepAlive: true }
  // },
  {
    path: '/flowRecordGeneralCustomers',
    name: 'flowRecordGeneralCustomers',
    component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/flow-record/FlowRecordGeneralCustomers.vue'),
    meta: { title: '通用客户流水记录', keepAlive: true }
  },
  {
    path: '/flowRecordGeneralOutlet',
    name: 'flowRecordGeneralOutlet',
    component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/flow-record/FlowRecordGeneralOutlet.vue'),
    meta: { title: '通用网点流水记录', keepAlive: true }
  },
  {
    path: '/flowRecordSalesman',
    name: 'flowRecordSalesman',
    component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/flow-record/FlowRecordSalesman.vue'),
    meta: { title: '收派员流水记录', keepAlive: true }
  },
  // 回收管控 recovery-control
  {
    path: '/generalRecoveryDetails',
    name: 'generalRecoveryDetails',
    component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/recovery-control/generalRecoveryDetails.vue'),
    meta: { title: '通用回收明细', keepAlive: true }
  },
  {
    path: '/generalMakeupDetail',
    name: 'generalMakeupDetail',
    component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/recovery-control/GeneralMakeupDetail.vue'),
    meta: { title: '通用补扣明细', keepAlive: true }
  },
  // {
  //   path: '/pddRecoveryDetailsNew',
  //   name: 'pddRecoveryDetailsNew',
  //   component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/recovery-control/PddRecoveryDetailsNew.vue'),
  //   meta: { title: '桃花岛回收明细', keepAlive: true }
  // },
  // {
  //   path: '/YjRecoveryDetailsNew',
  //   name: 'YjRecoveryDetailsNew',
  //   component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/recovery-control/YjRecoveryDetailsNew.vue'),
  //   meta: { title: '云集回收明细', keepAlive: true }
  // },
  // {
  //   path: '/ZjsRecoveryDetailsNew',
  //   name: 'ZjsRecoveryDetailsNew',
  //   component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/recovery-control/ZjsRecoveryDetailsNew.vue'),
  //   meta: { title: '紫金山回收明细', keepAlive: true }
  // },
  // {
  //   path: '/PaperCodeRecovery',
  //   name: 'PaperCodeRecovery',
  //   component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/recovery-control/PaperCodeRecovery.vue'),
  //   meta: { title: '纸质二维码回收', keepAlive: true }
  // },

  // 面单统计 statisticsing
  {
    path: '/accessDetail',
    name: 'accessDetail',
    component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/statisticsing/accessDetail.vue'),
    meta: { title: '面单取号详情', keepAlive: true }
  },
  // {
  //   path: '/ZjsAccessDetail',
  //   name: 'ZjsAccessDetail',
  //   component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/electronic-surface-sheet-new/statisticsing/ZjsAccessDetail.vue'),
  //   meta: { title: '紫金山取号详情', keepAlive: true }
  // }
  // 基础信息 - 路由
  {
    path: '/addressMappingPositive',
    name: 'addressMappingPositive',
    component: () => import(/* webpackChunkName: "customerPlatform" */ '@views/basic-information/address-mapping/AddressMappingPositive.vue'),
    meta: { title: '平台地址映射', keepAlive: true }
  }
]

const baseUrl = `/app/${process.env.VUE_APP_SYS_NAME}`

export default routes.map(item => ({
  path: baseUrl + item.path,
  name: item.name,
  component: item.component,
  meta: item.meta
}))
