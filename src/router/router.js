/*
 * @Author: MX
 * @Date: 2020-08-03 11:36:59
 * @LastEditTime: 2021-03-03 16:44:41
 */
// 大客户平台
export default [
  // {
  //   path: '/index',
  //   name: 'index',
  //   component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/Home.vue'),
  //   meta: { title: '首页', keepAlive: true },
  // },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/dashboard/index.vue'),
    meta: { title: '货量看板', keepAlive: true },
  },
  {
    path: '/singleOrder',
    name: 'singleOrder',
    component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/place-order/single-order/SingleOrder.vue'),
    meta: { title: '单票下单', keepAlive: true },
  },
  {
    path: '/batchOrder',
    name: 'batchOrder',
    component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/place-order/batch-order/BatchOrder.vue'),
    meta: { title: '批量下单', keepAlive: true },
  },
  {
    path: '/order/:typeNum?',
    name: 'order',
    component: () => import(/* webpackChunkName: "customer" */ '@views/my-order/Order.vue'),
    meta: { title: '网页下单', keepAlive: false },
  },
  //   {
  //     path: '/externalOrders/:typeNum?',
  //     name: 'externalOrders',
  //     component: () => import(/* webpackChunkName: "customer" */ '@views/my-order/ExternalOrders.vue'),
  //     meta: { title: '三方下单', keepAlive: false },
  //   },
  {
    path: '/waybill/:typeNum?',
    name: 'waybill',
    component: () => import(/* webpackChunkName: "customer" */ '@views/my-waybill/Waybill.vue'),
    meta: { title: '我的运单', keepAlive: false },
  },
  {
    path: '/mailingAddress',
    name: 'mailingAddress',
    component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/address-manage/MailingAddress.vue'),
    meta: { title: '寄件地址', keepAlive: true },
  },
  {
    path: '/receivingAddress',
    name: 'receivingAddress',
    component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/address-manage/ReceivingAddress.vue'),
    meta: { title: '收件地址', keepAlive: true },
  },
  {
    path: '/userManage',
    name: 'userManage',
    component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/UserManage.vue'),
    meta: { title: '用户管理', keepAlive: true },
  },
  {
    path: '/expressTracking/:orderIds?',
    name: 'expressTracking',
    component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/service-manage/ExpressTracking.vue'),
    meta: { title: '快件跟踪', keepAlive: true },
  },
  {
    path: '/aviationSingleOrder',
    name: 'aviationSingleOrder',
    component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/aviation-order-manage/aviation-single-order/Index.vue'),
    meta: { title: '单票下单', keepAlive: true },
  },
  {
    path: '/aviationBatchOrder',
    name: 'aviationBatchOrder',
    component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/aviation-order-manage/aviation-batch-order/Index.vue'),
    meta: { title: '批量下单', keepAlive: true },
  },
  {
    path: '/aviationWaybill',
    name: 'aviationWaybill',
    component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/aviation-order-manage/aviation-waybill/Index.vue'),
    meta: { title: '我的运单', keepAlive: true },
  },
  // {
  //   path: '/freightInquiry',
  //   name: 'freightInquiry',
  //   component: () => import(/* webpackChunkName: "VIPcustomer" */ '@views/service-manage/FreightInquiry.vue'),
  //   meta: { title: '运费查询', keepAlive: true },
  // },
]
