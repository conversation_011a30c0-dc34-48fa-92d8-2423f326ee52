/*
 * @title: 业务员App下载
 * @author: Alinc
 * @date: 2020-6-22 15:18:14
 */
<template>
  <div class="download-app">
    <topbar class="header" :isTool="true" />
    <article class="container">
      <section class="row">
        <h4>{{$lang('诸葛APP下载')}}</h4>
        <section class="img-list one" :class="{ one: ztAppAndroidLength === 1 }" v-if="ztAppAndroid.isShow">
          <div class="img-column" v-if="ztAppAndroid.isShow">
            <p class="title">
              <i class="iconfont iconanzhuo"></i>Android|<i class="iconfont iconios1"></i>Ios</p>
            <template>
              <jms-qrcode :size="size" :text="ztAppAndroid.url" :logoUrl="ztAppAndroid.logoUrl" :logoScale="0.2"></jms-qrcode>
            </template>
          </div>
        </section>
        <footer class="footer second" style="cursor: pointer;" v-clipboard:success="onCopy"  v-clipboard:copy="'https://zt.jtcargo.co.id/'">{{$lang('点击复制')}}</footer>
        <footer class="footer second" :class="{ one: appOutLength === 1 }"><span>{{$lang('请打开手机浏览器的扫一扫或拍照扫描进行下载')}}</span></footer>
      </section>

      <section class="row">
        <h4>{{$lang('外场APP下载')}}</h4>
        <section class="img-list one" :class="{ one: appOutLength === 1 }" v-if="appOutIos.isShow">
          <!-- JFS移除IOS APP -->
          <!-- <div class="img-column">
            <p class="title"><i class="iconfont iconios1"></i>iOS</p>
            <template>
              <jms-qrcode :size="size" :text="appOutIos.url" :logoUrl="appOutIos.logoUrl" :logoScale="0.2"></jms-qrcode>
            </template>
          </div> -->
          <div class="img-column" v-if="appOutAndriod.isShow">
            <p class="title"><i class="iconfont iconanzhuo"></i>Android</p>
            <template>
              <jms-qrcode :size="size" :text="appOutAndriod.url" :logoUrl="appOutAndriod.logoUrl" :logoScale="0.2"></jms-qrcode>
            </template>
            <!-- <p>https://jt-apk.oss-cn-shanghai.aliyuncs.com/country_in_out/idn-out/idn_out_pro_Release.apk</p> -->
            <!-- <a href="https://jt-apk.oss-cn-shanghai.aliyuncs.com/country_in_out/idn-out/idn_out_pro_Release.apk">https://jt-apk.oss-cn-shanghai.aliyuncs.com/country_in_out/idn-out/idn_out_pro_Release.apk</a> -->
          </div>
        </section>
        <footer class="footer second" style="cursor: pointer;" v-clipboard:success="onCopy"  v-clipboard:copy="'https://jt-apk.oss-cn-shanghai.aliyuncs.com/country_in_out/idn-out/idn_out_pro_Release.apk'">{{$lang('点击复制')}}</footer>
        <footer class="footer second" :class="{ one: appOutLength === 1 }"><span>{{$lang('请打开手机浏览器的扫一扫或拍照扫描进行下载')}}</span></footer>
      </section>
      <section class="row">
        <h4>{{$lang('内场APP下载')}}</h4>
        <section class="img-list one" v-if="appInAndriod.isShow">
          <div class="img-column">
            <p class="title"><i class="iconfont iconanzhuo"></i>Android</p>
            <template>
              <jms-qrcode :size="size" :text="appInAndriod.url" :logoUrl="appInAndriod.logoUrl" :logoScale="0.2"></jms-qrcode>
            </template>
            <!-- <p>https://jt-apk.oss-cn-shanghai.aliyuncs.com/country_in_out/idn-in/app-in_pro_-release.apk</p> -->
            <!-- <a href="https://jt-apk.oss-cn-shanghai.aliyuncs.com/country_in_out/idn-in/app-in_pro_-release.apk">https://jt-apk.oss-cn-shanghai.aliyuncs.com/country_in_out/idn-in/app-in_pro_-release.apk</a> -->
          </div>
        </section>
        <footer class="footer second" style="cursor: pointer;" v-clipboard:success="onCopy"  v-clipboard:copy="'https://jt-apk.oss-cn-shanghai.aliyuncs.com/country_in_out/idn-in/app-in_pro_-release.apk'">{{$lang('点击复制')}}</footer>
        <footer class="footer second"><span>{{$lang('请打开手机浏览器的扫一扫或拍照扫描进行下载')}}</span></footer>
      </section>
      <!-- <section class="row">
        <h4>{{$lang('决策APP下载')}}</h4>
        <section class="img-list one" v-if="appBigData.isShow">
          <div class="img-column">
            <p class="title"><i class="iconfont iconanzhuo"></i>Android</p>
            <template>
              <jms-qrcode :size="size" :text="appBigData.url" :logoUrl="appBigData.logoUrl" :logoScale="0.2"></jms-qrcode>
            </template>
          </div>
        </section>
        <footer class="footer second"><span>{{$lang('请打开手机浏览器的扫一扫或拍照扫描进行下载')}}</span></footer>
      </section> -->
      <section class="row">
        <h4>{{$lang('司机APP下载')}}</h4>
        <section class="img-list one" :class="{ one: appTransLength === 1 }" v-if="appTransLength">
         <!--  <div class="img-column">
            <p class="title"><i class="iconfont iconios1"></i>iOS</p>
            <template>
              <jms-qrcode :size="size" :text="appTransIos.url" :logoUrl="appTransIos.logoUrl" :logoScale="0.2"></jms-qrcode>
            </template>
          </div> -->
          <div class="img-column" v-if="appTransAndriod.isShow">
            <p class="title"><i class="iconfont iconanzhuo"></i>Android/IOS</p>
            <template>
              <img src="../assets/sijiapp.png" style="width: 200px;height: 200px;" alt="">
              <!-- <jms-qrcode :size="size" :text="appTransAndriod.url" :logoUrl="appTransAndriod.logoUrl" :logoScale="0.2"></jms-qrcode> -->
            </template>
            <!-- <p>https://jt-apk.oss-cn-shanghai.aliyuncs.com/h5/jmsDriver/apk/prod/diver-ynky-pro-20210814.apk</p> -->
            <!-- <a href="https://jt-apk.oss-cn-shanghai.aliyuncs.com/h5/jmsDriver/apk/prod/diver-ynky-pro-20210814.apk">https://jt-apk.oss-cn-shanghai.aliyuncs.com/h5/jmsDriver/apk/prod/diver-ynky-pro-20210814.apk</a> -->
          </div>
        </section>
        <footer class="footer second" style="cursor: pointer;" v-clipboard:success="onCopy" v-clipboard:copy="'https://driver.jtcargo.co.id/'">{{$lang('点击复制')}}</footer>
        <footer class="footer second" :class="{ one: appTransLength === 1 }"><span>{{$lang('请打开手机浏览器的扫一扫或拍照扫描进行下载')}}</span></footer>
      </section>
    </article>
  </div>
</template>
<script>
import Topbar from './TopBar'
import { RESPONSE_CODE } from '@public/http/config'
import jmsQrcode from '@components/jms-widget/qrcode/qrcode.vue'
const appInLogoUrl = require('@assets/images/app_in.png')
const appZtLogoUrl = require('@assets/images/app_zt.png')
const appOutLogoUrl = require('@assets/images/app_out.png')
const driverLogoUrl = require('@assets/images/driver.jpg')
export default {
  components: {
    Topbar, jmsQrcode
  },
  data() {
    return {
      size: 200,
      url: 'https://itunes.apple.com/WebObjects/MZStore.woa/wa/viewSoftware?id=1495617671',
      qrcodeList: []
    }
  },
  computed: {
    // 智兔App-安卓
    ztAppAndroid() {
      const base = {
        appId: 'zt_app',
        appName: 'Android',
        isShow: true,
        logoUrl: appZtLogoUrl,
        url: 'https://zt.jtcargo.co.id/'
      }
      return base
    },
    // 外场App-iOS
    appOutIos() {
      const base = {
        appId: 'app_out_iOS',
        appName: 'iOS',
        isShow: true,
        logoUrl: appOutLogoUrl,
        url: 'https://itunes.apple.com/WebObjects/MZStore.woa/wa/viewSoftware?id=1495617671'
      }
      return base
    },
    // 外场App-安卓
    appOutAndriod() {
      const base = {
        appId: 'app_out',
        appName: 'Android',
        isShow: true,
        logoUrl: appOutLogoUrl,
        url: ''
      }
      const ele = this.qrcodeList.find(item => item.appId === base.appId)
      if (!ele || (ele && !ele.downloadUrl)) base.isShow = false
      else base.url = ele.downloadUrl
      return base
    },
    // 内场App-安卓
    appInAndriod() {
      const base = {
        appId: 'app_in',
        appName: 'Android',
        isShow: true,
        logoUrl: appInLogoUrl,
        url: ''
      }
      const ele = this.qrcodeList.find(item => item.appId === base.appId)
      if (!ele || (ele && !ele.downloadUrl)) base.isShow = false
      else base.url = ele.downloadUrl
      return base
    },
    appOutLength() {
      let count = 1
      this.appOutAndriod.isShow && count++
      return count
    },
    ztAppAndroidLength() {
      let count = 1
      this.ztAppAndroid.isShow && count++
      return count
    },
    // 决策App
    appBigData() {
      const base = {
        appId: 'app_big_data',
        appName: 'Android',
        isShow: true,
        logoUrl: appOutLogoUrl,
        url: ''
      }
      const ele = this.qrcodeList.find(item => item.appId === base.appId)
      if (!ele || (ele && !ele.downloadUrl)) base.isShow = false
      else base.url = ele.downloadUrl
      return base
    },
    // 运力App-iOS
    appTransIos() {
      const base = {
        appId: 'app_tms_iOS',
        appName: 'iOS',
        isShow: true,
        logoUrl: appOutLogoUrl,
        url: 'https://itunes.apple.com/app/id1517998246'
      }
      return base
    },
    // 运力App-Android
    appTransAndriod() {
      const base = {
        appId: 'app_tms',
        appName: 'Android',
        isShow: true,
        logoUrl: driverLogoUrl,
        url: ''
      }
      const ele = this.qrcodeList.find(item => item.appId === base.appId)
      if (!ele || (ele && !ele.downloadUrl)) base.isShow = false
      else base.url = ele.downloadUrl
      return base
    },
    // 运力App的数量
    appTransLength() {
      let count = 0
      this.appTransIos.isShow && count++
      this.appTransAndriod.isShow && count++
      return count
    }
  },
  created() {
    this.initParams()
    this.initPage()
  },
  mounted() {
  },
  methods: {
    onCopy () {
      const text = this.$lang('复制成功！')
      this.$message.success(text)
    },
    // 页面参数初始化
    initParams() {
    },
    // 页面初始化
    initPage() {
      this.getAppsInfoList()
    },
    // 获取App信息列表
    async getAppsInfoList() {
      try {
        const { code, data, msg } = await this.$MApi.getAppInfoList()
        if (code === RESPONSE_CODE.SUCCESS) {
          this.qrcodeList = data || []
        } else {
          this.$message.error(msg)
        }
      } catch (error) {
        console.error('getAppsInfoList::error', error);
      }
    }
  },
  beforeDestroy() {
  }
}

</script>

<style scoped lang="scss">
$_containerW: 1000px;
$headerH: 48px;
$h20: 20px;
$h14: 14px;
$titleH: 40px;
$rowW: 200px;
$rowH: 240px;
$rowLeft: 90px;

// 背景色
@mixin download-bg() {
  @each $themename, $theme in $themes {
    [data-theme='#{$themename}'] & {
      background: map-get($map: $theme, $key: bg-color-base);
    }
  }
}

// 卡片颜色
@mixin download-row() {
  @each $themename, $theme in $themes {
    [data-theme='#{$themename}'] & {
      background: map-get($map: $theme, $key: bg-color-content);
      border-color: map-get($map: $theme, $key: border-color-lighter);
      color: map-get($map: $theme, $key: text-color-routine);
    }
  }
}

// 卡片标题样式
@mixin row-title() {
  @each $themename, $theme in $themes {
    [data-theme='#{$themename}'] & {
      background: map-get($map: $theme, $key: bg-color-light);
      border-color: map-get($map: $theme, $key: border-color-lighter);
      color: map-get($map: $theme, $key: text-color-base);
      &:before {
        background-color: map-get($map: $theme, $key: text-color-base);
      }
    }
  }
}

.download-app {
  overflow: auto;
  // height: calc(100% - 48px);
  // margin-top: $headerH;
  @include download-bg();
  .container {
    height: 100%;
    width: $_containerW;
    padding-top: $h20;
    // padding: 20px 240px 20px 240px;
    padding: 20px 0;
    margin: 0 auto;
  }
  .row {
    overflow: hidden;
    display: block;
    margin-bottom: $h20;
    height: 45%;
    border: 1px solid;
    border-radius: $radius-base;
    @include download-row();
    h4 {
      position: relative;
      height: $titleH;
      margin: 0;
      padding: 0 $h20/2;
      border-bottom: 1px solid $c-bc-2;
      font-weight: 500;
      line-height: $titleH;
      @include row-title();
      &:before {
        content: ' ';
        position: absolute;
        top: ($titleH - $h14)/2;
        left: 0;
        width: 2px;
        height: $h14;
        background-color: $c-fc-2;
      }
    }
    .img-list {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      &.one {
        justify-content: flex-start;
        .img-column {
          margin-left: 150px;
        }
      }
    }
    .img-column {
      width: $rowW;
      // height: $rowH;
      // margin: 0 $rowLeft;
      margin-top: 40px;
      text-align: center;
    }
    .title {
      line-height: 27px;
      margin-bottom: 0;
      vertical-align: middle;
    }
    .footer {
      // width: ($rowW * 2) + ($rowLeft * 4);
      width: $_containerW;
      // margin-top: 40px;
      padding: 20px 0;
      text-align: center;
      &.second, &.one {
        // width: $rowW + $rowLeft*2;
        width: $_containerW / 2;
      }
    }
  }
  .iconfont {
    font-size: 24px;
    margin-right: 5px;
  }
}
</style>
