<template>
  <el-dialog v-bind="$attrs" v-on="$listeners" @open="onOpen" @close="onClose" :title="dialogTitle">
    <el-form ref="elForm" :model="formData" :rules="rules" class="section-form" size="medium" label-width="px">
      <el-form-item :label="$lang('小节名称')" prop="sectionName">
        <el-input v-model="formData.sectionName" :placeholder="$lang('请输入')"  :maxlength="50" show-word-limit :style="{ width: '100%' }"></el-input>
      </el-form-item>
      <el-form-item ref="uploadFormItem"   :key="123" :label="$lang('上传')" prop="sectionFile">
        <Upload
          :option="{
            projectName: 'community',
            moduleName: 'KBM',
          }"
          :limit="1"
          :fileImgSize="2"
          :multiple="false"
          :list="formData.sectionFile"
          :fileTypeList="['mp4', 'pdf']"
          :isAll="true"
          @uploadSuccess="success"
          @uploadRemove="remove"
        ></Upload>
        <div class="tip">*{{$lang('最多上传一个，支持pdf、mp4视频文件')}}</div>
      </el-form-item>
      <el-form-item :label="$lang('助教老师')" prop="teacher">
        <el-input v-model="formData.teacher" :placeholder="$lang('请输入')"  :maxlength="20" show-word-limit :style="{ width: '100%' }"></el-input>
      </el-form-item>
      <el-form-item :label="$lang('联系方式')" prop="contacts">
        <el-input v-model="formData.contacts" :placeholder="$lang('请输入')"   :maxlength="50" show-word-limit :style="{ width: '100%' }"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="close">{{$lang('取消')}}</el-button>
      <el-button type="primary" @click="handelConfirm">{{$lang('确定')}}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import Upload from './upload.vue';
import Vue from 'vue';
const vuePrototype = Vue.prototype
export default {
  inheritAttrs: false,
  name: 'AddOrUpdateSectionDialog',
  components: { Upload },
  props: [],
  data() {
    return {
      formData: {
        sectionName: undefined,
        contacts: undefined,
        teacher: undefined,
        sectionFile: []
      },
      editSectionItem: null,
      parentChapter: null,
      rules: {
        sectionName: [
          {
            required: true,
            message: '请输入',
            trigger: 'blur'
          }
        ],
        contacts: [
          {
            required: true,
            message: '请输入',
            trigger: 'blur'
          }
        ],
        teacher: [
          {
            required: true,
            message: '请输入',
            trigger: 'blur'
          }
        ],
        sectionFile: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('请上传文件'))
              } else {
                callback()
              }
            },
            message: '请上传文件',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.editSectionItem ? vuePrototype.$lang('编辑小节') : vuePrototype.$lang('新增小节')
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    remove(res) {
      this.formData.sectionFile = res
    },
    success(res) {
      this.formData.sectionFile = res
      this.$refs.uploadFormItem.clearValidate()
    },
    onOpen() {},
    onClose() {
      this.editSectionItem = null
      this.$refs['elForm'].resetFields()
    },
    close() {
      this.$emit('update:visible', false)
    },
    setParameter(editSectionItem, parentChapter) {
      this.editSectionItem = editSectionItem
      this.parentChapter = parentChapter
      if (editSectionItem) {
        this.formData.sectionName = editSectionItem.sectionName
        this.formData.contacts = editSectionItem.contacts
        this.formData.teacher = editSectionItem.teacher
        this.formData.sectionFile = editSectionItem.sectionFile
      }
    },
    handelConfirm() {
      console.log('handleConfirm---------clicked');
      this.$refs['elForm'].validate((valid) => {
        console.log(valid, 'validvalidvalidvalidvalidvalidvalidvalidvalid');
        if (!valid) return
        const formData = cloneDeep(this.formData)
        if (this.editSectionItem) {
          this.$emit('edit', { ...this.editSectionItem, ...formData }, this.parentChapter, this.close)
        } else {
          this.$emit('add', formData, this.parentChapter, this.close)
        }
        // this.close()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.section-form {
  padding: 0 20px;
  ::v-deep .el-form-item {
    margin-bottom: 22px !important;
  }
  ::v-deep input[maxlength]{
      padding-right: 46px;
  }
  .tip {
    margin-top: 15px;
    font-size: 12px;
    font-weight: 400;
    color: #bbbbbb;
    line-height: 12px;
  }
}

</style>
