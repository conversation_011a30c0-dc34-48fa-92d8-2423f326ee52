<template>
  <div class="course-papers">
    <!-- <NoData v-if="choosedPapers.length === 0" ></NoData> -->
      <ChoosePapers   ref="choosedPapersComponent" :visible.sync="choosePapersDialogVisible"  @papers-selected ="onPapersSelected"></ChoosePapers>
      <div class="choosed-paper-block">
        <div class="choosed-paper-item" v-for="item of choosedPapers" :key="item.id" >
          <span>{{item.paperName}}</span> <i @click="delPaper(item)" class="el-icon-delete"></i>
        </div>
      </div>
  </div>
</template>

<script>
// import NoData from './NoData.vue';
import ChoosePapers from './CoursePapersChoosePapers.vue';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'CoursePapers',
  components: { ChoosePapers },
  data() {
    return {
      choosePapersDialogVisible: false,
      choosedPapers: [] // [{id,paperName},]
    }
  },
  methods: {
    // ============================event handler======================================
    onPapersSelected(papers) {
      this.$emit('papers-selected', papers)
      this.choosedPapers = papers
    },

    // ============================expose====================================
    setChoosedPapers(choosedPapers) {
      console.log(`============================choosedPapers====================================`);
      console.log('🚀 ~ file: CoursePapers.vue ~ line 36 ~ setChoosedPapers ~ choosedPapers', choosedPapers)
      this.choosedPapers = choosedPapers
    },
    showChoosePapersDialog() {
      this.choosePapersDialogVisible = true;
    },
    getChoosedPapers() {
      return this.choosedPapers;
    },
    async doChoosedPapers(papers) {
      this.choosedPapers = cloneDeep(papers);
      this.showChoosePapersDialog()
      await this.$nextTick()
      this.$refs.choosedPapersComponent.setSelectedList(this.choosedPapers)
    },
    // ============================private================================
    delPaper(item) {
      this.choosedPapers.splice(this.choosedPapers.indexOf(item), 1)
      this.$emit('papers-selected', this.choosedPapers)
    }
  }
}
</script>

<style lang="scss" scoped>

.choosed-paper-item{
  width: 100%;
  height: 60px;
  box-sizing: border-box;
  background: #ffffff;
  border: 1px solid #c4c8cc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 500;
  text-align: left;
  color: #4c4d57;
  .el-icon-delete{
    font-size: 14px;
    font-weight: bold;
    &:hover{
      color: #e60012;
      cursor: pointer;
    }
  }
}
</style>
