<template>
  <div class="view-score">
    <div class="view-left">
      <div class="subject-type">
        单选题(共10题，合计20分)
      </div>
      <div class="subject-row">
        <div class="subject">
          <div class="subject-title">1.（单选）若客户下单后，订单无法在当日发出，客户需次日才能发出，应该如何操作</div>
          <div class="subject-content">
            <el-radio-group v-model="radio">
              <div>
                <el-radio :label="1"><label :class="radio === 1 ? 'red' : ''">A. 联系客户后外场APP操作取消</label></el-radio>
              </div>
            </el-radio-group>
          </div>
          <div class="subject-tips">
            <div class="tips-title">答错了，下次继续努力</div>
            <div class="tips-content"><span class="content-left">选择答案：A</span><span>正确答案：</span><span class="red">A</span></div>
          </div>
        </div>
      </div>
      <div class="subject-row">
        <div class="subject">
          <div class="subject-title">1.（单选）若客户下单后，订单无法在当日发出，客户需次日才能发出，应该如何操作</div>
          <div class="subject-content">
            <el-radio-group v-model="radio">
              <div>
                <el-radio :label="1"><label :class="radio === 1 ? 'red' : ''">A. 联系客户后外场APP操作取消</label></el-radio>
              </div>
            </el-radio-group>
          </div>
          <div class="subject-tips">
            <div class="tips-title">答错了，下次继续努力</div>
            <div class="tips-content"><span class="content-left">选择答案：A</span><span>正确答案：</span><span class="red">A</span></div>
          </div>
        </div>
      </div>
      <div class="subject-row">
        <div class="subject">
          <div class="subject-title">1.（单选）若客户下单后，订单无法在当日发出，客户需次日才能发出，应该如何操作</div>
          <div class="subject-content">
            <el-radio-group v-model="radio">
              <div>
                <el-radio :label="1"><label :class="radio === 1 ? 'red' : ''">A. 联系客户后外场APP操作取消</label></el-radio>
              </div>
            </el-radio-group>
          </div>
          <div class="subject-tips">
            <div class="tips-title">答错了，下次继续努力</div>
            <div class="tips-content"><span class="content-left">选择答案：A</span><span>正确答案：</span><span class="red">A</span></div>
          </div>
        </div>
      </div>
    </div>
    <div class="view-right">
      <div class="result">
        <div class="result-title">
          <div class="result-tips">新晋网点考试大全</div>
          <div class="result-fraction">
            <p>总分：100分</p>
            <p>单选题(10题)：20分</p>
            <p>多选题(10题)：40分</p>
            <p>判断题(10题)：40分</p>
          </div>
          <div class="result-time">
            <div class="title">考试时长</div>
            <div class="time">02:00:00</div>
          </div>
        </div>
        <div class="user">
          <div class="user-fraction">98分</div>
          <div class="user-cont">
            <p>姓名：胡图图</p>
            <p>工号：00327373</p>
            <p>网点：山东井冈山网点</p>
          </div>
        </div>
        <div class="subject-index">
          <div>1</div>
          <div>2</div>
          <div>3</div>
          <div>4</div>
          <div>5</div>
          <div class="bag_red">6</div>
          <div>7</div>
          <div>8</div>
          <div>9</div>
        </div>
      </div>
      <div class="backBut">
        <el-button  type="primary" >结束浏览</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ManageScoreViewScore',
  data() {
    return {
      radio: ''
    }
  }
}
</script>
<style lang="scss" scoped>
.view-score {
  display: flex;
  height: calc(100vh - 128px);
  .view-left {
    flex: 1;
    padding: 30px 60px 30px 30px;
    background: #ffffff;
    border-radius: 0px 4px 4px 4px;
    box-shadow: 0px 0px 10px 0px rgba(209, 213, 223, 0.2);
    margin-right: 20px;
    .subject-type {
      font-size: 20px;
      color: #2b2d42;
      line-height: 20px;
    }
    .subject-row {
      padding-left: 30px;
      margin-top: 30px;
    }
    .subject {
      border-bottom: 1px solid #dcdee1;
      .red {
        color: #e60012;
      }
      .subject-title {
        font-size: 18px;
        color: #2b2d42;
        line-height: 25px;
      }
      .subject-content {
        margin-top: 21px;
        font-size: 14px;
      }
      .subject-tips {
        background: #f2f2f2;
        margin-top: 11px;
        height: 58px;
        margin-bottom: 30px;
        .tips-title {
          font-size: 14px;
          color: #2b2d42;
          font-weight: 500;
          line-height: 14px;
          padding-top: 10px;
          padding-left: 10px;
        }
        .tips-content {
          font-weight: 400;
          color: #4c4d57;
          line-height: 14px;
          margin-top: 10px;
          .content-left {
            margin-right: 20px;
            padding-left: 10px;
          }
        }
      }
    }
  }
  .view-right {
    width: 298px;
    padding: 0 20px;
    opacity: 1;
    background: rgb(235, 235, 235);
    border-radius: 4px;
    .result {
      padding: 0 20px;
      text-align: center;
      padding-top: 50px;
      background-color: #ffffff;
      .result-title {
        border-bottom: 1px solid #dcdee1;
        .result-tips {
          font-size: 20px;
          font-weight: 500;
          color: #2b2d42;
          margin-bottom: 20px;
        }
        .result-fraction {
          color: #a1a3a6;
          font-size: 16px;
          line-height: 16px;
          font-weight: 500;
          p {
            margin: 14px 0;
          }
        }
        .result-time {
          .title {
            font-size: 16px;
            color: #4c4d57;
            font-weight: 500;
            margin-top: 16px;
            margin-bottom: 10px;
          }
          .time {
            font-size: 24px;
            color: #a1a3a6;
            line-height: 24px;
            font-weight: 500;
            padding-bottom: 20px;
          }
        }
      }
      .user {
        .user-fraction {
          font-size: 36px;
          color: #e60012;
          line-height: 36px;
          font-weight: 500;
          margin-top: 20px;
          margin-bottom: 4px;
        }
        .user-cont {
          padding-bottom: 20px;
          border-bottom: 1px solid #dcdee1;
          p {
            font-size: 16px;
            font-weight: 500;
            color: #a1a3a6;
            line-height: 16px;
            margin: 14px 0;
          }
        }
      }
      .subject-index {
        display:  inline-grid;
        grid-row: 1;
        grid-row-gap: 20px;
        grid-column-gap: 20px;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
        margin: 20px 0;
        div {
          width: 34px;
          height: 34px;
          background: #1ac88d;
          border-radius: 4px;
          color: #ffffff;
          text-align: center;
          line-height: 34px;
        }
        .bag_red{
          background-color: #e60012;
        }
      }
    }
    .backBut{
        text-align: center;
        margin-top: 20px;
        button{
          width: 139px;
          height: 38px;
        }
      }
  }
}
</style>
