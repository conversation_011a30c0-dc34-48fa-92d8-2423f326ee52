<!--
 * @Author: lianxin
 * @Date: 2020-12-07 11:27:36
 * @Description: 文档管理
 * @LastEditTime: 2020-12-22 09:16:11
-->
<template>
  <div class="main-wrap mw-tab">
    <avue-crud
      class="box-list"
      ref="params"
      :data="tableList"
      :option="listOpt"
      :page="page"
      :table-loading="loading"
      @size-change="sizeChange"
      @current-change="currentChange"
      @search-reset="resetList"
      @search-change="handleSearchChange"
      @custom-add="rowAdd"
      @export-excel="registerExport"
      @custom-edit="rowEdit"
      @delete="deleteView"
    >
      <template slot="menuLeft">
        <el-button type="info" size="small" @click="dialogOutputVisible = true" v-if="hasPower('OUTPUT')" :loading="loadingOutput"
          ><i class="icon-iconfont iconfont iconanniu-xiazaizhongxin"></i><span>{{ $lang('下载中心') }}</span></el-button
        >
      </template>
      <template slot="firstCatalogueNameSearch">
        <el-select  v-model="firstCatalogueId" :placeholder="$lang('请选择')" >
          <el-option v-for="item in firstCatalogueNameList" :key="item.id" :label="item.catalogueName" :value="item.id"> </el-option>
        </el-select>
      </template>
      <template template slot="updateByNameSearch">
        <SelectSingle ref="updateByNameSearchRef" :isAvue="true" :value.sync="searchData.updateByName" :initSelectData="staffByNamesData" />
      </template>
      <template slot="publishByNameSearch">
        <SelectSingle ref="publishByNameSearchRef" :isAvue="true" :value.sync="searchData.publishByName" :initSelectData="publishByNamesData" />
      </template>
    </avue-crud>
    <ExportDownload :moduleType="moduleType" :apiConf="ExportApiConf" :dialogOutputVisible.sync="dialogOutputVisible" />
  </div>
</template>

<script>
import mixin from '@mixins/mixin'
import { DocumentReport } from '../pool'
import ExportDownload from '@/common/components/ExportDownload'
import SelectSingle from './components/BaseSelectSingle';
export default {
  name: 'DocumentReport',
  mixins: [mixin],
  components: {
    ExportDownload, SelectSingle
  },
  data() {
    return {
      COM_HTTP: { ...DocumentReport, regExportJob: DocumentReport.download },
      ExportApiConf: {
        getExportTaskList: DocumentReport.getExportTaskList,
        delLoadExcel: DocumentReport.delLoadExcel,
        download: DocumentReport.download
      },
      MODULE_ROUTER: {
        viewRouter: this.routeDefs('documentReport', 'Add'),
        addRouter: this.routeDefs('documentReport', 'Add'),
        editRouter: this.routeDefs('documentReport', 'Edit')
      },
      formData: {
        issueWarehouseId: '',
        issueWarehouseName: ''
      },
      moduleType: '文档报表',
      firstCatalogueNameList: [], // 所属模块列表
      firstCatalogueId: undefined, // 获取当前所属模块
      disabled: true,
      searchData: {
        issueWarehouseId: '',
        updateByName: '',
        publishByName: ''
      },
      dialogOutputVisible: false,
      loadingOutput: false
    }
  },
  computed: {
    listOpt() {
      return {
        menu: false,
        exportBtn: this.hasPower('OUTPUT'),
        column: [
          {
            label: '日期',
            search: true,
            prop: '|range',
            defaultTime: ['00:00:00', '23:59:59'],
            searchDefault: [
              this.$dayjs()
                .startOf('date')
                .format('YYYY-MM-DD HH:mm:ss'),
              this.$dayjs()
                .endOf('date')
                .format('YYYY-MM-DD HH:mm:ss')
            ],
            type: 'date',
            more: true,
            clearable: false,
            pickerOptions: {
              disabledDate: time => {
                return (
                  time.getTime() >
                  this.$dayjs()
                    .endOf('date')
                    .toDate()
                )
              }
            },
            // 日期组件格式化
            format: 'yyyy-MM-dd', // 展示值
            valueFormat: 'yyyy-MM-dd HH:mm:ss', // value
            // 单元格格式化
            formatter: row => (row.statisticsTime ? this.$dayjs(row.statisticsTime).format('YYYY-MM-DD') : '--')
          },
          {
            label: '所属模块',
            prop: 'firstCatalogueName',
            search: true,
            searchslot: true,
            sort: 2
          },
          {
            label: '文档名称',
            prop: 'knowledgeWordTitle',
            search: true,
            searchPlaceholder: '请输入关键字',
            sort: 1
          },
          {
            label: '发布人',
            prop: 'publishByName',
            sort: 4,
            viewDisplay: false,
            addDisplay: false,
            editDisplay: false,
            search: true,
            searchslot: true
          },

          {
            label: '最后修改人',
            prop: 'updateByName',
            viewDisplay: false,
            addDisplay: false,
            editDisplay: false,
            search: true,
            searchslot: true,
            sort: 3
          },
          {
            label: '阅读量',
            prop: 'readCount'
          },
          {
            label: '评论量',
            prop: 'commentCount'
          },
          {
            label: '收藏量',
            prop: 'collectCount'
          },
          {
            label: '点赞数',
            prop: 'praiseCount'
          },
          {
            label: '下载数',
            prop: 'downloadCount'
          }
        ]
      }
    },
    // 业务员搜索
    staffByNamesData() {
      return {
        id: 'updateBy',
        url: `${DocumentReport.getCreateBy}`,
        props: ['updateByName'],
        isSearch: true,
        searchName: 'updateByName',
        valueDefault: this.$lang('请输入姓名')
      }
    },
    publishByNamesData() {
      return {
        id: 'publishBy',
        url: `${DocumentReport.publisherListUrl}`,
        props: ['publishByName'],
        isSearch: true,
        searchName: 'publishByName',
        valueDefault: this.$lang('请输入发布人姓名')
      }
    }
  },

  created() {
    this.getModuleList()
    this.MODULE_ROUTER = this.MODULE_ITEM_ROUTER
  },
  activated() {
    this.getModuleList()
  },
  methods: {
    searchFunBefore() {
      // 校验时间跨度，最大一个月
      const createTime = this.$refs.params.searchForm['|range']
      const isDateArr = createTime => Array.isArray(createTime) && createTime.every(item => this.$dayjs(item).isValid())
      if (isDateArr(createTime)) {
        const [startTime, endTime] = createTime
        if (this.$dayjs(endTime).diff(startTime, 'day') < 31) {
          return true
        } else {
          this.$message.warning(this.$lang('查询日期跨度，最大一个月'))
          return false
        }
      } else {
        this.$message.warning(this.$lang('查询日期不合法'))
      }
    },
    handleSearchChange(args) {
      this.searchChange(args)
    },
    // 重置方法回调重置页码
    resetList() {
      this.$router.replace({
        query: ''
      })
      this.firstCatalogueId = undefined
      this.searchData.updateByName = ''
      this.searchData.publishByName = ''
    },
    // 获取所选模块列表
    async getModuleList() {
      const res = await this.COM_HTTP.reqModule()
      res.data.unshift({ 'catalogueName': this.$lang('全部') })
      this.firstCatalogueNameList = res.data || []
      if (this.$route.query.firstCatalogueId) {
        const firstCatalogue = this.firstCatalogueNameList.find(data => this.$route.query.firstCatalogueId === data.id) || {}
        this.$nextTick(() => {
          this.firstCatalogueId = firstCatalogue.id || ''
          this.$refs.params.searchChange()
        })
      }
    },
    // 自定义搜索参数
    setSearchParams(params) {
      params.updateBy = this.searchData.updateByName
      params.publishBy = this.searchData.publishByName
      params.firstCatalogueId = this.firstCatalogueId || ''
      return params
    }
  }
}
</script>
<style lang="scss" scoped>
.margin-left {
  margin-left: 10px;
}
.hand {
  cursor: pointer;
}
.hand:hover {
  color: #e60012;
}
.font-color {
  background-color: #fff;
  color: #61666d;
}
.default {
  background-color: #f4f4f4 !important;
  color: #c0c4cc !important;
}
</style>
