<template>
  <div class="editorBox" ref="editorBox" :style="{width: width ? width+'px' : '100%',height: height ? height+'px' : '100%'}">
    <div class="editor">
      <div ref="toolbar" class="toolbar"></div>
      <div ref="editor" class="text"></div>
    </div>
  </div>
</template>

<script>
import E from 'wangeditor4'
import i18next from 'i18next'
import { DOMModify } from '@views/KBM/components/WangEditWithUploadFile.vue';
export default {
  name: 'Editoritem',
  data() {
    return {
      editor: null,
      textConten: '',
      affixConten: ''
    }
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    isClear: {
      type: Boolean,
      default: false
    },
    width: {
      type: Number,
      default: 0
    },
    height: {
      type: Number,
      default: 0
    }
  },
  watch: {
    isClear: {
      handler(val) {
        if (val) {
          this.editor.txt.clear()
          this.textConten = null
        }
      }
    }
  },
  // created() {
  //   this.seteditor()
  // },
  mounted() {
    this.seteditor()
    this.editor.txt.html(this.value)
    this.DOMModify(this.$refs.editorBox)
  },
  methods: {
    DOMModify,
    seteditor() {
      this.editor = new E(this.$refs.toolbar, this.$refs.editor)
      this.editor.config.pasteFilterStyle = false //  关闭掉粘贴样式的过滤
      this.editor.config.pasteIgnoreImg = false //  是否忽略粘贴图片
      this.editor.config.uploadImgShowBase64 = true // base 64 存储图片
      // this.editor.config.uploadImgServer = ''// 配置服务器端地址
      // this.editor.config.uploadImgHeaders = { }// 自定义 header
      this.editor.config.uploadFileName = 'file' // 后端接受上传文件的参数名
      this.editor.config.uploadImgMaxSize = 0.1 * 1024 * 1024 // 将图片大小限制为 2M
      this.editor.config.uploadImgMaxLength = 5 // 限制一次最多上传图片数量
      this.editor.config.uploadImgTimeout = 3 * 60 * 1000 // 设置超时时间
      // 国际化editor
      const lang = localStorage.getItem('lang') || 'CN'
      if (lang !== 'CN') {
        // 引入 i18next 插件
        this.editor.config.lang = 'en'
        this.editor.i18next = i18next
      }
      // 插入链接的校验
      this.editor.config.linkCheck = function(text, link) {
        if ((!link.startsWith('http://')) && (!link.startsWith('https://'))) {
          return this.$lang('插入链接应以http://或者https://开头')
        } else {
          return true
        }
      }
      // 配置菜单
      this.editor.config.menus = [
        // 'head', // 标题
        // 'bold', // 粗体
        // 'fontSize', // 字号
        // 'fontName', // 字体
        // 'italic', // 斜体
        // 'underline', // 下划线
        // 'strikeThrough', // 删除线
        // 'foreColor', // 文字颜色
        // 'backColor', // 背景颜色
        'link', // 插入链接
        // 'list', // 列表
        // 'justify', // 对齐方式
        'quote', // 引用
        'emoticon' // 表情
        // 'image' //  插入图片
        // 'table', // 表格
        //  demo：<iframe src='https://vdept.bdstatic.com/784e656a78705976724974444d316e76/5a6a326c655a566c/bdbd3d5b61cb87ce2b30728a848f67bdd963f4085e2c8dabe2d159c6643e8ccb2e3f5476429c8209e755c9b6db6d8660.mp4?auth_key=1592643781-0-0-3c66dba4c8ea656b0dd039412e90e29b'></iframe>
        // 'video', // 插入视频  // 'code', // 插入代码
        // 'undo', // 撤销
        // 'redo', // 重复
        // 'fullscreen' // 全屏
      ]
      // 输入
      this.editor.config.onchange = (html) => {
        console.log('wangeditor config', html)
        this.$emit('change', html) // 将内容同步到父组件中
      }
      /**
      // 自定义处理粘贴的文本内容
      this.editor.config.pasteTextHandle = (content) => {
        // content 即粘贴过来的内容（html 或 纯文本），可进行自定义处理然后返回
        console.log('wangeditor content', content)
        return content
      }
      // 粘贴
      this.editor.config.pasteTextHandle = (affixHtml) => {
        this.textConten += affixHtml
        this.$emit('change', this.textConten) // 将内容同步到父组件中
      }
      */
      // 初始化上传
      this.uploadInit()
      // 创建富文本编辑器
      this.editor.create()
    },
    // 初始化上传方法 @defect 监听不到拖入文本框的图片
    uploadInit() {
      // 获取相关 DOM 节点的 ID
      var btnId = this.editor.imgMenuId;
      var containerId = this.editor.toolbarElemId;
      var textElemId = this.editor.textElemId;
      console.log('content', btnId, containerId, textElemId)
    }
  }
}
</script>

<style lang="css">
  .editorBox{
    border: 1px solid  #EEEEEE;
    border-radius: 4px;
    overflow: hidden;
  }
  .editor {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    z-index: 0;
  }
  .toolbar {
    align-items: center;
    background: #EEEEEE;
    height: 33px;
  }
  .text {
    height: calc(100% - 38px);
  }
  .w-e-text{
    overflow-y: auto;
  }

  .w-e-menu{
  z-index: 2 !important;
  }
  .w-e-text-container{
    z-index: auto !important;
    /* z-index: 1 !important; */
  }
 .el-form.el-form-detail .el-form-item .el-form-item__label{
    color:#333333
  }
  .el-radio{
    color:#333333
  }
</style>
