<!--
 * @Author: zengpan
 * @Date: 2020-12-07 15:04:02
 * @Description: 阅读量管理
 * @LastEditTime: 2021-08-30 10:28:23
-->
<template>
  <div class="main-wrap mw-tab">
    <avue-crud ref="params" :data="tableList" :option="listOpt" :page="page" :table-loading="loading" @size-change="sizeChange"
      @current-change="currentChange" @search-change="searchChange" @search-reset="resetList" @export-excel="registerExport"
      :export-loading="exportLoading">
      <template slot="menuLeft">
        <el-button type="info" size="small" @click="dialogOutputVisible = true" v-if="hasPower('OUTPUT')" :loading="loadingOutput"
          ><i class="icon-iconfont iconfont iconanniu-xiazaizhongxin"></i><span>{{ $lang('下载中心') }}</span></el-button
        >
      </template>
      <template slot="status" slot-scope="{row}">
        <span class="dot active" v-if="row.status==='已阅' || row.status==='Read'"> {{$lang('已阅')}}</span>
        <span class="dot" v-if="row.status==='未阅'|| row.status==='Unread'"> {{$lang('未阅')}}</span>
      </template>
    </avue-crud>
    <ExportDownload :moduleType="moduleType" :apiConf="ExportApiConf" :dialogOutputVisible.sync="dialogOutputVisible" />
  </div>
</template>

<script>
import mixin from '@mixins/mixin'
import { DICT } from '@/common/utils/dict'
import { AgencyArea } from '../pool'
import ExportDownload from '@/common/components/ExportDownload'
export default {
  name: 'AgencyArea',
  mixins: [mixin],
  components: {
    ExportDownload
  },
  data () {
    return {
      COM_HTTP: { ...AgencyArea, regExportJob: AgencyArea.download },
      dialogOutputVisible: false,
      loadingOutput: false,
      moduleType: '阅读量管理',
      ExportApiConf: {
        getExportTaskList: AgencyArea.getExportTaskList,
        delLoadExcel: AgencyArea.delLoadExcel,
        download: AgencyArea.download
      }
    }
  },
  created() {},
  computed: {
    listOpt() {
      const that = this
      return {
        menu: false,
        exportBtn: this.hasPower('OUTPUT'),
        column: [
          {
            label: '所属模块',
            prop: 'catalogueName'
          },
          {
            label: '文档标题',
            prop: 'commentWordName',
            maxlength: 30,
            searchPlaceholder: '请输入关键字',
            search: true
          },
          {
            label: '代理区',
            prop: 'agentId',
            labelAlias: '代理区',
            type: 'network',
            viewProp: 'agentName',
            search: true,
            props: {
              label: 'name',
              value: 'id'
            },
            searchPlaceholder: '请输入代理区',
            useCurNetworkInfo: true,
            remoteQuery: {
              typeIds: [
                that.organLevelObj.NT_AGENT_AREA
              ]
            },
            formatter: (row, value, label, column) => row.proxyName
          },
          {
            label: '加盟商/转运中心',
            prop: 'transferId',
            labelAlias: '加盟商/转运中心',
            viewProp: 'transferName',
            search: true,
            searchPlaceholder: '请输入加盟商',
            useCurNetworkInfo: true,
            type: 'select',
            remote: true,
            props: {
              label: 'name',
              value: 'id'
            },
            tpyeformat(item, label, value) {
              return `${item.name}`
            },
            dicUrl: '/basicdata/network/getFranchiseeNetworkByNetworkId?name={{key}}',
            formatter: (row, value, label, column) => row.franchiseeName
          },
          {
            label: '网点',
            prop: 'networkId',
            labelAlias: '网点',
            type: 'network',
            viewProp: 'networkName',
            props: {
              label: 'name',
              value: 'id'
            },
            useCurNetworkInfo: true,
            search: true,
            remoteQuery: {
              typeIds: [that.organLevelObj.NT_FIRST_LEVEL, that.organLevelObj.NT_SECOND_LEVEL]
            },
            searchDefault: that.user.networkId === that.$constant.NT_HEADQUARTERS || that.$constant.NT_AGENT_AREA ? '' : that.user.networkCode,
            selectDic: [
              { id: that.user.networkId, name: that.user.networkName, code: that.user.networkCode }
            ],
            formatter: (row, value, label, column) => row.pickNetworkName
          },
          {
            label: '状态',
            prop: 'status',
            search: false,
            type: 'select',
            slot: true,
            valueDefault: 1,
            dicData: DICT.isStatus
          }
        ]
      }
    }
  },
  methods: {
    // 清空搜索条件
    resetList () {
      this.page.current = 1
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
