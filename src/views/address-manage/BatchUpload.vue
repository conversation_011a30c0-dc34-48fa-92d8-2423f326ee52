<!--
 * @Author: MX
 * @Date: 2020-12-08 17:32:01
 * @Description:新建上传组件 由于批量下单、批量导入模板调整 为了保持原组件 故单独抽离出组件供 收件地址、批量导入 模块使用
-->
<template>
  <div>
    <el-dialog
      lock-scroll
      :modal-append-to-body="false"
      :append-to-body="true"
      :title="$lang('批量导入')"
      :close-on-click-modal="false"
      :visible.sync="visible"
      @close="close"
      width="900px"
      top="0px"
    >
      <div class="overflow">
        <div>
          <div class="upload" >
            <el-upload
              ref="upload"
              :action="orderTemplate===3?uploadDataAddress:uploadData"
              :headers="header"
              :multiple="false"
              list-type="text"
              :limit="1"
              :show-file-list="false"
              :on-change="handleChange"
              :on-success="onSuccess"
              :on-error="onError"
              :auto-upload="false"
              accept=".xls,.xlsx"
              :on-exceed="onExceed"
            >
              <template slot="default">
                <div class="upload-area">
                    <svg class="icon" aria-hidden="true" v-if="!isActive">
                        <use xlink:href="#iconwendang-moren"></use>
                    </svg>
                    <svg class="icon" aria-hidden="true" v-else>
                        <use xlink:href="#iconwendang-jihuo"></use>
                    </svg>
                  <p class="name">{{$lang(text) || $lang('点击此处选择Excel文档')}}</p>
                </div>
              </template>
            </el-upload>
          </div>
          <p>{{$lang('省、市、区、详细地址已拆分')}}</p>
          <div class="button">
            <el-button type="info select" @click="clearFile" v-if="isActive">{{$lang('重新选择')}}</el-button>
            <el-button type="primary" @click="upload" :loading="upLoading" v-if="text && noError1">{{$lang('立即上传')}}</el-button>
          </div>
        </div>
        <div v-if="orderTemplate!==3">
          <div class="upload">
            <el-upload
              ref="upload_"
              :action="uploadData_"
              :headers="header"
              :multiple="false"
              list-type="text"
              :show-file-list="false"
              :limit="1"
              :on-change="handleChange_"
              :on-success="onSuccess_"
              :on-error="onError_"
              :auto-upload="false"
              :on-exceed="onExceed"
              accept=".xls,.xlsx"
            >
              <template slot="default">
                <div class="upload-area">
                 <svg class="icon" aria-hidden="true" v-if="!isActive_">
                        <use xlink:href="#iconwendang-moren"></use>
                    </svg>
                    <svg class="icon" aria-hidden="true" v-else>
                        <use xlink:href="#iconwendang-jihuo"></use>
                    </svg>
                  <p class="name">{{$lang(text_) || $lang('点击此处选择Excel文档')}}</p>
                </div>
              </template>
            </el-upload>
          </div>
          <p>{{$lang('省、市、区、详细地址未拆分，需要系统智能拆分')}}</p>
          <div class="button">
            <el-button type="info select" @click="clearFile_" v-if="isActive_">{{$lang('重新选择')}}</el-button>
            <el-button type="primary" @click="upload_" :loading="upLoading_" v-if="text_ && noError_">{{$lang('立即上传')}}</el-button>
          </div>
        </div>
      </div>
      <div class="tips">
        <p>{{$lang('1、您所导入内容必须严格按照模板字段顺序正确填写')}}；</p>
        <p>{{$lang('2、模板表头带*号的字段为必填字段，否则将会导入失败')}}；</p>
        <p>{{$lang('3、Excel模板单次导入一次最多支持1000条数据')}}；</p>
        <template v-if="orderTemplate!== 3">
          <p>{{$lang('4、当填写保价金额后系统会默认您已购买了保险，未填写保价金额系统则默认为您尚未购买保险')}}；</p>
          <p>{{$lang('5、“智能地址识别”由第三方付费接口提供技术支持，若解析时出现地址匹配错误，请及时反馈给工作人员，本系统无关联责任')}}。</p>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { EXCEL_URL, EXCEL_URL_2, EXCEL_URL_3 } from '@constants'
import { mapGetters } from 'vuex'
export default {
  name: 'BatchUpload',
  props: {
    value: {},
    spotType: {
      type: String,
      default: '',
    },
    orderTemplate: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      data: '',
      visible: false,
      dialogImageUrl: '',
      dialogVisible: false,
      disabled: false,
      disabled_: false,
      fileList: [],
      fileList_: [],

      text: '',
      text_: '',
      uploadData: EXCEL_URL,
      uploadData_: EXCEL_URL_2,
      uploadDataAddress: EXCEL_URL_3,
      isActive: false,
      upLoading: false,
      isActive_: false,
      upLoading_: false,

      noError1: true,
      noError_: true,
    }
  },
  watch: {
    value: {
      handler(val) {
        this.visible = val
      },
      immediate: true,
    },
    orderTemplate: {
      handler(val) {

      },
    },
  },
  computed: {
    ...mapGetters({
      user: 'user',
    }),
    // 用户信息
    userInfo() {
      return this.$store.state.base.user || {}
    },
    header() {
      return {
        'authToken': this.userInfo.token,
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleRemove(file) {
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleDownload(file) {
    },
    handleChange(file, fileList) {
      //  排除上传成功和失败，只有刚读取的文件才执行上传
      if (!file || file.status !== 'ready') {
        return
      }
      this.text = file.name
      this.isActive = true
    },
    handleChange_(file, fileList) {
      //  排除上传成功和失败，只有刚读取的文件才执行上传
      if (!file || file.status !== 'ready') {
        return
      }
      this.text_ = file.name
      this.isActive_ = true
    },
    onSuccess(data) {
      if (data.code === 1) {
        this.$emit('success', data)
        this.clearFile()
      } else {
        this.$message.error(data.msg)
        this.noError1 = false
      }
      this.upLoading = false
    },
    onSuccess_(data) {
      if (data.code === 1) {
        this.$emit('success', data)
      } else {
        this.$message.error(data.msg)
        this.noError_ = false
      }
      this.clearFile_()
      this.upLoading_ = false
    },
    onExceed(file, fileList) {
      this.$message.error(this.$lang('模板已存在,请重新选择'))
    },
    onError() {
      this.upLoading = false
    },
    onError_() {
      this.upLoading_ = false
    },
    upload(item) {
      this.upLoading = true
      this.$refs.upload.submit()
    },
    upload_(item) {
      this.upLoading_ = true
      this.$refs.upload_.submit()
    },
    clearFile() {
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()
      })
      this.text = this.$lang('点击此处选择Excel文档')
      this.isActive = false
      this.text = ''
      this.noError1 = true
    },
    clearFile_() {
      this.$nextTick(() => {
        this.$refs.upload_.clearFiles()
      })
      this.text_ = this.$lang('点击此处选择Excel文档')
      this.isActive_ = false
      this.text_ = ''
      this.noError_ = true
    },
    close() {
      this.visible = false
      this.$emit('input', this.visible)
      this.$emit('change', this.visible)
      if (this.orderTemplate === 3) {
        this.clearFile()
      } else {
        this.clearFile()
        this.clearFile_()
      }
    },
  },
}
</script>

<style scoped lang="scss" scoped>
.overflow {
  text-align: center;
  border-bottom: 1px dashed rgba(238, 238, 238, 1);
  display: flex;
  justify-content: space-around;
}
.overflow  p{
  font-size: 12px;
  color:#999;
}
.upload-area {
  margin-top: 45px;
  .file {
    font-size: 72px;
    background-image: linear-gradient(to bottom, #e0bd7b, #cfa561);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .name {
    font-size: 14px;
  }
  .icon{
      font-size: 48px
  }
}
.upload {
  display: inline-block;
  text-align: center;
  width: 240px;
  height: 200px;
background: #f7f7f9;
    border: 1px solid #eee;
  border-radius: 4px;
  border: 1px solid transparent;
  margin-top: 33px;
}
.tips {
  margin-top: 16px;
  font-size: 12px;
  color: #999;
}
.button {
  height: 63px;
  box-sizing: border-box;
  padding: 15px;
}
button.select.is-disabled {
  color: #999;
  border-color: #ebeef5;
  background-color: #ebeef5;
}
</style>
