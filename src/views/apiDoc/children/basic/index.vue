<template>
  <div class="main-r-wrap">
    <div class="main-r-top">
      <span
        class="main-r_top_nav"
        :class="mainStatus === 'online' && 'active'"
        @click.stop="handleClick('online')"
      >在线文档</span>
      <span
        class="main-r_top_nav"
        :class="mainStatus === 'apiTest' && 'active'"
        @click.stop="handleClick('apiTest')"
      >测试工具</span>
    </div>
    <!-- 在线文档 START -->
    <div class="main-r-bottom" v-show="mainStatus === 'online'">
      <online-doc :onlineData="onlineData"></online-doc>
    </div>
    <!-- 在线文档 END -->
    <!-- 测试工具 START -->
    <div class="main-r-bottom" v-show="mainStatus === 'apiTest'">
      <api-test-doc :apiTestData="apiTestData"></api-test-doc>
    </div>
    <!-- 测试工具 END -->
  </div>
</template>

<script>

import onlineDoc from "../components/onlineDoc";

import apiTestDoc from "../components/apiTestDoc";

import {OPEN_API_TEST,OPEN_API_PRO} from '@/constants/index'

export default {
  components: { onlineDoc , apiTestDoc },
  data() { 
    return {
      mainStatus: "online",
      //当前请求URL
      apiBaseUrl: process.env.VUE_APP_API_URL + "/location/getLocation",

      onlineData: {
        //接口描述
        apiDesc: "通过国家三字码获取省市区",
        //接口地址
        testApi: `${OPEN_API_TEST}/location/getLocation`,
        proApi: `${OPEN_API_PRO}/location/getLocation`,
        //参数描述  START
       postDataList:[
            
        //   请求头参数
            {
                title:'Headers',
                value:[
                    {name: "apiAccount",type: "Number",require: "Y",normal: "",describe: "接入方在平台的api账户标识",},
                    {name: "digest",type: "String",require: "Y",normal: "",describe: "签名字符串",},
                    {name: "timestamp",type: "Number",require: "Y",normal: "",describe: "时间戳，毫秒",},
                ]
            },
            { 
                title:'请求参数', 
                 value:[
                    {name: "bizContent",type: "String",require: "Y",normal: "业务参数",describe: "业务参数模块内json格式的string类型",},
                ]
            },
            {
              title:"业务参数",
              value:[
                {name: "countryCode",type: "String(30)",require: "Y",normal: "",describe: "国家三字码 中国CHN",},
              ]
            }

        ],
        //参数描述  END
        //报文描述 start
        responseDataList:[
            
        //   响应参数
            {
                title:'响应参数',
                value:[
                    { name: "code",type: "String", require: "Y",normal: "",describe: "返回码，见附录",},
                    {name: "msg",type: "String",require: "Y",normal: "",describe: "描述",},
                    {name: "data",type: "Object",require: "Y",normal: "",describe: "业务数据",},
                  ]
            },
            {
              title:"data类型说明",
              value:[
                  {name: "prov",type: "String(32)",require: "Y",normal: "",describe: "省份名称",},
                  {name: "provCode",type: "String(32)",require: "Y",normal: "",describe: "省份编码",},
                  {name: "city",type: "String(32)",require: "Y",normal: "",describe: "城市",},
                  {name: "cityCode",type: "String(32)",require: "Y",normal: "",describe: "城市编码",},
                  {name: "area",type: "String(32)",require: "Y",normal: "",describe: "区域",},
                  {name: "areaCode",type: "String(32)",require: "Y",normal: "",describe: "区域编码",},
                  {name: "postCode",type: "String(32)",require: "Y",normal: "",describe: "邮编",},
                ]
            }

        ],
        //请求示例
        requestCode: `Header：
    apiAccount=1627
    digest=U40e5sumorgd3YgZzU61Mw==
    timestamp=*************

Body：
    bizContent={
            "countryCode": "CHN"
            }`,
        //返回示例
        responseCode: JSON.parse(
          '{"code": "10","msg": "success","data": [{"parentId": 3,"prov": "安徽省","provCode": "3","city": "安庆","cityCode": "36","area": "迎江区","postCode": "12342","areaCode": "398"}]}'
        ),

        //错误代码提示
        errcodeJson: [{code:'*********',msg:"三字码信息不全"}],
      },

      apiTestData: {
          _headerData: {
            apiAccount: "1001",
            digest: "U40e5sumorgd3YgZzU61Mw==",
            timestamp: "*************",
          },
          data:{
             bizContent: `{"countryCode":"CHN"}`,
          },
        //必须要验证的参数
        requestData:['apiAccount','digest','timestamp','bizContent'],
        textareaData:{
          bizContent:true
        },
        //促发接口
        apiUrl: "/mock/location/getLocation",
        //返回
        ResponseJson: "",
        //当前请求URL
        apiBaseUrl: process.env.VUE_APP_API_URL + "/mock/location/getLocation",
        //错误代码提示
        errcodeJson: [{code:'*********',msg:"三字码信息不全"}],
      },
    };
  },
  created() {},
  methods: {
    handleClick(status) {
      this.mainStatus = status;
    }
  },
};
</script>

<style lang="scss" scoped>
@import "../../css/apiTest.scss";
</style>