<!--
 * @Author: hongye
 * @Date: 2020-09-15 18:30:32
 * @LastEditTime: 2021-12-06 14:16:52
 * @Description: 
-->
<template>
  <div class="online_doc">
    <div class="online_doc_item">
      <p class="online_doc_item_top">
        <i class="iconfont iconapiwendang_jiekoushuoming"></i>
        <span>{{ $t('onlineDoc.接口说明') }}</span>
      </p>
      <p class="explain_info" v-html="onlineData.apiDesc"></p>
    </div>
    <div class="online_doc_item explain">
      <p class="online_doc_item_top">
        <i class="iconfont iconapiwendang_jiekoudizhi1x"></i>
        <span>{{ $t('onlineDoc.接口信息') }}</span>
      </p>
      <slot name="api_addr_info">
        <p class="api_addr_info api_addr_info_new">
          {{ $t('onlineDoc.测试地址') }}{{ onlineData.testApi + address }}
        </p>
        <p class="api_addr_info">
          {{ $t('onlineDoc.正式地址') }}{{ onlineData.proApi }}
        </p>
<!--        <p class="api_addr_info">
          {{ $t('onlineDoc.测试apiAccount') }}<span>333109354167275546</span>
        </p>
        <p class="api_addr_info">
          {{ $t('onlineDoc.测试privateKey')
          }}<span>462e05f5847342b282e3bf3e64bfd510</span>
        </p>-->
        <p class="api_addr_info">
          {{ $t('onlineDoc.请求方法') }}<span>POST</span>
        </p>
        <p class="api_addr_info">
          {{ $t('onlineDoc.数据类型') }}<span>X-WWW-FORM-URLENCODED </span>
        </p>
        <p class="api_addr_info">
          {{ $t('onlineDoc.响应类型') }}<span>JSON</span>
        </p>
      </slot>
    </div>
    <div class="online_doc_item explain">
      <p class="online_doc_item_top">
        <i class="iconfont iconapiwendang_canshumiaoshusvg"></i>
        <span>{{ $t('onlineDoc.参数描述') }}</span>
      </p>
      <!-- 参数描述 table表格  -->
      <template v-for="(item, index) in onlineData.postDataList">
        <api-table
          :title="item.title || ''"
          :dataList="item.value"
          :key="index"
          :style="{ marginTop: index ? '20px' : '0' }"
        ></api-table>
      </template>
    </div>
    <div class="online_doc_item explain">
      <p class="online_doc_item_top">
        <i class="iconfont iconapiwendang_baowenmiaoshu"></i>
        <span>{{ $t('onlineDoc.报文描述') }}</span>
      </p>
      <!-- 报文描述  table表格  -->
      <template v-for="(item, index) in onlineData.responseDataList">
        <api-table
          :title="item.title || ''"
          :dataList="item.value"
          :key="index"
          :style="{ marginTop: index ? '20px' : '0' }"
        ></api-table>
      </template>
    </div>
    <div class="online_doc_item explain">
      <p class="online_doc_item_top">
        <i class="iconfont iconapiwendang_qingqiushili"></i>
        <span>{{ $t('onlineDoc.请求示例') }}</span>
      </p>
      <json-viewer
        class="pre_api"
        :expanded="true"
        :expand-depth="5"
        :value="onlineData.requestCode"
      ></json-viewer>
    </div>

    <div class="online_doc_item explain">
      <p class="online_doc_item_top">
        <i class="iconfont iconapiwendang_fanhuishili"></i>
        <span>{{ $t('onlineDoc.返回示例') }}</span>
      </p>

      <!-- <json-viewer class="pre_api" :expanded="true" :expand-depth="5" :value="onlineData.responseCode"></json-viewer> -->
      <div class="pre_api jv-container jv-light">
        <img
          src="@/assets/images/api/rImg.jpg"
          style="width: 350px; height: 350px; padding: 20px"
        />
      </div>
    </div>

    <div class="online_doc_item explain">
      <p class="online_doc_item_top">
        <i class="iconfont iconapiwendang_cuowudaima"></i>
        <span>{{ $t('onlineDoc.错误代码') }}</span>
      </p>

      <!-- 错误代码 -->
      <api-errcode-table :dataList="onlineData.errcodeJson"></api-errcode-table>
      <!-- <json-viewer class="pre_api" :value="onlineData.errcodeJson"></json-viewer> -->
    </div>
  </div>
</template>

<script>
import apiTable from '../components/apiTable'
import apiErrcodeTable from './apiErrcodeTable'
import { RESPONSE_SUCCESS } from '@/constants'
import { mapGetters, mapActions } from 'vuex'

export default {
  components: { apiTable, apiErrcodeTable },
  props: ['onlineData'],
  computed: {
    ...mapGetters(['token', 'uuid']),
    address() {
      return this.uuid.length > 0 ? `?uuid=${this.uuid}` : ''
    },
  },
  mounted() {
    // 已登录，this.uuid为空再去请求
    if (this.token && this.uuid.length == 0) {
      // 获取用户UUid
      this.getUUid()
    }
  },
  methods: {
    ...mapActions(['SET_UUID']),
    getUUid() {
      this.$API.getCertificationInfo().then((res) => {
        if (res.code === RESPONSE_SUCCESS) {
          // 这里需要判断是否认证通过，认证通过，并且uuid为空，即需要保存
          if (res.data.auditStatus == 2 && this.uuid.length == 0) {
            this.SET_UUID(res.data.uuid)
          }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../css/apiTest.scss';
</style>
