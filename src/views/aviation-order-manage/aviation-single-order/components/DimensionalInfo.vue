<template>
  <el-dialog :title="$lang('添加尺寸信息（支持添加多个尺寸）')" :visible.sync="dimensionalInfoVisible" @close="close" width="45%" append-to-body>
    <el-form :model="totalData" :inline="true">
      <el-row class="totalDataHead">
        <el-form-item :label="$lang('总体积(m³)')">
          <el-input v-model="totalData.totalVolume" disabled="true"></el-input>
        </el-form-item>
        <el-form-item :label="$lang('总件数')">
          <el-input v-model="totalData.totalPackageNumber" disabled="true"></el-input>
        </el-form-item>
        <el-button  style="margin-left: 20px;" type="primary" @click="clear" >{{$lang('一键删除')}}</el-button>
      </el-row>
      <div style="max-height: 200px;overflow: auto;">
      <template v-for="(item, index) in totalData.dimensions ">
        <el-row v-bind:key="index" class="totalDataArray">
          <el-form-item>
            <el-input v-model="item.length" @input="handleInput(item.length, 'length', index)" :placeholder="$lang('长(cm)')"></el-input>
          </el-form-item>
          <el-form-item>
            <template>
              x
            </template>
          </el-form-item>
          <el-form-item >
            <el-input v-model="item.width" @input="handleInput(item.width, 'width', index)" :placeholder="$lang('宽(cm)')"></el-input>
          </el-form-item>
          <el-form-item>
            <template>
              x
            </template>
          </el-form-item>
          <el-form-item >
            <el-input v-model="item.height" @input="handleInput(item.height, 'height', index)" :placeholder="$lang('高(cm)')" ></el-input>
          </el-form-item>
          <el-form-item>
            <template>
              x
            </template>
          </el-form-item>
          <el-form-item >
            <el-input v-model="item.packageNumber" @input="handleInput(item.packageNumber, 'packageNumber', index)" :placeholder="$lang('件数')"></el-input>
          </el-form-item>
          <el-form-item>
            <template>
              =
            </template>
          </el-form-item>
          <el-form-item >
            <el-input v-model="item.volume" disabled="true" :placeholder="$lang('总体积')"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button style="margin-left: 20px;" type="primary" icon="el-icon-plus" @click="addRow"  v-if="index==0" >{{$lang('添加')}}</el-button>
            <el-button v-if="index>0" style="margin-left: 20px;"    @click="delRow(index)" >{{$lang('删除')}}</el-button>
          </el-form-item>
        </el-row>
      </template>
    </div>
    </el-form>
    <div style="text-align: center;margin-top: 10px">
      <el-button type="primary" @click="save" size="medium">{{$lang('确认')}}</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'DimensionalInfo',
  data() {
    return {
      volumeList: [],
      volumeSize: 1,
      totalData: {
        totalVolume: 0,
        totalPackageNumber: 0,
        dimensions: [
          {
            length: '',
            width: '',
            height: '',
            packageNumber: '',
            volume: '',
          },
        ],
      },
      dimensionalInfoVisible: false,
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    visible(val) {
      this.dimensionalInfoVisible = val
    },
  },
  methods: {
    delRow(index) {
      this.totalData.dimensions.splice(index,1)
      this.calculateTotalData()
    },

    clear(){
        this.totalData.totalVolume=0;
        this.totalData.totalPackageNumber=0;
        this.totalData.dimensions=[ {
            length: '',
            width: '',
            height: '',
            packageNumber: '',
            volume: '',
          }]
    },

    close() {
      this.$emit('dimensionalInfoClose')
    },
    handleInput(value, propertyName, index) {
      console.log(value, propertyName, index)
      // 更新当前行对应的item中的属性值
      this.totalData.dimensions[index][propertyName] = value.replace(/\D/g, '')
      const dimensions = this.totalData.dimensions[index]
      const volume = (dimensions.length || 0) * (dimensions.width || 0) * (dimensions.height || 0) * (dimensions.packageNumber || 0)
      const volumeCubicMeter = (volume / 1000000).toFixed(3)
      this.totalData.dimensions[index].volume = volumeCubicMeter < 0.001 ? 0.001 : volumeCubicMeter

      // 这里可以根据更新后的尺寸等信息重新计算体积、总件数等相关数据
      this.calculateTotalData()
    },
    addRow() {
      this.totalData.dimensions.push({
        length: '',
        width: '',
        height: '',
        packageNumber: '',
        volume: '',
      })
    },
    calculateTotalData() {
      let totalVolume = 0
      let totalPackageNumber = 0

      this.totalData.dimensions.forEach((item) => {
        totalVolume += parseFloat(item.volume) || 0
        totalPackageNumber += parseFloat(item.packageNumber) || 0
      })

      this.totalData.totalVolume = totalVolume.toFixed(3)
      this.totalData.totalPackageNumber = totalPackageNumber
      if(this.totalData.totalVolume>99.999&&this.totalData.totalPackageNumber>999){
          this.$message.error(this.$lang('总体积，总件数已超出限制，无法添加'))
          hasError = true
          return false
        }
        if(this.totalData.totalVolume>99.999){
          this.$message.error(this.$lang('总体积已超出限制，无法添加'))
          hasError = true
          return false
        }
        if(this.totalData.totalPackageNumber>999){
          this.$message.error(this.$lang('总件数已超出限制，无法添加'))
          hasError = true
          return false
        }

    },
    save() {
      const validDimensions = []
      let hasError = false // 初始化为false，用于标记是否有错误

      this.totalData.dimensions.forEach((item) => {
        // 检查是否所有非空字段都满足条件（即只要有一个不为空，其他都必须不为空）
        const nonEmptyFields = [item.length, item.width, item.height, item.packageNumber]
        const hasNonEmpty = nonEmptyFields.some(field => field !== '')
        const allNonEmptyOrNone = nonEmptyFields.every(field => hasNonEmpty ? field !== '' : true)

        if (hasNonEmpty && !allNonEmptyOrNone) {
          // 如果有非空字段但其他字段有空值，则显示错误消息
          this.$message.error(this.$lang('长宽高件数必须全部填写完整'))
          hasError = true // 设置错误标记为true
          return false // 跳出当前循环的迭代（但不影响其他item的检查）
        }
        if(this.totalData.totalVolume>99.999&&this.totalData.totalPackageNumber>999){
          this.$message.error(this.$lang('总体积，总件数已超出限制，无法添加'))
          hasError = true
          return false
        }
        if(this.totalData.totalVolume>99.999){
          this.$message.error(this.$lang('总体积已超出限制，无法添加'))
          hasError = true
          return false
        }
        if(this.totalData.totalPackageNumber>999){
          this.$message.error(this.$lang('总件数已超出限制，无法添加'))
          hasError = true
          return false
        }

        // 如果所有字段都满足条件（非空或全为空，但在这里我们关心的是全非空的情况）
        if (item.length && item.width && item.height && item.packageNumber) {
          validDimensions.push(item)
        }
      })

      // 如果没有错误，则更新dimensions并触发save事件
      if (!hasError && validDimensions.length > 0) {
        this.totalData.dimensions = validDimensions
        this.calculateTotalData()
        this.$emit('save', this.totalData)
      } else if (!hasError) {
        this.$emit('save', null)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.totalDataHead{
  .el-input{
    width: 120px!important;
  }
}
.totalDataArray{
  .el-input{
    width: 60px!important;
  }
}

</style>
