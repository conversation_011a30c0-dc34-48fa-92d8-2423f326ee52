<template>
  <div >
    <el-dialog
        :title="$lang('查看订单揽收时效')"
        :visible.sync="dialogVisible"
        width="60%"
        :before-close="handleClose">
      <el-form label-position="top" :model="form" ref="form" class="body-content" >
        <el-row>
          <el-divider content-position="left">{{ $lang('平台揽收时效信息') }}</el-divider>
        </el-row>
        <el-row>
          <el-col :span="8" class="col">
            <el-form-item :label="$lang('渠道来源')" prop="channelSourceName">
              <span>{{form.channelSourceName ? form.channelSourceName : '--'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="col">
            <el-form-item :label="$lang('下单开始时间点')" prop="orderStartTime">
              <span>{{form.orderStartTime ? form.orderStartTime : '--'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="col">
            <el-form-item :label="$lang('下单结束时间点')" prop="orderEndTime">
              <span>{{form.orderEndTime ? form.orderEndTime : '--'}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" class="col">
            <el-form-item :label="$lang('代理区')" prop="proxyAreaName">
              <span>{{form.proxyAreaName ? form.proxyAreaName : '--'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="col">
            <el-form-item :label="$lang('最晚揽收时间')" prop="latestPickDateStr">
              <span>{{form.latestPickDateStr ? form.latestPickDateStr : '--'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="col">
            <el-form-item :label="$lang('最晚揽收时间点')" prop="latestPickTime">
              <span>{{form.latestPickTime ? form.latestPickTime : '--'}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-divider content-position="left">{{ $lang('平台节假日信息') }}</el-divider>
        </el-row>
        <el-row>
          <el-col :span="8" class="col">
            <el-form-item :label="$lang('节假日-月份')" prop="holidayMonths">
              <span>{{form.holidayMonths ? form.holidayMonths : '--'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="16" class="col">
            <el-form-item :label="$lang('节假日-日期')" prop="holidayDate">
              <span>{{form.holidayDate ? form.holidayDate : '--'}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-divider content-position="left">{{ $lang('操作人信息') }}</el-divider>
        </el-row>
        <el-row>
          <el-col :span="6" class="col">
            <el-form-item :label="$lang('创建时间')" prop="createTime">
              <span>{{form.createTime ? form.createTime : '--'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6" class="col">
            <el-form-item :label="$lang('创建人')" prop="createName">
              <span>{{form.createName ? form.createName : '--'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6" class="col">
            <el-form-item :label="$lang('更新人')" prop="updateName">
              <span>{{form.updateName ? form.updateName : '--'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6" class="col">
            <el-form-item :label="$lang('更新时间')" prop="updateTime">
              <span>{{form.updateTime ? form.updateTime : '--'}}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </el-dialog>

  </div>

</template>

<script>
export default {
  name: 'CollectionAgingView',
  data() {
    return {
      dicUrl: '/basicdata/network/web/page?name={{key}}&current=1&size=50&id=22&isEnable=1&isOrderByUpdateTimeDesc=1&typeIds=22,334',
      avueSelectRemote: true,
      dialogVisible: false,
      form: {
        latestPickDate: 0
      },
      filterable: true,
      clearable: true,
      selectProps: {
        label: 'name',
        value: 'code'
      },
      remoteQuery: {
        'id': 22,
        'isEnable': 1,
        'isOrderByUpdateTimeDesc': 1,
        'typeIds': '22,334'
      },
      tpyeformat: (data) => `${data.name} | ${data.code}`,
      proxyAreaDic: [],
      hasAll: false,
    }
  },
  props: {
    data: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false
    },
    channelDictList: {
      type: Array,
      default: null
    },
    latestPickDateDictList: {
      type: Array,
      default: null
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      }
    },
    data: {
      handler(val) {
        this.form = val
        this.proxyAreaDic = [{ name: val.proxyAreaName, code: val.proxyAreaCode }]
      }
    }
  },
  methods: {
    save(formName) {
      this.loading = true
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('update', this.form)
          this.loading = false
        } else {
          this.loading = false
          return false;
        }
      });
    },
    proxyAreaChange(val) {
      if (!val) {
        return;
      }
      if (!this.$refs.proxyAreaSelect) {
        return;
      }
      const data = this.$refs.proxyAreaSelect.$data.netDic
      for (let i = 0; i < data.length; i++) {
        if (val === data[i].code) {
          this.form.proxyAreaName = data[i].name
          return
        }
      }
    },
    channelSourceChange(val) {
      if (!val) {
        return;
      }
      if (!this.$refs.channelSourceSelect) {
        return;
      }
      const data = this.$refs.channelSourceSelect.$data.netDic
      for (let i = 0; i < data.length; i++) {
        if (val === data[i].code) {
          this.form.channelSourceName = data[i].name
          return
        }
      }
    },
    handleClose() {
      this.$emit('handleClose')
    }
  }
}
</script>

<style scoped>

</style>
