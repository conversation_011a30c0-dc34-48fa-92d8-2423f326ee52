/*
 * @Author: zhc
 * @Date: 2021-06-11 15:04:44
 * @Last Modified by: zhc
 * @Last Modified time: 2021-06-11 15:04:44
 */
import fetch from '@public/http/request'
// import fetch from '@/common/utils/pddFetch'
import { GATEWAY } from '@public/config/index'

/**
 * 平台地址映射正向
 */

export const MappingPositive = {
  // 详情查询
  viewUrl: `${GATEWAY.CUS}/order/lmdm/address/map/detail`,
  reqDel: (reqData) => fetch.post(`${GATEWAY.CUS}/order/lmdm/address/map/delete`, reqData).then(res => res),
  // 编辑数据
  reqUpdate: (reqData) => fetch.post(`${GATEWAY.CUS}/order/lmdm/address/map/edit`, reqData).then(res => res),
  // 导出接口
  reqExcel: (reqData) => fetch.post(`${GATEWAY.CUS}/order/lmdm/address/map/export`, reqData, { responseType: 'arraybuffer' }).then(res => res),
  // 获取平台名称
  reqPlatform: params => fetch.post(`${GATEWAY.CUS}/order/lmdm/address/map/platform`, params).then(res => res),
  // JMS区域code获取
  getAreaDetail: (params) => fetch.get(`${GATEWAY.BAS}/sysArea/detail?id=${params.id}`).then(res => res),
  // 平台省市区级联查询
  reqAddress: (params) => fetch.post(`${GATEWAY.CUS}/order/lmdm/address/map/cascader`, params).then(res => res),
  // 分页查询
  reqList: (params) => fetch.post(`${GATEWAY.CUS}/order/lmdm/address/map/page`, params).then(res => res),
  // 平台地址正向映射新增
  reqAdd: (params) => fetch.post(`${GATEWAY.CUS}/order/lmdm/address/map/add`, params).then(res => res),
  // 导入模板
  downloadTemplate() {
    return fetch.get(`${GATEWAY.CUS}/order/lmdm/address/downloadTemplate`, { responseType: 'arraybuffer' })
  }
}

