<!--
 * @Author: hongye
 * @Description: 紫金山(平台客户面单库存)
-->
<template>
<div class="main-wrap operator-wrap">
  <avue-crud
    ref="params"
      :data="tableList"
      :option="listOpt"
      :page="page"
      :table-loading="loading"
      @row-save="addFun"
      @row-update="updateFun"
      @size-change="sizeChange"
      @current-change="currentChange"
      @search-reset="resetList"
      @export-excel="outExcel"
      @search-change="searchChange"
      @row-del="deleteView">
      <template slot="menuLeft">
        <el-button type="info" size="small" @click="dialogOutputVisible = true" v-if="hasPower('OUTPUT')" :loading="loadingOutput">{{$lang('下载中心')}}</el-button>
        <el-button type="info" size="small"  v-if="hasPower('GRANT')"  @click.stop="$refs.paymentDialog.dialogShow(COM_HTTP)">{{$lang('充值发放')}}</el-button>
      </template>
      <template  slot-scope="{row}" slot="menu">
        <template v-if="hasPower('WITHDRAW')">
                 <button class="el-button el-button--text el-button--small" :title="$lang('撤回')" type="button" @click.stop="openActionDialog(row)">{{$lang('撤回')}}</button>
        </template>
        <template v-if="hasPower('RECORD')">
                 <button class="el-button el-button--text el-button--small" :title="$lang('流水记录')" type="button" @click.stop="$router.push({name:'flowRecordPlatformCustomers',query:{activeName:'ZJS',customerCode:row.customerCode,customerName:row.customerName,_t:new Date().getTime()}})">{{$lang('流水记录')}}</button>
        </template>
        <template v-if="hasPower('EDIT')" >
                 <button class="el-button el-button--text el-button--small" :title="$lang('库存功能设置')"  @click.stop="openWarningDialog(row)" type="button">{{$lang('功能设置')}}</button>
        </template>
      </template>
      <template slot="warningPhone" slot-scope="{row}">
        <!-- iconyincang -->
        <div class="toggleIcon"><span>{{row.warningPhone || '--'}}</span><i
          class="icon-mobile icon-iconfont iconfont " :class="row.isOpen ? 'iconxianshimima' : 'iconyincang'"
          @click="mobileClick(row)"
        ></i></div>
      </template>
      <template slot="isSmsReminder" slot-scope="{row}">
        <span v-if="row.isSmsReminder === 1"> {{$lang('是')}}</span>
        <span v-else-if="row.isSmsReminder === 0"> {{$lang('否')}}</span>
      </template>
      <template slot="isAutoIssue" slot-scope="{row}">
        <span v-if="row.isAutoIssue === 1"> {{$lang('是')}}</span>
        <span v-else-if="row.isAutoIssue === 0"> {{$lang('否')}}</span>
      </template>
  </avue-crud>
   <!-- 自定义撤回面单 -->
    <el-dialog lock-scroll
              :modal-append-to-body="false"
              append-to-body
              :title="$lang('撤回面单（紫金山）')"
              :width="dialogWidth"
              custom-class="batch-dialog"
              :visible.sync="withdrawDialogVisible"
              :styleType="'noBorder'"
              :close-on-click-modal="false"
              @close="closeDialog">
              <div ref="content" v-loading="$parent.dialogLoading">
                <el-scrollbar style="height:100%">
                  <el-form ref="withdrawForm" :rules="rules" :model="withdrawForm" label-position="top">
                    <el-form-item :label="$lang('客户名称：')" prop="customerName">
                      <el-input disabled v-model="withdrawForm.customerName" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item :label="$lang('商家名称：')" prop="mallName">
                      <el-input disabled v-model="withdrawForm.mallName" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item :label="$lang('商家剩余电子面单数量：')" prop="businessNum">
                      <el-input disabled v-model="withdrawForm.businessNum" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item :label="$lang('撤回数量：')" prop="grantNum">
                      <el-input  :placeholder="$lang('请输入')" v-model="withdrawForm.grantNum" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item :label="$lang('网点剩余电子面单数量：')" prop="networkNum">
                      <el-input disabled v-model="withdrawForm.networkNum" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item :label="$lang('备注：')" prop="remark" style="display:block;width:640px;">
                      <el-input :placeholder="$lang('请输入')" type="textarea" v-model="withdrawForm.remark" auto-complete="off" style="height:80px;" :showWordLimit="true" maxlength="200"></el-input>
                    </el-form-item>
                  </el-form>
                </el-scrollbar>
              </div>
              <span slot="footer"
                class="dialog-footer">
                <el-button :size="$parent.controlSize"  @click="closeDialog" :title="$lang('取消')">{{$lang('取 消')}}</el-button>
                <el-button type="primary"
                          :size="$parent.controlSize"
                          :title="$lang('保存')"
                          :loading="keyBtn"
                          @click="withdrawSubmitForm"
                          >{{$lang('保 存')}}</el-button>
              </span>
    </el-dialog>
    <!-- 下载中心 -->
    <ExportDownload :moduleNames="moduleNames" :moduleType="moduleType" :dialogOutputVisible.sync="dialogOutputVisible"/>
    <!-- 充值发放 -->
    <recharge-payment-dialog ref='paymentDialog' DialogName="ZJS" :title="$lang('充值发放（紫金山）')" :user="user" :COM_HTTP='COM_HTTP'></recharge-payment-dialog>
        <!-- 库存功能设置 -->
    <customer-setting ref="customer_pdd_set" :title="$lang('库存功能设置（紫金山）')" :isEdit='isEdit' saveFetchPath="updateWarningCustomerPDD"></customer-setting>

  </div>
</template>
<script>
import mixin from '@mixins/mixin'
import sheetNewMixin from '@/common/utils/sheetNewMixin'
import { CustomerBalanceInquiryZJS_new } from '../../pool'
import { RULES } from '@/common/utils/validators'
import { interfaceWaitHandle } from '@public/utils/common'
import { RESPONSE_CODE } from '@public/http/config'
import debounce from 'lodash/debounce'
import { GATEWAY } from '@public/config/index'

// 导出
import ExportDownload from '@/components/ExportDownloadSheet'
// 充值发放
import rechargePaymentDialog from './components/rechargePaymentDialog'
// 库存功能设置
import customerSetting from './components/customerSetting';
export default {
  name: 'BalanceInquiryZJS',
  mixins: [mixin, sheetNewMixin],
  components: {
    ExportDownload,
    rechargePaymentDialog, customerSetting
  },
  data() {
    var grantNum = (rule, value, callback) => {
      if (this.withdrawForm.grantNum > this.withdrawForm.businessNum) {
        callback(new Error(this.$lang('撤回数量需小于商家剩余电子面单数量')));
      } else {
        callback();
      }
    }
    return {
      COM_HTTP: CustomerBalanceInquiryZJS_new,
      tableList: [],
      isShowFirst: false, // 阻止查询方法第一时间调用
      dialogOutputVisible: false, // 导出下载中心弹窗
      loadingOutput: false,
      moduleNames: 'dy_customer_stock',
      moduleType: this.$lang('平台客户库存(紫金山)'),
      materialData: {}, // 物料详情 默认紫金山
      withdrawDialogVisible: false, // 默认关闭撤回弹窗
      customerInfo: {}, // 商家信息
      isEdit: true, // 是否属于总部、代理、网点级别
      customerData: {}, // 库存设置信息
      keyBtn: false, // 保存loading效果
      withdrawForm: {// 撤回弹窗详情
        mallName: '', // 商家名称
        customerName: '', // 客户名称
        businessNum: 0, // 商家剩余数量
        grantNum: null, // 撤回、发放数量
        networkNum: 0, // 网点剩余数量
        remark: ''
      },
      rules: {
        grantNum: [
          { required: true, message: this.$lang('请输入撤回值数量'), trigger: 'blur' },
          { validator: grantNum, trigger: ['blur', 'change'] },
          RULES.moreZeroInteger,
          RULES.isNumber
        ]
      }

    }
  },
  created() {
    const { NT_HEADQUARTERS, NT_AGENT_AREA, NT_CENTER } = this.organLevelObj
    const jurArray = [NT_HEADQUARTERS, NT_AGENT_AREA, NT_CENTER]
    this.isEdit = jurArray.includes(this.user.institutionalLevelId)
  },
  mounted () {
    this.$nextTick().then(async (res) => {
      this.searchFun()
    })
  },
  computed: {
    listOpt() {
      const that = this
      return {
        header: true,
        editBtn: false,
        fixHeight: 50, // 底部显示高度
        menuWidth: 200, // 菜单操作宽度
        exportBtn: this.hasPower('OUTPUT'),
        searchResetBtn: true,
        viewUrl: `${GATEWAY.CUS}/dyCustomerStock/getCustomerStockById`,
        column: [
          {
            label: '网点名称',
            prop: 'branchCode',
            type: 'network',
            searchClearable: false,
            rules: [RULES.required],
            remoteQuery: {
              queryLevel: 3
            },
            viewProp: 'branchName',
            // senior: false, // 标识高级搜索字段
            props: {
              label: 'name',
              value: 'code'
            },
            sort: 0,
            selectDic: [{ id: that.user.networkId, name: that.user.networkName, code: that.user.networkCode }],
            searchDefault: that.user.networkCode,
            useCurNetworkInfo: true,
            // 其他表单中的禁用
            disabled: true,
            search: true
            // hide: true
          },
          {
            label: '网点编码',
            prop: 'branchName',
            formatter: ({ branchCode }) => {
              return branchCode
            }
          },
          {
            label: '客户名称',
            prop: 'customerName'
          },
          {
            label: '客户编码',
            labelAlias: '客户名称',
            prop: 'customerCode',
            viewDisplay: false, // 查看不显示
            searchPlaceholder: '请输入关键字查询',
            type: 'select',
            remote: true,
            search: true,
            sort: 1,
            props: {
              label: 'customerName',
              value: 'customerCode'
            },
            tpyeformat: (data) => (data.customerName + ' | ' + data.customerCode),
            dicUrl: `esscustomer/dyCustomerStock/getNameList?customerName={{key}}&branchCode=${this.user.networkCode}&current=1&size=9999`
          },
          {
            label: '商家名称',
            prop: 'mallName'
          },
          {
            label: '商家ID',
            prop: 'mallId',
            labelAlias: '商家名称',
            searchPlaceholder: '请输入关键字查询',
            type: 'select',
            remote: true,
            search: true,
            sort: 2,
            props: {
              label: 'accountName',
              value: 'mallId'
              // res: 'records'
            },
            tpyeformat: (data) => (data.accountName + ' | ' + data.mallId),
            dicUrl: `esscustomer/dyCustomerStock/getNameList?accountName={{key}}&branchCode=${this.user.networkCode}&current=1&size=9999`
          },
          {
            label: '可用余额',
            prop: 'stockCount'
          },
          {
            label: '累计已分配的面单数量',
            prop: 'allocatedQuantity'
          },
          {
            label: '商家取消的面单数量',
            prop: 'cancelQuantity'
          },
          {
            label: '回收面单数量',
            prop: 'recycledQuantity'
          },
          {
            label: '预警库存数量',
            prop: 'warningQuantity'
          },
          {
            label: '短信提醒',
            prop: 'isSmsReminder',
            slot: true
          },
          {
            label: '预警手机号',
            prop: 'warningPhone',
            slot: true,
            width: 150
          },
          {
            label: '自动发放',
            prop: 'isAutoIssue',
            slot: true
          },
          {
            label: '自动发放数量',
            prop: 'autoIssueNum'
          },
          {
            label: '最大库存数量',
            prop: 'maxStockNum'
          },
          {
            label: '更新时间',
            prop: 'updateTime'
          }
        ]
      }
    }
  },
  methods: {
    // 打开库存设置弹窗
    openWarningDialog(row) {
      this.$refs['customer_pdd_set'].openWarningDialog(row)
    },
    // 撤回弹窗
    openActionDialog(row) {
      this.withdrawForm.mallName = row.mallName
      this.withdrawForm.customerName = row.customerName
      this.getCustomerInfoValue(row)
      this.withdrawDialogVisible = true
    },
    // 关闭弹窗
    closeDialog() {
      this.withdrawDialogVisible = false
      this.$refs.withdrawForm.resetFields();
    },

    // 撤回面单 获取剩余数量
    async getCustomerInfoValue({ customerCode }) {
      const { code, msg, data } = await this.COM_HTTP.getCustomerInfo({ customerCode, grantType: 2 })
      if (code === RESPONSE_CODE.SUCCESS) {
        this.customerInfo = data
        this.withdrawForm.businessNum = data.customerStockBalance
        this.withdrawForm.networkNum = data.siteStockBalance
      } else {
        this.$message.error(msg)
      }
    },
    // 撤回提交
    async withdrawSubmitForm() {
      this.$refs.withdrawForm.validate((valid) => {
        if (!valid) {
          return
        }
        try {
          if (valid) {
            const data = {
              grantNum: this.withdrawForm.grantNum,
              grantType: 2, // 发放类型1-充值 2-扣减
              grantCode: this.customerInfo.customerCode,
              grantName: this.customerInfo.customerName,
              remark: this.withdrawForm.remark,
              siteCode: this.customerInfo.siteCode,
              siteName: this.customerInfo.siteName,
              operatorCode: this.user.staffNo,
              operatorName: this.user.name
            }
            this.handlerAction(data)
          }
        } catch (err) {
          this.keyBtn = false;
        }
      });
    },
    async handlerAction(data) {
      this.keyBtn = true
      try {
        const { code, msg } = await this.COM_HTTP.reqAdd(data)
        if (code === RESPONSE_CODE.SUCCESS) {
          interfaceWaitHandle(() => {
            this.$refs.params.selectClear()
            this.selectedRows = []
            this.closeDialog()
            this.$message.success(msg)
            this.keyBtn = false
            this.searchFun()
          })
        } else {
          this.$message.error(msg)
          this.keyBtn = false
        }
      } catch (err) {
        this.keyBtn = false;
      }
    },
    searchAfterFun() {
      // 获取数据后为每一列添加自定义属性，决定手机号一栏icon的样式
      if (this.tableList && this.tableList.length > 0) {
        this.tableList = [...this.tableList.map((item) => ({ ...item, isOpen: false, warningPhoneNew: '', warningPhoneOld: '' }))]
      }
    },
    // 点击眼睛触发方法
    mobileClick: debounce(async function(row) {
      // 如果是手机号为空白，那应该终止程序往下执行
      if (!row.warningPhone) return this.$message.warning(this.$lang('该条数据手机号为空'))
      // 眼睛是闭着的，可以发送请求,可以先判断contactPhoneNew的长度，如果长度大于0,说明之前请求过真实的手机号，可以直接用
      if (!row.isOpen) {
        const params = {
          id: row.id
        }
        if (row.warningPhoneNew.length !== 0) {
          row.isOpen = !row.isOpen
          row.warningPhone = row.warningPhoneNew
        } else {
          // 眼睛是睁开的，可以发送请求
          const { code, data, msg } = await this.COM_HTTP.getMobile(params)
          if (code === RESPONSE_CODE.SUCCESS) {
          // 请求成功了
            row.isOpen = !row.isOpen
            row.warningPhoneOld = row.warningPhone // 把加密的手机号的先保存起来
            row.warningPhoneNew = data.warningPhone // 详情接口拿到的真实手机号先存起来
            row.warningPhone = data.warningPhone // 显示真实手机号
          } else {
            this.$message.error(msg)
          }
        }
      } else {
        // 眼睛睁开说明页面上是真实的手机号，这时候前端做一些处理就好了，直接取之前保存的加密手机号
        row.warningPhone = row.warningPhoneOld
        row.isOpen = !row.isOpen
      }
    }, 500)

  }
}
</script>
<style lang="scss" scoped>
.operator-wrap {
  padding: 0px;
}
.toggleIcon{
    .icon-mobile{
      @include base-color("clr");
      cursor: pointer;
      float: right;
    }
}
</style>
<style lang="scss">
    .warn-dialog .el-form{
      padding-bottom: 20px;
      .tipsImg{
        position: absolute;
        top: -32px;
        left: 105px;
      }
    }
    .batch-dialog{
      .el-form-item{
        width: 200px;
        margin-bottom: 0;
        margin: 10px;
        display: inline-block;
        vertical-align: top;
      }
      .el-radio-group{
        display:inline-flex;
        .el-radio{
          margin-right:20px;
        }
      }
      .el-textarea__inner{
        height: 100% !important;
      }
      .dialog-footer {
        padding-right: 0 !important;
        margin-bottom: 0 !important;
      }
    }
    .el-form{
      .el-form-item{
        .el-textarea{
          vertical-align:text-bottom;
        }
      }
    }
</style>
