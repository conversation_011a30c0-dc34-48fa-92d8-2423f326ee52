<!--
 * @Author: hongye
 * @Date: 2020-09-27 11:13:53
 * @Description:平台客户流水
-->
<template>
  <div class="main-wrap network-balance">
      <el-tabs class="mw-tab" v-model="activeName">
      <el-tab-pane :label="$lang('桃花岛')" name="PDD">
        <platform-customer-comp typeName="PDD"></platform-customer-comp>
      </el-tab-pane>
      <el-tab-pane :label="$lang('云集')" name="YJ" :lazy="true">
        <platformCustomerYJ typeName="YJ"></platformCustomerYJ>
      </el-tab-pane>
      <el-tab-pane :label="$lang('紫金山')" name="ZJS" :lazy="true">
        <platformCustomerZJS typeName="ZJS"></platformCustomerZJS>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import platformCustomerComp from './children/platformCustomerComp'
import platformCustomerYJ from './children/platformCustomerYJ'
import platformCustomerZJS from './children/platformCustomerZJS'
export default {
  name: 'FlowRecordPlatformCustomers',
  components: {
    platformCustomerComp,
    platformCustomerYJ,
    platformCustomerZJS
  },
  data() {
    return {
      activeName: 'PDD'
    }
  },
  created() {
    this.onIntData();
  },
  activated() {
    this.onIntData();
  },
  methods: {
    onIntData() {
    // 如果路由带着activeName这个参数
      console.log(this.$route.query.activeName);

      if (!this.$route.query.activeName) {
        this.activeName = 'PDD'
      } else {
        this.activeName = this.$route.query.activeName
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.salesman-wrap {
  padding: 0px;
}
.toggleIcon{
    .icon-mobile{
      @include base-color("clr");
      cursor: pointer;
      float: right;
    }
}
</style>
