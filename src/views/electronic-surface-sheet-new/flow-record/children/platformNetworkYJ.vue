<!--
 * @Author: hongye
 * @Description: 平台网点流水
-->
<!--

@props{
    typeName:"" YJ--云集  PDD--桃花岛
}

-->
<template>
  <div class="main-wrap salesman-wrap">
    <avue-crud ref="params"
    :data="tableList"
    :option="listOpt"
    :page="page"
    v-model="formData"
    :table-loading="loading"
      @size-change="sizeChange"
      @current-change="currentChange"
      @search-change="searchChange"
      @export-excel="outExcel"
      @search-reset="resetList"
       @custom-edit="EditClick">
      <!-- 顶部筛选按钮 -->
      <template slot="menuLeft">
        <el-button type="info" size="small" @click="dialogOutputVisible = true" v-if="hasPower('OUTPUT')">{{ $lang('下载中心') }}</el-button>
      </template>
      <!-- 面单类型 -->
      <template slot="sheetType">
        {{sheetTypeList['title']}}
      </template>
      <template #turnOverType>
        123
      </template>
      <template slot="traceIdSearch">
        <base-input-tag
            v-model="formData.traceId"
            :addTagOnBlur="true"
            :clearBtnVisible="true"
            validate="orderName"
            class="cms-input-tag">
          </base-input-tag>
      </template>
    </avue-crud>
    <!-- 下载中心 -->
    <ExportDownload :moduleNames="moduleNames" :moduleType="moduleType" :dialogOutputVisible.sync="dialogOutputVisible" />
    <!-- 编辑 -->
    <el-dialog :title="$lang('编辑备注')" :visible.sync="dialogVisible" width="500px">
      <el-form>
        <el-form-item :label="$lang('备注')" label-width="50px">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" v-model="mark" autocomplete="off" maxlength="100" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{$lang('取 消')}}</el-button>
        <el-button type="primary" :loading="editLoad" @click="saveEdit">{{$lang('确 定')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// 分页、查询等
import mixin from '@mixins/mixin'
import sheetNewMixin from '@/common/utils/sheetNewMixin'
import { GATEWAY } from '@public/config/index'
import { PlatformNetworkYJ } from '../../../pool'
// 验证  校验
import { RULES } from '@/common/utils/validators'
import { commonFun } from '@public/utils/common'
// import debounce from 'lodash/debounce' // 防抖
import { interfaceWaitHandle } from '@public/utils/common'
import { RESPONSE_CODE } from '@public/http/config'
import ExportDownload from '@/components/ExportDownloadSheet'
const [startTime, endTime] = commonFun.GetTodayDate();
import dayjs from 'dayjs'
export default {
  name: 'PlatformNetworkYJ',
  mixins: [mixin, sheetNewMixin],
  props: {
    typeName: {
      type: String,
      defaule: ''
    }
  },
  components: {
    ExportDownload
  },
  data() {
    return {
      COM_HTTP: PlatformNetworkYJ,
      tableList: [],
      formData: {
        endTime: endTime,
        startTime: startTime,
        traceId: []
      },
      mark: '', // 编辑备注需要的参数
      id: '', // 编辑备注需要的参数
      dialogVisible: false,
      editLoad: false, // 编辑loading
      // 下载中心
      dialogOutputVisible: false,
      moduleNames: 'yl_yj_site_stock_detail',
      moduleType: this.$lang('云集网点流水'),
      //   流水类型
      FlowType: [
        {
          label: this.$lang('网点申购'),
          value: 0
        }, {
          label: this.$lang('客户充值'),
          value: 1
        }, {
          label: this.$lang('客户撤回'),
          value: 2
        }, {
          label: this.$lang('取消申购'),
          value: 3
        }
      ],
      first: true,
      // 接住路由中传过来的参数
      detail: { siteCode: '', siteName: '' }
    }
  },
  computed: {
    sheetTypeList() {
      if (this.typeName === 'YJ') {
        return {
          title: this.$lang('云集电子面单'),
          selectUrl: `${GATEWAY.CUS}/yjcssCustomerVerify/getNameList?jmsCustomerName={{key}}&branchCode=${this.user.networkId}&current=1&size=9999`
        }
      } else {
        return {
          title: this.$lang('桃花岛电子面单'),
          selectUrl: `${GATEWAY.CUS}/ebcssCustomerVerify/getNameList?jmsCustomerName={{key}}&branchCode=${this.user.networkId}&current=1&size=9999`
        }
      }
    },
    listOpt() {
      const that = this
      return {
        header: true,
        fixHeight: 20, // 底部显示高度
        menuWidth: 100, // 操作宽度
        editBtn: true,
        exportBtn: this.hasPower('OUTPUT'),
        customEdit: true,
        column: [
          {
            label: '网点名称',
            prop: 'siteName'
          },
          {
            label: '网点编码',
            labelAlias: '网点名称',
            prop: 'siteCode',
            type: 'network',
            addDisplay: false, // 新增不显示
            rules: [RULES.required],
            searchClearable: false,
            remoteQuery: {
              queryLevel: 3
            },
            props: {
              label: 'name',
              value: 'code',
              res: 'records'
            },
            selectDic: this.detail.siteCode ? [{
              name: this.detail.siteName || '',
              code: this.detail.siteCode || ''
            }] : [{ name: that.user.networkName, code: that.user.networkCode }],
            // searchDefault: that.$route.query.siteCode ? that.$route.query.siteCode + '' : that.user.networkCode,
            searchDefault: that.detail.siteCode || that.user.networkCode,

            // 使用当前用户登录的网点信息
            useCurNetworkInfo: true,
            search: true,
            sort: 0,
            formatter: ({ siteCode }) => siteCode
          }, {
            label: '面单类型',
            prop: 'sheetType',
            slot: true
          }, {
            label: '流水号',
            prop: 'traceId',
            searchslot: true,
            searchPlaceholder: '请输入流水号',
            search: true,
            sort: 5
          }, {
            label: '流水时间',
            labelAlias: '流水开始日期',
            prop: 'startTime',
            viewProp: 'traceTime',
            type: 'date',
            editable: false,
            relationProps: ['endTime'], // 时间控件关联操作的关键字段 开始时间必须添加此字段，可以在选择开始时间后自动让结束时间获取焦点
            search: true,
            sort: 1,
            searchDefault: startTime,
            searchClearable: false,
            viewDisplay: false, // 查看不显示
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            pickerOptions: {
              disabledDate: time => {
                // const that = this
                // const { endTime } = that.$refs.params.searchForm
                const endDate = new Date(new Date(endTime).toLocaleDateString()).getTime()
                const space = 180 * 24 * 3600 * 1000
                const minTime = endDate - space
                const maxTime = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
                return time.getTime() < minTime || time.getTime() > maxTime
              }
            },
            change({ value }) {
              const startVal = new Date(value).getTime()
              const { endTime } = that.$refs.params.searchForm
              const endVal = new Date(endTime).getTime()
              const fifteenDay = startVal + 30 * 24 * 3600 * 1000
              if (endVal > fifteenDay) {
                // 结束时间 在 开始时间30天之后  重置结束时间
                that.$refs.params.searchForm.endTime = new Date(fifteenDay)
              }
            }
          },
          {
            label: '流水结束日期',
            prop: 'endTime',
            type: 'date',
            editable: false,
            searchDefault: endTime,
            searchClearable: false,
            viewDisplay: false, // 查看不显示
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            hide: true,
            search: true,
            sort: 2,
            pickerOptions: {
              disabledDate: time => {
                const space = 30 * 24 * 3600 * 1000
                const { startTime } = that.$refs.params.searchForm
                const minTime = startTime ? new Date(startTime).getTime() : new Date().getTime() - space
                const endDate = new Date(new Date(startTime).toLocaleDateString()).getTime() + space
                const maxTime = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
                return time.getTime() < minTime || time.getTime() > maxTime || time.getTime() > endDate
              }
            }
          },
          {
            label: '流水类型',
            prop: 'turnOverType',
            type: 'select',
            search: true,
            dicData: this.FlowType,
            rules: [RULES.requiredOptions]
          },
          {
            label: '交易数量',
            prop: 'traceNum'
          }, {
            label: '交易状态',
            prop: 'traceStatus',
            type: 'select',
            dicData: [{ label: this.$lang('未处理'), value: 0 }, { label: this.$lang('交易成功'), value: 1 }, { label: this.$lang('交易失败'), value: 2 }]
          }, {
            label: '库存余额',
            prop: 'stockBalance'
          }, {
            label: '流水描述',
            prop: 'traceDesc'
          }, {
            label: '备注',
            prop: 'remark'
          }, {
            label: '创建人',
            prop: 'createByName'
          }

        ]
      }
    }
  },
  created() {
    if (this.$route.query.activeName === 'YJ') {
      // 跳转过来再执行这个函数
      this.detail = this.$route.query
    }
  },
  activated() {
    // 此处解决参数刷新  页面搜索条件不刷新的BUG
    if (this.$route.query.activeName === 'YJ' && this.$route.query.siteCode && String(this.detail._t) !== String(this.$route.query._t)) {
      // 跳转过来再执行这个函数

      this.resetList();
      this.$refs.params.searchReset()
      this.detail = Object.assign({}, this.$route.query)
      this.$nextTick(() => {
        // 此处解决参数刷新  页面搜索条件不刷新的BUG
        this.$refs.params.searchForm.siteCode = this.detail.siteCode
        // 触发搜索
        this.searchChange(this.$refs.params.searchForm)
      })
    }
  },
  methods: {
    // 列表查询参数处理
    searchFunParamsHandle_after(params) {
      // 流水号
      if (this.formData.traceId && this.formData.traceId.length) {
        const p_traceId = this.formData.traceId.join()
        // 判断流水号输入超过500条
        const traceIdArray = p_traceId.split(',')
        if (traceIdArray.length > 500) {
          this.$message.error(this.$lang('流水号超过500条'))
          return false
        }
        if (isNaN(p_traceId.replace(/,/g, ''))) {
          this.$message.error(this.$lang('流水号为全数字'))
          return false
        }
        params = {
          startTime: params.startTime,
          endTime: params.endTime,
          branchCode: this.user.networkCode,
          traceId: p_traceId
        }
      }
      return params
    },
    setSearchParams(params) {
      const { endTime } = this.$refs.params.searchForm
      params.endTime = dayjs(endTime).format('YYYY-MM-DD') + ' 23:59:59'
      return params
    },
    searchAfterFun() {
      // 获取数据后为每一列添加自定义属性，决定手机号一栏icon的样式
      if (this.tableList && this.tableList.length > 0) {
        this.tableList = [...this.tableList.map(item => ({ ...item, isOpen: false, warningPhoneNew: '', warningPhoneOld: '' }))]
      }
    },
    // 清空搜索条件
    resetList() {
      if (this.detail.siteCode) {
        this.detail.siteCode = ''
      }
      this.$refs.params.searchForm.siteCode = this.user.networkCode
      this.formData = {
        traceId: []
      }
    },
    EditClick(row, index) {
      console.log(row, index)
      this.id = row.id || '';
      this.mark = row.remark || '';
      this.dialogVisible = true;
    },
    async saveEdit() {
      // 设置备注
      this.editLoad = true
      const params = { id: this.id, remark: this.mark }
      const { code, msg } = await this.COM_HTTP.reqEdit(params)
      if (code === RESPONSE_CODE.SUCCESS) {
        interfaceWaitHandle(() => {
          this.editLoad = false
          this.dialogVisible = false
          this.$message.success(msg)
          this.searchFun()
        })
      } else {
        this.editLoad = false
        this.dialogVisible = false
        this.$message.error(msg)
      }
    }

  }
}
</script>

<style scoped lang="scss"></style>
