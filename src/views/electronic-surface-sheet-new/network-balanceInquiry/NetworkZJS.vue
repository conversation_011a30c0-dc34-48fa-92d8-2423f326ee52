/**
 * @description: 紫金山(平台网点面单库存)
 * <AUTHOR>
 */
<template>
  <div class="main-wrap operator-wrap">
    <avue-crud
    ref="params"
      :data="tableList"
      :option="listOpt"
      :page="page"
      :table-loading="loading"
      @row-save="addFun"
      @row-update="updateFun"
      @size-change="sizeChange"
      @current-change="currentChange"
      @search-reset="resetList"
      @search-change="searchChange"
      @export-excel="outExcel"
      @row-del="deleteView">
      <template slot="menuLeft">
        <el-button type="info" size="small" @click="dialogOutputVisible = true" v-if="hasPower('OUTPUT')" :loading="loadingOutput">{{$lang('下载中心')}}</el-button>
      </template>
      <template slot-scope="{row}" slot="menu">
        <button v-if="hasPower('RECORD')" class="el-button el-button--text el-button--small" :title="$lang('流水记录')" @click.stop="$router.push({name:'flowRecordPlatformOutlet',query:{activeName:'ZJS',siteCode:row.siteCode,siteName:row.siteName,_t:new Date().getTime()}})" type="button">{{$lang('流水记录')}}</button>
        <button  v-if="hasPower('EDIT')" class="el-button el-button--text el-button--small" :title="$lang('库存功能设置')" @click.stop="openWarningDialog(row)" type="button">{{$lang('功能设置')}}</button>
      </template>
      <!-- <template slot="materialName">
        {{$lang('紫金山电子面单')}}
      </template> -->
      <template slot="warningPhone" slot-scope="{row}">
        <!-- iconyincang -->
        <div class="toggleIcon"><span>{{row.warningPhone || '--'}}</span><i
          class="icon-mobile icon-iconfont iconfont " :class="row.isOpen ? 'iconxianshimima' : 'iconyincang'"
          @click="mobileClick(row)"
        ></i></div>
      </template>
      <template slot="isSmsReminder" slot-scope="{row}">
        <span v-if="row.isSmsReminder === 1"> {{$lang('是')}}</span>
        <span v-else-if="row.isSmsReminder === 0"> {{$lang('否')}}</span>
      </template>
  </avue-crud>
  <!-- 库存功能设置 -->
  <el-dialog lock-scroll
            :modal-append-to-body="false"
            append-to-body
            :title="$lang('库存功能设置（紫金山）')"
            :width="dialogWidth"
            custom-class="batch-dialog warn-dialog"
            :visible.sync="warnDialogVisible"
            :styleType="'noBorder'"
            :close-on-click-modal="false"
            @closed="closeDialog">
            <div ref="content" v-loading="$parent.dialogLoading">
              <el-scrollbar style="height:100%">
                <el-form ref="warnForm" :rules="rules" :model="warnForm" label-position="top">
                  <el-form-item :label="$lang('网点名称：')" prop="networkName">
                    <el-input disabled v-model="warnForm.networkName" auto-complete="off"></el-input>
                  </el-form-item>
                  <el-form-item :label="$lang('预警库存数量：')" prop="warningQuantity">
                      <el-input v-model="warnForm.warningQuantity" :placeholder="$lang('请输入')" auto-complete="off"></el-input>
                    </el-form-item>
                    <br/>
                    <el-form-item :label="$lang('短信提醒')">
                      <el-switch
                        v-model="warnForm.isSmsReminder"
                        @change="isSmsReminderChange">
                      </el-switch>
                    </el-form-item>
                    <el-form-item :label="$lang('预警手机号：')" prop="warningPhone">
                      <el-input v-model="warnForm.warningPhone" auto-complete="off"></el-input>
                    </el-form-item>
                    <br/>
                  <el-form-item :label="$lang('最大库存数量：')" prop="maxStockNum">
                    <span class="tipsImg" :title="$lang('加盟商及以上层级可编辑')">
                        <i class="icon-iconfont iconfont iconshujujiacang-tishi font-16"></i>
                      </span>
                    <el-input :disabled="!isEdit" v-model="warnForm.maxStockNum" auto-complete="off"></el-input>
                  </el-form-item>
                </el-form>
              </el-scrollbar>
            </div>
            <span slot="footer"
              class="dialog-footer">
              <el-button :size="$parent.controlSize"  @click="closeDialog" :title="$lang('取消')">{{$lang('取 消')}}</el-button>
              <el-button type="primary"
                        :size="$parent.controlSize"
                        :title="$lang('保存')"
                        :loading="keyBtn"
                        @click="warnSubmitForm"
                        >{{$lang('保 存')}}</el-button>
            </span>
    </el-dialog>
  <ExportDownload :moduleNames="moduleNames" :moduleType="moduleType"  :dialogOutputVisible.sync="dialogOutputVisible"/>
  </div>
</template>
<script>
import mixin from '@mixins/mixin'
import sheetNewMixin from '@/common/utils/sheetNewMixin'
import { NetworkBalanceInquiryZJS_new } from '../../pool'
import { RULES } from '@/common/utils/validators'
import { RESPONSE_CODE } from '@public/http/config'
import debounce from 'lodash/debounce'
import { interfaceWaitHandle } from '@public/utils/common'
import ExportDownload from '@/components/ExportDownloadSheet'
export default {
  name: 'NetworkPDD',
  mixins: [mixin, sheetNewMixin],
  components: {
    ExportDownload
  },
  data() {
    return {
      COM_HTTP: NetworkBalanceInquiryZJS_new,
      isShowFirst: false, // 阻止查询方法第一时间调用
      warnDialogVisible: false,
      dialogOutputVisible: false, // 导出下载中心弹窗
      loadingOutput: false,
      moduleNames: 'dy_site_stock',
      moduleType: this.$lang('平台网点库存(紫金山)'),
      isEdit: true, // 是否属于总部、代理、网点级别
      tableList: [],
      keyBtn: false, // 保存loading效果
      warnForm: {// 预警设置弹窗初始化参数
        id: '',
        networkName: '',
        warningQuantity: null,
        isSmsReminder: false, // 短信提醒
        warningPhone: '',
        maxStockNum: 0
      }
    }
  },
  created() {
    const { NT_HEADQUARTERS, NT_AGENT_AREA, NT_CENTER } = this.organLevelObj
    const jurArray = [NT_HEADQUARTERS, NT_AGENT_AREA, NT_CENTER]
    this.isEdit = jurArray.includes(this.user.institutionalLevelId)
  },
  mounted () {
    this.$nextTick().then(async (res) => {
      this.searchFun()
    })
  },
  computed: {
    rules() {
      const warningQuantity1 = [RULES.integer, RULES.required, RULES.max999999999]
      const warningPhone1 = [RULES.phoneOrChMobile]
      if (this.warnForm.isSmsReminder) {
        warningQuantity1.push(...[RULES.moreZeroInteger])
        warningPhone1.push(...[RULES.required])
      }
      return {
        warningQuantity: warningQuantity1,
        maxStockNum: [RULES.integer, RULES.max999999999],
        warningPhone: warningPhone1
      }
    },
    listOpt() {
      const that = this
      return {
        editBtn: false,
        header: true,
        menuWidth: 150, // 菜单操作宽度
        fixHeight: 50, // 底部显示高度
        exportBtn: this.hasPower('OUTPUT'),
        column: [
          {
            label: '网点名称',
            prop: 'siteName'
          },
          {
            label: '网点编码',
            labelAlias: '网点名称',
            prop: 'siteCode',
            type: 'network',
            rules: [RULES.required],
            searchClearable: false,
            remoteQuery: {
              queryLevel: 3
            },
            // senior: false, // 标识高级搜索字段
            props: {
              label: 'name',
              value: 'code'
            },
            selectDic: [{ id: that.user.networkId, name: that.user.networkName, code: that.user.networkCode }],
            searchDefault: that.user.networkCode,
            // 使用当前用户登录的网点信息
            useDetailForm: true,
            useCurNetworkInfo: true,
            // // 其他表单中的禁用
            search: true,
            sort: 0
          },
          {
            label: '面单类型',
            prop: 'materialName',
            formatter: ({ materialName }) => {
              return that.$lang('紫金山电子面单')
            }
          },
          {
            label: '库存余额',
            prop: 'stockCount'
          },
          {
            label: '预警库存数量',
            prop: 'warningQuantity'
          },
          {
            label: '短信提醒',
            prop: 'isSmsReminder',
            slot: true
          },
          {
            label: '预警手机号',
            prop: 'warningPhone',
            slot: true,
            width: 150
          },
          {
            label: '最大库存数量',
            prop: 'maxStockNum'
          }
        ]
      }
    }
  },
  methods: {
    /**
     * 短信提醒
     */
    isSmsReminderChange(val) {
      this.warnForm.isSmsReminder = val
      if (val) {
        this.$message.warning(this.$lang('短信提醒已开启'))
      } else {
        this.$message.warning(this.$lang('短信提醒已关闭'))
        this.warnForm.warningPhone = ''
      }
    },
    /**
     * 预警弹窗
     */
    openWarningDialog(row) {
      if (row.isSmsReminder) {
        this.rules.warningPhone.push(...[RULES.required])
      }
      console.log(row);
      if (!row.warningPhoneNew) {
        this.warnForm.warningPhone = '';
        this.$nextTick(() => {
          this.getWarningMoble(row)
        })
      } else {
        this.warnForm.warningPhone = row.warningPhoneNew
      }
      this.warnForm.id = row.id
      this.warnForm.networkName = row.siteName
      this.warnForm.maxStockNum = row.maxStockNum || 0
      this.warnForm.warningQuantity = (row.warningQuantity || 0).toString()
      this.warnForm.isSmsReminder = row.isSmsReminder !== 0
      this.warnDialogVisible = true
    },
    async getWarningMoble(row) {
      const params = {
        id: row.id
      }
      const { code, data, msg } = await this.COM_HTTP.getMobile(params)
      if (code === RESPONSE_CODE.SUCCESS) {
        this.warnForm.warningPhone = data.warningPhone
        this.warnForm.maxStockNum = data.maxStockNum || 0
      } else {
        this.$message.error(msg)
      }
    },
    closeDialog() {
      this.warnDialogVisible = false;
      this.$refs.warnForm.resetFields();
      this.warnForm.isSmsReminder = false
      this.warnForm.warningPhone = ''
      if (this.rules.warningPhone.length > 1) {
        this.rules.warningPhone.pop()
      }
    },
    /**
     * 库存功能设置保存
     *  */
    async warnSubmitForm() {
      this.$refs.warnForm.validate((valid) => {
        if (!valid) {
          return
        }
        try {
          if (valid) {
            this.keyBtn = true;
            const paramData = {
              ...this.warnForm,
              isSmsReminder: this.warnForm.isSmsReminder ? 1 : 0
            }
            this.warnSubmitAction(paramData)
          }
        } catch (err) {
          this.keyBtn = false;
          console.log('err', 222222);
        }
      });
    },
    async warnSubmitAction(paramData) {
      const { code, msg } = await this.COM_HTTP.updateWarningNetworkPDD(paramData)
      if (code === RESPONSE_CODE.SUCCESS) {
        interfaceWaitHandle(() => {
          this.$refs.params.selectClear()
          this.closeDialog()
          this.$message.success(msg)
          this.keyBtn = false
          this.searchFun()
        })
      } else {
        this.$message.error(msg)
        this.keyBtn = false;
      }
    },
    searchAfterFun() {
      // 获取数据后为每一列添加自定义属性，决定手机号一栏icon的样式
      if (this.tableList && this.tableList.length > 0) {
        this.tableList = [...this.tableList.map((item) => ({ ...item, isOpen: false, warningPhoneNew: '', warningPhoneOld: '' }))]
      }
    },
    // 点击眼睛触发方法
    mobileClick: debounce(async function(row) {
      // 如果是手机号为空白，那应该终止程序往下执行
      if (!row.warningPhone) return this.$message.warning(this.$lang('该条数据手机号为空'))
      // 眼睛是闭着的，可以发送请求,可以先判断contactPhoneNew的长度，如果长度大于0,说明之前请求过真实的手机号，可以直接用
      if (!row.isOpen) {
        const params = {
          id: row.id
        }
        if (row.warningPhoneNew.length !== 0) {
          row.isOpen = !row.isOpen
          row.warningPhone = row.warningPhoneNew
        } else {
          // 眼睛是睁开的，可以发送请求
          const { code, data, msg } = await this.COM_HTTP.getMobile(params)
          if (code === RESPONSE_CODE.SUCCESS) {
          // 请求成功了
            row.isOpen = !row.isOpen
            row.warningPhoneOld = row.warningPhone // 把加密的手机号的先保存起来
            row.warningPhoneNew = data.warningPhone // 详情接口拿到的真实手机号先存起来
            row.warningPhone = data.warningPhone // 显示真实手机号
          } else {
            this.$message.error(msg)
          }
        }
      } else {
        // 眼睛睁开说明页面上是真实的手机号，这时候前端做一些处理就好了，直接取之前保存的加密手机号
        row.warningPhone = row.warningPhoneOld
        row.isOpen = !row.isOpen
      }
    }, 500)

  }
}
</script>
<style lang="scss" scoped>
.operator-wrap {
  padding: 0px;
}
.toggleIcon{
    .icon-mobile{
      @include base-color("clr");
      cursor: pointer;
      float: right;
    }
}
</style>
<style lang="scss">
  .warn-dialog .el-form{
    padding-bottom: 20px;
    .tipsImg{
      position: absolute;
      top: -32px;
      left: 105px;
    }
  }
  .batch-dialog{
    .el-form-item{
      width: 200px;
      margin-bottom: 0;
      margin: 10px;
      display: inline-block;
      vertical-align: top;
    }
    .el-radio-group{
      display:inline-flex;
      .el-radio{
        margin-right:20px;
      }
    }
    .el-textarea__inner{
      height: 100% !important;
    }
    .dialog-footer {
      padding-right: 0 !important;
      margin-bottom: 0 !important;
    }
  }
</style>
