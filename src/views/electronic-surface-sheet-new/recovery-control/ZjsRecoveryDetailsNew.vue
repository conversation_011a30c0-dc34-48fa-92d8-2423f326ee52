<!--
 * @Author: hongye
 * @Date: 2020-09-27 15:29:19
 * @Description:紫金山回收明细
-->
<template>
  <div class="main-wrap">
    <avue-crud ref="params" :data="tableList" :option="listOpt" :page="page" :table-loading="loading" class="mw-tab" dialogClass="cause-maintenance"
      v-model="formData" @row-save="addFun" @row-update="updateFun" @size-change="sizeChange" @current-change="currentChange" @search-reset="resetList"
      @search-change="searchChange" @export-excel="outExcel" @row-del="deleteView">
      <template slot="menuLeft">
        <el-button type="info" size="small" @click="dialogOutputVisible = true" v-if="hasPower('OUTPUT')" :loading="loadingOutput">{{$lang('下载中心')}}</el-button>
      </template>
      <template slot="billCodeSearch">
        <base-input-tag @input="billCodeChange" v-model="formData.billCode" v-if="showBillCode" :addTagOnBlur="true" :clearBtnVisible="true"
          validate="orderName" class="cms-input-tag">
        </base-input-tag>
        <el-input v-else :disabled="true"></el-input>
      </template>
      <template slot="traceIdSearch">
        <base-input-tag @input="traceIdChange" v-model="formData.traceId" v-if="showtraceId" :addTagOnBlur="true" :clearBtnVisible="true"
          validate="orderName" class="cms-input-tag">
        </base-input-tag>
        <el-input v-else :disabled="true"></el-input>
      </template>
    </avue-crud>
    <ExportDownload :moduleNames="moduleNames" :moduleType="moduleType" :dialogOutputVisible.sync="dialogOutputVisible" />
  </div>
</template>
<script>
import mixin from '@mixins/mixin'
import sheetNewMixin from '@/common/utils/sheetNewMixin'
import dayjs from 'dayjs'
import { ZjsRecoveryDetailsNew } from '../../pool'
import { commonFun } from '@public/utils/common'
import { RULES } from '@/common/utils/validators'
import ExportDownload from '@/components/ExportDownloadSheet'
const [startTime, endTime] = commonFun.GetTodayDate()
export default {
  name: 'ZjsRecoveryDetailsNew',
  mixins: [mixin, sheetNewMixin],
  components: {
    ExportDownload
  },
  data() {
    return {
      COM_HTTP: ZjsRecoveryDetailsNew,
      tableList: [],
      dialogOutputVisible: false, // 导出下载中心弹窗
      loadingOutput: false,
      moduleNames: 'dy_recycle_detail',
      moduleType: this.$lang('紫金山回收明细'),
      formData: {
        endTime: endTime,
        startTime: startTime,
        billCode: [],
        traceId: []
      },
      showtraceId: true,
      showBillCode: true
    }
  },
  created() {},
  mounted() {},
  computed: {
    listOpt() {
      const that = this
      return {
        fixHeight: 20, // 底部显示高度
        menuWidth: 120, // 操作宽度
        menu: false,
        exportBtn: this.hasPower('OUTPUT'),
        column: [
          {
            label: '网点名称',
            prop: 'branchName',
            viewDisplay: false // 查看不显示
          },
          {
            label: '网点编码',
            labelAlias: '网点名称',
            prop: 'branchCode',
            type: 'network',
            viewDisplay: false, // 查看不显示
            rules: [RULES.required],
            searchClearable: false,
            remoteQuery: {
              queryLevel: 3
            },
            props: {
              label: 'name',
              value: 'code'
            },
            selectDic: [{ name: that.user.networkName, code: that.user.networkCode }],
            searchDefault: that.user.networkCode,
            // 使用当前用户登录的网点信息
            useCurNetworkInfo: true,
            disabled: true, // 其他表单中的禁用
            search: true,
            sort: 0
          },
          {
            label: '流水号',
            prop: 'traceId',
            searchPlaceholder: '请输入流水号',
            search: true,
            searchslot: true,
            sort: 7
          },
          {
            label: '回收时间',
            labelAlias: '回收开始时间',
            prop: 'startTime',
            viewProp: 'recycleTime',
            type: 'date',
            editable: false,
            relationProps: ['endTime'], // 时间控件关联操作的关键字段 开始时间必须添加此字段，可以在选择开始时间后自动让结束时间获取焦点
            search: true,
            sort: 1,
            searchDefault: startTime,
            searchClearable: false,
            addDisplay: false, // 新增不显示
            format: 'yyyy-MM-dd ',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            pickerOptions: {
              disabledDate: time => {
                const endDate = new Date(new Date(endTime).toLocaleDateString()).getTime()
                const space = 180 * 24 * 3600 * 1000
                const minTime = endDate - space
                const maxTime = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
                return time.getTime() < minTime || time.getTime() > maxTime
              }
            },
            change({ value }) {
              const startVal = new Date(value).getTime()
              const { endTime } = that.$refs.params.searchForm
              const endVal = new Date(endTime).getTime()
              const fifteenDay = startVal + 30 * 24 * 3600 * 1000
              if (endVal > fifteenDay) {
                // 结束时间 在 开始时间30天之后  重置结束时间
                that.$refs.params.searchForm.endTime = new Date(fifteenDay)
              }
            }
          },
          {
            label: '回收结束时间',
            prop: 'endTime',
            type: 'date',
            editable: false,
            searchDefault: endTime,
            searchClearable: false,
            addDisplay: false, // 新增不显示
            viewDisplay: false, // 查看不显示
            editDisplay: false, // 编辑不显示
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            hide: true,
            sort: 2,
            search: true,
            pickerOptions: {
              disabledDate: time => {
                const space = 30 * 24 * 3600 * 1000
                const { startTime } = that.$refs.params.searchForm
                const minTime = startTime ? new Date(startTime).getTime() : new Date().getTime() - space
                const endDate = new Date(new Date(startTime).toLocaleDateString()).getTime() + space
                const maxTime = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
                return time.getTime() < minTime || time.getTime() > maxTime || time.getTime() > endDate
              }
            }
          },
          {
            label: '客户名称',
            prop: 'customerName'
          },
          {
            label: '客户编码',
            labelAlias: '客户名称',
            prop: 'customerCode',
            viewDisplay: false, // 查看不显示
            searchPlaceholder: '请输入关键字查询',
            type: 'select',
            remote: true,
            search: true,
            sort: 3,
            props: {
              label: 'customerName',
              value: 'customerCode'
            },

            tpyeformat: (data) => data.customerName + ' | ' + data.customerCode,
            dicUrl: `esscustomer/dyCustomerStock/getNameList?customerName={{key}}&brandCode=${this.user.networkCode}`
          },
          {
            label: '商家名称',
            prop: 'mallName'
          },
          {
            label: '商家ID',
            labelAlias: '商家名称',
            prop: 'mallId',
            searchPlaceholder: '请输入关键字查询',
            type: 'select',
            remote: true,
            search: true,
            sort: 4,
            props: {
              label: 'accountName',
              value: 'mallId'
            },
            tpyeformat: (data) => data.accountName + ' | ' + data.mallId,
            dicUrl: `esscustomer/dyCustomerStock/getNameList?accountName={{key}}&brandCode=${this.user.networkCode}`
          },
          {
            label: '运单号',
            prop: 'billCode',
            searchPlaceholder: '输入多个运单号请用","分割',
            search: true,
            searchslot: true,
            sort: 5
          },

          {
            label: '取号时间',
            prop: 'getBillCodeTime'
          }
        ]
      }
    }
  },
  methods: {
    // 订单号和流水号只能选择一种
    billCodeChange(val) {
      this.showtraceId = val.length === 0
    },
    traceIdChange(val) {
      this.showBillCode = val.length === 0
    },
    // 列表查询参数处理
    searchFunParamsHandle_after(params) {
      // 流水号
      if (this.formData.traceId && this.formData.traceId.length) {
        const p_traceId = this.formData.traceId.join()
        // 判断流水号输入超过500条
        const traceIdArray = p_traceId.split(',')
        if (traceIdArray.length > 500) {
          this.$message.error(this.$lang('流水号超过500条'))
          return false
        }
        if (isNaN(p_traceId.replace(/,/g, ''))) {
          this.$message.error(this.$lang('流水号为全数字'))
          return false
        }
        params = {
          startTime: params.startTime,
          endTime: params.endTime,
          branchCode: this.user.networkCode,
          traceIds: traceIdArray
        }
      }
      // 运单号
      if (this.formData.billCode && this.formData.billCode.length) {
        const p_billCode = this.formData.billCode.join()
        // 判断运单号输入超过500条
        const billCodeArray = p_billCode.split(',')
        if (billCodeArray.length > 500) {
          this.$message.error(this.$lang('运单号超过500条'))
          return false
        }
        // 判断运单号是否合法
        for (let index = 0; index < billCodeArray.length; index++) {
          const element = billCodeArray[index];
          if (!RULES.waybillNewRule.value.test(element)) {
            this.$message.error(this.$lang(RULES.waybillNewRule.label))
            return false;
          }
        }

        //
        params = {
          startTime: params.startTime,
          endTime: params.endTime,
          branchCode: this.user.networkCode,
          billCodes: billCodeArray
        }
      }
      return params
    },
    setSearchParams(params) {
      const { endTime } = this.$refs.params.searchForm
      params.endTime = dayjs(endTime).format('YYYY-MM-DD') + ' 23:59:59'
      return params
    },
    // 清空搜索条件
    resetList() {
      this.formData = {
        billCode: [],
        traceId: []
      }
      this.showtraceId = true
      this.showBillCode = true
    }
  }
}
</script>
