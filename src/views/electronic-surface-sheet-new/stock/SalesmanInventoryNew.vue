<!--
 * @Author: hongye
 * @Date: 2020-09-22 13:57:38
 * @Description:收派员库存
-->
 <template>
  <div class="main-wrap salesman-wrap">
    <avue-crud
      ref="params"
      :data="tableList"
      :option="listOpt"
      :page="page"
      :table-loading="loading"
      dialogClass="cause-maintenance"
      v-model="formData"
      @row-save="addFun"
      @row-update="updateFun"
      @size-change="sizeChange"
      @current-change="currentChange"
      @search-reset="resetList"
      @search-change="searchChange"
      @export-excel="outExcel"
      @row-del="deleteView">
      <template slot="menuLeft">
        <el-button type="info" size="small" @click="dialogOutputVisible = true" v-if="hasPower('OUTPUT')" :loading="loadingOutput">{{$lang('下载中心')}}</el-button>
        <el-button type="info" size="small" v-if="hasPower('GRANT')" @click.stop="$refs.paymentDialog.dialogShow()">{{$lang('充值发放')}}</el-button>
      </template>
       <template  slot-scope="{row}" slot="menu">
          <template v-if="hasPower('WITHDRAW')">
            <button class="el-button el-button--text el-button--small" @click="withdrawDialog($event,row)" :title="$lang('撤回')" type="button">
              <i class="icon-iconfont iconfont iconchehui font-16"></i>
            </button>
          </template>
          <template v-if="hasPower('RECORD')">
           <button class="el-button el-button--text el-button--small" @click="$router.push({name:'flowRecordSalesman',query:{saleCode:row.saleCode,saleName:row.saleName,_t:new Date().getTime(),materialsCode:row.materialsCode}})" :title="$lang('流水记录')" type="button"><i class="icon-iconfont iconfont iconbiaodan-liushuijilu font-16"></i></button>
          </template>
          <template v-if="hasPower('DETAIL')">
           <button class="el-button el-button--text el-button--small" @click="$router.push({name:'accessDetail',query:{deductObject:1,deductCode:row.saleCode,deductName:row.saleName,_t:new Date().getTime(),materialsCode:row.materialsCode}})"  :title="$lang('面单详情')" type="button"><i class="icon-iconfont iconfont iconbiaodan-miandanxiangqing font-16"></i></button>
          </template>
          <!-- <template v-if="hasPower('EDIT')" >
            <button class="el-button el-button--text el-button--small" @click="openSalesmanDialog(row)" :title="$lang('库存功能设置')" type="button"><i class="icon-iconfont iconfont iconbiaodan-gongnengshezhi font-16"></i></button>
          </template> -->
       </template>
      <template slot="warnMobile" slot-scope="{row}">
        <!-- iconyincang -->
        <div class="toggleIcon"><span>{{row.warnMobile || '--'}}</span><i
          class="icon-mobile icon-iconfont iconfont " :class="row.isOpen ? 'iconxianshimima' : 'iconyincang'"
          @click="mobileClick(row)"
        ></i></div>
      </template>
      <template slot="isSmsPop" slot-scope="{row}">
        <span v-if="row.isSmsPop === 1"> {{$lang('是')}}</span>
        <span v-else-if="row.isSmsPop === 0"> {{$lang('否')}}</span>
      </template>
      <template slot="isAutoCharge" slot-scope="{row}">
        <span v-if="row.isAutoCharge === 1"> {{$lang('是')}}</span>
        <span v-else-if="row.isAutoCharge === 0"> {{$lang('否')}}</span>
      </template>
    </avue-crud>
    <!-- 自定义撤回 -->
    <el-dialog lock-scroll
              :modal-append-to-body="false"
              append-to-body
              :title="$lang('撤回收派员库存')"
              :width="dialogWidth"
              custom-class="batch-dialog warn-dialog"
              :visible.sync="withdrawDialogVisible"
              :styleType="'noBorder'"
              :close-on-click-modal="false"
              @closed="closeDialog">
              <div ref="content" v-loading="$parent.dialogLoading">
                <el-scrollbar style="height:100%">
                  <el-form ref="withdrawForm" :rules="rules" :model="withdrawForm" label-position="top">
                    <el-form-item :label="$lang('网点名称：')" prop="siteName">
                      <el-input disabled v-model="withdrawForm.siteName" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="">
                      <template slot="label">
                        <span :title="$lang('收派员名称：')">{{$lang('收派员名称：')}}</span>
                      </template>
                      <el-input disabled v-model="withdrawForm.saleName" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="">
                      <template slot="label">
                        <span :title="$lang('收派员编号：')">{{$lang('收派员编号：')}}</span>
                      </template>
                      <el-input disabled v-model="withdrawForm.saleCode" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item :label="$lang('库存数量：')" prop="stockBalance">
                      <el-input disabled v-model="withdrawForm.stockBalance" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item :label="$lang('撤回数量：')" prop="grantNum">
                      <el-input  :placeholder="$lang('请输入')" v-model="withdrawForm.grantNum" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item :label="$lang('备注：')" prop="remark" style="display:block;width:640px;">
                      <el-input :placeholder="$lang('请输入')" type="textarea" v-model="withdrawForm.remark" auto-complete="off" style="height:80px;" :showWordLimit="true" maxlength="200"></el-input>
                    </el-form-item>
                  </el-form>
                </el-scrollbar>
              </div>
              <span slot="footer"
                class="dialog-footer">
                <el-button :size="$parent.controlSize" :title="$lang('取消')"  @click="closeDialog">{{$lang('取 消')}}</el-button>
                <el-button type="primary"
                          :loading="keyBtn"
                          :title="$lang('保存')"
                          :size="$parent.controlSize"
                          @click="withdrawSubmitForm"
                          >{{$lang('保 存')}}</el-button>
              </span>
    </el-dialog>
    <ExportDownload :moduleNames="moduleNames" :moduleType="moduleType" :dialogOutputVisible.sync="dialogOutputVisible"/>
    <!-- 库存功能设置 -->
    <salesman-setting ref="salemanSet" :isEdit="isEdit"></salesman-setting>
    <!-- 发放充值 -->
    <recharge-payment-salesman ref='paymentDialog'></recharge-payment-salesman>
  </div>
</template>
<script>
import mixin from '@mixins/mixin'
import sheetNewMixin from '@/common/utils/sheetNewMixin'
import { SalesmanInventory_new } from '../../pool'
import { RESPONSE_CODE } from '@public/http/config'
import { commonFun, interfaceWaitHandle } from '@public/utils/common'
import { RULES } from '@/common/utils/validators'
import debounce from 'lodash/debounce'
import { DICT } from '@/common/utils/dict'
import ExportDownload from '@/components/ExportDownloadSheet'
const [startTime, endTime] = commonFun.GetTodayDate()

// 库存设置
import salesmanSetting from './components/salesmanSetting';
// 充值发放
import rechargePaymentSalesman from './components/rechargePaymentSalesman'
export default {
  name: 'SalesmanInventoryNew',
  mixins: [mixin, sheetNewMixin],
  components: {
    ExportDownload, salesmanSetting, rechargePaymentSalesman
  },
  data() {
    var grantNum = (rule, value, callback) => {
      if (this.withdrawForm.grantNum > this.withdrawForm.stockBalance) {
        callback(new Error(this.$lang('撤回数量需小于收派员库存数量')));
      } else {
        callback();
      }
    }
    return {
      COM_HTTP: SalesmanInventory_new,
      tableList: [],
      keyBtn: false, // 保存loding效果
      dialogOutputVisible: false, // 导出下载中心弹窗
      loadingOutput: false,
      moduleNames: 'ess_stock_sale',
      moduleType: this.$lang('收派员库存'),
      isEdit: true, // 是否属于总部、代理、网点级别
      attributeType: 2, // 1客户 2收派员 3承包区网点
      customerList: {}, // 选中收派员信息
      smsReminderVal: this.$lang('已关闭'),
      withdrawDialogVisible: false, // 默认关闭撤回弹窗
      formData: {
        endTime: endTime,
        startTime: startTime
      },
      withdrawForm: {// 撤回数据初始化
        siteCode: '',
        siteName: '',
        grantNum: null,
        stockBalance: 0,
        saleName: '',
        saleCode: '',
        remark: ''
      },
      rules: {
        warningStockCount: [
          { required: true, message: this.$lang('请输入预警数量'), trigger: ['blur', 'change'] },
          RULES.maxFiveNum
        ],
        grantNum: [
          { required: true, message: this.$lang('请输入撤回值数量'), trigger: 'blur' },
          { validator: grantNum, trigger: ['blur', 'change'] },
          RULES.moreZeroInteger,
          RULES.isNumber
        ]
      }
    }
  },
  created() {
    const { NT_HEADQUARTERS, NT_AGENT_AREA, NT_CENTER } = this.organLevelObj
    const jurArray = [NT_HEADQUARTERS, NT_AGENT_AREA, NT_CENTER]
    this.isEdit = jurArray.includes(this.user.institutionalLevelId)
  },
  mounted () {
    this.$nextTick().then(async (res) => {
      this.searchFun()
    })
  },
  computed: {
    listOpt() {
      const that = this
      return {
        header: true,
        fixHeight: 20, // 底部显示高度
        menuWidth: 250, // 操作宽度
        editBtn: false,
        customEdit: true,
        viewBtn: false, // 不展示查看按钮
        exportBtn: this.hasPower('OUTPUT'),
        column: [
          {
            label: '网点名称',
            prop: 'siteName'
          },
          {
            label: '网点编码',
            labelAlias: '网点名称',
            prop: 'siteCode',
            type: 'network',
            addDisplay: false, // 新增不显示
            searchClearable: false,
            rules: [RULES.required],
            remoteQuery: {
              queryLevel: 3
            },
            props: {
              label: 'name',
              value: 'code'
            },
            selectDic: [{ name: that.user.networkName, code: that.user.networkCode }],
            searchDefault: that.user.networkCode,
            // 使用当前用户登录的网点信息
            useCurNetworkInfo: true,
            search: true,
            sort: 0,
            formatter: ({ siteCode }) => siteCode
          },
          {
            label: '收派员名称',
            prop: 'saleName'
          },
          {
            label: '收派员编码',
            labelAlias: '收派员名称',
            searchPlaceholder: '请输入关键字搜索',
            prop: 'saleCode',
            type: 'select',
            remote: true,
            search: true,
            rules: [RULES.required],
            sort: 1,
            props: {
              label: 'name',
              value: 'code'
            },
            tpyeformat: (data) => (data.name + ' | ' + data.code),
            dicUrl: `${that.$selectUrl.staff}?institutionalLevelId=${that.user.institutionalLevelId}&networkId=${that.user.networkId}&name={{key}}&current=1&size=9999`,
            change(valueObj) {
              that.customerList = valueObj.valueObj
            }
          },
          {
            label: '面单类型',
            search: true,
            type: 'select',
            prop: 'materialsCode',
            dicData: DICT.materialsLsit,
            formatter: (row) => {
              const map = {
                'PMT1M100013': that.$lang('通用电子面单主单'),
                'PMT1T500260': that.$lang('通用电子面单子单')
              }
              return map[row.materialsCode]
            }
          },
          {
            label: '库存数量',
            prop: 'stockBalance'
          }
          // {
          //   label: '预警库存数量',
          //   prop: 'warnNum'
          // },
          // {
          //   label: '短信提醒',
          //   prop: 'isSmsPop',
          //   slot: true
          // },
          // {
          //   label: '预警手机号',
          //   prop: 'warnMobile',
          //   slot: true,
          //   width: 150
          // },
          // {
          //   label: '自动发放',
          //   prop: 'isAutoCharge',
          //   slot: true
          // },
          // {
          //   label: '自动发放数量',
          //   prop: 'autoNum'
          // },
          // {
          //   label: '最大库存数量',
          //   prop: 'maxNum'
          // }
        ]
      }
    }
  },
  methods: {
    // 撤回操作
    withdrawDialog(e, row) {
      this.withdrawForm.siteName = row.siteName
      this.withdrawForm.siteCode = row.siteCode
      this.withdrawForm.saleName = row.saleName
      this.withdrawForm.saleCode = row.saleCode
      this.withdrawForm.stockBalance = row.stockBalance
      this.withdrawForm.materialsCode = row.materialsCode
      this.withdrawDialogVisible = true
    },
    // 关闭弹窗
    closeDialog(value) {
      this.withdrawDialogVisible = false
      this.$refs.withdrawForm.resetFields()
    },
    // 撤回提交
    async withdrawSubmitForm() {
      this.$refs.withdrawForm.validate((valid) => {
        if (!valid) {
          return
        }
        try {
          if (valid) {
            const data = {
              grantCode: this.withdrawForm.saleCode,
              grantName: this.withdrawForm.saleName,
              grantNum: this.withdrawForm.grantNum,
              remark: this.withdrawForm.remark,
              materialsCode: this.withdrawForm.materialsCode,
              grantType: 2, // 发放类型1-充值 2-扣减
              operatorCode: this.user.staffNo, // 创建人编号
              operatorName: this.user.name, // 创建人名称
              siteCode: this.withdrawForm.siteCode, // 所属网点编号
              siteName: this.withdrawForm.siteName // 所属网点编名称
            }
            this.handlerAction(data)
          }
        } catch (err) {
          this.keyBtn = false;
        }
      });
    },
    async handlerAction(data) {
      this.keyBtn = true
      try {
        const { code, msg } = await this.COM_HTTP.grantApi(data)
        if (code === RESPONSE_CODE.SUCCESS) {
          interfaceWaitHandle(() => {
            this.$refs.params.selectClear()
            this.selectedRows = []
            this.closeDialog()
            this.$message.success(msg)
            this.keyBtn = false
            this.searchFun()
          })
        } else {
          this.$message.error(msg)
          this.keyBtn = false
        }
      } catch (err) {
        this.keyBtn = false;
      }
    },
    resetList() {
      this.customerList = ''
    },
    // 混入自定义查询参数
    setSearchParams(params) {
      if (this.page.current > 1 && this.page.total) {
        params.count = this.page.total
      }
      return params
    },
    // 功能设置弹窗
    openSalesmanDialog(row) {
      this.$refs.salemanSet.openWarningDialog(row)
    },

    searchAfterFun() {
      // 获取数据后为每一列添加自定义属性，决定手机号一栏icon的样式
      if (this.tableList && this.tableList.length > 0) {
        this.tableList = [...this.tableList.map((item) => ({ ...item, isOpen: false, warnMobileNew: '', warnMobileOld: '' }))]
      }
    },
    // 点击眼睛触发方法
    mobileClick: debounce(async function(row) {
      // 如果是手机号为空白，那应该终止程序往下执行
      if (!row.warnMobile) return this.$message.warning(this.$lang('该条数据手机号为空'))
      // 眼睛是闭着的，可以发送请求,可以先判断contactPhoneNew的长度，如果长度大于0,说明之前请求过真实的手机号，可以直接用
      if (!row.isOpen) {
        const params = {
          id: row.id
        }
        if (row.warnMobileNew.length !== 0) {
          row.isOpen = !row.isOpen
          row.warnMobile = row.warnMobileNew
        } else {
          // 眼睛是睁开的，可以发送请求
          const { code, data, msg } = await this.COM_HTTP.getMobile(params)
          if (code === RESPONSE_CODE.SUCCESS) {
          // 请求成功了
            row.isOpen = !row.isOpen
            row.warnMobileOld = row.warnMobile // 把加密的手机号的先保存起来
            row.warnMobileNew = data.warnMobile // 详情接口拿到的真实手机号先存起来
            row.warnMobile = data.warnMobile // 显示真实手机号
          } else {
            this.$message.error(msg)
          }
        }
      } else {
        // 眼睛睁开说明页面上是真实的手机号，这时候前端做一些处理就好了，直接取之前保存的加密手机号
        row.warnMobile = row.warnMobileOld
        row.isOpen = !row.isOpen
      }
    }, 500)

  }
}
</script>
<style lang="scss" scoped>
.toggleIcon{
    .icon-mobile{
      @include base-color("clr");
      cursor: pointer;
      float: right;
    }
}
</style>
<style lang="scss">
.warn-dialog .el-form{
      padding-bottom: 20px;
      .el-form-item{
        .el-form-item__label{
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 100%;
        }
      }
      // .tipsImg{
      //   position: absolute;
      //   top: -32px;
      //   left: 105px;
      // }
    }
.batch-dialog {
  .el-form-item {
    width: 200px;
    margin-bottom: 0;
    margin: 10px;
    display: inline-block;
    vertical-align: top;
  }
  .el-radio-group {
    display: inline-flex;
    .el-radio {
      margin-right: 20px;
    }
  }
  .el-checkbox-group {
    padding-left: 10px;
    border: 1px solid rgba(221, 221, 221, 1);
    border-radius: 4px;
    width: 640px;
    height: 80px;
    overflow: auto;
  }
  .dialog-footer {
    padding-right: 0 !important;
    margin-bottom: 0 !important;
  }
  .el-textarea__inner{
    min-height:80px !important;
  }
}
</style>
