/**
 * @description: 原因维护
 * @author: hls
 */
<template>
  <div class="main-wrap">
    <avue-crud
  ref="params"
    :data="tableList"
    :option="listOpt"
    :page="page"
    :table-loading="loading"
    dialogClass="cause-maintenance"
    v-model="formData"
    @row-save="addFun"
    @row-update="updateFun"
    @size-change="sizeChange"
    @current-change="currentChange"
    @search-reset="resetList"
    @search-change="searchChange"
    @row-del="deleteView">
</avue-crud>
  </div>
</template>
<script>
import mixin from '@mixins/mixin'
import dayjs from 'dayjs'
import { CauseMaintenance } from '../pool'
import { commonFun } from '@public/utils/common'
import { RULES } from '@/common/utils/validators'
const [startTime, endTime] = commonFun.GetTodayDate()
export default {
  name: 'CauseMaintenance',
  mixins: [mixin],
  data() {
    return {
      COM_HTTP: CauseMaintenance,
      tableList: [],
      formData: {
        endTime: endTime,
        startTime: startTime
      },
      platFormList: [
        {
          label: this.$lang('桃花岛'),
          value: 0
        },
        {
          label: this.$lang('云集'),
          value: 1
        }
      ]
    }
  },
  created() {},
  mounted() {},
  computed: {
    listOpt() {
      const that = this
      return {
        header: true,
        fixHeight: 20, // 底部显示高度
        menuWidth: 120, // 操作宽度
        // addBtn: false, // 隐藏默认新加按钮
        viewBtn: false,
        // viewUrl: `${GATEWAY.CUS}/ebcssRejectedCode/detail`,
        column: [
          {
            label: '创建时间',
            labelAlias: '创建开始日期',
            prop: 'startTime',
            viewProp: 'updateTime',
            formSort: 5, // 显示位置
            disabled: true,
            type: 'date',
            relationProps: ['endTime'], // 时间控件关联操作的关键字段 开始时间必须添加此字段，可以在选择开始时间后自动让结束时间获取焦点
            search: true,
            searchDefault: dayjs(new Date().getTime() - 7 * 24 * 3600 * 1000).format('YYYY-MM-DD') + ' 00:00:00',
            searchClearable: false,
            addDisplay: false, // 新增不显示
            editDisplay: false, // 编辑不显示
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            pickerOptions: {
              disabledDate: time => {
                const that = this
                const { endTime } = that.$refs.params.searchForm
                const endDate = new Date(new Date(endTime).toLocaleDateString()).getTime()
                const space = 90 * 24 * 3600 * 1000
                const minTime = endDate - space
                const maxTime = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
                return time.getTime() < minTime || time.getTime() > maxTime
              }
            }

          },
          {
            label: '发放结束日期',
            prop: 'endTime',
            type: 'date',
            searchDefault: endTime,
            searchClearable: false,
            addDisplay: false, // 新增不显示
            editDisplay: false, // 编辑不显示
            viewDisplay: false,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            hide: true,
            search: true,
            pickerOptions: {
              disabledDate: time => {
                const space = 90 * 24 * 3600 * 1000
                const { startTime } = that.$refs.params.searchForm
                const minTime = startTime ? new Date(startTime).getTime() : new Date().getTime() - space
                const endDate = new Date(new Date(startTime).toLocaleDateString()).getTime() + space
                const maxTime = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
                return time.getTime() < minTime || time.getTime() > maxTime || time.getTime() > endDate
              }
            }
          }, {
            label: '平台',
            prop: 'sourceType',
            type: 'select',
            hasAll: false,
            formSort: 0,
            dicData: this.platFormList,
            valueDefault: 0,
            searchDefault: 0,
            // placeholder: this.$lang('请选择'),
            search: true,
            rules: [RULES.required]
          }, {
            label: '原因代码',
            prop: 'rejectedCode',
            placeholder: this.$lang('请输入'),
            formSort: 1,
            nextLine: true,
            rules: [RULES.required]
          }, {
            label: '原因描述',
            labelAlias: '原因',
            prop: 'rejectedDesc',
            minRows: 5, // 最小行（当 type 为 textarea）
            formSort: 2, // 显示位置
            span: 2,
            placeholder: this.$lang('请输入'),
            type: 'textarea',
            nextLine: true, // 默认显示一行
            showWordLimit: true, // 显示下标字数
            maxlength: 50,
            rules: [RULES.required]
          }, {
            label: '备注',
            prop: 'remark',
            minRows: 5, // 最小行（当 type 为 textarea）
            formSort: 3, // 显示位置
            span: 3,
            hide: true,
            type: 'textarea',
            placeholder: this.$lang('请输入'),
            nextLine: true, // 默认显示一行
            showWordLimit: true, // 显示下标字数
            maxlength: 200
            // rules: [RULES.required]
          }, {
            label: '创建人',
            prop: 'updateByName',
            addDisplay: false, // 新增不显示
            editDisplay: false, // 编辑不显示
            formSort: 4, // 显示位置
            disabled: true
          }
        ]
      }
    }
  },
  methods: {
    // 混入自定义查询参数
    setSearchParams(params) {
      const { endTime } = this.$refs.params.searchForm
      params.endTime = dayjs(endTime).format('YYYY-MM-DD') + ' 23:59:59'
      return params
    }
  }
}
</script>
<style lang="scss">
.cause-maintenance {
  .el-textarea__inner{
    height: 80px !important;
    min-height: 80px !important;
  }
  .dialog-line{
    width: 100% !important;
    visibility:hidden;
    margin-bottom: -1px;
  }
}

</style>
