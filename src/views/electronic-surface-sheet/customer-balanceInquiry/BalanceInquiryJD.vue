/**
 * @description: 京东(平台客户面单库存)
 * <AUTHOR>
 */
<template>
  <avue-crud
  ref="params"
    :data="tableList"
    :option="listOpt"
    :page="page"
    :table-loading="loading"
    @row-save="addFun"
    @row-update="updateFun"
    @size-change="sizeChange"
    @current-change="currentChange"
    @search-reset="resetList"
    @search-change="searchChange"
    @row-del="deleteView">
</avue-crud>

</template>
<script>
import mixin from '@mixins/mixin'
import { CustomerBalanceInquiry } from '../../pool'
import { RULES } from '@/common/utils/validators'
import { RESPONSE_CODE } from '@public/http/config'
import { GATEWAY } from '@public/config/index'
import dayjs from 'dayjs'
// import { PDD_TO_THD } from '@/common/utils/pddFetch'
export default {
  name: 'BalanceInquiryJD',
  mixins: [mixin],
  data() {
    // const $this = this
    return {
      COM_HTTP: CustomerBalanceInquiry,
      tableList: [],
      isShowFirst: false, // 阻止查询方法第一时间调用
      materialData: {} // 物料详情 默认桃花岛
    }
  },
  created() {},
  mounted () {
    this.$nextTick().then(async (res) => {
      await this.getRegularWaybillMaterialId()
      this.searchFun()
    })
  },
  computed: {
    listOpt() {
      const that = this
      return {
        header: true,
        fixHeight: 50, // 底部显示高度
        // menu: false,
        column: [
          {
            label: '商家ID',
            prop: 'mallId'
          }, {
            label: '商家名称',
            prop: 'accountName'
          }, {
            label: '网点',
            prop: 'branchName'
          }, {
            label: '可用余额',
            prop: 'quantity'
          }, {
            label: '联系电话',
            search: true,
            hide: true,
            sort: 2,
            prop: 'contactPhone'
          }, {
            label: '网点编号/名称',
            prop: 'networkCode',
            type: 'network',
            rules: [RULES.required],
            remoteQuery: {
              queryLevel: 3
            },
            // senior: false, // 标识高级搜索字段
            props: {
              label: 'name',
              value: 'id'
            },
            sort: 0,
            selectDic: [{ id: that.user.networkId, name: that.user.networkName, code: that.user.networkCode }],
            searchDefault: that.user.networkId,
            change({ valueObj }) {
              that.$refs.params.searchForm.networkCode = valueObj.code
            },
            // 使用当前用户登录的网点信息
            // useDetailForm: true,
            useCurNetworkInfo: true,
            // 其他表单中的禁用
            disabled: true,
            search: true,
            hide: true
          }, {
            label: '客户名称',
            prop: 'accountName',
            type: 'select',
            remote: true,
            search: true,
            hide: true,
            sort: 1,
            props: {
              label: 'accountName',
              value: 'accountName',
              res: 'records'
            },
            // tpyeformat: (data) => PDD_TO_THD(data.name),
            dicUrl: `${GATEWAY.CUS}/ebcssCustomerVerify/getNameList?accountName={{key}}&branchCode=${this.user.networkId}&current=1&size=9999`
          }, {
            label: '累计已分配的面单数量',
            prop: 'allocatedQuantity'
          }, {
            label: '回收的面单数量',
            prop: 'recycledQuantity'
          }, {
            label: '取消的面单数量',
            prop: 'cancelQuantity'
          }, {
            label: '更新时间',
            prop: 'updateTime'
          }
        ]
      }
    }
  },
  methods: {
    // 空数据
    async searchFun(params, current) {
    },
    // 查询面单物料id
    async getRegularWaybillMaterialId() {
      const { code, data, msg } = await this.COM_HTTP.getMaterialId({ current: 1, size: 9999 })
      if (code === RESPONSE_CODE.SUCCESS) {
        data.records.forEach(element => {
        // 桃花岛电子面单
          if (element.code === 'PMT1M400016') {
            this.materialData = element
          }
        });
      } else {
        this.$message.error(msg)
      }
    },
    // 混入自定义查询参数
    setSearchParams(params) {
      const { endTime } = this.$refs.params.searchForm
      params.attributeType = this.attributeType
      params.materialId = this.materialData.id
      params.endTime = dayjs(endTime).format('YYYY-MM-DD') + ' 23:59:59'
      return params
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
