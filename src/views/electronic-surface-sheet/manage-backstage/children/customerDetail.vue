/**
 * @description: 流失用户明细
 * @author: hls
 */
<template>
  <div class="lost-users">
    <avue-crud ref="params" :data="tableList" v-model="formData" :option="listOpt" :page="page" :table-loading="loading" @size-change="sizeChange"
    @search-reset="resetList" @current-change="currentChange" @search-change="searchChange" @export-excel="outExcel">
    <template slot="mobile" slot-scope="{row}">
        <!-- iconyincang -->
        <div class="toggleIcon"><span>{{row.mobile|| '--'}}</span><i
          class="icon-mobile icon-iconfont iconfont " :class="row.isOpen ? 'iconxianshimima' : 'iconyincang'"
          @click="mobileClick(row)"
        ></i></div>
      </template>
  </avue-crud>
  </div>
</template>

<script>
import mixin from '@mixins/mixin'
import dayjs from 'dayjs'
import debounce from 'lodash/debounce'
import { RESPONSE_CODE } from '@public/http/config'
import { CustomerAnalysis } from '../../../pool'

export default {
  name: 'CustomerAnalysis',
  mixins: [mixin],
  props: {
    detail: {},
    detailType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      COM_HTTP: {
        reqList: this.detailType === 1 ? CustomerAnalysis.reqListLostUsers : (this.detailType === 2 ? CustomerAnalysis.reqListAddUsers : CustomerAnalysis.reqListAddLostUsers)
      },
      formData: {},
      loading: false, // 表格loading状态
      loadingOutput: false
    }
  },
  created() {
    this.init()
  },
  watch: {
    'detail'(newValue, oldValue) {
      if (newValue.detailType !== oldValue.detailType) {
        this.page.current = 1
        this.page.size = 20
      }
      this.formData = {}
      this.detailType = newValue.detailType
      this.COM_HTTP.reqList = this.detailType === 1 ? CustomerAnalysis.reqListLostUsers : (this.detailType === 2 ? CustomerAnalysis.reqListAddUsers : CustomerAnalysis.reqListAddLostUsers)
      if (this.detailType === 2 || this.detailType === 3) { this.init() }
      this.canUsePrevListParam = false
      this.searchFun()
    }
  },
  methods: {
    searchFunParamsHandle(parms) {
      const newParm = Object.assign(this.page, parms, this.formData)
      if (this.detailType === 1) {
        delete newParm.startCreatedTime
        delete newParm.endCreatedTime
      } else {
        const { endCreatedTime } = newParm
        newParm.endCreatedTime = dayjs(endCreatedTime).format('YYYY-MM-DD') + ' 23:59:59'
      }
      return newParm
    },
    init() {
      // 赋值传进来的参数
      const arr = ['startCreatedTime', 'endCreatedTime']
      Object.keys(this.detail).forEach(item => {
        if (arr.includes(item) && this.detail[item]) {
          this.formData[item] = this.detail[item]
        }
      })
    },
    searchAfterFun() {
      // 获取数据后为每一列添加自定义属性，决定手机号一栏icon的样式
      if (this.tableList && this.tableList.length > 0) {
        this.tableList = [...this.tableList.map((item) => ({ ...item, isOpen: false, contactPhoneNew: '', contactPhoneOld: '' }))]
      }
    },
    // 点击眼睛触发方法
    mobileClick: debounce(async function(row) {
      // 如果是手机号为空白，那应该终止程序往下执行
      if (!row.mobile) return this.$message.warning('该条数据手机号为空')
      // 眼睛是闭着的，可以发送请求,可以先判断contactPhoneNew的长度，如果长度大于0,说明之前请求过真实的手机号，可以直接用
      if (!row.isOpen) {
        const params = {
          id: row.id
        }
        if (row.contactPhoneNew.length !== 0) {
          row.isOpen = !row.isOpen
          row.mobile = row.contactPhoneNew
        } else {
          // 眼睛是睁开的，可以发送请求
          const { code, data, msg } = await CustomerAnalysis.getMobile(params)
          if (code === RESPONSE_CODE.SUCCESS) {
          // 请求成功了
            row.isOpen = !row.isOpen
            row.contactPhoneOld = row.mobile // 把加密的手机号的先保存起来
            row.contactPhoneNew = data.mobile // 详情接口拿到的真实手机号先存起来
            row.mobile = data.mobile // 显示真实手机号
          } else {
            this.$message.error(msg)
          }
        }
      } else {
        // 眼睛睁开说明页面上是真实的手机号，这时候前端做一些处理就好了，直接取之前保存的加密手机号
        row.mobile = row.contactPhoneOld
        row.isOpen = !row.isOpen
      }
    }, 500)
  },
  mounted() {},
  computed: {
    listOpt() {
      return {
        addBtn: false,
        menu: false,
        header: true,
        border: true,
        pagination: true,
        // fixHeight: 0,
        column: [
          {
            label: '兔兔用户名',
            prop: 'accountName'
          },
          {
            label: '兔兔用户账号',
            prop: 'accountId'
          },
          {
            label: '兔兔手机号',
            prop: 'mobile',
            slot: true
          },
          {
            label: '注册时间',
            prop: 'createTime'
          },
          {
            label: '最近一次打单时间',
            prop: 'lastPrintTime'
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss">
.lost-users{
  .top-menu{
    display: none;
  }
  .table-search{
    display: none;
  }
}
</style>
<style lang="scss" scoped>
.toggleIcon{
    .icon-mobile{
      @include base-color("clr");
      cursor: pointer;
      float: right;
      margin-right: 105px;
    }
}
</style>
