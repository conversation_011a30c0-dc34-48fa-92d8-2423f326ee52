/**
 * @description: 云集(平台网点面单库存)
 * <AUTHOR>
 */
<template>
<div class="main-wrap operator-wrap">
  <avue-crud
    ref="params"
    :data="tableList"
    :option="listOpt"
    :page="page"
    :table-loading="loading"
    @row-save="addFun"
    @row-update="updateFun"
    @size-change="sizeChange"
    @current-change="currentChange"
    @search-reset="resetList"
    @export-excel="outExcel"
    @search-change="searchChange"
    @row-del="deleteView">
    <template v-if="hasPower('EDIT')" slot-scope="{row}" slot="menu">
      <button class="el-button el-button--text el-button--small" :title="$lang('库存功能设置')" type="button"><i
          class="icon-iconfont iconfont iconshezhi2 font-16" @click="openWarningDialog(row)"></i></button>
    </template>
    <template slot="warningPhone" slot-scope="{row}">
        <!-- iconyincang -->
        <div class="toggleIcon"><span>{{row.warningPhone || '--'}}</span><i
          class="icon-mobile icon-iconfont iconfont " :class="row.isOpen ? 'iconxianshimima' : 'iconyincang'"
          @click="mobileClick(row)"
        ></i></div>
      </template>
    <template slot="isSmsReminder" slot-scope="{row}">
        <span v-if="row.isSmsReminder === 1"> {{$lang('是')}}</span>
        <span v-else-if="row.isSmsReminder === 0"> {{$lang('否')}}</span>
      </template>
</avue-crud>
<!-- 库存功能设置 -->
<el-dialog lock-scroll
          :modal-append-to-body="false"
          append-to-body
          :title="$lang('库存功能设置（云集）')"
          :width="dialogWidth"
          custom-class="batch-dialog warn-dialog"
          :visible.sync="warnDialogVisible"
          :styleType="'noBorder'"
          :close-on-click-modal="false"
          @close="closeDialog">
          <div ref="content" v-loading="$parent.dialogLoading">
            <el-scrollbar style="height:100%">
              <el-form ref="warnForm" :rules="rules" :model="warnForm" label-position="top">
                <el-form-item :label="$lang('网点名称：')" prop="networkName">
                  <el-input disabled v-model="warnForm.networkName" auto-complete="off"></el-input>
                </el-form-item>
                <el-form-item :label="$lang('预警库存数量：')" prop="warningQuantity">
                      <el-input v-model="warnForm.warningQuantity" :placeholder="$lang('请输入')" auto-complete="off"></el-input>
                    </el-form-item>
                    <br/>
                    <el-form-item :label="$lang('短信提醒')">
                      <el-switch
                        v-model="warnForm.isSmsReminder"
                        @change="isSmsReminderChange">
                      </el-switch>
                    </el-form-item>
                    <el-form-item :label="$lang('预警手机号：')" prop="warningPhone">
                      <el-input v-model="warnForm.warningPhone" auto-complete="off"></el-input>
                    </el-form-item>
                    <br/>
                <el-form-item :label="$lang('最大库存数量：')" prop="maxStockNum">
                  <span class="tipsImg" :title="$lang('加盟商及以上层级可编辑')">
                      <i class="icon-iconfont iconfont iconshujujiacang-tishi font-16"></i>
                    </span>
                  <el-input :disabled="!isEdit" v-model="warnForm.maxStockNum" auto-complete="off"></el-input>
                </el-form-item>
              </el-form>
            </el-scrollbar>
          </div>
          <span slot="footer"
            class="dialog-footer">
            <el-button :size="$parent.controlSize"  @click="closeDialog" :title="$lang('取消')">{{$lang('取 消')}}</el-button>
            <el-button type="primary"
                      :size="$parent.controlSize"
                      :title="$lang('保存')"
                      :loading="keyBtn"
                      @click="warnSubmitForm"
                      >{{$lang('保 存')}}</el-button>
          </span>
  </el-dialog>
</div>
</template>
<script>
import mixin from '@mixins/mixin'
import { NetworkBalanceInquiryYJ, NetworkBalanceInquiry } from '../../pool'
import { RULES } from '@/common/utils/validators'
import { interfaceWaitHandle } from '@public/utils/common'
import debounce from 'lodash/debounce'
import { RESPONSE_CODE } from '@public/http/config'
export default {
  name: 'NetworkPDD',
  mixins: [mixin],
  data() {
    return {
      COM_HTTP: NetworkBalanceInquiryYJ,
      isShowFirst: false, // 阻止查询方法第一时间调用
      warnDialogVisible: false,
      isEdit: true, // 是否属于总部、代理、网点级别
      tableList: [],
      keyBtn: false, // 保存loading效果
      customerData: {}, // 库存信息
      warnForm: {// 预警设置弹窗初始化参数
        networkName: '',
        warningQuantity: null,
        isSmsReminder: false, // 短信提醒
        warningPhone: '',
        maxStockNum: 0
      },
      rules: {
        warningQuantity: [RULES.integer, RULES.required, RULES.max999999999],
        maxStockNum: [RULES.integer, RULES.max999999999],
        warningPhone: [RULES.phoneOrChMobile]
      }
    }
  },
  created() {
    const { NT_HEADQUARTERS, NT_AGENT_AREA, NT_CENTER } = this.organLevelObj
    const jurArray = [NT_HEADQUARTERS, NT_AGENT_AREA, NT_CENTER]
    this.isEdit = jurArray.includes(this.user.institutionalLevelId)
  },
  mounted () {
    this.$nextTick().then(async (res) => {
      await this.getRegularWaybillMaterialId()
      this.searchFun()
    })
  },
  computed: {
    listOpt() {
      const that = this
      return {
        header: true,
        fixHeight: 50, // 底部显示高度
        // menu: false,
        menuWidth: 80, // 菜单操作宽度
        editBtn: false,
        exportBtn: this.hasPower('OUTPUT'),
        column: [
          {
            label: '网点名称',
            prop: 'networkName'
          },
          {
            label: '网点编码',
            prop: 'networkId',
            type: 'network',
            rules: [RULES.required],
            remoteQuery: {
              queryLevel: 3
            },
            // senior: false, // 标识高级搜索字段
            props: {
              label: 'name',
              value: 'id'
            },
            selectDic: [{ id: that.user.networkId, name: that.user.networkName, code: that.user.networkCode }],
            searchDefault: that.user.networkId,
            change({ valueObj }) {
              that.$refs.params.searchForm.networkId = valueObj.id || ''
            },
            // 使用当前用户登录的网点信息
            useDetailForm: true,
            useCurNetworkInfo: true,
            // // 其他表单中的禁用
            // disabled: true,
            search: true,
            sort: 0,
            formatter: ({ networkCode }) => networkCode
          },
          {
            label: '品名',
            prop: 'materialName'
          },
          {
            label: '库存余额',
            prop: 'stockCount'
          },
          {
            label: '预警库存数量',
            prop: 'warningQuantity'
          },
          {
            label: '短信提醒',
            prop: 'isSmsReminder',
            slot: true
          },
          {
            label: '预警手机号',
            prop: 'warningPhone',
            slot: true,
            width: 150
          },
          {
            label: '最大库存数量',
            prop: 'maxStockNum'
          }
        ]
      }
    }
  },
  methods: {
    // 短信提醒
    isSmsReminderChange(val) {
      this.warnForm.isSmsReminder = val
      if (val) {
        this.$message.warning(this.$lang('短信提醒已开启'))
        this.rules.warningQuantity.push(...[RULES.moreZeroInteger])
        this.rules.warningPhone.push(...[RULES.required])
      } else {
        this.$message.warning(this.$lang('短信提醒已关闭'))
        this.warnForm.warningPhone = ''
        this.rules.warningQuantity.pop()
        this.rules.warningPhone.pop()
      }
    },
    // 预警弹窗
    openWarningDialog(row) {
      this.$nextTick(() => {
        this.getWarningMoble(row)
      })
      if (row.isSmsReminder) {
        this.rules.warningPhone.push(...[RULES.required])
      }
      this.warnForm.networkName = row.networkName
      this.warnForm.maxStockNum = row.maxStockNum
      this.warnForm.warningQuantity = row.warningQuantity
      this.warnForm.isSmsReminder = row.isSmsReminder !== 0
      this.customerData = row
      this.warnDialogVisible = true
    },
    async getWarningMoble(row) {
      const params = {
        id: row.id
      }
      const { code, data, msg } = await this.COM_HTTP.getMobile(params)
      if (code === RESPONSE_CODE.SUCCESS) {
        this.warnForm.warningPhone = data.warningPhone
      } else {
        this.$message.error(msg)
      }
    },
    closeDialog() {
      this.warnDialogVisible = false;
      this.$refs.warnForm.resetFields();
      this.warnForm.isSmsReminder = false
      this.warnForm.warningPhone = ''
      if (this.rules.warningPhone.length > 1) {
        this.rules.warningPhone.pop()
      }
    },
    // 库存发放设置提交
    async warnSubmitForm() {
      this.$refs.warnForm.validate((valid) => {
        if (!valid) {
          return
        }
        try {
          if (valid) {
            this.keyBtn = true;
            const { id, networkId } = this.customerData
            const paramData = {
              id,
              networkId,
              ...this.warnForm,
              isSmsReminder: this.warnForm.isSmsReminder ? 1 : 0
            }
            this.warnSubmitAction(paramData)
          }
        } catch (err) {
          this.keyBtn = false;
          console.log('err', 222222);
        }
      });
    },
    async warnSubmitAction(paramData) {
      const { code, msg } = await this.COM_HTTP.updateWarningNetworkYJ(paramData)
      if (code === RESPONSE_CODE.SUCCESS) {
        interfaceWaitHandle(() => {
          this.$refs.params.selectClear()
          this.closeDialog()
          this.$message.success(msg)
          this.keyBtn = false
          this.searchFun()
        })
      } else {
        this.$message.error(msg)
        this.keyBtn = false;
      }
    },
    // 查询面单物料id
    async getRegularWaybillMaterialId() {
      const { code, data, msg } = await NetworkBalanceInquiry.getMaterialId({ current: 1, size: 9999 })
      if (code === RESPONSE_CODE.SUCCESS) {
        data.records.forEach(element => {
        // 云集电子面单
          if (element.code === 'PMT1T400150') {
            this.materialData = element
          }
        });
      } else {
        this.$message.error(msg)
      }
    },
    // 混入自定义查询参数
    setSearchParams(params) {
      params.materialId = this.materialData.id
      return params
    },
    searchAfterFun() {
      // 获取数据后为每一列添加自定义属性，决定手机号一栏icon的样式
      if (this.tableList && this.tableList.length > 0) {
        this.tableList = [...this.tableList.map((item) => ({ ...item, isOpen: false, warningPhoneNew: '', warningPhoneOld: '' }))]
      }
    },
    // 点击眼睛触发方法
    mobileClick: debounce(async function(row) {
      // 如果是手机号为空白，那应该终止程序往下执行
      if (!row.warningPhone) return this.$message.warning('该条数据手机号为空')
      // 眼睛是闭着的，可以发送请求,可以先判断contactPhoneNew的长度，如果长度大于0,说明之前请求过真实的手机号，可以直接用
      if (!row.isOpen) {
        const params = {
          id: row.id
        }
        if (row.warningPhoneNew.length !== 0) {
          row.isOpen = !row.isOpen
          row.warningPhone = row.warningPhoneNew
        } else {
          // 眼睛是睁开的，可以发送请求
          const { code, data, msg } = await this.COM_HTTP.getMobile(params)
          if (code === RESPONSE_CODE.SUCCESS) {
          // 请求成功了
            row.isOpen = !row.isOpen
            row.warningPhoneOld = row.warningPhone // 把加密的手机号的先保存起来
            row.warningPhoneNew = data.warningPhone // 详情接口拿到的真实手机号先存起来
            row.warningPhone = data.warningPhone // 显示真实手机号
          } else {
            this.$message.error(msg)
          }
        }
      } else {
        // 眼睛睁开说明页面上是真实的手机号，这时候前端做一些处理就好了，直接取之前保存的加密手机号
        row.warningPhone = row.warningPhoneOld
        row.isOpen = !row.isOpen
      }
    }, 500)
  }
}
</script>
<style lang="scss" scoped>
.operator-wrap {
  padding: 0px;
}
.toggleIcon{
    .icon-mobile{
      @include base-color("clr");
      cursor: pointer;
      float: right;
    }
}
</style>
<style lang="scss">
  .warn-dialog .el-form{
    padding-bottom: 20px;
    .tipsImg{
      position: absolute;
      top: -32px;
      left: 105px;
    }
  }
  .batch-dialog{
    .el-form-item{
      width: 200px;
      margin-bottom: 0;
      margin: 10px;
      display: inline-block;
      vertical-align: top;
    }
    .el-radio-group{
      display:inline-flex;
      .el-radio{
        margin-right:20px;
      }
    }
    .el-textarea__inner{
      height: 100% !important;
    }
    .dialog-footer {
      padding-right: 0 !important;
      margin-bottom: 0 !important;
    }
  }
</style>

