<!--
 * @Date: 2020-08-20 16:33:25
 * @Author: MX
 * @LastEditTime: 2021-12-06 13:38:22
 * @Description: 首页-头部
-->
<template>
  <header class="header" :class="$route.name">
    <div class="header-logo pointer" @click="navClick('/')">
    <img src="@/assets/images/home/<USER>" alt="">
      <!-- <i class="iconfont icona-JTexpress "></i> -->
    </div>
    <div class="header-menu">
      <div class="header-menu-item" v-for="(item, index) in menuList" :key="index" :class="{ active: $route.meta.pathSet === item.path }" @click="navClick(item.path)">
        {{ $lang(item.title) }}
      </div>
    </div>

    <div class="header-action">
      <template v-if="!token">
        <div class="header-login m15" @click="navClick('/login')">{{$lang("登录")}}</div>
        <div class="header-register m15" @click="navClick('/register')">{{$lang("注册")}}</div>
      </template>
      <el-dropdown class=" m15" v-else>
        <span class="el-dropdown-link">
          <img src="@/assets/images/register/head2.png" alt="" />
          <span class="name">{{ user.account }}</span>
          <i class="el-icon-arrow-down"></i>
        </span>
        <el-dropdown-menu slot="dropdown" class="dropdown">
          <el-dropdown-item @click.native="navClick('/personalCenter')">
            <i class="iconfont icondaohangshezhi_gerenzhongxinmoren"></i>
            {{$lang("个人中心")}}
          </el-dropdown-item>
          <el-dropdown-item @click.native="logOut">
            <i class="iconfont icondaohangshezhi_tuichumoren"></i>
            {{$lang("退出")}}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      
      <el-dropdown class="lm-i18n">
        <span class="el-dropdown-link">
        <i class="iconfont icondaohang_icoyuyan"></i>   {{i18nValue}}<i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown" class="dropdown">
          <el-dropdown-item v-for="item in langList" :key="item.value" @click.native="SET_LANG(item.value)" :class="lang === item.value && 'active'">{{item.local}}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </header>
</template>

<script type="text/javascript">
import { mapGetters, mapActions } from "vuex";
export default {
  components: {},
  data() {
    return {
      menuList: [
        { title: "首页", path: "/home" },
        { title: "API文档", path: "/apiDoc" },
        { title: "控制台", path: "/control" },
        { title: "帮助中心", path: "/helpCenter" },
        // { title: "关于我们", path: "/aboutUs" },
      ],
    };
  },
  computed: {
    ...mapGetters(["token", "user", "lang", "langList"]),
    i18nValue() {
      const langObj = this.langList.find((v) => v.value === this.lang);
      if (langObj) {
        return langObj.local;
      } else {
        return "";
      }
    },
  },
  methods: {
    ...mapActions(["LOGIN_OUT", "SET_LANG"]),
    navClick(path){
      if(this.$route.path === path)return;
      this.$router.push({ path });
    },
    /**
     * @description 退出
     */
    logOut() {
      this.LOGIN_OUT().then(() => {
        this.navClick("/login");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
header {
  position: fixed;
  top: 0;
  z-index: 99;
  height: 64px;
  background: $theme-color;
  font-size: 20px;
  font-family: PingFangSC, PingFangSC-Semibold;
  font-weight: 600;
  color: #ffffff;
  min-width: 1200px;
  width: 100%;
  padding-left: 20px;
  padding-right: 55px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  user-select: none;
  .iconfont {
    font-weight: normal;
  }
  .header-logo {
    img{
      vertical-align: middle;
    }
    .icona-JTexpress {
      font-size: 23px;
    }
  }
  .header-menu {
    flex: 1;
    margin: 0 auto;
    height: 100%;
    line-height: 64px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    max-width: 800px;
    .header-menu-item {
      display: inline-block;
      cursor: pointer;
      position: relative;
      &.active:after {
        content: "";
        width: 30px;
        height: 4px;
        background: #ffffff;
        border-radius: 2px;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
  .lm-i18n {
    padding: 5px 5px 5px 10px;
    font-size: 14px;
    cursor: pointer;
    .icondaohang_icoyuyan{
      margin: 0 10px;
    }
  }
  .header-action {
    cursor: pointer;
    display: flex;
    .m15{
      margin:0  15px;
    }
  }
  .el-dropdown-link {
    color: #fff;
    img {
      vertical-align: middle;
      width: 28px;
      height: 28px;
    }
    .iconfont {
      font-size: 14px;
    }
    .name {
      margin: 0 10px;
    }
  }
}
</style>
<style lang="scss">
.dropdown {
  .el-dropdown-menu__item:hover {
    background: #f4f6f8 !important;
    color: $theme-color !important;
  }
  .active {
    color: $theme-color !important;
  }
}
</style>
