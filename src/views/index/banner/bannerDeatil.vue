<!--
 * @Author: jin<PERSON>o
 * @Date: 2021-01-18 16:59:32
 * @Description: file content
 * @LastEditTime: 2021-11-13 07:48:58
-->
<template>
  <div class="main-wrap bannerDeatil" style="overflow-y: auto;">
    <span class="title">{{ $lang("banner详情") }}</span>
    <el-form class="from" label-position="top">
        <el-form-item  :label="$lang('标题：')"  class="formitem">
           <span>{{ fakelist.title }}</span>
        </el-form-item>
        <div  style="display:flex;just-content:flex-start;">
        <el-form-item :label="$lang('发布时间：')"   class="formitem">
            <span>{{ fakelist.createTime }}</span>
        </el-form-item>
        <el-form-item :label="$lang('创建时间：')"    class="formitem" style="margin-left: 40px;">
            <span>{{ fakelist.createTime }}</span>
        </el-form-item>
        </div>
         <el-form-item  :label="$lang('排列顺序')"   class="formitem">
              <span>{{ fakelist.bannerOrder }}</span>
        </el-form-item>
        <el-form-item  :label="$lang('图片(pc)：')"   class="formitem">
           <div class="imgbox"><img :src="fakelist.img" alt=""></div>
        </el-form-item>
         <el-form-item :label="$lang('图片(移动端)：')"    class="formitem">
           <div class="imgbox"><img :src="fakelist.imgM" alt=""></div>
        </el-form-item>
         <el-form-item  :label="$lang('跳转链接(pc)')"   class="formitem">
            <span>{{ fakelist.url }}</span>
        </el-form-item>
        <el-form-item :label="$lang('跳转链接(移动端)')"   class="formitem">
            <span>{{ fakelist.urlM }}</span>
        </el-form-item>
    </el-form>
  </div>
</template>

<script type="text/javascript">
import { Banner } from '@/api/index.js'
export default {
  name: 'BannerDeatil',
  data() {
    return {
      COM_HTTP: Banner,
      bannerId: 1,
      fakelist: {}
    }
  },
  mounted() {
    this.bannerId = this.$route.query.id;
    this.BannerDetail()
  },
  methods: {
    async BannerDetail() {
      const { data, code, msg } = await this.COM_HTTP.reqDetail({
        id: this.bannerId
      })
      if (code === 1) {
        this.fakelist = data
      } else {
        this.$message.warning(msg)
      }
    }
  }

}
</script>

<style lang="scss">
.bannerDeatil{
.el-form .el-form-item .el-form-item__label{
    color: #9499A3;
    line-height: 40px;
}
.el-form .el-form-item .el-form-item__label{
    font-weight: 400;
}
.el-form-item__content{
    line-height: 1;
}
}
</style>
<style lang="scss" scoped>
.bannerDeatil{
.title{
    font-size: 20px;
    font-weight: bold;
    color: #2B2D42;
    line-height: 24px;
}
.from{
    .formitem{
        margin-top: 20px;
    }
    .title-input{
        width: 894px;
    }
    .timeinput{
        width: 270px;
    }
  .imgbox{
    // width: 428px;
    // height: 188px;
    border-radius: 4px;
    overflow: hidden;
    img{
        width: 100%;
        height: 100%;
    }
  }
  .contenthtml{
      margin-top: 10px;
  }
}
.btnbox{
  position: absolute;
    background: #fff;
    left: 230px;
    bottom: 18px;
    z-index: 999;
  .canel{
      width: 120px;
  }
  }
}
</style>

