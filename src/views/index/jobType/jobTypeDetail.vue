<!--
 * @Author: jinmiao
 * @Date: 2020-11-25 11:11:16
 * @Description: 职位详情
 * @LastEditTime: 2022-01-24 10:24:50
-->
<template>
  <div class="main-wrap jobTypeDetail">
    <div class="title">{{$lang('职位详情')}}</div>
    <el-form label-position="top" v-if="fakeList.length">
        <div class="froms">
        <el-form-item :label="$lang('职位名称(中文)：')" class="formitem">
            <div v-if="fakeList[0]">{{ fakeList[0].dictName }}</div>
        </el-form-item>
        <el-form-item :label="$lang('职位名称(英文)：')" class="formitem">
            <div v-if="fakeList[1]">{{ fakeList[1].dictName }}</div>
        </el-form-item>
        <el-form-item :label="$lang('职位名称(印尼文)：')" class="formitem">
            <div v-if="fakeList[2]">{{ fakeList[2].dictName }}</div>
        </el-form-item>
        <el-form-item :label="$lang('是否启用')" class="formitem">
            <div v-if="fakeList[0]">{{fakeList[0].enable === 1 ? $lang("是") : $lang("未启用")   }}</div>
        </el-form-item>
        </div>
    </el-form>
  </div>
</template>

<script type="text/javascript">
import { Jobtype } from '@/api'
export default {
  name: 'SearchDetail',
  data() {
    return {
      COM_HTTP: Jobtype,
      fakeList: {}
    }
  },
  mounted() {
    this.recruitId = this.$route.query.id;
    this.JobtypeDetail()
  },
  methods: {
    async JobtypeDetail() {
      const res = await this.COM_HTTP.reqDetail({
        infoNumber: this.recruitId
      })
      
      if (res.code === 1 && res.data) {
        this.fakeList = res.data
        console.log("🚀 ~ file: jobTypeDetail.vue ~ line 51 ~ JobtypeDetail ~ res.data", res.data);
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.jobTypeDetail{
.title{
    height: 24px;
    font-size: 20px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-weight: 600;
    text-align: left;
    color: #2b2d42;
    line-height: 24px;
    margin-bottom: 20px;
}
.froms {
    display: flex;
    flex-flow: wrap;
    transition: height 0.3s;
    align-content: flex-start;
    .formitem {
        min-width: 200px;
        margin-right: 20px;
        margin-bottom: 0px;
        font-size: 14px;
        ::v-deep .el-form-item__label {
          height: 18px;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          color: #9499a3;
          line-height: 18px;
        }
        ::v-deep .el-form-item__content {
          height: 18px;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          color: #9499a3;
          line-height: 18px;
          margin-top: 4px;
        }
    }
}
}
</style>
