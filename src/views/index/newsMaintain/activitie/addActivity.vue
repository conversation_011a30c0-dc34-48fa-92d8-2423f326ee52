<!--
 * @Author: jinmiao
 * @Date: 2020-11-30 14:33:33
 * @Description: 增加动态
 * @LastEditTime: 2021-11-16 17:55:16
-->
<template>
  <div class="main-wrap addActivity">
    <AddList :titleSecond="titleSecond" :title="title" ref="AddList" @content="getcontent" :source="source"></AddList>
  </div>

</template>

<script type="text/javascript">
import AddList from '@/components/add-list/index'
import { News } from '@/api'
export default {
  name: 'AdminActivityAdd',
  components: { AddList },
  data() {
    return {
      COM_HTTP: News,
      value: '',
      imageUrl: '',
      titleSecond: '标题',
      title: '媒体新增',
      activityId: '',
      source: 'activity'
    }
  },
  mounted() {
    this.activityId = this.$route.query.id;
    if (this.activityId) {
      this.getOldactivity()
    }
  },
  methods: {
    getcontent(e) {
      console.log(e)
      const content = e
      const list = {
        title: content.title,
        releaseTime: content.time,
        coverImg: content.thumbImg,
        content: content.editvomtent,
        isTop: content.isTop
      }
      if (this.activityId) {
        list.id = this.activityId
        this.upload(list)
      } else {
        this.addActivity(list)
      }
    },
    async addActivity (params) {
      params.infoType = 2
      const res = await this.COM_HTTP.reqAdd(params)
      if (res.code === 1) {
        this.$message.success(this.$lang('发布成功'))
        // this.$router.push({ path: '/app/newsMaintain/adminActivitySearch' })
      } else {
        this.$message.warning(res.msg)
      }
    },
    async upload(params) {
      const res = await this.COM_HTTP.reqModify(params)
      if (res.code === 1) {
        this.$message.success(this.$lang('修改成功'))
        // this.$router.push({ path: '/app/newsMaintain/adminActivitySearch' })
      } else {
        this.$message.warning(res.msg)
      }
    },
    async getOldactivity() {
      const res = await this.COM_HTTP.reqDetail({ id: this.activityId, infoType: 2 })
      if (res.code === 1) {
        const list = res.data
        const fromDate = {
          title: list.title,
          time: list.releaseTime,
          thumbImg: list.coverImg,
          editvomtent: list.content,
          isTop: list.isTop
        }
        this.$refs.AddList.setFromdata(fromDate)
      } else {
        this.$message.warning(res.msg)
      }
    }
  }

}
</script>

<style lang="scss" scoped>
</style>
