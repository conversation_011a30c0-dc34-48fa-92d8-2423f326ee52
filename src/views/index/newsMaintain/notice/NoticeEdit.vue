<!--
 * @Author: jinmiao
 * @Date: 2020-12-22 11:21:21
 * @Description: 公告编辑
 * @LastEditTime: 2021-11-12 23:23:27
-->
<template>
  <div class="main-wrap AddNews" style="overflow-y: auto;">
    <AddList :titleSecond="titleSecond" :title="title" ref="AddList" @content="getcontent" :source="source" list-type="edit"></AddList>
  </div>
</template>

<script type="text/javascript">
import AddList from '@/components/add-list/index'
import { News } from '@/api'
export default {
  name: 'AdminNoticeUpload',
  components: { AddList },
  data() {
    return {
      COM_HTTP: News,
      value: '',
      titleSecond: '公告标题',
      title: '公告编辑',
      noticeId: '',
      source: 'notice'
    }
  },
  mounted() {
    this.noticeId = this.$route.query.id;
    if (this.noticeId) {
      this.getOldnotice()
    }
  },
  methods: {
    getcontent(e) {
      console.log(e)
      const content = e
      const list = {
        title: content.title,
        releaseTime: content.time,
        coverImg: content.thumbImg,
        content: content.editvomtent,
        isTop: content.isTop
      }
      if (this.noticeId) {
        list.id = this.noticeId
        this.upload(list)
      } else {
        this.addActivity(list)
      }
    },
    async addActivity (params) {
      params.infoType = 1
      const res = await this.COM_HTTP.reqAdd(params)
      if (res.code === 1) {
        this.$message.success(this.$lang('发布成功'))
        // this.$router.push({ path: '/app/newsMaintain/adminNoticeSearch' })
      } else {
        this.$message.warning(res.msg)
      }
      console.log(res)
    },
    async upload(params) {
      const res = await this.COM_HTTP.reqModify(params)
      if (res.code === 1) {
        this.$message.success(this.$lang('修改成功'))
        // this.$router.push({ path: '/app/newsMaintain/adminNoticeSearch' })
      } else {
        this.$message.warning(res.msg)
      }
    },
    async getOldnotice() {
      const res = await this.COM_HTTP.reqDetail({ id: this.noticeId, infoType: 1 })
      if (res.code === 1) {
        const list = res.data
        const fromDate = {
          title: list.title,
          time: list.releaseTime,
          thumbImg: list.coverImg,
          editvomtent: list.content,
          isTop: list.isTop
        }
        this.$refs.AddList.setFromdata(fromDate)
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
