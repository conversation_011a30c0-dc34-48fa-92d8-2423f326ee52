<template>
  <div class="main-wrap AddRecruit" style="overflow-y: auto;">
    <div class="title">{{$lang('招聘新增')}}</div>
    <el-form label-position="top" :model="baseForm" :rules="baseFormRules" ref="baseForm" size="small">
      <div class="from">
        <el-form-item :label="$lang('紧急程度')" prop="exigence"  class="formitem">
          <el-select v-model="baseForm.exigence">
            <el-option :label="$lang('是')" :value="1"></el-option>
            <el-option :label="$lang('否')" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$lang('招聘人数：')" class="formitem" prop="recruits">
          <el-input :placeholder="$lang('请输入招聘人数')" class="timeinput" v-model="baseForm.recruits" maxlength="10"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <div class="forms">
      <el-form label-position="top" :model="fromDateIDN" :rules="rulesIDN" ref="ruleFormIDN">
        <div class="from">
          <el-form-item :label="$lang('职位名称（印尼文）：')" class="formitem" prop="jobName">
              <el-input :placeholder="$lang('请输入职位名称')" class="timeinput" size="small" v-model="fromDateIDN.jobName" type="text" maxlength="100"></el-input>
          </el-form-item>
          <el-form-item :label="$lang('职位类型（印尼文）：')" class="formitem" prop="jobType">
              <el-select v-model="fromDateIDN.jobType" :placeholder="$lang('请选择职位类型')" size="small">
              <el-option
                v-for="item in jobTypeListIDN"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item :label="$lang('岗位类型（印尼文）：')" class="formitem" prop="postType">
              <el-input :placeholder="$lang('请输入岗位类型')" class="timeinput" size="small" v-model="fromDateIDN.postType" type="text" maxlength="16"></el-input>
          </el-form-item> -->
          </div>
          <div class="from">
          <el-form-item :label="$lang('工作经验（印尼文）：')" class="formitem" prop="experience">
              <el-input :placeholder="$lang('请输入工作经验')" class="timeinput" size="small" v-model="fromDateIDN.experience" type="text" maxlength="100"></el-input>
          </el-form-item>
          <el-form-item :label="$lang('薪资范围（印尼文）：')" class="formitem" prop="compensation">
              <el-input :placeholder="$lang('请输入薪资范围')" class="timeinput" size="small" v-model="fromDateIDN.compensation" type="text" maxlength="100"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <el-form label-position="top" :model="fromDateEN" :rules="rulesEN" ref="ruleFormEN">
        <div class="from">
          <el-form-item :label="$lang('职位名称（英文）：')" class="formitem" prop="jobName">
              <el-input :placeholder="$lang('请输入职位名称')" class="timeinput" size="small" v-model="fromDateEN.jobName" type="text" maxlength="100"></el-input>
          </el-form-item>
          <el-form-item :label="$lang('职位类型（英文）：')" class="formitem" prop="jobType">
              <el-select v-model="fromDateEN.jobType" :placeholder="$lang('请选择职位类型')" size="small">
              <el-option
                v-for="item in jobTypeListEN"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item :label="$lang('岗位类型（英文）：')" class="formitem" prop="postType">
              <el-input :placeholder="$lang('请输入岗位类型')" class="timeinput" size="small" v-model="fromDateEN.postType" type="text" maxlength="16"></el-input>
          </el-form-item> -->
          </div>
          <div class="from">
          <el-form-item :label="$lang('工作经验（英文）：')" class="formitem" prop="experience">
              <el-input :placeholder="$lang('请输入工作经验')" class="timeinput" size="small" v-model="fromDateEN.experience" type="text" maxlength="100"></el-input>
          </el-form-item>
          <el-form-item :label="$lang('薪资范围（英文）：')" class="formitem" prop="compensation">
              <el-input :placeholder="$lang('请输入薪资范围')" class="timeinput" size="small" v-model="fromDateEN.compensation" type="text" maxlength="100"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <el-form label-position="top" :model="fromDate" :rules="rules" ref="ruleForm">
        <div class="from">
          <el-form-item :label="$lang('职位名称（中文）：')" class="formitem" prop="jobName">
              <el-input :placeholder="$lang('请输入职位名称')" class="timeinput" size="small" v-model="fromDate.jobName" type="text" maxlength="100"></el-input>
          </el-form-item>
          <el-form-item :label="$lang('职位类型（中文）：')" class="formitem" prop="jobType">
              <el-select v-model="fromDate.jobType" :placeholder="$lang('请选择职位类型')" size="small">
              <el-option
                v-for="item in jobTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item :label="$lang('岗位类型（中文）：')" class="formitem" prop="postType">
              <el-input :placeholder="$lang('请输入岗位类型')" class="timeinput" size="small" v-model="fromDate.postType" type="text" maxlength="16"></el-input>
          </el-form-item> -->
          </div>
          <div class="from">
          <el-form-item :label="$lang('工作经验（中文）：')" class="formitem" prop="experience">
              <el-input :placeholder="$lang('请输入工作经验')" class="timeinput" size="small" v-model="fromDate.experience" type="text" maxlength="100"></el-input>
          </el-form-item>
          <el-form-item :label="$lang('薪资范围（中文）：')" class="formitem" prop="compensation">
              <el-input :placeholder="$lang('请输入薪资范围')" class="timeinput" size="small" v-model="fromDate.compensation" type="text" maxlength="100"></el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="forms-2cols">
      <el-form label-position="top" :model="fromDateIDN" :rules="rulesIDN" ref="ruleFormIDN1">
        <div class="from">
          <el-col :span="24">
            <el-form-item :label="$lang('工作地点（印尼文）：')" class="formitem" prop="workplace">
                <el-input :placeholder="$lang('请输入工作地点')" class="timeinput" size="small" v-model="fromDateIDN.workplace" type="text" maxlength="300"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px;">
            <el-form-item :label="$lang('职位描述（印尼文）')" class="formitem" prop="dutyDesc">
              <wangEditor class="describe" :source="source" @onChange="onChange" ref="editorIDN" id="editorIDN"></wangEditor>
            </el-form-item>
          </el-col>
        </div>
      </el-form>
      <el-form label-position="top" :model="fromDateEN" :rules="rulesEN" ref="ruleFormEN1">
        <div class="from">
          <el-col :span="24">
            <el-form-item :label="$lang('工作地点（英文）：')" class="formitem" prop="workplace">
                <el-input :placeholder="$lang('请输入工作地点')" class="timeinput" size="small" v-model="fromDateEN.workplace" type="text" maxlength="300"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px;">
            <el-form-item :label="$lang('职位描述（英文）')" class="formitem" prop="dutyDesc">
              <wangEditor class="describe" :source="source" @onChange="onChange" ref="editorEN" id="editorEN"></wangEditor>
            </el-form-item>
          </el-col>
        </div>
      </el-form>
      <el-form label-position="top" :model="fromDate" :rules="rules" ref="ruleForm1">
        <div class="from">
          <el-col :span="24">
            <el-form-item :label="$lang('工作地点（中文）：')" class="formitem" prop="workplace">
              <el-input :placeholder="$lang('请输入工作地点')" class="timeinput" size="small" v-model="fromDate.workplace" type="text" maxlength="300"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px;">
            <el-form-item :label="$lang('职位描述（中文）')" class="formitem" prop="dutyDesc">
              <wangEditor class="describe" :source="source" @onChange="onChange" ref="editor"></wangEditor>
            </el-form-item>
          </el-col>
        </div>
      </el-form>
    </div>
    <div class="btnbox">
            <el-button size="small" class="publish" @click="publish">{{$lang('发布')}}</el-button>
            <el-button size="small" class="canel" @click="canelUser">{{$lang('取消')}}</el-button>
    </div>
  </div>
</template>

<script type="text/javascript">
import mixin from '@/mixins/mixin'
import wangEditor from '@/components/editor/editor'
import { RULES } from '@/@public/utils/rules'
import { Recruit, Jobtype } from '@/api'
import dayjs from 'dayjs';
export default {
  name: 'RecruitAdd',
  components: { wangEditor },
  mixins: [mixin],
  data() {
    return {
      COM_HTTP: Recruit,
      fromDate: {
        country: 'CN',
        jobName: '',
        jobType: '',
        workplace: '',
        dutyDesc: '',
        compensation: '',
        releaseTime: '',
        recruits: '',
        experience: ''
      },
      fromDateEN: {
        country: 'EN',
        jobName: '',
        jobType: '',
        workplace: '',
        dutyDesc: '',
        compensation: '',
        releaseTime: '',
        recruits: '',
        experience: ''
      },
      fromDateIDN: {
        country: 'ID',
        jobName: '',
        jobType: '',
        workplace: '',
        dutyDesc: '',
        compensation: '',
        releaseTime: '',
        recruits: '',
        experience: ''
      },
      rules: {
        jobName: [{
          required: true,
          message:this.$lang('职位名称不能为空'),
          trigger: ['blur', 'change']
        }],
        jobType: [{
          required: true,
          message:this.$lang('职位类型不能为空'),
          trigger: ['blur', 'change']
        }],
        // postType: [{
        //   required: true,
        //   message: '岗位类型不能为空',
        //   trigger: ['blur', 'change']
        // }],
        workplace: [{
          required: true,
          message:this.$lang('工作地点不能为空'),
          trigger: ['blur', 'change']
        }],
        dutyDesc: [{
          required: true,
          message:this.$lang('职位描述不能为空'),
          trigger: ['blur', 'change']
        }],
        experience: [{
          required: true,
          message:this.$lang('工作经验不能为空'),
          trigger: ['blur', 'change']
        }],
        // recruits: [{
        //   required: true,
        //   message: '招聘人数不能为空',
        //   trigger: ['blur', 'change']
        // }],
        recruits: [RULES.required, RULES.isNumber]
      },
      rulesEN: {
        jobName: [{
          required: true,
          message:this.$lang('职位名称不能为空'),
          trigger: ['blur', 'change']
        }],
        jobType: [{
          required: true,
          message:this.$lang('职位类型不能为空'),
          trigger: ['blur', 'change']
        }],
        // postType: [{
        //   required: true,
        //   message: '岗位类型不能为空',
        //   trigger: ['blur', 'change']
        // }],
        workplace: [{
          required: true,
          message:this.$lang('工作地点不能为空'),
          trigger: ['blur', 'change']
        }],
        dutyDesc: [{
          required: true,
          message:this.$lang('职位描述不能为空'),
          trigger: ['blur', 'change']
        }],
        experience: [{
          required: true,
          message:this.$lang('工作经验不能为空'),
          trigger: ['blur', 'change']
        }],
        // recruits: [{
        //   required: true,
        //   message: '招聘人数不能为空',
        //   trigger: ['blur', 'change']
        // }],
        recruits: [RULES.required, RULES.isNumber]
      },
      rulesIDN: {
        jobName: [{
          required: true,
          message:this.$lang('职位名称不能为空'),
          trigger: ['blur', 'change']
        }],
        jobType: [{
          required: true,
          message:this.$lang('职位类型不能为空'),
          trigger: ['blur', 'change']
        }],
        // postType: [{
        //   required: true,
        //   message: '岗位类型不能为空',
        //   trigger: ['blur', 'change']
        // }],
        workplace: [{
          required: true,
          message:this.$lang('工作地点不能为空'),
          trigger: ['blur', 'change']
        }],
        dutyDesc: [{
          required: true,
          message:this.$lang('职位描述不能为空'),
          trigger: ['blur', 'change']
        }],
        experience: [{
          required: true,
          message:this.$lang('工作经验不能为空'),
          trigger: ['blur', 'change']
        }],
        // recruits: [{
        //   required: true,
        //   message: '招聘人数不能为空',
        //   trigger: ['blur', 'change']
        // }],
        recruits: [RULES.required, RULES.isNumber]
      },
      baseForm: {
        exigence: 2,
        recruits: ''
      },
      baseFormRules: {
        exigence: [{
          required: true,
          message:this.$lang('不能为空'),
          trigger: ['blur', 'change']
        }],
        recruits: [RULES.required, RULES.isNumber]
      },
      source: 'recruit',
      recruitId: '',
      jobTypeList: [],
      jobTypeListEN: [],
      jobTypeListIDN: [],
    }
  },
  mounted() {
    this.recruitId = this.$route.query.id;
    if (this.recruitId) {
      this.getOldrecruit()
    }
    this.jobType()
  },
  methods: {
    numLimit(content) {
      var temp = content.replace(/<\/?.+?>/g, '')
      var result = temp.replace(/ /g, '')
      var re = /<img[^>]+>/g
      var ImgA = content.match(re)
      let imgLen
      if (ImgA) {
        imgLen = ImgA.length
      }
      if (!ImgA) {
        imgLen = 0
      }
      if (Number(result.length) + imgLen > 5000) {
        return true
      } else {
        return false
      }
      // 取消前端限制，由后端空数字数长度
      return true
    },
    onChange(val) {
      switch (val.id) {
        case 'editorIDN':
          this.fromDateIDN.dutyDesc = val.newHtml
          break
        case 'editorEN':
          this.fromDateEN.dutyDesc = val.newHtml
          break
        default: 
          this.fromDate.dutyDesc = val.newHtml
          break
      }
    },
    canelUser() {
      this.$router.go(-1)
      this.removeTag('recruitAdd')
    },
    publish() {
      const state = this.showcontent()
      if (!state) { return false }
      const h = this.$createElement;
      const that = this
      this.$msgbox({
        message: h('p', null, [
          h('span', { style: 'color: #2B2D42;line-height:80px' },this.$lang('是否发布该招聘动态？'))
        ]),
        showCancelButton: true,
        confirmButtonText: this.$lang('确定'),
        cancelButtonText: this.$lang('取消'),
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            const state = that.showcontent()
            let paramsList = [
              this.fromDate,
              this.fromDateEN,
              this.fromDateIDN
            ]
            paramsList.map(item => Object.assign(item, this.baseForm))
            if (state) {
              if (this.recruitId) {
                that.upload(paramsList)
              } else {
                that.AddRecruit(paramsList)
              }
            }
            setTimeout(() => {
              done();
              this.$router.go(-1)
              this.removeTag('recruitAdd')
              setTimeout(() => {
                instance.confirmButtonLoading = false;
              }, 300);
            }, 3000);
          } else {
            done();
          }
        }
      })
    },
    showcontent() {
      let state
      let formData
      let fromDateIDN
      let fromDateEN
      if(this.fromDate.dutyDesc) {
        formData = this.numLimit(this.fromDate.dutyDesc)
      }
      if(this.fromDateIDN.dutyDesc) {
        fromDateIDN = this.numLimit(this.fromDateIDN.dutyDesc)
      }
      if(this.fromDateEN.dutyDesc) {
        fromDateEN = this.numLimit(this.fromDateEN.dutyDesc)
      }

      if(formData || fromDateIDN || fromDateEN) {
        this.$message.error(this.$lang('职位描述内容不能超过5000个字符'))
        return false
      }

      this.fromDate.dutyDesc = this.$refs.editor.getEditorData()
      this.fromDateEN.dutyDesc = this.$refs.editorEN.getEditorData()
      this.fromDateIDN.dutyDesc = this.$refs.editorIDN.getEditorData()
      this.$refs.baseForm.validate((valid) => {
        if (valid) {
          state = true
        } else {
          state = false;
        }
      })
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          state = true
        } else {
          state = false;
        }
      })
      this.$refs.ruleFormEN.validate((valid) => {
        if (valid) {
          state = true
        } else {
          state = false;
        }
      })
      this.$refs.ruleFormIDN.validate((valid) => {
        if (valid) {
          state = true
        } else {
          state = false;
        }
      })
      this.$refs.ruleFormIDN1.validate((valid) => {
        if (valid) {
          state = true
        } else {
          state = false;
        }
      })
      this.$refs.ruleFormEN1.validate((valid) => {
        if (valid) {
          state = true
        } else {
          state = false;
        }
      })
      this.$refs.ruleForm1.validate((valid) => {
        if (valid) {
          state = true
        } else {
          state = false;
        }
      })
      return state
    },
    async AddRecruit (params) {
      const res = await this.COM_HTTP.reqAdd(params)
      if (res.code === 1) {
        this.$message.success(this.$lang('发布成功'))
        // this.$router.push({ path: '/app/recruit' })
      } else {
        this.$message.warning(res.msg)
      }
    },
    async upload(params) {
      const res = await this.COM_HTTP.reqModify(params)
      if (res.code === 1) {
        this.$message.success(this.$lang('修改成功'))
        // this.$router.push({ path: '/app/recruit' })
      } else {
        this.$message.warning(res.msg)
      }
    },
    async getOldrecruit() {
      const res = await this.COM_HTTP.reqDetail({ id: this.recruitId })
      if (res.code === 1) {
        const list = res.data.data[0]
        this.fromDate = {
          jobName: list.jobName,
          jobType: list.jobType,
          workplace: list.workplace,
          dutyDesc: list.dutyDesc,
          compensation: list.compensation,
          releaseTime: list.releaseTime,
          recruits: list.recruits,
          experience: list.experience
        }
        this.$refs.editor.setEditorData(this.fromDate.dutyDesc)
      } else {
        this.$message.warning(res.msg)
      }
    },
    async jobType(params) {
      const { data, code } = await Jobtype.reqList({ size: '999', page: '1', enable: 1})
      if (code === 1) {
        data.records.forEach(item => {
          switch (item.country) {
            case 'CN':
              this.jobTypeList.push({ 'value': item.dictCode, 'label': item.dictName })
              break;
            case 'EN':
              this.jobTypeListEN.push({ 'value': item.dictCode, 'label': item.dictName })
              break;
            case 'ID':
              this.jobTypeListIDN.push({ 'value': item.dictCode, 'label': item.dictName })
              break;
            default:
              break;
          }
        })
      }
    }
  }

}
</script>
<style lang="scss">
.el-form-item__label{
  line-height: 1;
}
</style>
<style lang="scss" scoped>
.AddRecruit{
.title{
    font-size: 20px;
    font-weight: bold;
    color: #2B2D42;
    line-height: 24px;
    margin-bottom: 10px;
}
.forms{
  width: 100%;
  display: flex;
  .el-form{
    width: 220px;
  }
}
.forms-2cols{
  width: 100%;
  height: 620px;
  display: flex;
  .el-form{
    width: 440px;
  }
}
.from{
    // margin: 0 -10px;
    display: flex;
    flex-flow: wrap;
    transition: height 0.3s;
    align-content: flex-start;
    .formitem{
        margin-bottom: 0;
        padding: 14px 20px 10px 0;
        min-width: 200px;
        height: 50px;
    }
}
  .btnbox{
      position: absolute;
      bottom: 30px;
      left: 250px;
      .publish{
        background: #00B075;
        color: #fff;
        width:120px;
        border: none;
      }
      .canel{
          width: 120px;
      }
  }
  .el-form-item__label{
      line-height: 18px;
    }
    .describe{
      margin-top: 10px;
    }
    .editbox{
      padding: 10px 0;
    }
}
</style>
