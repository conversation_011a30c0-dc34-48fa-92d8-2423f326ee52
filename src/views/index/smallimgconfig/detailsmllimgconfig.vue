<template>
  <div class="main-wrap bannerDeatil" style="overflow-y: auto;">
    <span class="title">{{$lang("底部子图详情")}}</span>
    <el-form class="from" label-position="top">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$lang('位置目录：')" class="formitem" >
            <div>{{$lang("级")}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$lang('子级背景图排序：')" class="formitem" >
            <div>{{fakelist.secondLevel || '--'}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" type="flex">
        <el-col :span="6">
          <el-form-item :label="$lang('图片')">
            <div class="uploadImg">
              <img v-if="fakelist.imgLink" :src="fakelist.imgLink" class="avatar">
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$lang('标题（中文）：')" class="formitem" >
            <div>{{fakelist.title || '--'}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$lang('内容（中文）：')" class="formitem" >
            <div>{{fakelist.content || '--'}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$lang('标题（英文）：')" class="formitem" >
            <div>{{fakelist.enTitle || '--'}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$lang('内容（英文）：')" class="formitem" >
            <div>{{fakelist.enContent || '--'}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$lang('标题（印尼）：')" class="formitem" >
            <div>{{fakelist.inTitle || '--'}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$lang('内容（印尼）：')" class="formitem" >
            <div>{{fakelist.inContent || '--'}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$lang('创建时间：')" class="formitem" >
            <div>{{fakelist.createTime || '--'}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$lang('创建人：')" class="formitem" >
            <div>{{fakelist.createUser || '--'}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$lang('最后修改时间：')" class="formitem" >
            <div>{{fakelist.updateTime || '--'}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$lang('最后修改人：')" class="formitem" >
            <div>{{fakelist.updateUser || '--'}}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script type="text/javascript">
import { Imgconfig } from '@/api/index.js'
export default {
  name: 'detailImgconfig',
  data() {
    return {
      COM_HTTP: Imgconfig,
      id: 1,
      fakelist: {}
    }
  },
  mounted() {
    this.id = this.$route.query.id;
    this.BannerDetail()
  },
  methods: {
    async BannerDetail() {
      const { data, code, msg } = await this.COM_HTTP.reqDetail({
        id: this.id
      })
      if (code === 1) {
        this.fakelist = data
      } else {
        this.$message.warning(msg)
      }
    },
    statusfilters: function (a) {
      switch (a) {
        case 0:
          a ="$lang('待发布')";
          break;
        case 1:
          a ="$lang('已发布')";
          break;
        case 2:
          a ="$lang('已删除')";
          break;
      }
      return a;
    }
  }

}
</script>
<style lang="scss">
.bannerDeatil{
  .el-form .el-form-item .el-form-item__label{
    color: #9499A3;
    line-height: 40px;
  }
  .el-form .el-form-item .el-form-item__label{
    font-weight: 400;
  }
  .el-form-item__content{
    line-height: 1;
  }
}
</style>
<style lang="scss" scoped>
.bannerDeatil{
  .title{
    font-size: 20px;
    font-weight: bold;
    color: #2B2D42;
    line-height: 24px;
  }
  .from{
    .formitem{
      margin-top: 20px;
    }
    .title-input{
      width: 894px;
    }
    .timeinput{
      width: 270px;
    }
    .imgbox{
      // width: 428px;
      // height: 188px;
      border-radius: 4px;
      overflow: hidden;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .contenthtml{
      margin-top: 10px;
    }
  }
  .btnbox{
    position: absolute;
    background: #fff;
    left: 230px;
    bottom: 18px;
    z-index: 999;
    .canel{
      width: 120px;
    }
  }
}
</style>
