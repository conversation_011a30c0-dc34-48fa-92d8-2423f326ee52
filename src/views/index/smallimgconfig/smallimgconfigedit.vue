<template>
  <div class="main-wrap AddRecruit" style="overflow-y: auto;">
    <div class="title">{{$lang('底部子图修改')}}</div>
    <el-form label-position="top" :model="baseForm" :rules="rules" ref="baseForm" size="small">
      <el-row :gutter="20" type="flex">
        <el-col :span="8">
          <el-form-item :label="$lang('请选择上级底图目录')" prop="level"  class="formitem">
            <el-select :placeholder="$lang('请选择上级底图目录：')" size="small" v-model="fromDate.level">
              <el-option
                  v-for="item in levelList"
                  :key="item.level"
                  :label=$lang(item.name)
                  :value="item.level">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$lang('子级背景图排序')" prop="status"  class="formitem">
            <el-select  size="small" v-model="fromDate.secondLevel">
              <el-option label="1" :value="1"></el-option>
              <el-option label="2" :value="2"></el-option>
              <el-option label="3" :value="3"></el-option>
              <el-option label="4" :value="4"></el-option>
              <el-option label="5" :value="5"></el-option>
              <el-option label="6" :value="6"></el-option>
              <el-option label="7" :value="7"></el-option>
              <el-option label="8" :value="8"></el-option>
              <el-option label="9" :value="9"></el-option>
              <el-option label="10" :value="10"></el-option>
              <el-option label="11" :value="11"></el-option>
              <el-option label="12" :value="12"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" type="flex">
        <el-col :span="6">
          <el-form-item :label="$lang('图片')">
            <div class="uploadImg">
              <el-upload class="avatar-uploader" :headers="header" :action='actionUrl' :show-file-list="false"
                         :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" accept="image/jpeg,image/jpg,image/png">
                <img v-if="fromDate.imgLink" :src="fromDate.imgLink" class="avatar">
              </el-upload>
            </div>
            <a v-if="fromDate.imgLink" :href="fromDate.imgLink" target="_blank" class="_target_a"><i class="iconfont iconchakandatu"></i>{{$lang('查看大图')}}</a>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item :label="$lang('标题（中文）')" class="formitem" prop="title">
            <el-input class="timeinput" size="small"  v-model="fromDate.title" maxLength='200'></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$lang('内容（中文）')" class="formitem" prop="title">
            <el-input class="timeinput" size="small"  v-model="fromDate.content" maxLength='500'></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item :label="$lang('标题（英文）')" class="formitem" prop="title">
            <el-input class="timeinput" size="small"  v-model="fromDate.enTitle" maxLength='200'></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$lang('内容（英文）')" class="formitem" prop="title">
            <el-input class="timeinput" size="small"  v-model="fromDate.enContent" maxLength='500'></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item :label="$lang('标题（印尼）')" class="formitem" prop="title">
            <el-input class="timeinput" size="small"  v-model="fromDate.inTitle" maxLength='200'></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$lang('内容（印尼）')" class="formitem" prop="title">
            <el-input class="timeinput" size="small"  v-model="fromDate.inContent" maxLength='500'></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="btnbox">
      <el-button size="small" class="publish" @click="publish">{{$lang('保存')}}</el-button>
      <el-button size="small" class="canel" @click="canelUser">{{$lang('取消')}}</el-button>
    </div>
  </div>
</template>
<script type="text/javascript">
import mixin from '@/mixins/mixin'
import { RULES } from '@/@public/utils/rules'
import { Imgconfig, UPLOADUMG } from '@/api'
export default {
  name: 'imgconfigEdit',
  mixins: [mixin],
  data() {
    return {
      COM_HTTP: Imgconfig,
      actionUrl: UPLOADUMG,
      levelList: [],
      fromDate: {
        type: 1,
        level: '',
        secondLevel: '',
        title: '',
        content: '',
        inTitle: '',
        enTitle: '',
        enContent: '',
        inContent: '',
        imgurl: '',
        imgLink: '',
        id: '',
      },
    }
  },
  mounted() {
    this.getLevels()
    this.id = this.$route.query.id
    if (this.id) {
      this.getOldrecruit()
    }
  },
  methods: {
    async getOldrecruit() {
      const res = await this.COM_HTTP.reqDetail({ id : this.id })
      if (res.code === 1) {
        const list = res.data
        this.fromDate.level = list.level;
        this.fromDate.secondLevel = list.secondLevel;
        this.fromDate.title = list.title;
        this.fromDate.enTitle = list.enTitle;
        this.fromDate.inTitle = list.inTitle;
        this.fromDate.content = list.content
        this.fromDate.enContent = list.enContent
        this.fromDate.inContent = list.inContent
        this.fromDate.imgLink = list.imgLink
        this.fromDate.imgurl = list.imgurl
        // this.fromDate.imgpath
      } else {
        this.$message.warning(res.msg)
      }
    },
    publish() {
      if (!this.fromDate.level || this.fromDate.level === '') {
        this.$message.warning(this.$lang('提交前必须选择上级底图目录'));
        return
      }
      if (!this.fromDate.secondLevel || this.fromDate.secondLevel === '') {
        this.$message.warning(this.$lang('提交前必须选择子级背景图排序'));
        return
      }
      if (!this.fromDate.imgurl || this.fromDate.imgurl === '') {
        this.$message.warning(this.$lang('提交前必须上传图片'));
        return
      }
      if (!this.fromDate.title || this.fromDate.title === '') {
        this.$message.warning(this.$lang('中文标题不能为空'))
        return
      }
      if (!this.fromDate.content || this.fromDate.content === '') {
        this.$message.warning(this.$lang('中文内容不能为空'))
        return
      }
      if (!this.fromDate.enTitle || this.fromDate.enTitle === '') {
        this.$message.warning(this.$lang('英文标题不能为空'))
        return
      }
      if (!this.fromDate.enContent || this.fromDate.enContent === '') {
        this.$message.warning(this.$lang('英文内容不能为空'))
        return
      }
      if (!this.fromDate.inTitle || this.fromDate.inTitle === '') {
        this.$message.warning(this.$lang('印尼标题不能为空'))
        return
      }
      if (!this.fromDate.inContent || this.fromDate.inContent === '') {
        this.$message.warning(this.$lang('印尼内容不能为空'))
        return
      }
      const h = this.$createElement;
      this.$msgbox({
        message: h('p', null, [
          h('span', { style: 'color: #2B2D42;line-height:80px' }, this.$lang('是否修改该配置？'))
        ]),
        showCancelButton: true,
        confirmButtonText: this.$lang('确定'),
        cancelButtonText: this.$lang('取消'),
        iconClass: 'icon-jinggao',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            const list = {
              type: 2,
              level: this.fromDate.level,
              secondLevel: this.fromDate.secondLevel,
              title: this.fromDate.title,
              content: this.fromDate.content,
              enTitle: this.fromDate.enTitle,
              enContent: this.fromDate.enContent,
              inTitle: this.fromDate.inTitle,
              inContent: this.fromDate.inContent,
              imgurl: this.fromDate.imgurl,
              id: this.id
            }
            // paramsList.map(item => Object.assign(item, this.baseForm))
            this.upload(list)
            setTimeout(() => {
              done();
              this.$router.go(-1)
              this.removeTag('editSmallImgconfig')
              setTimeout(() => {
                instance.confirmButtonLoading = false;
              }, 300);
            }, 3000);
          } else {
            done();
          }
        }
      })
    },
    async upload(params) {
      const res = await this.COM_HTTP.reqModify(params)
      if (res.code === 1) {
        this.$message.success(this.$lang('修改成功'))
        // this.$router.push({ path: '/app/recruit' })
      } else {
        this.$message.warning(res.msg)
      }
    },
    handleAvatarSuccess(res) {
      this.fromDate.imgurl = res.data;
      this.COM_HTTP.reqImgUrl({ path: res.data }).then((res) => {
        this.fromDate.imgLink = res.data[0] || "";
      });
    },
    beforeAvatarUpload(file) {
      const isJPG =
          file.type === "image/jpeg" ||
          file.type === "image/png" ||
          file.type === "image/jpg";
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error(this.$lang('Please upload a picture file with the suffix of .jpeg|.png|or.jpg'));
        return false;
      }
      if (!isLt2M) {
        this.$message.error(this.$lang('Upload image size cannot exceed 2M'));
        return false;
      }
      return isJPG && isLt2M;
    },
    async getLevels() {
      const { data } = await this.COM_HTTP.reqLevel()
      this.levelList = data
    }
  }
}

</script>
<style lang="scss">
.bannerDeatil{
  .el-form .el-form-item .el-form-item__label{
    color: #9499A3;
    line-height: 40px;
  }
  .el-form .el-form-item .el-form-item__label{
    font-weight: 400;
  }
  .el-form-item__content{
    line-height: 1;
  }
}
</style>
<style lang="scss" scoped>
.bannerDeatil{
  .title{
    font-size: 20px;
    font-weight: bold;
    color: #2B2D42;
    line-height: 24px;
  }
  .from{
    .formitem{
      margin-top: 20px;
    }
    .title-input{
      width: 894px;
    }
    .timeinput{
      width: 270px;
    }
    .imgbox{
      // width: 428px;
      // height: 188px;
      border-radius: 4px;
      overflow: hidden;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .contenthtml{
      margin-top: 10px;
    }
  }
  .btnbox{
    position: absolute;
    background: #fff;
    left: 230px;
    bottom: 18px;
    z-index: 999;
    .canel{
      width: 120px;
    }
  }
}
.AddRecruit{
  .title{
    font-size: 20px;
    font-weight: bold;
    color: #2B2D42;
    line-height: 24px;
    margin-bottom: 10px;
  }
  .forms{
    width: 100%;
    display: flex;
    .el-form{
      width: 220px;
    }
  }
  .forms-2cols{
    width: 100%;
    height: 620px;
    display: flex;
    .el-form{
      width: 440px;
    }
  }
  .from{
    // margin: 0 -10px;
    display: flex;
    flex-flow: wrap;
    transition: height 0.3s;
    align-content: flex-start;
    .formitem{
      margin-bottom: 0;
      padding: 14px 20px 10px 0;
      min-width: 200px;
      height: 50px;
    }
  }
  .btnbox{
    position: absolute;
    bottom: 30px;
    left: 250px;
    .publish{
      background: #00B075;
      color: #fff;
      width:120px;
      border: none;
    }
    .canel{
      width: 120px;
    }
  }
  .el-form-item__label{
    line-height: 18px;
  }
  .describe{
    margin-top: 10px;
  }
  .editbox{
    padding: 10px 0;
  }
  .arequired {
    position: relative;
    &::before {
      content: '*';
      position: absolute;
      left: 7px;
      top: 7px;
      font-size: 14px;
      color: #f44;
    }
  }
  .uploadImg {
    width: 100%;
    height: 110px;
    position: relative;
    box-sizing: border-box;
    display: inline-block;
    border: 1px solid #c4c8cc;

    .avatar-uploader,
    & ::v-deep .el-upload {
      width: 100%;
      height: 100%;
    }

    img {
      width: 100%;
      height: 110px;
      font-size: 0;
      vertical-align: middle;
    }
  }
}
</style>
