<!--
 * @Date: 2020-09-04 16:58:35
 * @Author: MX
 * @LastEditTime: 2021-12-06 18:19:10
 * @Description: 头部
-->
<template>
  <div class="topBar"  :style="{display:isLogin?'none':''}">
    <el-header style="height:48px;" class="top-header">
      <div class="top-header-logo">
        <img src="@/assets/images/home/<USER>" alt="">
        <!-- <i class="iconfont iconjitulogo_fanbai"></i>
        <i class="iconfont iconlogo_kaifangpingtaifanbai"></i> -->
      </div>
      <div class="header-action">
        <!-- <el-dropdown class="lm-i18n m15">
          <span class="el-dropdown-link">
            {{i18nValue}}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown" class="dropdown">
            <el-dropdown-item v-for="item in langList" :key="item.value" @click.native="SET_LANG(item.value)" :class="lang === item.value && 'active'">{{item.local}}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown> -->
        <img src="@/assets//images/register/head.png" alt="" />
        <span class="name">{{ user.account }}</span>
        <i class="iconfont icondaohangshezhi_tuichujihuo" @click="logOut"></i>
      </div>
    </el-header>
  </div>
</template>

<script type="text/javascript">
import { mapGetters,mapActions } from 'vuex'
import store from '@/store'
export default {

  name: "TopBar",
  props: {},
  components: { },
  data() {
    return {
      showAside:true
    };
  },
  computed: {
    ...mapGetters(['token', 'user', "lang", "langList"]),
    i18nValue() {
      const langObj = this.langList.find((v) => v.value === this.lang);
      if (langObj) {
        return langObj.local;
      } else {
        return "";
      }
    },
    isLogin() {
      return store.getters.noLogin;
    },
  },
  methods: {
       ...mapActions(['LOGIN_OUT', "SET_LANG"]),
       /**
         * @description 退出
         */
        logOut(){
            
            this.LOGIN_OUT().then(() => {
                this.$router.push({ path: '/login' })
            })
        },
  },
  watch: {
    isLogin(status) {
      if (status) {
        this.showAside = false;
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.topBar{
    background: $theme-color;
    color: #ffffff;
    line-height: 48px;
    .top-header {
      display: flex;
      justify-content: space-between;
      .top-header-logo{
        img{
          vertical-align: text-top;
        }
      }
    }
    .iconfont{
        font-size: 23px;
    }
    .iconlogo_kaifangpingtaifanbai {
      border-left: 1px solid #fff;
      margin-left: 20px;
      padding-left: 20px;
    }
    .lm-i18n {
      margin-right: 24px;
      border: 1px solid #fff;
      border-radius: 15px;
      padding: 5px 5px 5px 10px;
      font-size: 12px;
      cursor: pointer;
      line-height: 1;
    }
    .header-action{
        // float: right;
        img{
            vertical-align: middle;
            width: 28px;
            height: 28px;
        }
        .name{
            margin: 0 10px;
        }
        .icondaohangshezhi_tuichujihuo{
            font-size: 14px;
            cursor: pointer;
        }
        .m15{
          margin:0  15px;
        }
        .el-dropdown-link {
          color: #fff;
        }
        .icondaohangshezhi_tuichujihuo {
          margin: 0 10px;
        }
    }
}
</style>
<style lang="scss">
.dropdown {
  .el-dropdown-menu__item:hover {
    background: #f4f6f8 !important;
    color: $theme-color !important;
  }
  .active {
    color: $theme-color !important;
  }
}
</style>