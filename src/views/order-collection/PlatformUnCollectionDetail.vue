<!--
 * @Date: 2021-01-28 15:46:24
 * @Author: MX
 * @Description:未及时揽收明细
-->
<template>
  <div class="main-wrap platform-unCollection-detail">
    <avue-crud
      ref="params"
      :data="tableList"
      :option="listOpt"
      :page="page"
      :table-loading="loading"
      @size-change="sizeChange"
      @search-reset="resetList"
      @current-change="currentChange"
      @export-excel="outExcel"
      @search-change="searchChange">
         <!-- 订单运单号 -->
      <template slot="idListSearch">
        <div class="inputOrder">
          <template>
            <el-radio v-model="orderNoType" :label="noType.trackingNo">{{ $lang('订单号') }}</el-radio>
            <el-radio v-model="orderNoType" :label="noType.orderNo">{{ $lang('运单号') }}</el-radio>
          </template>
          <base-input-tag
            v-if="orderNoType === noType.trackingNo"
            v-model="searchForm.orderId"
            :addTagOnBlur="true"
            :clearBtnVisible="true"
            validate="orderName"
            maxHeight="360px"
            :limit="1"
            :placeholder="$lang('请输入订单号(仅支持单条查询)')"
            class="cms-input-tag">
          </base-input-tag>
          <base-input-tag v-else v-model="searchForm.waybillId" :addTagOnBlur="true" :clearBtnVisible="true" validate="codeName" maxHeight="360px" :limit="1" class="cms-input-tag"  :placeholder="'请输入运单号(仅支持单条查询)'">
          </base-input-tag>
        </div>
      </template>
        <!-- 下载中心 -->
      <template slot="menuLeft">
        <el-button type="info" size="small" @click="dialogOutputVisible = true" v-if="hasPower('OUTPUT')" :loading="loadingOutput">{{ $lang('下载中心') }}</el-button>
             <el-tooltip effect="dark" placement="bottom">
                <span class="tooltip-click">?</span>
                <div slot="content">
                    1.{{$lang('按订单揽收截止时间统计于2021-03-04 00:00开始，最早可查询2021-03-04的订单数据')}}<br>
                    2.{{$lang('按订单录入时间统计于2021-03-03 23:59结束，最晚可查询2021-03-03的订单数据')}}<br>
                    3.{{$lang('延误揽收：指在揽收截止时间后至统计时间前揽收的订单')}}<br>
                    4.{{$lang('统计前未揽收：指在截止统计时间点还未揽收的订单')}}<br>
                    5.{{$lang('统计时间：按订单录入时间统计，每日08:00统计T-3的数据;按订单揽收截止时间统计每日08:00统计T-1的数据，当日数据每小时准点刷新一次')}}<br>
                    6.{{$lang('由于历史按订单录入时间统计未加入客户信息，仅按订单揽收截止时间统计的情况下可以查询客户信息')}}
                </div>
            </el-tooltip>
      </template>
    </avue-crud>
    <!-- 下载中心弹窗 -->
    <ExportDownload :moduleType="moduleType" downLoadLog="downloadPlatformOrderPickRateDescStat" :dialogOutputVisible.sync="dialogOutputVisible" />
  </div>
</template>

<script type="text/javascript">
import { noType } from '@public/utils/const'
import mixin from '@mixins/mixin'
import { DICT } from '@/common/utils/dict'
import ExportDownload from '@/components/ExportDownload'
import { NT_HEADQUARTERS, NT_AGENT_AREA, NT_CENTER, NT_FIRST_LEVEL } from '@public/utils/const'
import { PlatformUnCollectionDetail, OrderClassifyList } from '../pool.js'
import { RESPONSE_CODE } from '@public/http/config'
export default {
  name: 'PlatformUnCollectionDetail',
  mixins: [mixin],
  props: {},
  components: { ExportDownload },
  data() {
    return {
      COM_HTTP: PlatformUnCollectionDetail,
      orderClassifyList: OrderClassifyList,
      noType, // 运单号 订单号
      searchType: null, // 代理区加盟商网点
      loadingOutput: false, // 导出loading
      orderNoType: noType.trackingNo, // 默认订单号
      searchForm: {
        waybillId: [],
        orderId: []
      },
      moduleType: '平台未及时揽收明细', // 下载中心type
      dialogOutputVisible: false, // 下载中心dialog
      getDictList: [
        {
          code: 'D67',
          name: this.$lang('紫金山')
        },
        {
          code: 'D190',
          name: this.$lang('逍遥峰')
        }
      ], // 字典列表
      orderGroupList: [], // 订单分类
      onLineDay: '2021-3-3' // 上线时间
    }
  },
  computed: {
    listOpt() {
      const that = this
      const startTime = this.$dayjs(new Date().getTime()).format('YYYY-MM-DD') + ' 00:00:00'
      const endTime = this.$dayjs(new Date().getTime()).format('YYYY-MM-DD') + ' 23:59:59'
      return {
        searchExtend: true,
        header: true,
        border: true,
        fixHeight: 50,
        menu: false,
        exportBtn: this.hasPower('OUTPUT'),
        column: [
          {
            label: '',
            prop: 'idList',
            search: true,
            type: 'select',
            disabled: true,
            searchslot: true,
            hide: true,
            sort: 0
          },
          {
            label: this.$lang('订单揽收截止开始时间'),
            searchPlaceholder: this.$lang('请选择订单揽收截止开始时间'),
            search: true,
            type: 'datetime',
            hide: true,
            prop: 'pickEndTimeStart',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            searchDefault: this.$route.query.time ? this.$dayjs(new Date(this.$route.query.time).getTime()).format('YYYY-MM-DD') + ' 00:00:00' : startTime,
            relationProps: ['pickEndTimeEnd'],
            searchClearable: false,
            pickerOptions: {
              disabledDate: time => {
                const space = 90 * 24 * 3600 * 1000
                let minTime;
                const onLineDaytimes = new Date(this.onLineDay).getTime()
                // 1.如果当前天 - 发版日20号 < 90天 最小天为发版日20号
                // 2.如果当前天 - 发版日20号 > 90天 最小天 当前天 - 90天
                if (new Date().getTime() - onLineDaytimes < space) {
                  minTime = onLineDaytimes
                } else {
                  minTime = new Date().getTime() - space
                }
                const maxTime = new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1// 最大值为当前
                return time.getTime() < minTime || time.getTime() > maxTime
              }
            },
            change(e) {
              that.$refs.params.searchForm.pickEndTimeEnd = ''
            }
          }, {
            label: this.$lang('订单揽收截止结束时间'),
            searchPlaceholder: this.$lang('请选择订单揽收截止结束时间'),
            search: true,
            type: 'datetime',
            hide: true,
            prop: 'pickEndTimeEnd',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            searchDefault: this.$route.query.time ? this.$dayjs(new Date(this.$route.query.time).getTime()).format('YYYY-MM-DD') + ' 23:59:59' : endTime,
            searchClearable: false,
            pickerOptions: {
              disabledDate: time => {
                const space = 90 * 24 * 3600 * 1000
                const { pickEndTimeStart } = that.$refs.params.searchForm
                const minTime = pickEndTimeStart ? new Date(pickEndTimeStart).getTime() : new Date().getTime() - space
                const endTime = new Date(pickEndTimeStart).getTime() + 7 * 24 * 60 * 60 * 1000 - 1
                const maxTime = new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1// 最大值为当前
                return time.getTime() < minTime || time.getTime() > endTime || time.getTime() > maxTime
              }
            }
          },
          {
            label: this.$lang('代理区/加盟商/网点'),
            prop: 'searchCode',
            type: 'network',
            props: {
              label: 'name',
              value: 'code'
            },
            selectDic: [{ name: this.$route.query.searchCodeName, code: this.$route.query.searchCode }],
            useCurNetworkInfo: true, // 使用当前用户登录的网点信息
            search: true,
            hide: true,
            searchDefault: this.$route.query.searchCode ? this.$route.query.searchCode : '',
            change({ valueObj }) {
              if (valueObj.typeId === NT_AGENT_AREA) { that.searchType = 1 } // 代理区334
              if (valueObj.typeId === NT_CENTER) { that.searchType = 2 } // 加盟商335
              if (valueObj.typeId === NT_FIRST_LEVEL) { that.searchType = 3 } // 网点商336
              if (valueObj.typeId === NT_HEADQUARTERS) { that.searchType = null } // 总部
              if (!valueObj.typeId) { that.searchType = null }
            }
          },
          {
            label: this.$lang('订单揽收截止时间'),
            prop: 'pickEndTime'
          },
          {
            label: this.$lang('实际揽收时间'),
            prop: 'pickTime'
          },
          {
            label: this.$lang('订单录入时间'),
            prop: 'inputTime'
          },
          {
            label: this.$lang('订单号'),
            prop: 'id'
          },
          {
            label: this.$lang('运单号'),
            prop: 'waybillId'
          },
          {
            label: this.$lang('订单来源'),
            prop: 'orderSourceCodes',
            viewProp: 'orderSourceName',
            search: true,
            type: 'select',
            searchDefault: this.$route.query.orderSourceCode ? this.$route.query.orderSourceCode.split(',') : [],
            dicData: this.getDictList,
            searchPlaceholder: this.$lang('请选择订单来源'),
            props: {
              label: 'name',
              value: 'code',
              res: 'records'
            },
            dataType: 'array',
            searchMmultiple: true,
            searchFilterable: true,
            hasAll: false
          },
          {
            label: this.$lang('订单分类'),
            prop: 'orderSourceGroupId',
            viewProp: 'orderSourceGroupName',
            search: true,
            type: 'select',
            searchDefault: this.$route.query.orderSourceGroupId ? Number(this.$route.query.orderSourceGroupId) : '',
            props: {
              label: 'name',
              value: 'id'
            },
            dicData: this.orderGroupList
          },
          {
            label: this.$lang('揽收类型'),
            search: true,
            type: 'select',
            prop: 'takingType',
            dicData: DICT.collectType,
            formatter({ takingType }) {
              return takingType === 1 ? that.$lang('延误揽收') : that.$lang('统计前未揽收')
            }
          },
          {
            label: this.$lang('客户信息'),
            prop: 'customerCode',
            hide: true,
            search: true,
            type: 'select',
            remote: true,
            searchPlaceholder: this.$lang('请输入输入客户名称或编码'),
            props: {
              label: 'name',
              value: 'code'
            },
            dicUrl: `${this.$selectUrl.customer}?institutionalLevelId=${this.user.institutionalLevelId}&networkId=${this.user.networkId}&name={{key}}&current=1&size=9999`,
            tpyeformat: (data) => `${data.name} | ${data.code}`
          },
          {
            label: this.$lang('始发省份'),
            prop: 'senderProvinceName'
          },
          {
            label: this.$lang('始发城市'),
            prop: 'senderCityName'
          },
          {
            label: this.$lang('始发区县'),
            prop: 'senderAreaName'
          },
          {
            label: this.$lang('代理区'),
            prop: 'proxyAreaName'
          },
          {
            label: this.$lang('加盟商'),
            prop: 'franchiseesName'
          },
          {
            label: this.$lang('网点'),
            prop: 'pickNetworkName'
          },
          {
            label: this.$lang('网点编码'),
            prop: 'pickNetworkCode'
          },
          {
            label: this.$lang('客户编码'),
            prop: 'customerCode'
          },
          {
            label: this.$lang('客户名称'),
            prop: 'customerName'
          },
          {
            label: this.$lang('揽件员'),
            prop: 'pickStaffName'
          }
        ]
      }
    }
  },
  created() {
    // 过滤字典拼多多
    this.searchType = this.$route.query.searchType

    // 订单来源分类枚举列表
    this.getOrderGroupList()
  },
  activated() {
    this.$refs.params.searchForm.searchType = this.$route.query.searchType
    this.$refs.params.searchForm.searchCode = this.$route.query.searchCode ? this.$route.query.searchCode : ''
    this.$refs.params.searchForm.searchCodeName = this.$route.query.searchCodeName ? this.$route.query.searchCodeName : ''
    this.$refs.params.searchForm.orderSourceCodes = this.$route.query.orderSourceCode ? this.$route.query.orderSourceCode.split(',') : ''
    this.$refs.params.searchForm.orderSourceGroupId = this.$route.query.orderSourceGroupId ? Number(this.$route.query.orderSourceGroupId) : ''

    // 再次跳转时去掉选择了的条件
    this.$refs.params.searchForm.takingType = ''
    this.searchForm.orderId = []
    this.searchForm.waybillId = []
    // 处理时间
    this.handleTime()

    this.searchFun()
  },
  methods: {
    /**
     * 处理时间
    */
    handleTime() {
      this.$refs.params.searchForm.pickEndTimeStart = this.$dayjs(new Date(this.$route.query.time).getTime()).format('YYYY-MM-DD') + ' 00:00:00'
      this.$refs.params.searchForm.pickEndTimeEnd = this.$dayjs(new Date(this.$route.query.time).getTime()).format('YYYY-MM-DD') + ' 23:59:59'
    },

    /**
     * 订单来源分类枚举列表
    */
    async getOrderGroupList() {
      const { code, data, msg } = await this.orderClassifyList.getOrderGroupList()
      if (code === RESPONSE_CODE.SUCCESS) {
        this.orderGroupList = data
      } else {
        this.$message.error(msg)
      }
    },
    /**
     * 列表查询参数处理
     */
    searchFunParamsHandle(params) {
      params.current = this.page.current
      params.size = this.page.size

      // 因为分页查询优化，如果用户点击另一个页码，其他搜索条件不变，那么传递的参数额外加一个allCount
      if (params.current !== 1) {
        params.total = this.page.total
      }
      // 选了代理区/加盟商/网点
      if (this.searchType) {
        params = Object.assign({}, params, { searchType: this.searchType })
      }
      // 处理清空代理区/加盟商/网点情况
      if (!params.searchCode) {
        this.$message.warning(this.$lang('请选择代理区/加盟商/网点查询'))
        return false
      }
      // 处理订单号运单号 如果带运单号订单号 searchType、searchCode传账号的code和type
      let userAccountSearchType
      const { institutionalLevelId } = this.user
      if (institutionalLevelId === NT_AGENT_AREA) {
        userAccountSearchType = 1
      } // 代理区334
      if (institutionalLevelId === NT_CENTER) {
        userAccountSearchType = 2
      } // 加盟商335
      if (institutionalLevelId === NT_FIRST_LEVEL) {
        userAccountSearchType = 3
      } // 网点商336
      const { orderId, waybillId } = this.searchForm
      if (this.orderNoType === noType.trackingNo && orderId.length) {
        params = {
          orderId: orderId.join(','),
          current: this.page.current,
          size: this.page.size,
          searchCode: this.user.networkCode,
          searchType: userAccountSearchType
        }
        if (isNaN(params.orderId.replace(/,/g, ''))) {
          this.$message.error(this.$lang('订单编号为全数字'))
          return false
        }
      } else if (this.orderNoType === noType.orderNo && waybillId.length) {
        params = {
          waybillId: waybillId.join(','),
          current: this.page.current,
          size: this.page.size,
          searchCode: this.user.networkCode,
          searchType: userAccountSearchType
        }
      }
      return params
    },
    /**
     * 重置
     */
    resetList() {
      this.searchForm.orderId = []
      this.searchForm.waybillId = []
      this.orderNoType = noType.trackingNo
      this.page.current = 1
      this.searchType = this.$route.query.searchType
      this.$refs.params.searchForm.orderSourceCodes = []
      this.$refs.params.searchForm.searchCode = ''
      this.$refs.params.searchForm.orderSourceGroupId = ''

      this.handleTime()
    },
    /**
     * 异步导出
     */
    async outExcel() {
      try {
        let params = null
        if (this.$refs.hasOwnProperty('params')) {
          params = this.rangHandle(this.$refs.params.searchForm)
        }
        const param = this.searchFunParamsHandle(params)
        const countryCode = this.$store.state.base.lang
        param.countryCode = countryCode
        this.loading = true
        const res = await this.COM_HTTP.reqExcel(param)
        this.loading = false
        if (res) {
          // 请求是responseType: 'arraybuffer',错误时返回的是json
          const enc = new TextDecoder('utf-8')
          let errorData = { code: null }
          try {
            errorData = JSON.parse(enc.decode(new Uint8Array(res))) // 转化成json对象
          } catch (error) {
            errorData.code = null
          }
          if (!this.validatenull(errorData.code) && errorData.code !== 1) {
            this.$message.error(errorData.msg)
          } else {
            this.$message.success(this.$lang('导出成功,请到下载中心查看'))
          }
        }
      } catch (error) {
        this.loading = false
        console.log('outExcel::error', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.platform-unCollection-detail {
  .cms-input-tag {
    width: 354px;
  }
    .tooltip-click {
        @include base-color('bg');
        cursor: pointer;
        border-radius: 10px;
        color: #fff;
        display: inline-block;
        font-size: 12px;
        height: 18px;
        line-height: 18px;
        padding: 0 6px;
        text-align: center;
        white-space: nowrap;
        border: 1px solid #fff;
        -webkit-text-fill-color: #fff;
    }
}
</style>
