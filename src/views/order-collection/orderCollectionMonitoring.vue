/**
 * @description: 订单揽收监控-210127 优化
 * @author: lxw
 */
<template>
  <div class="main-wrap order-collection-monitoring">
    <el-tabs class="mw-tab" closable v-model="activeName" @tab-remove="tabRemove">
      <el-tab-pane :label="$lang('订单揽收监控')" name="first">
        <avue-crud ref="params" :data="tableList" v-model="formData" :option="listOpt" :page="page" :table-loading="loading" :exportLoading="exportLoading"
          @size-change="sizeChange" @search-reset="resetList" @current-change="currentChange" @search-change="searchChange" @export-excel="outExcel"
          @export-excel-all="outExcel">
          <template slot="senderAreaIdSearch">
            <BaseAddressCader ref="baseAddressCader"
              :clearable="true"
              v-model="senderAddr"
              :showLevel="2">
            </BaseAddressCader>
          </template>
          <!-- 加盟商 -->
          <template slot="franchiseeCodeSearch">
            <el-select v-model="franchiseeCode" filterable clearable :placeholder="$lang('请选择加盟商')">
              <el-option
                  v-for="item in franchiseesList"
                  :key="item.code"
                  :label="item.name"
                  clearable
                  :value="item.code">
              </el-option>
            </el-select>
          </template>
          <template slot="ignore100PercentSearch">
            <el-checkbox v-model="ignore100Percent" true-label='1' false-label='2'>{{ $lang('过滤揽收100%')}}</el-checkbox>
          </template>
          <template slot="totalQty" slot-scope="{row}">
            <div class="pm-click" @click="propClick(row, 1, row.totalQty)"><span>{{row.totalQty}}</span></div>
          </template>
          <template slot="takingQty" slot-scope="{row}">
            <div class="pm-click" @click="propClick(row, 2, row.takingQty)"><span>{{row.takingQty}}</span></div>
          </template>
          <template slot="noTakingQty" slot-scope="{row}">
            <div class="pm-click" @click="propClick(row, 3, row.noTakingQty)"><span>{{row.noTakingQty}}</span></div>
          </template>
          <template slot="noTakingQty6" slot-scope="{row}">
            <div class="pm-click" @click="propClick(row, 4, row.noTakingQty6)"><span>{{row.noTakingQty6}}</span></div>
          </template>
          <template slot="noTakingQty12" slot-scope="{row}">
            <div class="pm-click" @click="propClick(row, 5, row.noTakingQty12)"><span>{{row.noTakingQty12}}</span></div>
          </template>
          <template slot="noTakingQty1224" slot-scope="{row}">
            <div class="pm-click" @click="propClick(row, 6, row.noTakingQty1224)"><span>{{row.noTakingQty1224}}</span></div>
          </template>
          <template slot="noTakingQty2224" slot-scope="{row}">
            <div class="pm-click" @click="propClick(row, 7, row.noTakingQty2224)"><span>{{row.noTakingQty2224}}</span></div>
          </template>
          <template slot="noTakingQty2448" slot-scope="{row}">
            <div class="pm-click" @click="propClick(row, 8, row.noTakingQty2448)"><span>{{row.noTakingQty2448}}</span></div>
          </template>
          <template slot="noTakingQty48" slot-scope="{row}">
            <div class="pm-click" @click="propClick(row, 9, row.noTakingQty48)"><span>{{row.noTakingQty48}}</span></div>
          </template>
          <template slot="menuLeft">
            <el-button type="info" size="small" @click="dialogOutputVisible = true" v-if="hasPower('OUTPUT')" :loading="loadingOutput"><i class="icon-iconfont iconfont iconanniu-xiazaizhongxin"></i><span>{{$lang('下载中心')}}</span></el-button>
            <el-tooltip effect="dark" placement="bottom">
              <span class="tooltip-click">?</span>
              <div slot="content">
                  1.{{$lang('客户订单量：已取消的订单不统计')}}<br>
                  2.{{$lang('已揽收量：已取消的订单不统计')}}<br>
                  3.{{$lang('未揽收总量：已取消的订单不统计')}}
              </div>
            </el-tooltip>
          </template>
        </avue-crud>
      </el-tab-pane>
      <el-tab-pane v-if="isShowCustomer" :label="$lang(`${customerTile}`)" name="customerDetail">
        <customer-detail :detail="customerTableParams" @customerClick="customerClick" ref="customerTable"></customer-detail>
      </el-tab-pane>
      <el-tab-pane v-if="isShowDetal" :label="$lang(`${customerTile}明细`)" name="signDetail">
        <sign-table :params="signTableParams" ref="signTable"></sign-table>
      </el-tab-pane>
    </el-tabs>
    <ExportDownload :moduleType="moduleType" downLoadLog='downloadCollectionMonitorMain' :dialogOutputVisible.sync="dialogOutputVisible"/>
  </div>
</template>

<script>
import mixin from '@mixins/mixin'
import { OrderCollectionMonitoring } from '../pool'
import SignTable from './sign-monitor/signTable'
import customerDetail from './sign-monitor/customerDetail'
import { commonFun } from '@public/utils/common'
import ExportDownload from '@/components/ExportDownload'
import { RESPONSE_CODE } from '@public/http/config'
import dayjs from 'dayjs'
const dateRange = commonFun.GetTodayDate()

export default {
  name: 'OrderCollectionMonitoring',
  mixins: [mixin],
  components: {
    SignTable,
    customerDetail,
    ExportDownload
  },
  data() {
    return {
      COM_HTTP: OrderCollectionMonitoring,
      activeName: 'first',
      isShowDetal: false,
      isShowCustomer: false,
      customerTile: this.$lang('客户订单统计'),
      loading: false, // 表格loading状态
      getDictList: [], // 字典列表 过滤拼多多
      customerTableParams: {},
      signTableParams: {},
      detailsId: '',
      //  表单数据
      formData: {},
      senderAddr: {}, // 省市
      franchiseesList: [], // 加盟商列表
      franchiseeCode: null, // 加盟商code
      ignore100Percent: '2',
      noTaking: [
        // {
        //   label: this.$lang('未揽收总量'),
        //   value: '3'
        // },
        {
          label: this.$lang('6H以内'),
          value: '4'
        },
        {
          label: this.$lang('6H-12H'),
          value: '5'
        },
        {
          label: this.$lang('12H-22H'),
          value: '6'
        },
        {
          label: this.$lang('22H-24H'),
          value: '7'
        },
        {
          label: this.$lang('24H-48H'),
          value: '8'
        },
        {
          label: this.$lang('48H以上'),
          value: '9'
        }
      ],
      takingRate: [
        {
          label: this.$lang('是'),
          value: 1
        },
        {
          label: this.$lang('否'),
          value: 2
        }
      ],
      moduleType: this.$lang('订单揽收监控'),
      dialogOutputVisible: false,
      loadingOutput: false
    }
  },
  methods: {
    // 清空自定义的搜索参数，省市搜索控件还需要额外执行初始化操作
    resetList() {
      this.ignore100Percent = '2'
      this.franchiseeCode = null
      this.$nextTick(() => {
        this.senderAddr = {}
        setTimeout(() => {
          this.$refs.baseAddressCader.fillBack()
          this.$refs.baseAddressCader.clickTab(0)
        }, 100)
      })
    },
    searchAfterFun() {
      this.tableList = this.tableList.map(item => {
        return {
          ...item,
          noTakingQty: !isNaN(item.noTakingQty) && Number(item.noTakingQty) !== 0 ? Number(item.noTakingQty) : ' ',
          noTakingQty6: !isNaN(item.noTakingQty6) && Number(item.noTakingQty6) !== 0 ? Number(item.noTakingQty6) : ' ',
          noTakingQty12: !isNaN(item.noTakingQty12) && Number(item.noTakingQty12) !== 0 ? Number(item.noTakingQty12) : ' ',
          noTakingQty1224: !isNaN(item.noTakingQty1224) && Number(item.noTakingQty1224) !== 0 ? Number(item.noTakingQty1224) : ' ',
          noTakingQty2224: !isNaN(item.noTakingQty2224) && Number(item.noTakingQty2224) !== 0 ? Number(item.noTakingQty2224) : ' ',
          noTakingQty2448: !isNaN(item.noTakingQty2448) && Number(item.noTakingQty2448) !== 0 ? Number(item.noTakingQty2448) : ' ',
          noTakingQty48: !isNaN(item.noTakingQty48) && Number(item.noTakingQty48) !== 0 ? Number(item.noTakingQty48) : ' ',
          totalQty: !isNaN(item.totalQty) && Number(item.totalQty) !== 0 ? Number(item.totalQty) : ' ',
          takingQty: !isNaN(item.takingQty) && Number(item.takingQty) !== 0 ? Number(item.takingQty) : ' '
        }
      })
    },
    searchFunParamsHandle(parms) {
      console.log('parms', parms)
      if (parms.endTime) {
        // 结束时间，结尾都是23:59:59
        parms.endTime = dayjs(parms.endTime).format('YYYY-MM-DD') + ' 23:59:59'
      }
      this.detailsId = Number(parms.detailsId)
      parms.current = this.page.current
      parms.size = this.page.size
      parms.ignore100Percent = this.ignore100Percent
      // 混入省市id-2021/1/27
      if (this.senderAddr.provinceId) { parms.senderProvinceId = this.senderAddr.provinceId }
      if (this.senderAddr.cityId) { parms.senderCityId = this.senderAddr.cityId }
      // 混入加盟商code
      if (this.franchiseeCode) { parms.franchiseeCode = this.franchiseeCode }
      if (new Date(parms.endTime).getTime() - new Date(parms.startTime).getTime() > 15 * 24 * 60 * 60 * 1000) {
        this.$message.error(this.$lang('时间跨度最多为15天'))
        return false;
      }
      return Object.assign(parms)
    },
    tabRemove(name) {
      this.activeName = 'signDetail'
      if (name === 'signDetail') {
        this.isShowDetal = false
        this.activeName = 'customerDetail'
      } else if (name === 'customerDetail') {
        this.isShowCustomer = false
        this.isShowDetal = false
        this.activeName = 'first'
      }
    },
    customerClick(row) {
      this.activeName = 'signDetail'
      this.signTableParams = { ...row }
      this.isShowDetal = false
      this.$nextTick(() => {
        this.isShowDetal = true
      })
    },
    propClick(row, detailsId, total) {
      console.log('1111', row, detailsId, total);
      if (!total) {
        this.$message.error(this.$lang('没有数据'))
        return
      }
      if (detailsId === 1) {
        this.customerTile = this.$lang('客户订单统计')
      } else if (detailsId === 2) {
        this.customerTile = this.$lang('已揽收统计')
      } else {
        this.customerTile = this.$lang('未揽收统计')
      }
      this.activeName = 'customerDetail'
      this.customerTableParams = { ...row }
      this.customerTableParams.detailsId = detailsId
      this.customerTableParams.startTime = this.$dayjs(row.statDate).format('YYYY-MM-DD') + ' 00:00:00'
      this.customerTableParams.endTime = this.$dayjs(row.statDate).format('YYYY-MM-DD') + ' 23:59:59'
      this.customerTableParams.total = Number(total)
      this.isShowCustomer = false
      this.$nextTick(() => {
        this.isShowCustomer = true
      })
    },
    // avue-导出方法
    async outExcel() {
      try {
        let params = null
        if (this.$refs.hasOwnProperty('params')) {
          params = this.rangHandle(this.$refs.params.searchForm)
        }
        const param = this.searchFunParamsHandle(params)
        const countryCode = this.$store.state.base.lang
        param.countryCode = countryCode
        this.loading = true
        const res = await this.COM_HTTP.reqExcel(param)
        this.loading = false
        if (res) {
          // 请求是responseType: 'arraybuffer',错误时返回的是json
          const enc = new TextDecoder('utf-8')
          let errorData = { code: null }
          try {
            errorData = JSON.parse(enc.decode(new Uint8Array(res))) // 转化成json对象
          } catch (error) {
            errorData.code = null
          }
          if (!this.validatenull(errorData.code) && errorData.code !== 1) {
            this.$message.error(errorData.msg)
          } else {
            this.$message.success(this.$lang('导出成功,请到下载中心查看'))
          }
        }
      } catch (error) {
        this.loading = false
        console.log('outExcel::error', error)
      }
    },
    // 获取加盟商
    async getFranchisee() {
      this.COM_HTTP.getFranchisee().then(res => {
        if (res.code === RESPONSE_CODE.SUCCESS) {
          this.franchiseesList = res.data
        } else {
          this.franchiseesList = [];
        }
      })
    }
  },
  mounted() {},
  created() {
    // 过滤字典拼多多
    const dictList = commonFun.getDict('ZD108')
    this.getDictList = dictList.map(element => {
      return {
        ...element,
        name: element.name,
        label: element.label
      }
    })
    // 加盟商列表请求
    this.getFranchisee()
  },
  activated() {
    if (Number(this.$route.params.status) > 0) {
      this.$refs.params.searchReset()
      this.$refs.params.searchForm.startTime = dateRange[0]
      this.searchFun()
    }
  },
  computed: {
    listOpt() {
      const that = this
      // 客户来源
      that.getDictList.push({
        name: that.$lang('散客'),
        id: 0,
        code: 99
      })
      const { networkId, institutionalLevelId } = that.user
      const startTime = this.$route.params.status ? dateRange[0] : this.$dayjs(new Date().getTime() - 14 * 24 * 3600 * 1000).format('YYYY-MM-DD') + ' 00:00:00'
      return {
        addBtn: false,
        menu: false,
        header: true,
        border: true,
        // pagination: false,
        fixHeight: 50,
        customEdit: true,
        exportBtn: this.hasPower('OUTPUT'),
        column: [
          {
            label: '订单录入开始时间',
            prop: 'startTime',
            search: true,
            hide: true,
            type: 'date',
            searchClearable: false,
            editable: false,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            searchDefault: startTime,
            relationProps: ['endTime'],
            pickerOptions: {
              disabledDate: time => {
                const space = 30 * 24 * 3600 * 1000
                const minTime = new Date().getTime() - space
                const maxTime = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
                return time.getTime() < minTime || time.getTime() > maxTime
              }
            }
          },
          {
            label: '订单录入结束时间',
            prop: 'endTime',
            search: true,
            hide: true,
            type: 'date',
            searchClearable: false,
            editable: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            searchDefault: dateRange[1],
            pickerOptions: {
              disabledDate: time => {
                const space = 30 * 24 * 3600 * 1000
                const curDate = new Date().getTime()
                const { startTime } = that.$refs.params.searchForm
                const startTimes = startTime || new Date().getTime() - space
                const minTime = new Date(startTimes).getTime()
                const maxTime = new Date(new Date(new Date(startTimes).toLocaleDateString()).getTime() + 15 * 24 * 60 * 60 * 1000 - 1)
                return time.getTime() < minTime || time.getTime() > maxTime || time.getTime() > curDate
              }
            }
          },
          {
            label: '日期',
            prop: 'statDate',
            formatter: (row, value, label, column) => {
              return row.statDate ? this.$dayjs(row.statDate).format('YYYY-MM-DD') : '--'
            }
          },
          {
            label: '发件省',
            prop: 'senderProvinceName'
          },
          {
            label: '发件市',
            prop: 'senderCityName'
          },
          {
            label: '代理区',
            prop: 'proxyAreaCode',
            viewProp: 'proxyAreaName',
            type: 'network',
            search: networkId === this.$constant.NT_AGENT_AREA || networkId === this.$constant.NT_HEADQUARTERS,
            addDisplay: false,
            editDisabled: true,
            props: {
              label: 'name',
              value: 'code'
            },
            remoteQuery: {
              typeId: 334
            }
          },
          {
            label: '发件区域',
            prop: 'senderAreaId',
            search: true,
            hide: true,
            searchslot: true
          },
          {
            label: '加盟商',
            prop: 'franchiseeCode',
            type: 'select',
            hide: true,
            search: institutionalLevelId === this.$constant.NT_AGENT_AREA || institutionalLevelId === this.$constant.NT_HEADQUARTERS,
            searchslot: true
          },
          {
            label: '加盟商',
            prop: 'franchiseeName'
          },
          {
            label: '网点',
            prop: 'siteCode',
            viewProp: 'siteName',
            type: 'network',
            props: {
              label: 'name',
              value: 'code'
            },
            selectDic: [{ id: this.user.networkId, name: this.user.networkName, code: this.user.networkCode }],
            // 使用当前用户登录的网点信息
            // searchDefault: this.user.networkCode,
            useCurNetworkInfo: true,
            // formatter: (row, value, label, column) => row.networkName,
            search: true
          },
          {
            label: '客户订单量',
            prop: 'totalQty',
            slot: true
          },
          {
            label: '已揽收',
            prop: 'takingQty',
            slot: true
          },
          {
            label: '未揽收',
            labelAlias: '未揽收区间',
            prop: 'detailsId',
            type: 'select',
            // searchMmultiple: true, // 多选
            search: true,
            dicData: this.noTaking,
            headerAlign: 'center',
            children: [
              {
                label: '未揽收总量',
                prop: 'noTakingQty',
                sortable: true,
                slot: true
                // hide: that.detailsId && that.detailsId !== 3
              },
              {
                label: '6H以内',
                prop: 'noTakingQty6',
                sortable: true,
                slot: true
              },
              {
                label: '6H-12H以内',
                prop: 'noTakingQty12',
                sortable: true,
                slot: true
                // hide: that.detailsId && that.detailsId !== 4
              },
              {
                label: '12H-22H',
                prop: 'noTakingQty1224',
                sortable: true,
                slot: true
                // hide: that.detailsId && that.detailsId !== 5
              },
              {
                label: '22H-24H',
                prop: 'noTakingQty2224',
                sortable: true,
                slot: true
                // hide: that.detailsId && that.detailsId !== 5
              },
              {
                label: '24H-48H',
                prop: 'noTakingQty2448',
                sortable: true,
                slot: true
                // hide: that.detailsId && that.detailsId !== 6
              },
              {
                label: '48H以上',
                prop: 'noTakingQty48',
                sortable: true,
                slot: true
                // hide: that.detailsId && that.detailsId !== 7
              }
            ]
          },
          {
            label: '',
            labelAlias: '揽收进度',
            senior: false,
            prop: 'ignore100Percent',
            searchslot: true,
            hide: true,
            // type: 'select',
            search: true,
            // dicData: this.takingRate,
            formatter: (row) => {
              return row.takingQty ? this.$common.floatNumSub((row.takingQty / row.totalQty) * 100) + '%' : '0.00%'
            }
          },
          {
            label: '揽收进度',
            prop: 'takingQty',
            formatter: (row) => {
              return row.takingQty ? this.$common.floatNumSub((row.takingQty / row.totalQty) * 100) + '%' : '0.00%'
            }
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss">
.order-collection-monitoring{
  .el-radio-group{
    margin-top: 10px;
  }
  .el-checkbox{
    margin-top: 24px;
    margin-left: 10px;
  }
  .tooltip-click {
    @include base-color('bg');
    cursor: pointer;
    border-radius: 10px;
    color: #fff;
    display: inline-block;
    font-size: 12px;
    height: 18px;
    line-height: 18px;
    padding: 0 6px;
    text-align: center;
    white-space: nowrap;
    border: 1px solid #fff;
    -webkit-text-fill-color: #fff;
  }
}
</style>
