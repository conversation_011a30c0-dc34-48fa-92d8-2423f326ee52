<template>
  <div class="coupon-typeAdd">
    <el-dialog
      lock-scroll
      :modal-append-to-body="false"
      append-to-body
      destroy-on-close
      :title="$lang(title)"
      :width="$parent.dialogWidth"
      custom-class="type-dialog bottom-rule"
      :visible.sync="batchDetailVisible"
      :styleType="'noBorder'"
      :close-on-click-modal="false"
      @closed="closeCheckDialog"
    >
      <div ref="content" v-loading="$parent.$parent.dialogLoading">
        <el-scrollbar style="height: 100%">
          <el-form :model="checkForm" :rules="rules" ref="checkForm" label-width="110px" class="demo-ruleForm">
            <div>
              <el-row :gutter="24">
                <el-col :span="6">
                  <span class="label-text">{{ $lang('批次名称') }}:</span>
                  <div class="div-content" :title="checkForm.couponBatchName">{{ checkForm.couponBatchName || '--' }}</div>
                </el-col>
                <el-col :span="6" v-show="detailType === 1">
                  <span class="label-text">{{ $lang('批次状态') }}:</span>
                  <p class="filed-content">
                    {{
                      checkForm.batchStatus === 1
                        ? $lang('待审核')
                        : checkForm.batchStatus === 2
                        ? $lang('启用中')
                        : checkForm.batchStatus === 3
                        ? $lang('已下架')
                        : checkForm.batchStatus === 4
                        ? $lang('审核未通过')
                        : checkForm.batchStatus === 5
                        ? $lang('无库存')
                        : checkForm.batchStatus === 6
                        ? $lang('已过期')
                        : '--'
                    }}
                  </p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('优惠券类型') }}:</span>
                  <p class="filed-content">{{ checkForm.couponType || '--' }}</p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('产品类型') }}:</span>
                  <p class="filed-content">{{ checkForm.expressName || '--' }}</p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('有效期') }}:</span>
                  <p class="filed-content">{{ (!checkForm.fixedStartDate || !checkForm.fixedEndDate) ? (checkForm.validTerm || '--' ) : (checkForm.fixedStartDate + '~' + checkForm.fixedEndDate) }}</p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('费用类型') }}:</span>
                  <p class="filed-content">{{ checkForm.feeType || '--' }}</p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('使用地区') }}:</span>
                  <div class="div-content" :title="checkForm.addressListInfo">{{ checkForm.addressListInfo || '--' }}</div>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('支付方式') }}:</span>
                  <p class="filed-content">{{ checkForm.settleMethod || '--' }}</p>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="24">
                  <span class="label-text">{{ $lang('使用渠道') }}:</span>
                  <p class="filed-content">{{ checkForm.useChannel || '--' }}</p>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="6">
                  <span class="label-text">{{ $lang('生成数量') }}:</span>
                  <p class="filed-content">{{ checkForm.generatedQuantity || '--' }}</p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('预警值') }}:</span>
                  <p class="filed-content">{{ checkForm.warnValue || '--' }}</p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('账号限领量') }}:</span>
                  <p class="filed-content">{{ checkForm.limitedQuantity || '--' }}</p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('预警手机号') }}:</span>
                  <p class="filed-content">{{ checkForm.warnTel || '--' }}</p>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="6">
                  <span class="label-text">{{ $lang('发放数量') }}:</span>
                  <p class="filed-content">{{ checkForm.usedQuantity || '--' }}</p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('剩余数量') }}:</span>
                  <p class="filed-content">{{ checkForm.remainderQuantity || '--' }}</p>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="24">
                  <span class="label-text">{{ $lang('使用说明') }}:</span>
                  <p v-for="(item, index) in checkForm.instructions" :key="index" class="filed-content">
                    {{(index + 1)+ '、' + checkForm.instructions[index] }}
                  </p>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="6">
                  <span class="label-text">{{ $lang('申请人') }}:</span>
                  <p class="filed-content">{{ checkForm.creator || '--' }}</p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('申请部门') }}:</span>
                  <div class="div-content" :title="checkForm.expenseDep">{{ checkForm.expenseDep || '--' }}</div>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('申请时间') }}:</span>
                  <p class="filed-content">{{ checkForm.createTime || '--' }}</p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('申请原因') }}:</span>
                  <div class="div-content" :title="checkForm.applyReason">{{ checkForm.applyReason || '--' }}</div>
                </el-col>
              </el-row>
              <el-row :gutter="24" v-if="detailType === 1">
                <el-col :span="6">
                  <span class="label-text">{{ $lang('审核人') }}:</span>
                  <p class="filed-content">{{ checkForm.reviewer || '--' }}</p>
                </el-col>
                <el-col :span="6">
                  <span class="label-text">{{ $lang('审核时间') }}:</span>
                  <p class="filed-content">{{ checkForm.auditTime || '--' }}</p>
                </el-col>
                <el-col :span="12" v-if="checkForm.batchStatus === 4">
                  <span class="label-text">{{ $lang('审核不通过原因') }}:</span>
                  <div class="div-content" :title="checkForm.auditReason">{{ checkForm.auditReason || '--' }}</div>
                </el-col>
              </el-row>
            </div>
            <div v-if="detailType !== 1">
              <el-form-item :label="$lang('审核结果')" prop="adopt">
                <div class="area--block">
                  <el-radio-group v-model="checkForm.adopt">
                    <el-radio :label="1">{{ $lang('通过') }}</el-radio>
                    <el-radio :label="2">{{ $lang('不通过') }}</el-radio>
                  </el-radio-group>
                </div>
                <div class="area--block">
                  <el-input type="textarea" v-if="checkForm.adopt === 2" maxlength="100" v-model="auditReasonVal"></el-input>
                </div>
              </el-form-item>
            </div>
          </el-form>
        </el-scrollbar>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :size="$parent.$parent.controlSize" @click="closeCheckDialog">{{ detailType === 1 ? $lang('关 闭') : $lang('取 消') }}</el-button>
        <el-button type="primary" v-if="detailType !== 1" :size="$parent.$parent.controlSize" :title="$lang('确认')" :loading="checkBtnLoading" @click="checkResult">{{
          $lang('确 认')
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { RULES } from '@/common/utils/validators'
import { couponType, couponBatch } from '../pool.js'
import { RESPONSE_CODE } from '@public/http/config'
import { interfaceWaitHandle } from '@public/utils/common'
export default {
  name: 'CouponBatchDetail',
  data () {
    return {
      checkBtnLoading: false,
      batchDetailVisible: false,
      title: this.$lang('新增'),
      COM_HTTP: couponType,
      rules: {
        adopt: [
          RULES.requiredOptions
        ]
      },
      checkForm: {
        id: '',
        batchStatus: '',
        couponBatchName: '',
        couponType: '',
        activityName: '',
        validType: '',
        fixedStartDate: '',
        fixedEndDate: '',
        receiveAfterDay: '',
        useRegionType: '',
        useProCode: '',
        useCityCode: '',
        useAreaCode: '',
        useChannel: '',
        expressType: '',
        feeType: '',
        settleMethod: '',
        generatedQuantity: '',
        limitedQuantity: '',
        warnValue: '',
        warnTel: '',
        instructions: [],
        expenseDep: '',
        applyReason: '',
        adopt: 0,
        expressName: '',
        validTerm: '',
        usedQuantity: '',
        remainderQuantity: '',
        creator: '',
        createTime: '',
        addressListInfo: '',
        auditTime: '',
        auditReason: '' // 审核原因
      },
      auditReasonVal: '', // 审核原因
      provinceList: [], // 省
      cityList: [], // 市
      areaList: [], // 区
      options: [],
      detailType: 0,
      couponTypeList: [], // 优惠券类型
      useChannelOptions: [
        {
          value: 'D16',
          label: this.$lang('APP')
        },
        {
          value: 'D04',
          label: this.$lang('巴枪PDA')
        },
        {
          value: 'D06',
          label: this.$lang('JMS')
        },
        {
          value: 'D12',
          label: this.$lang('官网')
        }
      ],
      feeTypeList: [
        { value: '1', label: this.$lang('运费') },
        { value: '2', label: this.$lang('保价费') },
        { value: '3', label: this.$lang('COD服务费') },
        { value: '5', label: this.$lang('包材费') }
      ],
      settleMethodList: [
        { value: '1', label: this.$lang('寄付现结') }
        // { value: '2', label: this.$lang('寄付月结') }
      ]
    }
  },
  created () {
    // this.getCouponType()
  },
  methods: {
    closeCheckDialog () {
      this.batchDetailVisible = false
      this.$refs.checkForm.resetFields()
    },
    showDetailDailog (type, row) {
      type === 1 ? this.title = this.$lang('详情') : this.title = this.$lang('审核')
      this.detailType = type
      console.log('12345', row)
      this.$nextTick(_ => {
        // if (row.couponType) {
        //   var arr = row.couponType.split('/')
        //   row.couponType = arr[1]
        // }
        this.addressChange(row.useRegionType)
        // this.getCouponType()
        this.batchDetailVisible = true
      })
      this.$nextTick(_ => {
        this.checkForm = {
          id: row.id,
          couponBatchName: row.couponBatchName ? row.couponBatchName : '',
          couponType: row.couponType ? row.couponType : '',
          activityName: row.activityName ? row.activityName : '',
          batchStatus: row.batchStatus ? row.batchStatus : '',
          expressName: row.expressName ? row.expressName : '',
          validTerm: row.validTerm ? row.validTerm : '',
          usedQuantity: row.usedQuantity ? row.usedQuantity : '',
          remainderQuantity: row.remainderQuantity ? row.remainderQuantity : '',
          creator: row.creator ? row.creator : '',
          createTime: row.createTime ? row.createTime : '',
          auditTime: row.auditTime ? row.auditTime : '',
          reviewer: row.reviewer ? row.reviewer : '',
          auditReason: row.auditReason ? row.auditReason : '',
          validType: row.validType ? row.validType : '',
          fixedStartDate: row.fixedStartDate ? row.fixedStartDate : '',
          fixedEndDate: row.fixedEndDate ? row.fixedEndDate : '',
          receiveAfterDay: row.receiveAfterDay ? row.receiveAfterDay : '',
          useRegionType: (row.useRegionType || row.useRegionType === 0 ? row.useRegionType : ''),
          useProCode: row.useProCode ? row.useProCode.split(',') : '',
          useCityCode: row.useCityCode ? row.useCityCode.split(',') : '',
          useAreaCode: row.useAreaCode ? row.useAreaCode.split(',') : '',
          useChannel: row.useChannel ? this.checkStr(row.useChannel.split(','), this.useChannelOptions) : '',
          expressType: row.expressType ? row.expressType.split(',') : '',
          feeType: row.feeType ? this.checkStr(row.feeType.split(','), this.feeTypeList) : '',
          settleMethod: row.settleMethod ? this.checkStr(row.settleMethod.split(','), this.settleMethodList) : '',
          generatedQuantity: row.generatedQuantity ? row.generatedQuantity : '',
          limitedQuantity: row.limitedQuantity ? row.limitedQuantity : '',
          warnValue: row.warnValue ? row.warnValue : '',
          warnTel: row.warnTel ? row.warnTel : '',
          instructions: row.instructionsList ? row.instructionsList : '',
          expenseDep: row.expenseDep ? row.expenseDep : '',
          applyReason: row.applyReason ? row.applyReason : '',
          addressListInfo: (row.useRegionType === 0 ? this.$lang('全国') : row.useRegionType === 1 ? row.useProName : row.useRegionType === 2 ? row.useCityName : row.useRegionType === 3 ? row.useAreaName : '')
        }
      })
    },

    checkStr(arr, typeList) {
      var strList = []
      for (let i = 0; i < arr.length; i++) {
        const val = arr[i]
        for (let j = 0; j < typeList.length; j++) {
          if (val === typeList[j].value) {
            strList.push(typeList[j].label)
          }
        }
      }
      return strList.join(',')
    },
    // 审核
    async checkResult () {
      console.log(this.checkForm, 1)
      this.$refs.checkForm.validate((valid) => {
        if (valid) {
          console.log(123)
          var paramData = {}
          if (this.checkForm.adopt === 2) {
            paramData = {
              id: this.checkForm.id,
              batchStatus: 4,
              auditReason: this.auditReasonVal
            }
          } else {
            paramData = {
              id: this.checkForm.id,
              batchStatus: 2
            }
          }
          this.handlerEdit(paramData)
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },

    // 修改
    async handlerEdit(data) {
      this.checkBtnLoading = true
      try {
        const { code, msg } = await couponBatch.reqStatusUpdate(data)
        if (code === RESPONSE_CODE.SUCCESS) {
          this.checkBtnLoading = false
          interfaceWaitHandle(() => {
            this.closeCheckDialog()
            this.$message.success(msg)
            this.$emit('search-change')
          })
        } else {
          this.$message.error(msg)
          this.checkBtnLoading = false
        }
      } catch (err) {
        this.checkBtnLoading = false
      }
    },

    // 优惠券类型查询
    // async getCouponType() {
    //   try {
    //     this.loading = true
    //     const res = await this.COM_HTTP.reqType()
    //     if (res.code === RESPONSE_CODE.SUCCESS) {
    //       console.log(res)
    //       this.areaList = res.data
    //     } else {
    //       this.loading = false
    //       this.$message.error(res.msg)
    //     }
    //   } catch (error) {
    //     this.loading = false
    //     // this.$message.error(res.msg)
    //   }
    // },

    // 省 市 区地址信息查询
    addressChange(addressInfo) {
      console.log('addressInfo', addressInfo)
      if (addressInfo === 1) {
        const pam1 = {
          plateformType: 5,
          type: 2
        }
        this.getAddressInfo(pam1, 1)
      } else if (addressInfo === 2) {
        const pam2 = {
          plateformType: 5,
          type: 3
        }
        this.getAddressInfo(pam2, 2)
      } else if (addressInfo === 3) {
        const pam3 = {
          plateformType: 5,
          type: 4
        }
        this.getAddressInfo(pam3, 3)
      }
    },

    async getAddressInfo(param, addressType) {
      try {
        this.loading = true
        const res = await this.COM_HTTP.reqAreas(param)
        if (res.code === RESPONSE_CODE.SUCCESS) {
          console.log(res)
          if (addressType === 1) {
            this.provinceList = res.data
          } else if (addressType === 2) {
            this.cityList = res.data
          } else if (addressType === 3) {
            this.areaList = res.data
          }
        } else {
          this.loading = false
          this.$message.error(res.msg)
        }
      } catch (error) {
        1
      }
    },
    onSubmit () {

    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-scrollbar__view {
  padding-bottom: 22px;
}
.batch-dialog {
  .date__block {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    .el-radio {
      width: 80px;
    }
  }
  .area--block {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
}
.el-date-editor ::v-deep .el-range__icon {
  display: none;
}
.el-form-item {
  width: 480px !important;
}
.filed-content {
  height: 40px;
  margin-bottom: 0px;
}
.div-content {
  margin-top: 0;
  font-size: 14px;
  width: 100%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  padding-top: 14px;
}
</style>
