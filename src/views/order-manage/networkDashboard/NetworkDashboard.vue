<template>
  <div class="network-dashboard main-wrap">
    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane :label="$lang('调派网点-订单分布')" name="first">
        <avue-crud ref="networkDashboardOrderTableRef" :data="orderTableList" v-model="formData" :option="orderListOpt" :page="orderPage"
                   :table-loading="loading"
                   @size-change="sizeChange" @current-change="currentChange" @search-reset="resetList"
                   @search-change="search"
        >
          <template slot="orderCount" slot-scope="{row}">
            <div>
              <a :style="{ cursor: row.orderCount > 0 ? 'pointer' : '', color: row.orderCount > 0 ? '#6164e5' : '' }"
                 @click="handleOrderCountClick(row)">{{row.orderCount || 0 }}</a>
            </div>
          </template>
          <template slot="customerCount" slot-scope="{row}">
            <div>
              <a :style="{ cursor: row.customerCount > 0 ? 'pointer' : '', color: row.customerCount > 0 ? '#6164e5' : '' }"
                 @click="handleCustomerCountClick(row)">{{ row.customerCount || 0 }}</a>
            </div>
          </template>
        </avue-crud>
        <CustomerTotal
            :visible="customerTotalVisible"
            :table-list="customerTotalTableList"
            :search-params="customerTotalParams"
            @close="customerTotalVisible = false"/>
      </el-tab-pane>
      <el-tab-pane :label="$lang('调派业务员-取件情况')" name="second">
        <avue-crud ref="networkDashboardPickTableRef" :data="pickTableList" v-model="formData" :option="pickListOpt" :page="pickPage"
                   :table-loading="loading"
                   @size-change="sizeChange" @current-change="currentChange" @search-reset="resetList"
                   @search-change="search"
        >
          <template slot="orderCount" slot-scope="{row}">
            <div>
              <a :style="{ cursor: row.orderCount > 0 ? 'pointer' : '', color: row.orderCount > 0 ? '#6164e5' : '' }"
                 @click="handlePickOrderCountClick(row,[102,103])">{{row.orderCount || 0 }}</a>
            </div>
          </template>
          <template slot="pickedCount" slot-scope="{row}">
            <div>
              <a :style="{ cursor: row.pickedCount > 0 ? 'pointer' : '', color: row.pickedCount > 0 ? '#6164e5' : '' }"
                 @click="handlePickOrderCountClick(row,103)">{{row.pickedCount || 0 }}</a>
            </div>
          </template>
          <template slot="notPickedCount" slot-scope="{row}">
            <div>
              <a :style="{ cursor: row.notPickedCount > 0 ? 'pointer' : '', color: row.notPickedCount > 0 ? '#6164e5' : '' }"
                 @click="handlePickOrderCountClick(row,102)">{{row.notPickedCount || 0 }}</a>
            </div>
          </template>
        </avue-crud>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import mixin from '@mixins/mixin'
import dayjs from 'dayjs';
import { commonFun } from '@public/utils/common';
import { RULES } from '@/common/utils/validators';
import { NT_SECOND_LEVEL, NT_FIRST_LEVEL } from '@public/utils/const';
import { NetworkDashBoard } from '@views/pool';
import { RESPONSE_CODE } from '@public/http/config';
import CustomerTotal from '../networkDashboard/components/customerTotal.vue';

const dateRange = commonFun.GetTodayDate()

export default {
  name: 'NetworkDashboard',
  mixins: [mixin],
  COM_HTTP: NetworkDashBoard,
  components: {
    CustomerTotal
  },
  data() {
    return {
      activeName: 'first',
      customerTotalParams: {},
      customerTotalTableList: [],
      customerTotalVisible: false,
      isShowFirst: false,
      orderTableList: [],
      pickTableList: [],
      formData: {},
      loading: false,
      orderPage: {
        current: 1,
        size: 20,
        total: 0
      },
      pickPage: {
        current: 1,
        size: 20,
        total: 0
      },
      orderLastedParams: {},
      pickLastedParams: {}
    }
  },
  methods: {
    handleTabClick() {
      // 等待 DOM 更新后重新计算高度
      this.$nextTick(() => {
        if (this.activeName === 'first') {
          this.$refs.networkDashboardOrderTableRef && this.$refs.networkDashboardOrderTableRef.calcTableHeight();
        } else {
          this.$refs.networkDashboardPickTableRef && this.$refs.networkDashboardPickTableRef.calcTableHeight();
        }
      });
    },
    async handlePickOrderCountClick(row, orderStatusCode) {
      if (row.orderCount <= 0) return;
      const params = {
        startDate: this.pickLastedParams.startDate,
        endDate: this.pickLastedParams.endDate,
        pickNetworkCode: this.pickLastedParams.pickNetworkCode,
        pickStaffCode: row.pickStaffCode
      };

      // 判断是否为数组
      if (Array.isArray(orderStatusCode)) {
        params.orderStatusCodeList = orderStatusCode;
      } else {
        params.orderStatusCode = orderStatusCode;
      }

      const res = await NetworkDashBoard.queryIdListByPickStaff(params);
      if (res.code === RESPONSE_CODE.SUCCESS) {
        // 存储到 sessionStorage
        sessionStorage.setItem('networkDashboardIds', JSON.stringify(res.data));
        // 使用路由导航，将参数作为 query 传递
        this.$router.push({
          name: 'orderScheduling',
          query: { fromMenu: 'NetworkDashboard' }
        });
      } else {
        this.$message.error(res.msg)
      }
    },
    async handleOrderCountClick(row) {
      if (row.orderCount <= 0) return;

      const params = {
        startDate: this.orderLastedParams.startDate,
        endDate: this.orderLastedParams.endDate,
        pickNetworkCode: this.orderLastedParams.pickNetworkCode,
        senderProvinceId: row.senderProvinceId,
        senderCityId: row.senderCityId,
        senderAreaId: row.senderAreaId,
        senderTownship: row.senderTownship,
        senderDetailedAddress: row.senderDetailedAddress
      };

      const res = await NetworkDashBoard.queryIdListByAddress(params);
      if (res.code === RESPONSE_CODE.SUCCESS) {
        // 存储到 sessionStorage
        sessionStorage.setItem('networkDashboardIds', JSON.stringify(res.data));
        // 使用路由导航，将参数作为 query 传递
        this.$router.push({
          name: 'orderScheduling',
          query: { fromMenu: 'NetworkDashboard' }
        });
      } else {
        this.$message.error(res.msg)
      }
    },
    async handleCustomerCountClick(row) {
      const params = {
        startDate: this.orderLastedParams.startDate,
        endDate: this.orderLastedParams.endDate,
        pickNetworkCode: this.orderLastedParams.pickNetworkCode
      }
      params.senderProvinceId = row.senderProvinceId
      params.senderCityId = row.senderCityId
      params.senderAreaId = row.senderAreaId
      params.senderTownship = row.senderTownship
      params.senderDetailedAddress = row.senderDetailedAddress
      const res = await NetworkDashBoard.customerGroupTotal(params);
      if (res.code === RESPONSE_CODE.SUCCESS) {
        console.log(res.data)
        this.customerTotalVisible = true
        this.customerTotalTableList = res.data
        this.customerTotalParams = params
        this.customerTotalParams.senderProvinceName = row.senderProvinceName
        this.customerTotalParams.senderCityName = row.senderCityName
        this.customerTotalParams.senderAreaName = row.senderAreaName
        this.customerTotalParams.senderTownship = row.senderTownship
      } else {
        this.$message.error(res.msg)
      }
    },
    async search(params) {
      if (!params.startDate || !params.endDate) {
        this.$message.warning(this.$lang('请选择客户下单时间'))
        return false
      }
      if (!params.pickNetworkCode) {
        this.$message.warning(this.$lang('请选择调派网点'))
        return false
      }
      const startGettime = new Date(params.startDate).getTime()
      const endGettime = new Date(params.endDate).getTime()
      if (endGettime - startGettime > 3 * 24 * 3600 * 1000) {
        this.$message.warning(this.$lang('只能查询3天内的数据'))
        return false
      }

      if (this.activeName === 'first') {
        params.current = this.orderPage.current;
        params.size = this.orderPage.size;
        this.loading = true
        const res = await NetworkDashBoard.reqList(params);
        this.loading = false
        if (res.code === RESPONSE_CODE.SUCCESS) {
          this.orderTableList = res.data.records
            ? res.data.records
            : []
          this.orderPage.total = res.data.total || 0
          this.orderPage.current = res.data.current || 1
        } else {
          this.$message.error(res.msg)
        }
        this.orderLastedParams = this.deepClone(params)
      } else {
        params.current = this.pickPage.current;
        params.size = this.pickPage.size;
        this.loading = true
        const res = await NetworkDashBoard.totalByPickStaff(params);
        this.loading = false
        if (res.code === RESPONSE_CODE.SUCCESS) {
          this.pickTableList = res.data.records
            ? res.data.records
            : []
          this.pickPage.total = res.data.total || 0
          this.pickPage.current = res.data.current || 1
        } else {
          this.$message.error(res.msg)
        }
        this.pickLastedParams = this.deepClone(params)
      }
    },
    // 选择分页条数
    sizeChange(val) {
      if (this.activeName === 'first') {
        this.orderPage.current = 1
        this.orderPage.size = val
        this.search(this.orderLastedParams)
      } else {
        this.pickPage.current = 1
        this.pickPage.size = val
        this.search(this.pickLastedParams)
      }
    },
    // 跳转页码
    currentChange(val) {
      if (this.activeName === 'first') {
        this.orderPage.current = val
        this.search(this.orderLastedParams)
      } else {
        this.pickPage.current = val
        this.search(this.pickLastedParams)
      }
    },
    resetList() {
      if (this.activeName === 'first') {
        this.orderPage.current = 1
      } else {
        this.pickPage.current = 1
      }
    }
  },
  computed: {
    orderListOpt() {
      const that = this
      return {
        delBtn: false,
        customView: false,
        customEdit: false,
        // menuWidth: 0, // 操作宽度
        menu: false,
        header: false,
        selection: false, // 开启选择操作
        selectable: that.selectable,
        column: [
          {
            label: '',
            prop: 'id',
            hide: true,
            sort: 0
          },
          {
            label: that.$lang('寄件省'),
            prop: 'senderProvinceName',
            sort: 1
          },
          {
            label: that.$lang('寄件城市'),
            prop: 'senderCityName',
            sort: 2
          },
          {
            label: that.$lang('寄件区域'),
            prop: 'senderAreaName',
            sort: 3
          },
          {
            label: that.$lang('寄件乡镇'),
            prop: 'senderTownship',
            sort: 4
          },
          {
            label: that.$lang('寄件详细地址'),
            prop: 'senderDetailedAddress',
            sort: 5
          },
          {
            label: that.$lang('订单总量'),
            prop: 'orderCount',
            slot: true,
            sort: 6
          },
          {
            label: that.$lang('客户总量'),
            prop: 'customerCount',
            slot: true,
            sort: 7
          },
          {
            label: that.$lang('总件数'),
            prop: 'packageNumberCount',
            sort: 6
          },
          {
            label: that.$lang('总实际重量'),
            prop: 'packageTotalWeightSum',
            sort: 7
          },
          {
            label: that.$lang('客户最早下单时间'),
            prop: 'inputTime',
            width: 100,
            type: 'datetime',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            sort: 8
          },
          {
            label: that.$lang('客户下单最早开始时间'),
            prop: 'startDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            relationProps: ['createTimeEndStr'],
            sort: 9,
            hide: true,
            search: true,
            searchDefault: dayjs(new Date().getTime()).format('YYYY-MM-DD') + ' 00:00:00'
          },
          {
            label: that.$lang('客户下单最早结束时间'),
            prop: 'endDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            sort: 10,
            hide: true,
            search: true,
            searchDefault: dateRange[1],
            searchDefaultTime: '23:59:59',
            pickerOptions: {
              disabledDate: (time) => {

              }
            }
          },
          {
            label: '调派网点编号',
            labelAlias: '调派网点',
            prop: 'pickNetworkCode',
            type: 'network',
            rules: [RULES.required],
            hide: true,
            remoteQuery: {
              queryLevel: 3
              // typeId: commonFun.getDict('ZD08').find(item => item.code === '18') && commonFun.getDict('ZD08').find(item => item.code === '18').id
            },
            treeQuery: {
              queryLevel: 1
              // typeId: commonFun.getDict('ZD08').find(item => item.code === '18') && commonFun.getDict('ZD08').find(item => item.code === '18').id
            },

            props: {
              label: 'name',
              value: 'code'
            },
            selectDic: [{ id: this.user.networkId, name: this.user.networkName, code: this.user.networkCode }],
            searchDefault: this.user.institutionalLevelId === NT_FIRST_LEVEL || this.user.institutionalLevelId === NT_SECOND_LEVEL ? this.user.networkCode : null,
            // 使用当前用户登录的网点信息
            useDetailForm: true,
            useCurNetworkInfo: true,
            // 其他表单中的禁用
            disabled: true,
            search: true,
            sort: 11,
            formatter: ({ networkName }) => networkName
          }
        ]
      }
    },
    pickListOpt() {
      const that = this
      return {
        delBtn: false,
        customView: false,
        customEdit: false,
        // menuWidth: 0, // 操作宽度
        menu: false,
        header: false,
        selection: false, // 开启选择操作
        selectable: that.selectable,
        column: [
          {
            label: '',
            prop: 'id',
            hide: true,
            sort: 0
          },
          {
            label: that.$lang('业务员'),
            prop: 'pickStaffCode',
            hide: true,
            sort: 0
          },
          {
            label: that.$lang('业务员'),
            prop: 'pickStaffName',
            width: 230,
            sort: 1
          },
          {
            label: that.$lang('总订单量(应取件)'),
            prop: 'orderCount',
            slot: true,
            width: 180,
            sort: 2
          },
          {
            label: that.$lang('总订单量(已取件)'),
            prop: 'pickedCount',
            slot: true,
            width: 180,
            sort: 3
          },
          {
            label: that.$lang('总订单量(未取件)'),
            prop: 'notPickedCount',
            slot: true,
            width: 180,
            sort: 4
          },
          {
            label: that.$lang('总件数(未取件)'),
            prop: 'packageNumberCount',
            width: 180,
            sort: 5
          },
          {
            label: that.$lang('总物品重量(未取件)'),
            prop: 'packageTotalWeightSum',
            formatter: (row) => {
              return (row.packageTotalWeightSum || 0).toFixed(2)
            },
            width: 180,
            sort: 6
          },
          {
            label: that.$lang('客户下单最早开始时间'),
            prop: 'startDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            relationProps: ['createTimeEndStr'],
            sort: 9,
            hide: true,
            search: true,
            searchDefault: dayjs(new Date().getTime()).format('YYYY-MM-DD') + ' 00:00:00'
          },
          {
            label: that.$lang('客户下单最早结束时间'),
            prop: 'endDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            sort: 10,
            hide: true,
            search: true,
            searchDefault: dateRange[1],
            searchDefaultTime: '23:59:59',
            pickerOptions: {
              disabledDate: (time) => {

              }
            }
          },
          {
            label: '调派网点编号',
            labelAlias: '调派网点',
            prop: 'pickNetworkCode',
            type: 'network',
            rules: [RULES.required],
            hide: true,
            remoteQuery: {
              queryLevel: 3
              // typeId: commonFun.getDict('ZD08').find(item => item.code === '18') && commonFun.getDict('ZD08').find(item => item.code === '18').id
            },
            treeQuery: {
              queryLevel: 1
              // typeId: commonFun.getDict('ZD08').find(item => item.code === '18') && commonFun.getDict('ZD08').find(item => item.code === '18').id
            },

            props: {
              label: 'name',
              value: 'code'
            },
            selectDic: [{ id: this.user.networkId, name: this.user.networkName, code: this.user.networkCode }],
            searchDefault: this.user.institutionalLevelId === NT_FIRST_LEVEL || this.user.institutionalLevelId === NT_SECOND_LEVEL ? this.user.networkCode : null,
            // 使用当前用户登录的网点信息
            useDetailForm: true,
            useCurNetworkInfo: true,
            // 其他表单中的禁用
            disabled: true,
            search: true,
            sort: 11,
            formatter: ({ networkName }) => networkName
          }
        ]
      }
    }
  }
}

</script>

<style lang="scss" rel="text/css">
.network-dashboard .el-checkbox {
  margin-top: 24px;
  margin-left: 10px;
}

.network-dashboard .cms-input-tag {
  // width: 419px;
  width: 354px;
}

</style>
