/*
 *@description: 查看订单调度
 *@author: qdx
 */
<template>
  <div class="scheduling-view main-wrap add-content custom-collapse">
  <el-form ref="form" :model="form" label-width="100px" label-position="top" v-page-loading="pageLoading">
      <el-collapse v-model="activeName">
        <el-collapse-item :title="$lang('调派信息')" name="1">
          <div class="body-content">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item :label="$lang('订单编号')">
                  {{form.id || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('订单来源')">
                  {{form.orderSourceName || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('订单状态')">
                  {{form.orderStatusName || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('订单录入时间')">
                  {{form.inputTime || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$lang('客户下单时间')">
                  {{form.customerOrderTime || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('主运单号')">
                  {{form.waybillId || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('寄件服务方式')">
                  {{form.sendName || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('派件服务方式')">
                  {{form.dispatchName || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
             <el-row :gutter="20">
               <el-col :span="6">
                 <el-form-item :label="$lang('代理区')">
                  {{form.proxyAreaName && form.proxyAreaCode ? form.proxyAreaName + ' / ' + form.proxyAreaCode : '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('调派代理时间')">
                  {{form.dispatchProxyAreaTime || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$lang('取件网点')">
                  {{form.pickNetworkName && form.pickNetworkCode ? form.pickNetworkName + ' / ' + form.pickNetworkCode : '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('调派网点时间')">
                  {{form.dispatchNetworkTime || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
             <el-row :gutter="20">
               <el-col :span="6">
                 <el-form-item :label="$lang('收派员')">
                  {{form.pickStaffName && form.pickStaffCode ? form.pickStaffName + ' / ' + form.pickStaffCode : '--'}}
                </el-form-item>
              </el-col>
               <el-col :span="6">
                 <el-form-item :label="$lang('收派员电话')">
                  {{form.staffPhone || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('调派收派员时间')">
                  {{form.dispatchStaffTime || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="">
                  <template slot="label">
                     <span :title="$lang('最佳取件开始时间')">{{$lang('最佳取件开始时间')}}</span>
                   </template>
                  {{form.bestPickTimeStart || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item label="">
                   <template slot="label">
                     <span :title="$lang('最佳取件结束时间')">{{$lang('最佳取件结束时间')}}</span>
                   </template>
                  {{form.bestPickTimeEnd || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                 <el-form-item :label="$lang('取件失败原因')">
                  {{form.pickFailReason || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item >
                   <template slot="label">
                     <span :title="$lang('取件失败时间')">{{$lang('取件失败时间')}}</span>
                   </template>
                  {{form.pickFailTime || '--'}}
                </el-form-item>
              </el-col>
             <el-col :span="6">
                <el-form-item label="">
                  <template slot="label">
                     <span :title="$lang('订单取消原因')">{{$lang('订单取消原因')}}</span>
                   </template>
                  <div class="txt-area">
                  {{form.cancelReason || '--'}}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item label="">
                   <template slot="label">
                     <span :title="$lang('订单取消时间')">{{$lang('订单取消时间')}}</span>
                   </template>
                  {{form.cancelTime || '--'}}
                </el-form-item>
              </el-col>
               <el-col :span="6">
                 <el-form-item label="">
                   <template slot="label">
                     <span :title="$lang('取件失败网点')">{{$lang('取件失败网点')}}</span>
                   </template>
                  {{form.pickFailNetworkName || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item label="">
                   <template slot="label">
                     <span :title="$lang('取件失败业务员')">{{$lang('取件失败业务员')}}</span>
                   </template>
                  {{form.pickFailStaffName || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('商务件')">
                  {{form.isBusiness === 2 ? $lang('否') : $lang('是')}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item class="subWay-billIds" :label="$lang('子运单号')">
                  <el-tooltip class="item" effect="dark" :content="form.subWaybillIds" placement="top-start">
                    <span class="subWay-billIds">{{form.subWaybillIds || '--'}} </span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item :label="$lang('退转件')">
                  {{form.ifTransferStr}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="subWay-billIds" :label="$lang('退转件姓名')">
                  <el-tooltip class="item" effect="dark" :content="form.transferName" placement="top-start">
                    <span class="subWay-billIds">{{form.transferName || '--'}} </span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="subWay-billIds" :label="$lang('退转件电话')">
                  <el-tooltip class="item" effect="dark" :content="form.transferMobilePhone" placement="top-start">
                    <span class="subWay-billIds">{{form.transferMobilePhone?this.formartPhone(form.transferMobilePhone) : '--'}} </span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="subWay-billIds" :label="$lang('退转件公司')">
                  <el-tooltip class="item" effect="dark" :content="form.transferCompany" placement="top-start">
                    <span class="subWay-billIds">{{form.transferCompany || '--'}} </span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item class="subWay-billIds" :label="$lang('退转件详细地址')">
                  <el-tooltip class="item" effect="dark" :content="form.transferDetailedAddress" placement="top-start">
                    <span class="subWay-billIds">{{form.transferDetailedAddress || '--'}} </span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="subWay-billIds" :label="$lang('退转件省')">
                  <el-tooltip class="item" effect="dark" :content="form.transferProvinceName" placement="top-start">
                    <span class="subWay-billIds">{{form.transferProvinceName || '--'}} </span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="subWay-billIds" :label="$lang('退转件市')">
                  <el-tooltip class="item" effect="dark" :content="form.transferCityName" placement="top-start">
                    <span class="subWay-billIds">{{form.transferCityName || '--'}} </span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="subWay-billIds" :label="$lang('退转件区')">
                  <el-tooltip class="item" effect="dark" :content="form.transferAreaName" placement="top-start">
                    <span class="subWay-billIds">{{form.transferAreaName || '--'}} </span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item class="subWay-billIds" :label="$lang('退转件乡镇')">
                  <el-tooltip class="item" effect="dark" :content="form.transferTownshipName" placement="top-start">
                    <span class="subWay-billIds">{{form.transferTownshipName || '--'}} </span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="subWay-billIds" :label="$lang('退转件邮编')">
                  <el-tooltip class="item" effect="dark" :content="form.transferPostalCode" placement="top-start">
                    <span class="subWay-billIds">{{form.transferPostalCode || '--'}} </span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="subWay-billIds" :label="$lang('退转件十字码')">
                  <el-tooltip class="item" effect="dark" :content="form.transferDispatchCode" placement="top-start">
                    <span class="subWay-billIds">{{form.transferDispatchCode || '--'}} </span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <el-row :gutter="20">
              <el-col :span="18">
                <el-form-item label="取件失败原因">
                  <div class="txt-area">
                    {{form.pickFailReason || '--'}}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item label="取件失败时间">
                  {{form.pickFailTime || '--'}}
                </el-form-item>
              </el-col>
            </el-row> -->
          </div>
        </el-collapse-item>
        <el-collapse-item :title="$lang('快递信息')" name="2">
          <div class="body-content">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item :label="$lang('客户信息')">
                  {{form.customerName&&form.customerCode ? form.customerName + '/' + form.customerCode : '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('寄件人')">
                  {{form.senderName || '--'}}
                </el-form-item>
              </el-col>
               <el-col :span="6">
                <el-form-item :label="$lang('寄件人公司')">
                  {{form.senderCompany || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item label="">
                   <template slot="label">
                     <span :title="$lang('寄件人手机号')">{{$lang('寄件人手机号')}}</span>
                   </template>
                  {{this.formartPhone(form.senderMobilePhone) || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('寄件人固话')">
                  {{form.senderTelphone?this.formartPhone(form.senderTelphone): '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('寄件人邮编')">
                  {{form.senderPostalCode || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$lang('寄件地址')">
                  {{`${form.senderProvinceName ? form.senderProvinceName + '--' : form.senderProvinceName}${form.senderCityName ? form.senderCityName + '--' : form.senderCityName}${form.senderAreaName ? form.senderAreaName + '--' : form.senderAreaName}${form.senderTownship ? form.senderTownship + '--' : ''}${form.senderDetailedAddress || ''}`}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item :label="$lang('收件人')">
                  {{form.receiverName || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('收件公司')">
                  {{form.receiverCompany || '--'}}
                </el-form-item>
              </el-col>
               <el-col :span="6">
                <el-form-item label="">
                  <template slot="label">
                     <span :title="$lang('收件人手机号')">{{$lang('收件人手机号')}}</span>
                   </template>
                  {{this.formartPhone(form.receiverMobilePhone) || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('收件人固话')">
                  {{form.receiverTelphone?this.formartPhone(form.receiverTelphone): '--'}}
                </el-form-item>
              </el-col>
            </el-row>
             <el-row  :gutter="20">
               <el-col :span="6">
                 <el-form-item :label="$lang('收件邮编')">
                  {{form.receiverPostalCode || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="18">
                <el-form-item :label="$lang('收件地址')">
                  {{`${form.receiverProvinceName ? form.receiverProvinceName + '--' : form.receiverProvinceName}${form.receiverCityName ? form.receiverCityName + '--' : form.receiverCityName}${form.receiverAreaName ? form.receiverAreaName + '--' : form.receiverAreaName}${form.receiverTownship ? form.receiverTownship + '--' : ''}${form.receiverDetailedAddress || ''}`}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                 <el-form-item :label="$lang('产品名称')">
                    {{form.expressTypeName || '--'}}
                </el-form-item>
              </el-col>
               <el-col :span="6">
                 <el-form-item :label="$lang('物品重量')  + '(kg)'">
                  {{form.packageTotalWeight || '--'}}
                </el-form-item>
              </el-col>
               <el-col :span="6">
                 <el-form-item :label="$lang('包裹数量')">
                  {{form.packageNumber || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('物品类型')">
                    {{form.goodsTypeName || '--'}}
                </el-form-item>
              </el-col>
               <el-col :span="6">
                 <el-form-item :label="$lang('物品名称')">
                  {{form.goodsName || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                 <el-form-item :label="$lang('结算方式')">
                   {{form.paymentModeName || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-checkbox style="margin-top: 28px;" disabled v-model="insuredChecked">{{$lang('物品保价')}}</el-checkbox>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$lang('保价金额')">
                  {{form.declaredValue != undefined && form.declaredValue != null? commafy(form.declaredValue) : '--' }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$lang('保价费')">
                  {{form.insuredValue != undefined && form.insuredValue != null? commafy(form.insuredValue) : '--' }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                 <el-form-item :label="$lang('备注')">
                  {{form.remarks || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-form>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
import { RESPONSE_CODE } from '@public/http/config'
import { OrderScheduling } from '../pool.js'
export default {
  name: 'OrderSchedulingView',
  data() {
    return {
      activeName: ['1', '2'],
      // form: {},
      schedulingModel: {},
      expressModel: {},
      addressArr: [],
      form: {},
      pageLoading: false
    }
  },
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  created() {
    // if (this.id) {
    //   this.getDetail(this.id)
    // }
  },
  mounted() {
    // 获取查看详情数据
    console.log('orderSchedulingView111111111111111111', this.$route.params)
    const { id } = this.$route.params
    if (id) {
      this.getDetail(id)
    }
  },
  computed: {
    insuredChecked() {
      return this.form.insured === 1
    }
  },
  methods: {
    ...mapActions({
      delView: 'tags/delView'
    }),
    formartPhone(phone) {
      const str1 = phone && phone.length > 0 ? phone[0] : ''
      const str2 = phone && phone.length > 0 ? phone[phone.length - 1] : ''
      return str1 + '*********' + str2
    },
    // 获取详情
    async getDetail(id) {
      this.pageLoading = true
      const { code, msg, data } = await OrderScheduling.reqDetail(id)
      this.pageLoading = false
      if (code === RESPONSE_CODE.SUCCESS) {
        Object.keys(data).forEach(item => {
          this.form[item] = String(data[item])
        })
        return
      }
      this.$message.error(msg)
    },
    /**
     * 数字格式转换成千分位
     *@param{Object}num
     */
    commafy(val) {
      val = parseFloat(val).toLocaleString()
      // if (val.indexOf('.') === -1) {
      //   val = val + '.00'
      // } else if (val.charAt(val.indexOf('.') === val.length - 2)) {
      //   val = val + '0'
      // }
      return val
    },
    onBack() {
      const name = 'StandardPriceView'
      this.delView({ name: name, path: '/' + name }).then(({ visitedViews }) => {
        this.$router.push({ name: 'StandardPrice', path: '/StandardPrice' })
      })
    }
  }
}
</script>
<style lang="scss" rel="text/css">
.el-collapse-item__header {
    height: 42px;
    // background: rgba(238,238,238,1) !important;
    padding-left: 30px;
    line-height: 42px;
}
.scheduling-view{
  .el-form-item__label{
    color: #999999;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
  .txt-area {
    word-wrap: break-word;
  }
}
.subWay-billIds .el-form-item__content{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

