/*
 *@description: 调度统计 => 取消订单统计 => 取消订单统计详情
 *@author: hls
 */
<template>
<div class="main-wrap cancel-wrap">
    <avue-crud
      ref="params"
      :data="tableList"
      v-model="formData"
      :option="listOpt"
      :page="page"
      :table-loading="loading"
      @size-change="sizeChange"
      @current-change="currentChange"
      @search-reset="resetList"
      @search-change="searchChange">
      <template v-slot:idListSearch>
      <div>
        <el-row>
          <el-radio-group v-model="orderNoType">
            <el-radio :label="1">{{$lang('订单号')}}</el-radio>
            <el-radio :label="2">{{$lang('运单号')}}</el-radio>
          </el-radio-group>
        </el-row>
        <el-row>
          <base-input-tag v-if="orderNoType === 1" placeholder="最多可查询150条，以逗号、空格或回车键隔开" v-model="searchForm.ids" validate="orderName" maxHeight="360px"
            :addTagOnBlur="true" :clearBtnVisible="true" class="cms-input-tag">
          </base-input-tag>
          <base-input-tag v-else-if="orderNoType === 2" v-model="searchForm.waybillIds" placeholder="最多可查询150条，以逗号、空格或回车键隔开" maxHeight="360px"
            validate="codeName" :addTagOnBlur="true" class="cms-input-tag" :clearBtnVisible="true">
          </base-input-tag>
        </el-row>
      </div>
    </template>
    </avue-crud>
  </div>
</template>
<script>
import mixin from '@mixins/mixin'
import { OrderCancelling } from '../../pool.js'
import { commonFun } from '@public/utils/common'
// import dayjs from 'dayjs'
import { DICT } from '@/common/utils/dict'
// import { RESPONSE_CODE } from '@public/http/config'
export default {
  name: 'ReasonTable',
  mixins: [mixin],
  data() {
    return {
      COM_HTTP: OrderCancelling,
      formData: {},
      activeName: 'first',
      tableList: [],
      excelName: '取消订单统计明细',
      orderNoType: 1, // 搜索单号类型
      searchForm: {
        ids: [],
        waybillIds: []
      },
      first: true,
      getDictList: [], // 字典列表 过滤拼多多
      page: {
        size: 20,
        current: 1,
        total: 0
      }
    }
  },
  props: {
    detail: {}
  },
  created() {
    this.init()
  },
  mounted() {},
  computed: {
    listOpt() {
      return {
        menu: false,
        fixHeight: 70,
        column: [
          {
            label: '',
            prop: 'idList',
            search: true,
            type: 'select',
            disabled: true,
            searchslot: true,
            hide: true,
            sort: 0
          },
          {
            label: '订单录入时间',
            prop: 'orderDate'
          },
          {
            label: '订单号',
            prop: 'orderId'
          },
          {
            label: '运单号',
            prop: 'waybillId'
          },
          {
            label: '订单来源',
            prop: 'orderSourceCode',
            search: true,
            type: 'select',
            dicData: this.getDictList,
            viewProp: 'orderSourceName',
            searchMmultiple: true,
            props: {
              label: 'name',
              value: 'code',
              res: 'records'
            }
          },
          {
            label: '调派代理区时间',
            prop: 'dispatchProxyAreaTime'
          },
          {
            label: '调度网点时间',
            prop: 'dispatchNetworkTime'
          },
          {
            label: '调度业务员时间',
            prop: 'dispatchStaffTime'
          },
          {
            label: '取消原因',
            prop: 'cancelCode',
            search: true,
            type: 'select',
            dicData: DICT.reasonList,
            viewProp: 'cancelName',
            props: {
              label: 'name',
              value: 'code',
              res: 'records'
            }
          }
        ]
      }
    }
  },
  methods: {
    resetList() {
      this.searchForm = {
        ids: [],
        waybillIds: []
      }
      this.orderNoType = 1
      this.page.current = 1
      // this.searchChange()
    },
    searchFunParamsHandle(parms) {
      // 如果用户有输入订单号或者运单号，我们就要混入查询参数
      const { ids, waybillIds } = this.searchForm
      if (ids.length > 0) {
        parms.ids = ids
      }
      if (waybillIds.length > 0) {
        parms.waybillIds = waybillIds
      }
      const arr = ['orderStatus', 'netWorkCode', 'ids', 'waybillIds']
      const newParm = Object.assign({}, this.page, parms, this.formData)
      Object.keys(parms).forEach(item => {
        if (arr.includes(item)) {
          newParm[item] = this.first ? this.formData[item] : parms[item]
          // if (item !== 'netWorkCode') {
          // 有详情时去掉total参数
          delete newParm.total
          // }
        }
      })
      if (this.first) this.first = false
      return newParm
    },
    init() {
      // 过滤字典拼多多
      const dictList = commonFun.getDict('ZD24')
      this.getDictList = dictList.map(element => {
        return {
          ...element,
          name: element.name,
          label: element.label
        }
      })
      const arr = ['startDate', 'endDate', 'total', 'type', 'proxyAreaCode', 'netWorkCode', 'dispatchStaffCode']
      console.log({ ...this.detail })
      Object.keys(this.detail).forEach(item => {
        if (arr.includes(item) && this.detail[item] != null) {
          this.formData[item] = this.detail[item]
        }
      })
    }
    // resetList() {
    //   const { dateModel } = this.searchParams
    //   this.searchParams = { ...this.deepClone(searchParams), dateModel }
    // }
  }
}
</script>
