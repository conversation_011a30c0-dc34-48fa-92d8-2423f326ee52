<!--
 * @Author: your name
 * @Date: 2020-03-06 19:29:52
 * @LastEditTime: 2020-09-27 11:01:16
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \VIPSYSTEM\src\views\place-order\batch-order\components\CheckOrder.vue
 -->
<template>
  <div>
    <el-dialog lock-scroll
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="false"
      :title="$lang('以下客户订单号今日重复下单，是否继续')"
      :visible="repeatVisible"
      @close="hideDialog"
      width="550px">
      <div class="repeat-wrap">
            <div v-for="(item,index) in repeatOrderList" :key="index" class="repeat-wrap-item">
                    {{item}}
            </div>
      </div>
      <div class="dialog-footer" slot="footer">
        <el-button type="primary" @click="sureOrder">{{ $lang('确认下单') }}</el-button>
        <el-button @click="hideDialog">{{ $lang('取消下单') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'CheckOrder',
  data() {
    return {
    }
  },
  props: {
    repeatOrder: {
      type: String,
      default: '',
    },
    repeatVisible: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    repeatOrderList() {
      return this.repeatOrder.split(',')
    },
  },
  methods: {
    hideDialog() {
      this.$emit('checkOrderClose')
    },
    sureOrder() {
      this.$emit('sureOrder')
    },
  },
}
</script>
<style lang="scss" scoped>
.repeat-wrap{
    overflow: auto;
    height: 200px;
    // text-align: center;
    .repeat-wrap-item{
        display: inline-block;
        width: 100px;
    }
}
</style>
