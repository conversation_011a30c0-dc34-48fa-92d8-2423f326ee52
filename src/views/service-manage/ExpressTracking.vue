<!--
 * @Author: MX
 * @Date: 2020-08-03 11:36:59
 * @LastEditTime: 2021-03-27 09:40:57
 * @Description: 服务管理 - 快件跟踪
-->
<template>
  <div class="expressTracking main-wrap">
      <div class="track-left">
          <el-radio-group v-model="typeradio" size="medium">
            <el-radio  :label="1">{{$lang('普通单')}}</el-radio>
            <el-radio  :label="2">{{$lang('航空单')}}</el-radio>
          </el-radio-group>
          <base-input-tag ref="inputTag" v-model="searchKey" :validate="inputTagValid" :addTagOnBlur="true" maxHeight="360px" class="cms-input-tag"></base-input-tag>
          <div class="track-left-btns">
            <el-button type="primary" class="b_reset h32" plain  @click="resetSearch">{{ $lang('清空') }}</el-button>
            <el-button type="primary" class="h32"  :loading="keyBtn" @click="searchFun">{{ $lang('立即查询') }}</el-button>
          </div>
          <br>
          <br>
          <div class="tk_list tk_list_height">
              <div v-if="typeradio!==2">
                  <el-checkbox v-model="isShowAllSuborder">
                      <div class="max-width-196 line-height-1" :title="$lang('主单带出子单回单')">{{$lang('主单带出子单回单')}}</div>
                  </el-checkbox>
              </div>
              <div class="tk_list_title">
                  <div class="d_1">{{$lang('单号列表')}}</div>
              </div>
              <div class="tk_list_body" v-if="orderCodes.length>0">
                  <div class="tk_list_body_1">
                      <div>
                          <div class="tk_list_child" v-for="(item,index) in orderCodes" :key="index">
                              <div class="tk_list_child_item active" @click="searchByCode(item)"> {{item}} </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
     <div class="track-right" >
         <el-collapse v-model="activeName" v-if="traceList.length > 0">
              <el-collapse-item v-for="(tag, $index) in traceList" :key="tag.id" :title="$lang('运单编号')+'：' + tag.keyword" :name="$index">
                  <div class="track-right_item" v-if="tag.details&&tag.details.length > 0">
                      <div style="float: right">
                          <el-button type="text"  v-if="tag.picUrlList && tag.picUrlList.length > 0" @click="preViewImg(tag)">查看图片</el-button>
                      </div>
                      <el-steps direction="vertical" class="track-right_item_steps">
                          <el-step v-for="(item, index) in tag.details"
                                    :key="index"
                                    :title="item.scanTime"
                                    :class="[index === 0 ? 'isFirst':'']"
                                    :description="item.customerTracking">
                                     <template slot="icon">
                                        <span class="track-right_item_status"> {{$lang(item.status)}}</span>
                                    </template>
                          </el-step>
                      </el-steps>
                  </div>
                  <div v-else class="no-data">
                    {{ $lang('暂无数据') }}
                </div>
              </el-collapse-item>
         </el-collapse>
      </div>
  </div>
</template>

<script>
import { getDetailByWaybillNos, waybillList } from '@api/module/customer'
import { RESPONSE_SUCCESS } from '@constants'
export default {
  name: 'ExpressTracking',
  data() {
    return {
      typeradio: 1,
      isShowFirst: false,
      searchKey: [],
      searchKeyOld: [],
      traceList: [], // 物流跟踪list
      inputTagValid: 'orderName',
      orderIds: '',
      preViewVisible: false,
      keyBtn: false,
      activeName: 0,
      orderCodes: [],
      isShowAllSuborder: true, // 查询子单号
    }
  },
  mounted() {
    this.orderIds = this.$route.params.orderIds
    if (this.orderIds) {
      this.searchKey = this.orderIds.split(',')
      this.searchKeyOld = this.orderIds.split(',')
      this.searchFun()
    }
    const waybillId = this.$route.query.waybillId
    if (waybillId) {
      this.searchKey = waybillId.split(',')
      this.searchFun()
    }
  },
  activated() {
    const waybillId = this.$route.query.waybillId
    if (waybillId) {
      this.searchKey = waybillId.split(',')
      this.searchFun()
    }
  },
  watch: {

  },
  methods: {
    // 重置搜索
    resetSearch() {
      this.searchKey = []
      this.searchKeyOld = []
      this.traceList = []
      this.orderCodes = []
      this.$refs.inputTag.newTag = ''
      if (this.orderIds) this.$router.push({ path: `/redirect/expressTracking` })
    },
    clearData() {
      this.traceList = []
      this.orderCodes = []
    },
    // 搜索
    searchFun() {
      const reqList = []
      if (!this.searchKey || this.searchKey.length === 0) return this.$message.warning(this.$lang('请输入单号'))
      if (this.searchKey.length > 10) return this.$message.warning(this.$lang('您查询的单号已超出10条'))
      if (this.isShowAllSuborder && this.searchKey.length > 1 && (this.typeradio === 1 || this.typeradio === '1')) {
        return this.$message.warning(this.$lang('带出子单号，只能单独查询一条运单记录'))
      }
      //   const newArr = this.searchKey.filter(item => !this.searchKeyOld.some(ele => ele === item))// 单次只能查询10条数据
      //   if (newArr.length > 10) return this.$message.warning(this.$lang('单次只能查询10条数据'))
      //   this.searchKeyOld = this.searchKey.slice(0)
      this.searchKey.forEach(item => reqList.push(item))
      if (this.typeradio === 2 || this.typeradio === '2') {
        if (this.areElementsPrefixedWithSevenEight(reqList) === false) {
          return this.$message.warning(this.$lang('查询的运单号存在不属于航空单，请调整后重新查询'))
        }
      }
      this.getOrderList(reqList)
      this.orderCodes = []
      if (this.isShowAllSuborder) {
        this.getWaybillList(reqList[0])
      }
    },

    areElementsPrefixedWithSevenEight(array) {
      const regex = /^78\d{10}$/ // 以78开头，后跟9个数字
      return array.every(element => regex.test(element))
    },

    searchByCode(code1) {
      const reqList = []
      reqList.push(code1)
      this.getOrderList(reqList)
    },
    // 查询单号
    async getOrderList(reqList) {
      this.keyBtn = true
      const { code, msg, data } = await getDetailByWaybillNos(reqList)
      this.keyBtn = false
      if (code !== RESPONSE_SUCCESS) return this.$message.warning(this.$lang(msg))
      this.traceList = data
    },
    async getWaybillList(code1) {
      const param = { 'waybillNo': code1 }
      const { code, msg, data } = await waybillList(param)
      if (code === RESPONSE_SUCCESS) {
        this.orderCodes = data
      }
    },
    // 手机号加*
    disposePhoneFun(num) {
      let start = ''
      let end = ''
      if (num) {
        start = num.toString().substring(0, 3)
        end = num.toString().substring(num.length - 4, num.length)
      }
      return start + '****' + end
    },
    // 查看图片
    preViewImg(tag) {
      this.$viewerApi({
        images: tag.picUrlList,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.expressTracking{
    width: 100%;
    display: flex;
    flex-direction:row;
    .track-left{
        width: 440px;
        padding: 24px;
        box-sizing: border-box;
        .track-left-btns{
            float: right;
            margin-top: 16px;
        }
    }
    .b_reset{
        width: 88px;
    }
    .track-right {
        flex:1;
        border: 16px solid #EDEEF3;
        padding: 16px 24px;
        background: #fff;
        overflow: auto;
        .no-data {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
        .track-right_item{
                margin-left: 90px;
        }
        .track-right_item_status{
            position: absolute;
            top: -9px;
            right: 5px;
            line-height: 24px;
            width: 90px;
        }
    }

}
</style>
<style lang="scss">
.expressTracking{
     .input-tag-container {
          float: left;
          width: 100% !important;
          height: 160px;
          .vue-input-tag-wrapper {
            height: 160px !important;
            box-sizing: border-box;
          }
    }
    .el-collapse {
            border-bottom: 0;
            border-top-color: transparent !important;
          .el-collapse-item {
               background: #F7F7F9;
                border: 1px solid #EEEEEE;
                border-radius: 8px;
                margin-bottom: 24px;
                padding: 5px 24px;
            .el-collapse-item__header {
                background: #F7F7F9;
                font-weight: 600;
                justify-content: flex-start;
                font-size: 16px;
                padding-bottom: 0;
                border: 0;
                &.is-active{
                    color: $--color-primary;
                }
            }

            .el-collapse-item__wrap{
                background: #F7F7F9;
                border: 0;
          }
            .el-collapse-item__arrow{
                margin-left: 16px;
            }
          }
     }
     .el-step{
             position: relative;
             .el-step__head{
                 display: flex;
                justify-content: center;
             }
             .el-step__icon{
                width: 4px;
                height: 4px;
                border-radius: 50%;
                border: 0;
                background: #DDDDDD;
                position: relative;
                margin-top: 9px;
          }
          .el-step__description {
              margin-bottom:12px;
          }
          .el-step__line{
              top: 20px;
          }
          &.isFirst{
              .track-right_item_status,.el-step__title,.el-step__description{
                  color: #333333;
              }
              .el-step__icon{
                  width: 8px;
                    height: 8px;
                    background: #51c419;
              }
             .track-right_item_status{
                 font-weight: bold;
              }
          }
     }
    .tk_list {
        padding-top: 10px;
        width: 100%;
        height: calc(100% - 208px);
        overflow: hidden;
        &.tk_list_height{
            height: calc(100% - 258px);
        }
        .tk_list_count {
            width: 100%;
            height: 28px;
            line-height: 28px;
            font-size: 16px;
            color: #999999;
            /*margin-top: 8px;*/
            text-indent: 10px;
            text-align: right;
            label {
                font-size: 12px;
                padding-left: 6px;
            }
        }
        .tk_list_title {
            display: flex;
            height: 42px;
            line-height: 42px;
            // background: #fafafa;
            border: solid 1px;
            border-color:#dcdfe6;
            //  <!--@include bg-color('light');-->
            //  <!--@include border-color('lighter');-->
            div {
                // flex: 1;
                box-sizing: border-box;
                font-size: 14px;
                // color: #666666;
                //  <!--@include text-color('main');-->
                text-align: left;
                border-left: solid 1px;
                //  <!--@include border-color('lighter');-->
                /*padding-left:10px;*/
            }
            div:before{
                content: "|";
                display: inline-table;
                width: 2px;
                font-size: 12px;
                bottom: 2px;
                // color: #666666;
                //  <!--@include text-color('main');-->
                position: relative;
                left: -12px;
            }
            .d_1 {
                border-left: none;
                font-weight: bolder;
                margin-right: 10px;
            }
        }
        .tk_list_body{
            border: solid 1px;
            // <!--@include border-color('lighter');-->
            border-top: none;
            border-color:#dcdfe6;
            //  <!--@include bg-color('main');-->
            height: calc(100% - 84px);
            box-sizing: border-box;
            padding: 10px;
            .tk_list_body_1{
                height: 100%;
                box-sizing: border-box;
                overflow-y: auto;
            }
            .no-data{
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .tk_list_child{
                margin-bottom:10px;
                width: 98%;
                .tk_list_child_item{
                    height:28px;
                    line-height:28px;
                    border-radius:4px;
                    text-align:center;
                    overflow: hidden;
                    cursor: pointer;
                    color: #333333 ;
                    // user-select: none;
                    position: relative;
                    border: 1px solid;
                    //<!--@include border-color('lighter');-->
                    //<!--@include text-color('routine');-->
                    &:hover{
                        // <!--@include text-color('brand');-->
                    }
                    &.is-no{
                        cursor:auto;
                        background:rgba(255,85,85,0.1);
                        color: #FF5555;
                    }
                    &.is-no:hover{
                        color: #FF5555;
                    }
                    &.active{
                        //    <!--@include border-color('brand');-->
                        //     <!--@include text-color('brand');-->
                    }
                    .show-childs{
                        width: 24px;
                        height: 28px;
                        // border: 1px solid red;
                        position: absolute;
                        right: 0px;
                        top: 0px;
                        transition: all .2s;
                        &.hide-childs{
                            transform:rotate(90deg);
                        }
                    }
                }
                .tk_List_child_tip{
                    user-select: none;
                    color: #FF5555;
                    margin-top:4px;
                    font-size: 12px;
                    text-align:center;
                    .custom-class {
                        position: relative;
                        top: 1px;
                        margin-right: 4px;
                    }
                }
                .tk_list_child_box{
                    height: 0px;
                    overflow: hidden;
                    transition: all .3s;
                    &.show{
                        height: auto;
                    }
                    .tk_list_child_child{
                        height:16px;
                        font-size:12px;
                        color:rgba(153,153,153,1);
                        line-height:16px;
                        padding-top: 4px;
                        margin-top: 4px;
                        text-align:center;
                        cursor: pointer;
                        &.active{
                            color:rgba(45,124,255,1);
                        }
                    }
                }
                .waybill-status-box{
                    margin-top: 4px;
                    div{
                        // border: 1px solid;
                        // @include border-color('lighter');
                     //   <!--@include text-color('occupy');-->
                        display: inline-block;
                        width: 22px;
                        height: 22px;
                        border-radius: 4px;
                        font-size: 11px;
                        text-align: center;
                        line-height: 22px;
                        margin-right: 4px;
                        font-weight: bold;
                        &.wen-lan{
                            color:#F32434;
                            background: rgba($color: #F32434, $alpha: .2);
                        }
                        &.tui-zhuan{
                            color:#FF8500;
                            background: rgba($color: #FF8500, $alpha: .2);
                        }
                        &.liu{
                            color:#FF89D1;
                            background: rgba($color: #FF89D1, $alpha: .2);
                        }
                    }
                }
            }
        }
        .tk_list_item {
            display: flex;
            width: 100%;
            height: 40px;
            line-height: 40px;
            overflow: hidden;
            cursor: pointer;
            .current-selected{
                color: #2D7CFF;
            }
            div {
                flex: 1;
                padding: 0 5px;
                font-size: 12px;
                color: #666666;
                text-overflow: ellipsis;
                white-space: nowrap;
                border-bottom: #dedede solid 1px;
                overflow: hidden;
                i {
                    font-style: normal;
                    color: #999999;
                }
                .i_0 {
                    color: #ff4b4b;
                }
                .i_100 {
                    color: #00da00;
                }
            }
            .tk_list_statu{
                text-align: center;

            }
            .bd-left{
                border-left: #d8d8d8 solid 1px;
            }
            .d_1 {
                border-left: none;
            }
        }
    }
}
.el-radio-group{
    margin-bottom: 5px;
}
.el-radio__label{
    font-size: 16px;
}
.el-radio__inner{
    width: 16px;
    height: 16px;
}

</style>

